import { useState } from 'react';
import { apiClient } from '../utils/api';

interface ApiDemoState {
  loading: boolean;
  data: any;
  error: string | null;
}

export function ApiDemo() {
  const [serverInfo, setServerInfo] = useState<ApiDemoState>({
    loading: false,
    data: null,
    error: null,
  });

  const [demoData, setDemoData] = useState<ApiDemoState>({
    loading: false,
    data: null,
    error: null,
  });

  const [healthCheck, setHealthCheck] = useState<ApiDemoState>({
    loading: false,
    data: null,
    error: null,
  });

  const fetchServerInfo = async () => {
    setServerInfo({ loading: true, data: null, error: null });
    const response = await apiClient.getServerInfo();
    setServerInfo({
      loading: false,
      data: response.data,
      error: response.error || null,
    });
  };

  const fetchDemoData = async () => {
    setDemoData({ loading: true, data: null, error: null });
    const response = await apiClient.getDemoData();
    setDemoData({
      loading: false,
      data: response.data,
      error: response.error || null,
    });
  };

  const fetchHealthCheck = async () => {
    setHealthCheck({ loading: true, data: null, error: null });
    const response = await apiClient.getHealthCheck();
    setHealthCheck({
      loading: false,
      data: response.data,
      error: response.error || null,
    });
  };

  const renderApiSection = (
    title: string,
    state: ApiDemoState,
    onFetch: () => void
  ) => (
    <div style={{ marginBottom: '2rem', padding: '1rem', border: '1px solid #ddd', borderRadius: '8px' }}>
      <h3 style={{ marginTop: 0 }}>{title}</h3>
      <button
        onClick={onFetch}
        disabled={state.loading}
        style={{
          padding: '0.5rem 1rem',
          backgroundColor: state.loading ? '#ccc' : '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: state.loading ? 'not-allowed' : 'pointer',
          marginBottom: '1rem',
        }}
      >
        {state.loading ? 'Loading...' : 'Fetch Data'}
      </button>

      {state.error && (
        <div style={{ color: 'red', marginBottom: '1rem' }}>
          <strong>Error:</strong> {state.error}
        </div>
      )}

      {state.data && (
        <pre style={{
          backgroundColor: '#f8f9fa',
          padding: '1rem',
          borderRadius: '4px',
          overflow: 'auto',
          fontSize: '0.875rem',
        }}>
          {JSON.stringify(state.data, null, 2)}
        </pre>
      )}
    </div>
  );

  return (
    <div style={{ maxWidth: '800px', margin: '0 auto', padding: '2rem' }}>
      <h2>Frontend-Backend Communication Demo</h2>
      <p>This demo shows the frontend communicating with the new Express server.</p>

      {renderApiSection('Server Info (GET /)', serverInfo, fetchServerInfo)}
      {renderApiSection('Demo Data (GET /v1/demo/)', demoData, fetchDemoData)}
      {renderApiSection('Health Check (GET /v1/demo/health)', healthCheck, fetchHealthCheck)}
    </div>
  );
}

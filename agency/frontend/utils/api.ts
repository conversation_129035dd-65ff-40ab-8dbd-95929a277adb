import axios from '../lib/axios';

interface ApiResponse<T = any> {
  data?: T;
  error?: string;
}

class ApiClient {
  private baseUrl: string;

  constructor(baseUrl?: string) {
    this.baseUrl = baseUrl || '';
  }

  async request<T = any>(endpoint: string, options: {
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    body?: any;
    headers?: Record<string, string>;
  } = {}): Promise<ApiResponse<T>> {
    try {
      const { method = 'GET', body, headers } = options;
      
      const response = await axios.request({
        url: endpoint,
        method,
        data: body,
        headers,
      });

      return { data: response.data };
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 
                          error.message || 
                          'An unknown error occurred';
      return { error: errorMessage };
    }
  }

  async get<T = any>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  async post<T = any>(endpoint: string, body?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'POST', body });
  }

  async put<T = any>(endpoint: string, body?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'PUT', body });
  }

  async delete<T = any>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  // Demo API methods
  async getDemoData() {
    return this.get('/v1/demo/');
  }

  async getHealthCheck() {
    return this.get('/v1/demo/health');
  }

  async getServerInfo() {
    return this.get('/');
  }
}

export const apiClient = new ApiClient();
export default apiClient;

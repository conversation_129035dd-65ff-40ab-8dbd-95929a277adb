{"include": ["app/**/*", "components/**/*", "lib/**/*", "utils/**/*", "welcome/**/*", "*.ts", "*.tsx"], "exclude": ["node_modules", "build", ".react-router/**/*"], "compilerOptions": {"lib": ["DOM", "DOM.Iterable", "ES2022"], "types": ["node", "vite/client"], "target": "ES2022", "module": "ES2022", "moduleResolution": "bundler", "jsx": "react-jsx", "rootDirs": ["."], "baseUrl": ".", "paths": {"~/*": ["./*"]}, "esModuleInterop": true, "verbatimModuleSyntax": false, "allowImportingTsExtensions": true, "allowJs": true, "noEmit": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true}}
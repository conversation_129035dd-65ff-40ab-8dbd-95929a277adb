declare global {
  interface Window {
    ENV?: {
      PUBLIC_AGENCY_SERVER_URL?: string;
    };
  }
}

import axios from "axios";

const API_BASE_URL =
  typeof window !== "undefined"
    ? window.ENV?.PUBLIC_AGENCY_SERVER_URL || "http://localhost:8000"
    : process.env.PUBLIC_AGENCY_SERVER_URL || "http://localhost:8000";

export default axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

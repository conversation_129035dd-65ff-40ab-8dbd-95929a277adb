import { Request, Response, NextFunction } from 'express';

/**
 * A utility function to catch errors in async functions
 * @param fn - The async function to wrap
 * @returns Express middleware function
 */
type AsyncFunction = (req: Request, res: Response, next: NextFunction) => Promise<any>;

const catchAsync = (fn: AsyncFunction) => (req: Request, res: Response, next: NextFunction) => {
  Promise.resolve(fn(req, res, next)).catch((err: Error) => {
    console.log("err: ", err);
    next(err);
  });
};

export = catchAsync;

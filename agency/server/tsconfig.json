{"compilerOptions": {"target": "ES2018", "module": "CommonJS", "lib": ["ES2020"], "outDir": "./dist", "rootDir": ".", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declarationMap": false, "sourceMap": false, "removeComments": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "typeRoots": ["./node_modules/@types", "./types"], "baseUrl": "./", "paths": {"@/*": ["./*"], "web/*": ["../../web/*"]}}, "include": ["**/*.ts", "**/*.js"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"esm": false, "experimentalSpecifierResolution": "node"}}
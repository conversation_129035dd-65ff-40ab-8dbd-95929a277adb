import * as dotenv from "dotenv";
import * as path from "path";
import * as yup from "yup";

dotenv.config({ path: path.join(process.cwd(), ".env") });

interface EnvVars {
  NODE_ENV: string;
  AGENCY_SERVER_PORT: number;
  AGENCY_SERVER_URL: string;
  AGENCY_JWT_SECRET: string;
  AGENCY_JWT_ACCESS_EXPIRATION: number;
  AGENCY_JWT_REFRESH_EXPIRATION: number;
}

const envVarsSchema = yup.object().shape({
  NODE_ENV: yup.string().oneOf(["production", "development", "test"]).required(),
  AGENCY_SERVER_PORT: yup.number().default(8001).required(),
  AGENCY_SERVER_URL: yup.string().required(),
  AGENCY_JWT_SECRET: yup.string().required(),
  AGENCY_JWT_ACCESS_EXPIRATION: yup.number().default(300000),
  AGENCY_JWT_REFRESH_EXPIRATION: yup.number().default(300000),
});

interface Config {
  env: string;
  port: number;
  HOST: string;
  jwt: {
    secret: string;
    accessTokenExpirationDuration: number;
    refreshTokenExpirationDuration: number;
  };
}

let config: Config;

try {
  const envVars = envVarsSchema.validateSync(process.env, { abortEarly: false }) as EnvVars;

  config = {
    env: envVars.NODE_ENV,
    port: envVars.AGENCY_SERVER_PORT,
    HOST: envVars.AGENCY_SERVER_URL,
    jwt: {
      secret: envVars.AGENCY_JWT_SECRET,
      accessTokenExpirationDuration: envVars.AGENCY_JWT_ACCESS_EXPIRATION,
      refreshTokenExpirationDuration: envVars.AGENCY_JWT_REFRESH_EXPIRATION,
    },
  };
} catch (error: any) {
  throw new Error(`Config validation error: ${error.errors.join(", ")}`);
}

export = config;

import { ExtractJwt, Strategy as JwtStrategy, StrategyOptions, VerifiedCallback } from "passport-jwt";
import config = require("./config");
import userTokenType = require("./userTokenType");

interface JwtPayload {
  sub: string;
  email: string;
  name: string;
  scopes?: string[];
  type: string;
  iat?: number;
  exp?: number;
}

interface User {
  id: string;
  email: string;
  name: string;
  scopes: string[];
  type: string;
}

const jwtOptions: StrategyOptions = {
  secretOrKey: config.jwt.secret,
  jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
};

const jwtVerify = async (payload: JwtPayload, done: VerifiedCallback): Promise<void> => {
  try {
    if (payload.type !== userTokenType.ACCESS) {
      throw new Error("Invalid token type");
    }
    
    // For now, we'll create a simple user verification
    // This can be extended later with actual user service
    const user: User = {
      id: payload.sub,
      email: payload.email,
      name: payload.name,
      scopes: payload.scopes || [],
      type: payload.type
    };
    
    if (!user.id) {
      return done(null, false);
    }
    done(null, user);
  } catch (error) {
    done(error, false);
  }
};

export = new JwtStrategy(jwtOptions, jwtVerify);

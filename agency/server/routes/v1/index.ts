import { Router } from "express";
import demoRoute = require("./demo.route");

const router: Router = Router();

interface RouteConfig {
  path: string;
  route: Router;
}

const defaultRoutes: RouteConfig[] = [
  {
    path: "/demo",
    route: demoRoute,
  },
];

// TODO: API documentation with swagger
const devRoutes: RouteConfig[] = [
  // routes available only in development mode
];

defaultRoutes.forEach((route) => {
  router.use(route.path, route.route);
});

/* istanbul ignore next */
if (process.env.NODE_ENV === "development") {
  devRoutes.forEach((route) => {
    router.use(route.path, route.route);
  });
}

export = router;

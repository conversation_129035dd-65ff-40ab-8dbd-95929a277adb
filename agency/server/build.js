const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Clean dist directory
if (fs.existsSync('./dist')) {
  fs.rmSync('./dist', { recursive: true, force: true });
}

// Run TypeScript compilation
console.log('Compiling TypeScript...');
try {
  execSync('tsc', { stdio: 'inherit' });
} catch (error) {
  console.error('TypeScript compilation failed');
  process.exit(1);
}

// Function to copy files recursively
function copyRecursive(src, dest) {
  const stat = fs.statSync(src);
  if (stat.isDirectory()) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }
    const files = fs.readdirSync(src);
    files.forEach(file => {
      copyRecursive(path.join(src, file), path.join(dest, file));
    });
  } else {
    fs.copyFileSync(src, dest);
  }
}

// Move server files from nested structure to root of dist
const serverDistPath = './dist/agency/server';
const finalDistPath = './dist';

if (fs.existsSync(serverDistPath)) {
  console.log('Reorganizing build output...');
  
  // Create a temporary directory for the server files
  const tempDir = './temp_server_files';
  if (fs.existsSync(tempDir)) {
    fs.rmSync(tempDir, { recursive: true, force: true });
  }
  
  // Copy server files to temp directory
  copyRecursive(serverDistPath, tempDir);
  
  // Clean dist directory
  fs.rmSync('./dist', { recursive: true, force: true });
  
  // Move temp files to dist
  fs.renameSync(tempDir, './dist');
  
  console.log('Build completed successfully!');
} else {
  console.log('Build completed, but server files not found in expected location.');
  listDistContents('./dist');
}

function listDistContents(dir, indent = '') {
  if (!fs.existsSync(dir)) return;
  const items = fs.readdirSync(dir);
  items.forEach(item => {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    console.log(`${indent}${item}${stat.isDirectory() ? '/' : ''}`);
    if (stat.isDirectory()) {
      listDistContents(fullPath, indent + '  ');
    }
  });
}

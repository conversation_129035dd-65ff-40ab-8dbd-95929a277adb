import { Request, Response, NextFunction } from "express";
import { inHTMLData } from "xss-filters";

/**
 * Clean for xss.
 * @param data - The value to sanitize
 * @returns The sanitized value
 */
const clean = (data: any = ""): any => {
  let isObject = false;
  if (typeof data === "object") {
    data = JSON.stringify(data);
    isObject = true;
  }

  data = inHTMLData(data).trim();
  if (isObject) data = JSON.parse(data);

  return data;
};

const middleware = () => {
  return (req: Request, _res: Response, next: NextFunction): void => {
    if (req.body) req.body = clean(req.body);
    if (req.query) req.query = clean(req.query);
    if (req.params) req.params = clean(req.params);
    next();
  };
};

export = { middleware, clean };

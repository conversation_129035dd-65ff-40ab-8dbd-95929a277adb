const helmet = require("helmet");
const cors = require("cors");
const passport = require("passport");
const httpStatus = require("http-status");
const express = require("express");
const morgan = require("morgan");
const fileUpload = require("express-fileupload");

// Type imports
import type { NextFunction, Request, Response } from "express";

const { errorConverter, errorHandler } = require("./middlewares/error");
const { middleware: xss } = require("./middlewares/xss");
const jwtStrategy = require("./config/passport");
const ApiError = require("./utils/apiError");
const routes = require("./routes/v1");
const config = require("./config/config");

try {

  const app = express();
  
  // set security HTTP headers
  app.use(helmet());
  
  // parse json request body
  app.use(express.json());
  
  // parse urlencoded request body
  app.use(express.urlencoded({ extended: true }));
  
  // sanitize request data
  app.use(xss());
  
  // enable cors
  app.use(cors());
  app.options("*", cors());
  
  // jwt authentication
  app.use(passport.initialize());
  app.use(fileUpload({}));

  passport.use("jwt", jwtStrategy);

  // log requests to console
  app.use(morgan(":method :status :url :res[content-length] - :response-time ms"));

  app.get("/", (req: Request, res: Response) => {
    console.info("Agency server received a request");
    res.json({ message: "Agency Server API - Hello World!" });
  });

  // v1 api routes
  app.use("/v1", routes);

  // send back a 404 error for any unknown api request
  app.use((req: Request, res: Response, next: NextFunction) => {
    next(new ApiError(httpStatus.NOT_FOUND, "Not found"));
  });

  // convert error to ApiError, if needed
  app.use(errorConverter);

  // handle error
  app.use(errorHandler);

  app
    .listen(config.port, () => {
      console.info(`Agency server running here ${config.HOST}`);
      console.info(`Press Ctrl+C to quit.`);
    })
    .on("error", (err: Error) => {
      console.error(err);
    });
} catch (error) {
  console.error("error =", error);
}

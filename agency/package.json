{"name": "agency", "private": true, "type": "module", "workspaces": ["frontend", "server"], "scripts": {"build": "pnpm run build:frontend && pnpm run build:server", "build:frontend": "pnpm --filter agency-frontend build", "build:server": "pnpm --filter agency-server build", "dev": "concurrently \"pnpm run dev:frontend\" \"pnpm run dev:server\"", "dev:frontend": "pnpm --filter agency-frontend dev", "dev:server": "pnpm --filter agency-server dev:watch", "start": "concurrently \"pnpm run start:frontend\" \"pnpm run start:server\"", "start:frontend": "pnpm --filter agency-frontend start", "start:server": "pnpm --filter agency-server start"}, "dependencies": {}, "devDependencies": {"concurrently": "^8.2.2", "typescript": "^5.8.3"}}
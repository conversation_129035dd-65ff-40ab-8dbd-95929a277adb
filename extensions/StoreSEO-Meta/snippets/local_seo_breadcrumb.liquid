{% if shop.metafields.store_seo.local_seo_breadcrumb_schema.value != null %}
  {% assign bread_crumb_settings = shop.metafields.store_seo.local_seo_breadcrumb_schema.value %}
  {% if bread_crumb_settings.status == true %}
    <script
      type="application/ld+json"
      injected-by-storeseo="true"
    >
      {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [{
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": {{ shop.url | json }}
        }
        {% case template %}
        {% comment %} For blog page {% endcomment %}
        {% when 'blog' %}
        {% if current_tags %}
        ,{
          "@type": "ListItem",
          "position": 2,
          "name": {{ blog.title | json }},
          "item": {{ shop.url | append: blog.url | json }}
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": {{ current_tags | join: ' + ' | json }}
        }
        {% else %}
        ,{
          "@type": "ListItem",
          "position": 2,
          "name": {{ blog.title | json }}
        }
        {% endif %}
        {% comment %} For product page {% endcomment %}
        {% when 'product' %}
        {% if product.collections.size > 0  %}
        ,{
          "@type": "ListItem",
          "position": 2,
          "name": {{ product.collections.first.title | json }},
          "item": {{  shop.url | append:product.collections.first.url | json }}
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": {{ product.title | json }}
        }
        {% else %}
        ,{
          "@type": "ListItem",
          "position": 2,
          "name": {{ product.title | json }}
        }
        {% endif %}
        {% when 'article' %}
        ,{
          "@type": "ListItem",
          "position": 2,
          "name": {{ blog.title | json }},
          "item": {{ shop.url | append: blog.url | json }}
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": {{ article.title | json }}
        }
        {% else %}
        ,{
          "@type": "ListItem",
          "position": 2,
          "name": {{ page_title | json }}
        }
        {% endcase %}
        ]
      }
    </script>
  {% endif %}
{% endif %}

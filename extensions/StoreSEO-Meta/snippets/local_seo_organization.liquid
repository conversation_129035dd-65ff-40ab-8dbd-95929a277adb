{%- if request.page_type == 'index' -%}
  {% if shop.metafields.store_seo.local_seo_organization_schema.value != null %}
    {% assign organization_schema_settings = shop.metafields.store_seo.local_seo_organization_schema.value %}
    {% assign common_schema_settings = shop.metafields.store_seo.local_seo_common_schema.value %}

    {% if organization_schema_settings.status == true %}
      <script
        type="application/ld+json"
        injected-by-storeseo="true"
      >
      {
        "@context": "https://schema.org",
        "@type": "Organization",
        "url": {{ shop.url | json }},
        "sameAs": [
          {{ organization_schema_settings.settings.socialProfiles.twitter |  json }},
          {{ organization_schema_settings.settings.socialProfiles.facebook |  json }},
          {{ organization_schema_settings.settings.socialProfiles.instagram |  json }},
          {{ organization_schema_settings.settings.socialProfiles.pinterest |  json }},
          {{ organization_schema_settings.settings.socialProfiles.linkedin |  json }}
        ],
        "logo": {{ common_schema_settings.store_logo |  json }},
        "name": {{ common_schema_settings.basicInformation.businessName |  json }},
        "address": {
          "@type": "PostalAddress",
          "streetAddress": {{ organization_schema_settings.settings.address.streetAddress |  json }},
          "addressLocality": {{ organization_schema_settings.settings.address.city |  json }},
          "addressCountry": {{ organization_schema_settings.settings.address.country |  json }},
          "addressRegion": {{ organization_schema_settings.settings.address.state |  json }},
          "postalCode": {{ organization_schema_settings.settings.address.postalCode |  json }}
        },
          "contactPoint": {
          "@type": "ContactPoint",
          "telephone": {{ organization_schema_settings.settings.address.telephone |  json }}
        }
      }
      </script>
    {% endif %}
  {% endif %}
{% endif %}

{%- if product and shop.metafields.store_seo.jsonld_enabled == "true" -%}
  <script type="application/ld+json">
    {
      "@context": "http://schema.org/",
      "@type": "Product",
      "name": {{ product.title | json }},
      "url": {{ shop.url | append: product.url | json }},
      "image": {{ product | image_url: width: 115 | prepend: "https:" | json }},
      "description": {{ product.description | strip_html | json }},
      {% if product.selected_or_first_available_variant.sku != blank -%}
        "sku": {{ product.selected_or_first_available_variant.sku | json }},
        {%- endif %}
        "brand": {
          "@type": "Brand",
          "name": {{ product.vendor | json }}
        },
        "offers": [
            {%- for variant in product.variants -%}
            {
                "@type": "Offer",
                {%- if variant.sku != blank -%}
                "sku": {{ variant.sku | json }},
                {%- endif -%}
                "availability": "http://schema.org/{% if variant.available %} InStock{% else %}OutOfStock{% endif %}",
                "price": {{ variant.price | divided_by: 100.00 | json }},
                "priceCurrency": {{ cart.currency.iso_code | json }},
                "url": {{ shop.url | append: variant.url | json }}
              }{% unless forloop.last %},{% endunless %}
            {%- endfor -%}
          ]
        }
  </script>
{%- endif -%}
{%- if product.metafields.store_seo.facebook_preview_image_url.size > 2 -%}
  <meta name="og:image" content="{{ product.metafields.store_seo.facebook_preview_image_url | default: page_image }}" />
{%- endif -%}

{%- if product.metafields.store_seo.twitter_preview_image_url.size > 2 -%}
  <meta name="twitter:image" content="{{ product.metafields.store_seo.twitter_preview_image_url | default: page_image }}" />
{%- endif -%}

{%- if article.metafields.store_seo.facebook_preview_image_url.size > 2 -%}
  <meta name="og:image" content="{{ article.metafields.store_seo.facebook_preview_image_url | default: page_image }}" />
{%- endif -%}

{%- if article.metafields.store_seo.twitter_preview_image_url.size > 2 -%}
  <meta name="twitter:image" content="{{ article.metafields.store_seo.twitter_preview_image_url | default: page_image }}" />
{%- endif -%}

{%- if page.metafields.store_seo.facebook_preview_image_url.size > 2 -%}
  <meta name="og:image" content="{{ page.metafields.store_seo.facebook_preview_image_url | default: page_image }}" />
{%- endif -%}

{%- if page.metafields.store_seo.twitter_preview_image_url.size > 2 -%}
  <meta name="twitter:image" content="{{ page.metafields.store_seo.twitter_preview_image_url | default: page_image }}" />
{%- endif -%}
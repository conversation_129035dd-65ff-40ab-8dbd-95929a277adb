{% if template contains 'blog' %}
  {% if shop.metafields.store_seo.local_seo_blog_schema.value != null %}
    {% assign blog_schema_settings = shop.metafields.store_seo.local_seo_blog_schema.value %}

    {% if blog_schema_settings.status == true %}
      <script
        type="application/ld+json"
        injected-by-storeseo="true"
      >
        {
          "@context": "https://schema.org",
          "@type": "ItemList",
          "name": {{ blog.title | json }},
          "itemListElement": [
            {% for article in blog.articles %}
            {
              "@type": "ListItem",
              "position": {{ forloop.index }},
              "url": {{ shop.url | append:article.url | json }}
            }{% unless forloop.last %},{% endunless -%}
            {% endfor %}
          ]
        }
      </script>
    {% endif %}
  {% endif %}
{% endif %}

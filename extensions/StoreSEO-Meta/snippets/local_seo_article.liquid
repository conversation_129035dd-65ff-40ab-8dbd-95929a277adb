{%- if request.page_type == 'article' -%}
  {% if shop.metafields.store_seo.local_seo_article_schema.value != null %}
    {% assign article_schema_settings = shop.metafields.store_seo.local_seo_article_schema.value %}
    {% assign common_schema_settings = shop.metafields.store_seo.local_seo_common_schema.value %}

    {% if article_schema_settings.status == true %}
      <script type="application/ld+json" injected-by-storeseo="true">
        {
          "@context": "http://schema.org",
          "@type": "Article",
          "articleBody": {{ article.content | strip_html | json }},
          "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": {{ request.origin | append: page.url | json }}
          },
          "headline": {{ article.title | json }},
          {% if article.excerpt != blank %}
            "description": {{ article.excerpt | strip_html | json }},
          {% endif %}
          {% if article.image %}
            "image": [
              {{ article | image_url: width: 1920 | prepend: "https:" | json }}
            ],
          {% endif %}
          "datePublished": {{ article.published_at | date: '%Y-%m-%dT%H:%M:%SZ' | json }},
          "dateCreated": {{ article.created_at | date: '%Y-%m-%dT%H:%M:%SZ' | json }},
          "author": {
            "@type": "Person",
            "name": {{ article.author | json }},
            "url" : {{ shop.url | json }}
          },
          "publisher": {
            "@type": "Organization",
            "name": {{ shop.name | json }}
            {% if common_schema_settings.store_logo %}
            ,"logo": {
              "@type": "ImageObject",
              "url": {{ common_schema_settings.store_logo |  json }}
            }
            {% endif %}
          }
        }
      </script>
    {% endif %}
  {% endif %}
{% endif %}
{%- if request.page_type == 'index' -%}
  {% if shop.metafields.store_seo.local_seo_local_business_schema.value != null %}
    {% assign business_schema_settings = shop.metafields.store_seo.local_seo_local_business_schema.value %}
    {% assign common_schema_settings = shop.metafields.store_seo.local_seo_common_schema.value %}

    {% if business_schema_settings.status == true %}
      <script type="application/ld+json" injected-by-storeseo="true">
        {
          "@context": "https://schema.org",
          "@type": "Service",
          "serviceType": {{ common_schema_settings.basicInformation.businessType |  json }},
          "provider": {
            "@type": "LocalBusiness",
            "name": {{ common_schema_settings.basicInformation.businessName |  json }},
            "image": [
              {{ common_schema_settings.store_logo |  json }}
            ],
            "address": {
              "@type": "PostalAddress",
              "streetAddress": {{ business_schema_settings.settings.address.streetAddress |  json }},
              "addressLocality": {{ business_schema_settings.settings.address.locality |  json }},
              "addressRegion": {{ business_schema_settings.settings.address.region |  json }},
              "postalCode": {{ business_schema_settings.settings.address.postalCode |  json }}
            },
            "logo": {{ common_schema_settings.store_logo |  json }},
            "url": {{ shop.url | json }},
            "telephone": {{ business_schema_settings.settings.address.telephone |  json }},
            "priceRange": {{ common_schema_settings.basicInformation.businessPriceRange |  json }},
            "openingHoursSpecification": [
              {%  for workingDay in business_schema_settings.settings.workingDays  %} 
              {
                "@type": "OpeningHoursSpecification",
                "dayOfWeek": [
                  {{ workingDay.day |  json }}
                ],
                "opens": {{ workingDay.openingTime | json }},
                "closes": {{ workingDay.closingTime | json }}
              }
              {% unless forloop.last %},{% endunless %}
              {% endfor %}
            ]
          }
        }
      </script>
    {% endif %}
  {% endif %}
{% endif %}
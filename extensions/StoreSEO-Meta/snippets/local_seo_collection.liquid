{% if template contains 'collection' %}
  {% if shop.metafields.store_seo.local_seo_collection_schema.value != null %}
    {% assign collection_schema_settings = shop.metafields.store_seo.local_seo_collection_schema.value %}

    {% if collection_schema_settings.status == true %}
      <script
        type="application/ld+json"
        injected-by-storeseo="true"
      >
        {
          "@context": "https://schema.org",
          "@type": "ItemList",
          "itemListElement": [
            {% for product in collection.products %}
            {
              "@type": "ListItem",
              "position": {{ forloop.index }},
              "url": {{ shop.url | append:product.url | json }}
            }{% unless forloop.last %},{% endunless -%}
            {% endfor %}
          ]
        }
      </script>
    {% endif %}
  {% endif %}
{% endif %}

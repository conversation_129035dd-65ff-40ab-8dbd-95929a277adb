# StoreSEO Email Notification System - Quick Reference

## File Structure Overview

```
web/api/
├── queue/
│   ├── jobs/email/
│   │   ├── GenerateEmailQueue.js          # Email content generation
│   │   ├── EmailGenerateProcessor.js      # Email data processors
│   │   ├── SendEmailQueue.js              # Email delivery
│   │   └── EmailNotificationTriggerQueue.js # Scheduled triggers
│   └── email/
│       └── emailWatcher.js                # Email delivery monitor
├── services/email/
│   └── EmailTrackerService.js             # Database operations
├── config/
│   ├── email-notification/index.js        # Configuration
│   └── driver/
│       ├── mailgun.js                     # Mailgun provider
│       └── elasticEmail.js                # ElasticEmail provider
├── templates/                             # Email templates
├── controllers/
│   ├── SettingController.js               # Email settings API
│   └── NotificationController.js          # Notifications API
└── utils/
    └── emailTemplateBuilder.js            # Template utilities

web/frontend/
├── pages/settings/email-notification.jsx  # Settings UI
└── hooks/apiHooks/useEmailNotificationApi.js # API hooks

web/cronjobs/
├── kernel.js                              # Cron job definitions
└── tasks/events/daily-store-data-check.js # Daily email triggers

web/sequelize/
├── models/emailtracker.js                 # Database model
└── attributes/email-tracker-attributes.js # Model attributes
```

## Key Queue Names

```javascript
const QUEUE_NAMES = {
  GENERATE_EMAIL_QUEUE: "generate.email",
  SEND_EMAIL: "send.email", 
  EMAIL_NOTIFICATION_TRIGGER_QUEUE: "email.notification.trigger"
};
```

## Email Status Flow

```
READY_FOR_DELIVERY → QUEUED → DELIVERED
                           → DELIVERY_FAILED
                           → INTERNAL_EMAIL_SKIPPED_DELIVERY
```

## Event Topics (Email Types)

### Scheduled Reports
- `WEEKLY_STORE_REPORT`
- `AUTO_AI_CONTENT_OPTIMIZATION_REPORT`

### Integration Reminders  
- `GOOGLE_ANALYTICS_INTEGRATION`
- `GOOGLE_SEARCH_CONSOLE_INTEGRATION`
- `HTML_SITEMAP_INTEGRATION`
- `AUTO_IMAGE_OPTIMIZER_REMINDER`

### Usage Notifications
- `IMAGE_OPTIMIZER_USAGE_NOTIFICATION`
- `AI_CONTENT_OPTIMIZER_USAGE_NOTIFICATION`

### Instant Notifications
- `BULK_AI_CONTENT_OPTIMIZATION_COMPLETE`
- `BULK_IMAGE_OPTIMIZATION_COMPLETE`
- `PRODUCT_SYNC_COMPLETE`

## Common Code Patterns

### Dispatching Email Generation
```javascript
const { dispatchQueue } = require("../queue/queueDispatcher");
const { QUEUE_NAMES } = require("../queue/config");
const EventTopics = require("storeseo-enums/eventTopics");

dispatchQueue({
  queueName: QUEUE_NAMES.GENERATE_EMAIL_QUEUE,
  message: {
    shopDomain: shop.domain,
    topic: EventTopics.WEEKLY_STORE_REPORT,
    deliveryDate: new Date(), // or scheduled date
    bulkOperationTrackerId: id // only for bulk operations (BULK_AI_CONTENT_OPTIMIZATION_COMPLETE, BULK_IMAGE_OPTIMIZATION_COMPLETE)
  }
});
```

### Checking Email Settings
```javascript
const emailNotificationSettings = await ShopService.getShopSetting(
  shop.id, 
  EMAIL_NOTIFICATION
);

const weeklyReportSettings = emailNotificationSettings?.value?.items?.WEEKLY_STORE_REPORT;
if (weeklyReportSettings?.enabled) {
  // Trigger email
}
```

### Adding Email Processor
```javascript
// In EmailGenerateProcessor.js
async function generateEmailDataForNewFeature(shop) {
  const data = {
    store: {
      name: shop.name,
      domain: shop.domain,
      // ... other store data
    },
    // ... feature-specific data
  };

  return { data };
}

// For bulk operations, include bulkOperationTrackerId parameter
async function generateEmailDataForBulkImageOptimizationComplete(shop, bulkOperationTrackerId) {
  const bulkOperationData = await extractImageOptimizationData(shop.id, resourceType, startDate, endDate);

  const data = {
    store: {
      name: shop.name,
      domain: shop.domain,
      shopifyDomainHandle: shopifyDomainHandle,
      path: path,
    },
    bulkOperation: bulkOperationData,
    resourceTitle,
  };

  return { data, emailSubject };
}

module.exports = {
  // ... existing processors
  [EventTopics.NEW_FEATURE]: generateEmailDataForNewFeature,
  [EventTopics.BULK_IMAGE_OPTIMIZATION_COMPLETE]: generateEmailDataForBulkImageOptimizationComplete,
};
```

### Email Template Variables
```handlebars
{{!-- Common variables available in all templates --}}
{{store.name}}
{{store.domain}}
{{store.shopifyDomainHandle}}
{{store.path}}
{{STORESEO_LOGO}}
{{appName}}

{{!-- Bulk operation specific variables --}}
{{bulkOperation.imageCompressionType}}
{{bulkOperation.imageResizeOption}}
{{bulkOperation.numberOfImagesOptimized}}
{{resourceTitle}}

{{!-- Custom helpers --}}
{{#ifScore score 80}}
  High performing!
{{else}}
  Needs improvement
{{/ifScore}}

{{#ifMailGunProvider emailProvider}}
  Mailgun-specific content
{{/ifMailGunProvider}}
```

## Database Queries

### Get Emails Ready for Delivery
```javascript
const { emails } = await EmailTrackerService.emailsReadyForDelivery(limit);
```

### Check Previous Email by Topic
```javascript
const previousEmail = await EmailTrackerService.getEmailByTopic(
  shopId, 
  EventTopics.WEEKLY_STORE_REPORT
);
```

### Update Email Status
```javascript
await EmailTrackerService.updateEmailById(emailId, {
  status: emailStatus.DELIVERED,
  delivered_at: new Date()
});
```

## Environment Variables Checklist

```bash
# Required
MAIL_PROVIDER=mailgun
ENABLE_EMAIL=true

# Mailgun
MAILGUN_API_KEY=key-xxx
MAILGUN_DOMAIN=mg.yourdomain.com
MAILGUN_FROM_EMAIL=<EMAIL>
MAILGUN_FROM_NAME=StoreSEO

# Optional
BLOCKED_EMAIL_DOMAINS=internal.com,test.com
```

## Debugging Commands

### Check Email Status
```sql
SELECT * FROM email_tracker 
WHERE shop_id = ? AND topic = ? 
ORDER BY created_at DESC;
```

### Monitor Queue Processing
```bash
# Check RabbitMQ queues
rabbitmqctl list_queues name messages

# Check email watcher logs
tail -f logs/email-watcher.log
```

### Test Email Generation
```javascript
// In EmailGenerateProcessor.js
const emailData = await EmailGenerateProcessor.WEEKLY_STORE_REPORT(shop);
console.log(emailData);
```

## Common Troubleshooting

### Email Not Sending
1. Check `ENABLE_EMAIL=true`
2. Verify email provider credentials
3. Check if email domain is blocked
4. Verify email status in database

### Queue Backlog
1. Check RabbitMQ connection
2. Verify queue consumers are running
3. Check prefetch limits
4. Monitor error logs

### Template Errors
1. Verify Handlebars syntax
2. Check variable availability
3. Test template compilation
4. Review custom helpers

## API Endpoints Quick Reference

```javascript
// Get email notification settings
GET /api/setting/email-notification

// Update email notification settings  
POST /api/setting/email-notification
Body: { EMAIL_NOTIFICATION_REPORT: true, items: {...} }

// Get notifications
GET /api/notifications?status=0&limit=10&page=1

// Mark all notifications as read
PUT /api/notifications/read-all

// Update specific notification
PUT /api/notifications/:id
Body: { is_read: true }
```

## Cron Schedule Reference

```javascript
// Daily email trigger check - 2 AM daily
"0 2 * * *"

// Delete old emails - 8 AM every Saturday  
"0 8 * * 6"

// Email watcher - Every 2.5 seconds (not cron)
setInterval(watchEmails, 2500)
```

## Bulk Operation Email Features

### BULK_IMAGE_OPTIMIZATION_COMPLETE

**Trigger Conditions:**
- Triggered when bulk image optimization completes for PRODUCT_IMAGE, COLLECTION_IMAGE, or ARTICLE_IMAGE
- Dispatched from `BulkOperationTrackerQueue.js` when `op_type === "IMAGE_OPTIMIZATION"`

**Queue Dispatch Pattern:**
```javascript
// In BulkOperationTrackerQueue.js
triggerBulkImageOptimizationCompleteEmail(shopDomain, bulkOperationTrackerId) {
  dispatchQueue({
    queueName: QUEUE_NAMES.GENERATE_EMAIL_QUEUE,
    message: {
      shopDomain: shopDomain,
      topic: EventTopics.BULK_IMAGE_OPTIMIZATION_COMPLETE,
      deliveryDate: new Date(), // Instant notification
      bulkOperationTrackerId: bulkOperationTrackerId,
    },
  });
}
```

**Email Data Structure:**
```javascript
{
  store: {
    name: shop.name,
    domain: shop.domain,
    shopifyDomainHandle: shopifyDomainHandle,
    path: "admin.shopify.com/store/{handle}/apps/{appHandle}/image-optimizer/{resourcePath}"
  },
  bulkOperation: {
    imageCompressionType: "WebP", // or "JPEG"
    imageResizeOption: "1024", // pixel size
    numberOfImagesOptimized: 25 // count
  },
  resourceTitle: "Product" | "Collection" | "Blog Post"
}
```

**Template Variables:**
- `{{store.name}}` - Store name
- `{{store.domain}}` - Store domain
- `{{store.shopifyDomainHandle}}` - Shopify domain handle
- `{{store.path}}` - Deep link to image optimizer page
- `{{bulkOperation.imageCompressionType}}` - Compression type used
- `{{bulkOperation.imageResizeOption}}` - Resize option in pixels
- `{{bulkOperation.numberOfImagesOptimized}}` - Number of images optimized
- `{{resourceTitle}}` - Resource type (Product/Collection/Blog Post)
- `{{STORESEO_LOGO}}` - StoreSEO logo URL
- `{{emailProvider}}` - Email provider for conditional unsubscribe

**Email Subject:** "Bulk {{resourceType}} Image Optimization Complete"

**Resource Type Mapping:**
- `PRODUCT_IMAGE` → "Product"
- `COLLECTION_IMAGE` → "Collection"
- `ARTICLE_IMAGE` → "Blog Post"

**Path Generation:**
- Product Images: `/image-optimizer/` (default)
- Collection Images: `/image-optimizer/collections`
- Article Images: `/image-optimizer/blog-posts`

## Testing Checklist

- [ ] Email provider configuration valid
- [ ] Test email addresses not in blocked domains
- [ ] Queue consumers running
- [ ] Database migrations applied
- [ ] Email templates exist and compile
- [ ] Cron jobs scheduled
- [ ] Environment variables set
- [ ] RabbitMQ connection working
- [ ] Bulk operation tracker IDs are valid
- [ ] Image optimization data extraction works correctly

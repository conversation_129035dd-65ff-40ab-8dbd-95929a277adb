# StoreSEO Email Notification System Documentation

## Overview

The StoreSEO email notification system is a comprehensive, queue-based email delivery system that sends automated notifications to merchants about various events and reports. The system is built on RabbitMQ message queues and supports multiple email providers with configurable scheduling and delivery options.

## System Architecture

### Core Components

1. **Email Generation Queue** (`GenerateEmailQueue.js`) - Generates email content and stores it in the database
2. **Email Send Queue** (`SendEmailQueue.js`) - Handles actual email delivery using configured providers
3. **Email Watcher** (`emailWatcher.js`) - <PERSON>ron job that monitors ready emails and dispatches them for delivery
4. **Email Notification Trigger Queue** (`EmailNotificationTriggerQueue.js`) - Scheduled job that triggers various email notifications
5. **Email Tracker Service** (`EmailTrackerService.js`) - Database service for managing email records

### Database Schema

#### EmailTracker Table
```sql
- id: Primary key
- shop_id: Foreign key to shops table
- topic: Email notification type (EventTopics enum)
- send_to: Recipient email address
- data: JSON data containing email content
- scheduled_delivery_date: When the email should be sent
- delivered_at: Timestamp when email was delivered
- status: Email status (READY_FOR_DELIVERY, QUEUED, DELIVERED, etc.)
- failed_reason: Error message if delivery failed
- created_at/updated_at: Timestamps
```

## Email Flow Process

### 1. Email Trigger Phase
```
Cron Job (daily-store-data-check.js) 
    ↓
EmailNotificationTriggerQueue
    ↓
Checks notification settings & conditions
    ↓
Dispatches to GenerateEmailQueue
```

### 2. Email Generation Phase
```
GenerateEmailQueue receives message
    ↓
EmailGenerateProcessor generates content
    ↓
Stores email in EmailTracker table with READY_FOR_DELIVERY status
```

### 3. Email Delivery Phase
```
emailWatcher.js (runs every 2.5 seconds)
    ↓
Finds emails ready for delivery
    ↓
Dispatches to SendEmailQueue
    ↓
Updates status to QUEUED
    ↓
SendEmailQueue sends via MailService
    ↓
Updates status to DELIVERED or DELIVERY_FAILED
```

## Supported Email Types

### Scheduled Notifications
1. **WEEKLY_STORE_REPORT** - Weekly product optimization reports
2. **AUTO_AI_CONTENT_OPTIMIZATION_REPORT** - Weekly AI content optimization reports

### Integration Reminders (5 days after shop creation)
3. **GOOGLE_ANALYTICS_INTEGRATION** - Reminder to connect Google Analytics
4. **GOOGLE_SEARCH_CONSOLE_INTEGRATION** - Reminder to connect Search Console
5. **HTML_SITEMAP_INTEGRATION** - Reminder to set up HTML sitemap
6. **AUTO_IMAGE_OPTIMIZER_REMINDER** - Reminder to enable auto image optimization

### Usage Notifications
7. **IMAGE_OPTIMIZER_USAGE_NOTIFICATION** - Usage alerts at 50%, 80%, 95%
8. **AI_CONTENT_OPTIMIZER_USAGE_NOTIFICATION** - AI credit usage alerts

### Instant Notifications
9. **BULK_AI_CONTENT_OPTIMIZATION_COMPLETE** - Bulk AI content optimization completion
10. **BULK_IMAGE_OPTIMIZATION_COMPLETE** - Bulk image optimization completion
11. **PRODUCT_SYNC_COMPLETE** - Product synchronization completion

## Configuration

### Environment Variables
```bash
# Email Provider Configuration
MAIL_PROVIDER=mailgun|elasticmail
ENABLE_EMAIL=true|false
BLOCKED_EMAIL_DOMAINS=domain1.com,domain2.com

# Mailgun Configuration
MAILGUN_API_KEY=your_api_key
MAILGUN_DOMAIN=your_domain
MAILGUN_FROM_EMAIL=<EMAIL>
MAILGUN_FROM_NAME=StoreSEO

# ElasticEmail Configuration
ELASTICEMAIL_API_KEY=your_api_key
ELASTICEMAIL_FROM_EMAIL=<EMAIL>
ELASTICEMAIL_FROM_NAME=StoreSEO
```

### Email Notification Settings
Stored in shop settings with key `EMAIL_NOTIFICATION`:
```json
{
  "EMAIL_NOTIFICATION_REPORT": true,
  "items": {
    "WEEKLY_STORE_REPORT": {
      "enabled": true,
      "day": "monday",
      "time": 12,
      "configurable": true,
      "instantNotification": false
    }
  }
}
```

## Queue Configuration

### Queue Names
- `GENERATE_EMAIL_QUEUE` - Email content generation
- `SEND_EMAIL` - Email delivery
- `EMAIL_NOTIFICATION_TRIGGER_QUEUE` - Scheduled email triggers

### Prefetch Limits
- `GENERATE_EMAIL_QUEUE`: 5
- `SEND_EMAIL`: 10
- `EMAIL_NOTIFICATION_TRIGGER_QUEUE`: 3

## Email Providers

### Supported Providers
1. **Mailgun** - Primary email provider
2. **ElasticEmail** - Alternative email provider

### Provider Selection
The system automatically selects the provider based on the `MAIL_PROVIDER` environment variable.

## Email Templates

### Template System
- Templates are stored in `web/api/templates/` directory
- Uses Handlebars templating engine
- Templates are loaded dynamically and cached
- Support for conditional content and custom helpers

### Template Variables
Common variables available in all templates:
- `{{store.domain}}` - Store domain
- `{{store.name}}` - Store name
- `{{STORESEO_LOGO}}` - StoreSEO logo URL
- `{{appName}}` - Application name
- `{{emailProvider}}` - Email provider identifier

## Cron Jobs

### Daily Store Data Check
- **Schedule**: `0 2 * * *` (2 AM daily)
- **Function**: Triggers email notification checks for all active shops
- **File**: `web/cronjobs/tasks/events/daily-store-data-check.js`

### Delete Delivered Emails
- **Schedule**: `0 8 * * 6` (8 AM every Saturday)
- **Function**: Cleans up delivered emails older than 3 months
- **File**: `web/cronjobs/tasks/delete-delivered-emails.js`

## API Endpoints

### Email Notification Settings
```
GET  /api/setting/email-notification    - Get current settings
POST /api/setting/email-notification    - Update settings
```

### Notifications
```
GET  /api/notifications                  - Get notifications list
PUT  /api/notifications/read-all         - Mark all as read
PUT  /api/notifications/:id              - Update specific notification
```

## Error Handling

### Email Delivery Failures
- Failed emails are marked with `DELIVERY_FAILED` status
- Failure reasons are stored in `failed_reason` field
- No automatic retry mechanism (emails are acknowledged regardless of success)

### Blocked Emails
- Internal emails (configured domains) are skipped
- Status set to `INTERNAL_EMAIL_SKIPPED_DELIVERY`
- Configurable via `BLOCKED_EMAIL_DOMAINS` environment variable

## Monitoring and Logging

### Email Status Tracking
- All emails tracked in `EmailTracker` table
- Status progression: `READY_FOR_DELIVERY` → `QUEUED` → `DELIVERED`/`DELIVERY_FAILED`
- Delivery timestamps recorded

### Logging
- Queue processing logs with shop domain and topic
- Error logging for failed operations
- Debug logging for webhook events

## Security Features

### Email Validation
- Blocked domain filtering
- Unsubscribe list management (Mailgun)
- Email address validation

### Webhook Verification
- Shopify webhook verification for bulk operation triggers
- Request signature validation

## Performance Considerations

### Queue Processing
- Prefetch limits prevent queue overload
- Batch processing for email watcher (limit 10)
- Sleep intervals to prevent resource exhaustion

### Database Optimization
- Indexed on `shop_id` for efficient queries
- Automatic cleanup of old delivered emails
- JSON data storage for flexible email content

## Troubleshooting

### Common Issues
1. **Emails not sending**: Check `ENABLE_EMAIL` environment variable
2. **Provider errors**: Verify API keys and configuration
3. **Queue backlog**: Monitor RabbitMQ queue depths
4. **Template errors**: Check Handlebars syntax and variable availability

### Debug Steps
1. Check email status in `EmailTracker` table
2. Verify queue processing logs
3. Confirm email provider configuration
4. Test with non-blocked email domains

## Email Trigger Conditions

### Weekly Reports
- **WEEKLY_STORE_REPORT**: Triggered weekly based on shop timezone and configured day/time
- **AUTO_AI_CONTENT_OPTIMIZATION_REPORT**: Triggered weekly for shops with AI optimization features

### Integration Reminders
- **Condition**: 5 days after shop creation
- **Frequency**: One-time only (checks for existing emails)
- **Skip Conditions**:
  - Google Analytics: Already connected
  - Search Console: Already connected
  - HTML Sitemap: Setup completed (step 3)
  - Image Optimizer: Auto-optimization already enabled

### Usage Notifications
- **Thresholds**: 50%, 80%, 95% usage
- **Frequency**: Once per threshold level
- **Reset**: Monthly for recurring addons

### Bulk Operation Completion
- **Trigger**: Automatic when bulk AI optimization or image optimization completes
- **Resources**: Products, Articles, Collections
- **Delivery**: Instant notification

## Bulk Operation Email Integration

### Trigger Mechanism
```javascript
// In BulkOperationTrackerQueue.js

// For AI Content Optimization
await this.triggerBulkAiContentOptimizationCompleteEmail(shopDomain, bulkOperationTrackerId);

// For Image Optimization
await this.triggerBulkImageOptimizationCompleteEmail(shopDomain, bulkOperationTrackerId);
```

### AI Content Optimization Email Data Structure
```javascript
{
  batchSize: number,           // Total items processed
  metaTitle: number,           // Success count for meta titles
  metaDescription: number,     // Success count for meta descriptions
  tags: number|null,           // Success count for tags (if applicable)
  featuredImageAltText: number|null,  // Success count for featured image alt text
  productImageAltText: number|null    // Success count for product image alt text
}
```

### Image Optimization Email Data Structure
```javascript
{
  imageCompressionType: string,    // "WebP" or "JPEG"
  imageResizeOption: string,       // Pixel size (e.g., "1024")
  numberOfImagesOptimized: number  // Count of optimized images
}
```

### Resource-Specific Fields

#### AI Content Optimization
- **PRODUCT**: All fields available (meta, tags, featured image alt, product image alt)
- **ARTICLE**: Meta fields and tags only
- **COLLECTION**: Meta fields only

#### Image Optimization
- **PRODUCT_IMAGE**: Product images
- **COLLECTION_IMAGE**: Collection images
- **ARTICLE_IMAGE**: Article/blog post images

## Frontend Integration

### Settings Page
- **Location**: `/settings/email-notification`
- **Components**: React form with validation
- **API Hook**: `useEmailNotificationApi.js`
- **Validation**: Yup schema validation

### Settings Structure
```javascript
{
  EMAIL_NOTIFICATION_REPORT: boolean,  // Master toggle
  items: {
    [EventTopic]: {
      enabled: boolean,
      day: string,           // Day of week for scheduled emails
      time: number,          // Hour of day (24-hour format)
      configurable: boolean, // Whether user can modify settings
      instantNotification: boolean  // Whether it's an instant notification
    }
  }
}
```

## Email Template System

### Template Loading
- Templates loaded from `web/api/templates/[topic].html`
- Cached in memory after first load
- Handlebars compilation with custom helpers

### Custom Handlebars Helpers
```javascript
// Score comparison helper
{{#ifScore score 80}}High score{{else}}Low score{{/ifScore}}

// Email provider conditional
{{#ifMailGunProvider provider}}Mailgun content{{else}}Other provider{{/ifMailGunProvider}}
```

### Template Documentation
Complete template documentation available in:
`web/api/templates/EMAIL_TEMPLATES_DOCUMENTATION.md`

## Service Dependencies

### Core Services
- **ShopService**: Shop data and settings management
- **EmailTrackerService**: Email record management
- **MailService**: Email provider abstraction
- **BulkOperationTrackerService**: Bulk operation data

### External Dependencies
- **RabbitMQ**: Message queue system
- **Mailgun/ElasticEmail**: Email delivery providers
- **Handlebars**: Template engine
- **Moment.js**: Date/time handling with timezone support

## Development Guidelines

### Adding New Email Types
1. Add new event topic to `EventTopics` enum
2. Create email template in `web/api/templates/`
3. Add processor function in `EmailGenerateProcessor.js`
4. Add trigger logic in `EmailNotificationTriggerQueue.js`
5. Update email notification settings configuration
6. Add frontend UI components if configurable

### Testing Email Notifications
1. Set `ENABLE_EMAIL=true` in environment
2. Use non-blocked email domains for testing
3. Monitor queue processing logs
4. Check `EmailTracker` table for status updates
5. Verify email delivery in provider dashboard

### Queue Management
- Monitor RabbitMQ management interface
- Check queue depths and processing rates
- Adjust prefetch limits if needed
- Implement dead letter queues for failed messages

## Maintenance Tasks

### Regular Cleanup
- Delivered emails older than 3 months are automatically deleted
- Failed emails should be reviewed and potentially retried
- Monitor disk space usage for email data storage

### Performance Monitoring
- Track email delivery success rates
- Monitor queue processing times
- Check provider API rate limits and quotas
- Review email bounce and complaint rates

### Security Audits
- Regularly update blocked email domains list
- Review unsubscribe list management
- Audit email content for sensitive information
- Validate webhook signature verification

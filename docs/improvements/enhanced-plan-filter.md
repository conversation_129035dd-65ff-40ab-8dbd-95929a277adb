# Enhanced Plan Filter for Stores Table

## Overview

This enhancement provides granular filtering options for the admin stores table, allowing administrators to filter stores by specific subscription plans, billing intervals, and plan statuses.

## Features Implemented

### 1. Plan Options API
- **Endpoint**: `GET /admin/stores/plan-options`
- **Purpose**: Returns available filter options for the enhanced plan filter
- **Response Format**:
```json
{
  "planNames": [
    { "id": "all", "label": "All Plans", "value": "ALL" },
    { "id": "without", "label": "Without Plans", "value": "WITHOUT_PLANS" },
    { "id": "advanced-monthly", "label": "Advanced (M)", "value": "Advanced" },
    { "id": "essential-monthly", "label": "Essential (M)", "value": "Essential" }
  ],
  "planIntervals": [
    { "id": "all", "label": "All", "value": "ALL" },
    { "id": "monthly", "label": "Monthly", "value": "MONTHLY" },
    { "id": "yearly", "label": "Yearly", "value": "ANNUALLY" },
    { "id": "lifetime", "label": "Lifetime", "value": "LIFETIME" }
  ],
  "planStatuses": [
    { "id": "all", "label": "All", "value": "ALL" },
    { "id": "active", "label": "Active", "value": "ACTIVE" },
    { "id": "cancelled", "label": "Cancelled", "value": "CANCELLED" },
    { "id": "expired", "label": "Expired", "value": "EXPIRED" }
  ]
}
```

### 2. Enhanced Stores List API
- **Endpoint**: `GET /admin/stores`
- **New Query Parameters**:
  - `planName`: Filter by specific plan name (e.g., "Advanced", "Essential")
  - `planInterval`: Filter by billing interval ("MONTHLY", "ANNUALLY", "LIFETIME")
  - `planStatus`: Filter by subscription status ("ACTIVE", "CANCELLED", "EXPIRED")
  - `withoutPlan`: Legacy parameter for stores without subscriptions

### 3. Backward Compatibility
- Existing `plan` parameter continues to work for basic type filtering (FREE/PRO)
- All existing filters (search, status, date range, sorting) work seamlessly with new filters

## API Usage Examples

### Get Plan Options
```bash
GET /admin/stores/plan-options
```

### Filter by Specific Plan Name
```bash
GET /admin/stores?planName=Advanced&limit=20&page=1
```

### Filter by Billing Interval
```bash
GET /admin/stores?planInterval=MONTHLY&limit=20&page=1
```

### Filter by Plan Status
```bash
GET /admin/stores?planStatus=ACTIVE&limit=20&page=1
```

### Filter Stores Without Plans
```bash
GET /admin/stores?planName=WITHOUT_PLANS&limit=20&page=1
```

### Combined Filtering
```bash
GET /admin/stores?planName=Advanced&planInterval=MONTHLY&planStatus=ACTIVE&limit=20&page=1
```

## Implementation Details

### Database Schema
The implementation leverages existing database relationships:
- `shops` table: Contains `plan_id`, `plan_status`
- `subscription_plans` table: Contains `name`, `interval`, `type`, `status`
- Existing LEFT JOIN between shops and subscription_plans

### Filtering Logic
1. **Plan Name Filtering**: Filters by `subscription_plans.name`
2. **Plan Interval Filtering**: Filters by `subscription_plans.interval`
3. **Plan Status Filtering**: Filters by `shops.plan_status`
4. **Without Plans**: Filters where `shops.plan_id IS NULL`

### Plan Name Display Format
Plan names in the dropdown show the interval indicator:
- "Advanced (M)" for Monthly plans
- "Advanced (Y)" for Yearly plans
- "Advanced (L)" for Lifetime plans

## Files Modified

### Backend Changes
1. **Controller**: `web/admin/controllers/stores.controller.js`
   - Enhanced `getStoresList` to accept new parameters
   - Added `getPlanOptions` method

2. **Service**: `web/admin/services/store.service.js`
   - Enhanced `getStoresList` method with new filtering logic
   - Added `getPlanOptions` method
   - Added `getIntervalLetter` helper method

3. **Routes**: `web/admin/routes/v1/stores.route.js`
   - Added new route for plan options API

## Testing

The implementation has been tested with:
- Plan options API returning correct data structure
- Basic store listing functionality
- Plan name filtering (including "Without Plans")
- Plan interval filtering
- Plan status filtering
- Combined filter scenarios

## Frontend Integration Requirements

To complete the implementation, the frontend needs to:

1. **Update Filter Component**: Modify the stores table filter to use the new plan options API
2. **Add New Filter Controls**: Implement dropdowns for plan name, interval, and status
3. **URL Parameter Management**: Update URL parameters to include new filter values
4. **Filter State Management**: Maintain filter state across page refreshes and navigation

## Benefits

1. **Granular Control**: Administrators can filter by specific subscription plans
2. **Better Analytics**: Easier to analyze subscription patterns and billing intervals
3. **Improved Workflow**: Faster identification of stores requiring attention
4. **Backward Compatible**: Existing functionality remains unchanged
5. **Scalable**: Easy to add more filter options in the future

## Future Enhancements

Potential future improvements:
1. Add trial status filtering
2. Include subscription addon filtering
3. Add date-based subscription filtering (e.g., subscribed in last 30 days)
4. Export functionality with applied filters

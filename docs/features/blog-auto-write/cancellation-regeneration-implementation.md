# Blog Auto-Write Cancellation & Regeneration Implementation

## Document Information

| Field | Value |
|-------|-------|
| **Feature Name** | Real-time Cancellation & Regeneration System |
| **Implementation Version** | 1.0.0 |
| **Created Date** | 2025-06-29 |
| **Status** | Implementation Specification |
| **Owner** | Development Team |

## Overview

This document provides detailed implementation specifications for the real-time cancellation detection and regeneration features in the blog auto-write system.

## 🚀 **Recent Updates (2025-07-15)**

### Regeneration Available for All Job Statuses

**Enhancement**: Regeneration is now available for all job statuses including completed jobs, providing users with maximum flexibility to refine their AI-generated content.

**Changes Made:**
- ✅ **Backend**: Removed restriction preventing regeneration of completed jobs in `validateRegenerateCredits` middleware
- ✅ **Frontend**: Updated `shouldShowRegenerateButton()` to show regenerate button for all AI-generated jobs regardless of status
- ✅ **UI**: Regenerate button now appears for completed, failed, in-progress, and pending jobs
- ✅ **Documentation**: Updated API documentation and specifications to reflect new behavior

**Benefits:**
- Users can regenerate successfully completed blogs to try different parameters
- Consistent regeneration experience across all job statuses
- Enhanced user flexibility for content refinement
- No breaking changes to existing functionality

## Real-time Cancellation Detection

### Problem Statement

The current queue processing system lacks real-time cancellation detection, leading to:
- **Resource Waste**: Cancelled jobs continue processing, consuming OpenAI credits
- **Poor UX**: Users see "cancelled" status but processing continues in background
- **Cost Inefficiency**: Unnecessary API calls after user cancellation
- **Inconsistent State**: Frontend shows cancelled while backend continues processing

### Solution Architecture

#### 1. Enhanced Job Status Enum

```typescript
// web/packages/storeseo-enums/src/blogAutoWrite/jobStatus.ts
enum BlogAutoWriteJobStatus {
  PENDING = "pending",
  GENERATING_CONTENT = "generating_content",
  CREATING_DRAFT = "creating_draft",
  LINKING_ARTICLE = "linking_article",
  ANALYZING_SEO = "analyzing_seo",
  GENERATING_IMAGE = "generating_image",
  UPLOADING_IMAGE = "uploading_image",
  UPDATING_ARTICLE = "updating_article",
  FINALIZING_SEO = "finalizing_seo",
  PUBLISHING = "publishing",
  COMPLETED = "completed",
  FAILED = "failed",
  CANCELLED = "cancelled", // NEW: Distinct from FAILED for audit trails
}

// Add to labels mapping
[BlogAutoWriteJobStatus.CANCELLED]: "Cancelled",

// Add to progress mapping
[BlogAutoWriteJobStatus.CANCELLED]: 0, // Reset progress for cancelled jobs
```

#### 2. Queue Processor Enhancement

```javascript
// web/api/queue/jobs/BlogAutoWriteQueue.js

class JobCancelledException extends Error {
  constructor(message) {
    super(message);
    this.name = 'JobCancelledException';
    this.code = 'JOB_CANCELLED';
  }
}

class BlogAutoWriteQueue extends BaseQueue {
  /**
   * Check if job has been cancelled and throw error to stop processing
   * @param {string} jobId - Job ID to check
   * @throws {JobCancelledException} If job is cancelled
   */
  async checkJobCancellation(jobId) {
    const job = await BlogAutoWriteJob.findByPk(jobId, {
      attributes: ['id', 'status'], // Minimal data fetch for performance
    });
    
    if (!job) {
      throw new Error(`Job ${jobId} not found`);
    }
    
    if (job.status === BlogAutoWriteJobStatus.CANCELLED) {
      throw new JobCancelledException(`Job ${jobId} has been cancelled by user`);
    }
  }

  /**
   * Enhanced processJob with cancellation detection
   */
  async processJob(jobId, shopDomain) {
    try {
      // ✅ Checkpoint 1: Before starting any processing
      await this.checkJobCancellation(jobId);
      
      const job = await BlogAutoWriteService.getJobById(jobId);
      if (!job || !job.article) {
        throw new Error(`Job ${jobId} or associated article not found`);
      }

      const { input_data: inputData, article } = job;

      // Step 1: Content Generation (30% weight - most expensive)
      await BlogAutoWriteService.updateJobStatus(jobId, BlogAutoWriteJobStatus.GENERATING_CONTENT);
      await this.checkJobCancellation(jobId); // ✅ Checkpoint 2: Before OpenAI call
      
      const { output: generatedContent, usage } = await OpenAiService.generateBlogContent(inputData);

      // Step 2: Shopify Draft Creation
      await this.checkJobCancellation(jobId); // ✅ Checkpoint 3: Before Shopify API
      await BlogAutoWriteService.updateJobStatus(jobId, BlogAutoWriteJobStatus.CREATING_DRAFT);
      
      const draft = await ShopifyService.createDraft(generatedContent, article);

      // Step 3: Article Linking
      await this.checkJobCancellation(jobId); // ✅ Checkpoint 4: Before DB operations
      await BlogAutoWriteService.updateJobStatus(jobId, BlogAutoWriteJobStatus.LINKING_ARTICLE);
      
      await this.linkArticleWithDraft(article.id, draft.id);

      // Step 4: First SEO Analysis
      await this.checkJobCancellation(jobId); // ✅ Checkpoint 5: Before SEO analysis
      await BlogAutoWriteService.updateJobStatus(jobId, BlogAutoWriteJobStatus.ANALYZING_SEO);
      
      const seoAnalysis = await SeoService.analyzeContent(generatedContent);

      // Step 5: Image Generation (20% weight - expensive)
      if (inputData.generateFeaturedImage) {
        await this.checkJobCancellation(jobId); // ✅ Checkpoint 6: Before DALL-E call
        await BlogAutoWriteService.updateJobStatus(jobId, BlogAutoWriteJobStatus.GENERATING_IMAGE);
        
        const image = await OpenAiService.generateImage(inputData.featuredImageDescription || inputData.topic);

        // Step 6: Image Upload
        await this.checkJobCancellation(jobId); // ✅ Checkpoint 7: Before image upload
        await BlogAutoWriteService.updateJobStatus(jobId, BlogAutoWriteJobStatus.UPLOADING_IMAGE);
        
        const uploadedImage = await ShopifyService.uploadImage(image);

        // Step 7: Article Update with Image
        await this.checkJobCancellation(jobId); // ✅ Checkpoint 8: Before article update
        await BlogAutoWriteService.updateJobStatus(jobId, BlogAutoWriteJobStatus.UPDATING_ARTICLE);
        
        await ShopifyService.updateArticleWithImage(draft.id, uploadedImage.id);
      }

      // Step 8: Final SEO Analysis
      await this.checkJobCancellation(jobId); // ✅ Checkpoint 9: Before final SEO
      await BlogAutoWriteService.updateJobStatus(jobId, BlogAutoWriteJobStatus.FINALIZING_SEO);
      
      const finalSeo = await SeoService.finalizeAnalysis(draft);

      // Step 9: Publishing (if auto-publish enabled)
      if (inputData.autoPublish) {
        await this.checkJobCancellation(jobId); // ✅ Checkpoint 10: Before publishing
        await BlogAutoWriteService.updateJobStatus(jobId, BlogAutoWriteJobStatus.PUBLISHING);
        
        await ShopifyService.publishArticle(draft.id);
      }

      // Mark as completed
      await BlogAutoWriteService.updateJobStatus(jobId, BlogAutoWriteJobStatus.COMPLETED);
      
      // Settle credit usage
      await this.settleCreditUsage(jobId, shopDomain, usage);

      return {
        status: 'completed',
        jobId,
        articleId: article.id,
        shopifyArticleId: draft.id,
        creditsUsed: usage.totalCredits,
      };

    } catch (error) {
      if (error instanceof JobCancelledException) {
        // Job was cancelled - handle gracefully
        console.info(`Job ${jobId} cancelled during processing: ${error.message}`);
        
        // Calculate partial credit usage and refund unused credits
        await this.handleCancellation(jobId, shopDomain);
        
        return { 
          status: 'cancelled', 
          jobId, 
          reason: 'User cancelled',
          stoppedAt: new Date()
        };
      }
      
      // Handle other errors normally
      await BlogAutoWriteService.updateJobStatus(jobId, BlogAutoWriteJobStatus.FAILED, null, error.message);
      throw error;
    }
  }

  /**
   * Handle job cancellation with credit refunds
   */
  async handleCancellation(jobId, shopDomain) {
    const job = await BlogAutoWriteService.getJobById(jobId);
    if (!job) return;

    // Calculate credits used up to cancellation point
    const stepsCompleted = job.steps.filter(step => step.completed);
    const creditsUsed = this.calculatePartialCreditUsage(stepsCompleted);
    
    // Calculate refund amount
    const estimatedCredits = job.estimated_credit_usage.totalCredits;
    const refundAmount = Math.max(0, estimatedCredits - creditsUsed);
    
    // Process refund if applicable
    if (refundAmount > 0) {
      await cache.addons.decrementUsageCount(shopDomain, {
        addon: 'AI_OPTIMIZER',
        decrementBy: refundAmount
      });
    }
    
    // Update job with final credit usage
    await BlogAutoWriteService.updateJobCreditUsage(jobId, {
      totalCredits: creditsUsed,
      refundedCredits: refundAmount,
      cancellationReason: 'User cancelled'
    });
  }
}
```

## Regeneration Feature Implementation

### Problem Statement

Users need the ability to restart blog generation with refined parameters without losing their original attempt or starting completely from scratch.

### Solution Architecture

#### 1. Enhanced BlogAutoWriteService

```javascript
// web/api/services/BlogAutoWriteService.js

class BlogAutoWriteService {
  /**
   * Cancel a job with proper audit trail
   * @param {string} jobId - Job ID to cancel
   * @param {number} shopId - Shop ID for security
   * @returns {Promise<Object>} Cancellation result
   */
  async cancelJob(jobId, shopId) {
    const job = await this.getJobById(jobId, shopId);
    if (!job) {
      throw new Error('Job not found');
    }

    // Only allow cancellation of active jobs
    const cancellableStatuses = [
      BlogAutoWriteJobStatus.PENDING,
      BlogAutoWriteJobStatus.GENERATING_CONTENT,
      BlogAutoWriteJobStatus.CREATING_DRAFT,
      BlogAutoWriteJobStatus.LINKING_ARTICLE,
      BlogAutoWriteJobStatus.ANALYZING_SEO,
      BlogAutoWriteJobStatus.GENERATING_IMAGE,
      BlogAutoWriteJobStatus.UPLOADING_IMAGE,
      BlogAutoWriteJobStatus.UPDATING_ARTICLE,
      BlogAutoWriteJobStatus.FINALIZING_SEO,
      BlogAutoWriteJobStatus.PUBLISHING,
    ];

    if (!cancellableStatuses.includes(job.status)) {
      throw new Error(`Cannot cancel job with status: ${job.status}`);
    }

    // Update job status to cancelled and disconnect from article
    await BlogAutoWriteJob.update(
      { 
        status: BlogAutoWriteJobStatus.CANCELLED,
        article_id: null, // Disconnect for audit trail
        error_message: 'Cancelled by user',
        processing_completed_at: new Date()
      },
      { where: { id: jobId, shop_id: shopId } }
    );

    return { jobId, status: 'cancelled', cancelledAt: new Date() };
  }

  /**
   * Regenerate a job with optional input overrides
   * @param {string} originalJobId - Original job ID to regenerate
   * @param {number} shopId - Shop ID for security
   * @param {Object} inputOverrides - Optional input parameter overrides
   * @returns {Promise<Object>} New job details
   */
  async regenerateJob(originalJobId, shopId, inputOverrides = {}) {
    // Get original job
    const originalJob = await this.getJobById(originalJobId, shopId);
    if (!originalJob) {
      throw new Error('Original job not found');
    }

    // Get target article
    const targetArticle = originalJob.article;
    if (!targetArticle) {
      throw new Error('Target article not found');
    }

    // Cancel original job (preserves audit trail)
    await this.cancelJob(originalJobId, shopId);

    // Merge original input data with overrides
    const newInputData = {
      ...originalJob.input_data,
      ...inputOverrides
    };

    // Create new job with merged input data
    const result = await this.createBlogAutoWriteJob({
      shopId,
      shopDomain: req.user.shop, // Will need to pass this parameter
      inputData: newInputData,
      targetArticleId: targetArticle.id // Reuse same article
    });

    return {
      newJob: result.job,
      cancelledJobId: originalJobId,
      targetArticle: targetArticle,
      inputData: newInputData,
      creditEstimation: result.creditEstimation
    };
  }
}
```

#### 2. API Controller Enhancement

```javascript
// web/api/controllers/BlogAutoWriteController.js

class BlogAutoWriteController {
  /**
   * Enhanced cancel job with better credit handling
   * DELETE /api/blog-auto-write/:jobId
   */
  async cancelJob(req, res) {
    try {
      const { shopId, shop: shopDomain } = req.user;
      const { jobId } = req.params;

      // Cancel job using enhanced service method
      const result = await BlogAutoWriteService.cancelJob(jobId, shopId);

      // Calculate and process credit refunds
      const job = await BlogAutoWriteService.getJobById(jobId, shopId);
      let refundedCredits = 0;

      if (job && job.estimated_credit_usage) {
        const estimatedCredits = job.estimated_credit_usage.totalCredits || 0;
        const actualCredits = job.credit_usage.totalCredits || 0;
        refundedCredits = Math.max(0, estimatedCredits - actualCredits);

        if (refundedCredits > 0) {
          await cache.addons.decrementUsageCount(shopDomain, {
            addon: 'AI_OPTIMIZER',
            decrementBy: refundedCredits
          });
        }
      }

      res.json({
        success: true,
        message: 'Job cancelled successfully',
        data: {
          jobId: result.jobId,
          status: result.status,
          cancelledAt: result.cancelledAt,
          refundedCredits
        }
      });

    } catch (error) {
      console.error('Error cancelling job:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * Regenerate job with optional input overrides
   * POST /api/blog-auto-write/:jobId/regenerate
   */
  async regenerateJob(req, res) {
    try {
      const { shopId, shop: shopDomain } = req.user;
      const { jobId } = req.params;
      const inputOverrides = req.body || {};

      // Regenerate job using service method
      const result = await BlogAutoWriteService.regenerateJob(jobId, shopId, inputOverrides);

      // Dispatch new job to queue
      await dispatchQueue({
        queueName: QUEUE_NAMES.BLOG_AUTO_WRITE_QUEUE,
        message: {
          jobId: result.newJob.id,
          shopDomain,
        },
      });

      res.status(201).json({
        success: true,
        message: 'Job regenerated successfully',
        data: {
          newJobId: result.newJob.id,
          cancelledJobId: jobId,
          articleId: result.targetArticle.id,
          status: result.newJob.status,
          estimatedCredits: result.creditEstimation.totalCredits,
          availableCredits: result.creditEstimation.availableCredits,
          inputData: result.inputData
        }
      });

    } catch (error) {
      console.error('Error regenerating job:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  }
}
```

## Implementation Checklist

### Backend Implementation
- [ ] Add CANCELLED status to BlogAutoWriteJobStatus enum
- [ ] Implement checkJobCancellation() method in BlogAutoWriteQueue
- [ ] Add strategic cancellation checkpoints in processJob()
- [ ] Implement JobCancelledException error handling
- [ ] Add cancelJob() method to BlogAutoWriteService
- [ ] Add regenerateJob() method to BlogAutoWriteService
- [ ] Implement POST /blog-auto-write/:jobId/regenerate endpoint
- [ ] Enhance DELETE /blog-auto-write/:jobId endpoint
- [ ] Add credit refund logic for cancelled jobs
- [ ] Update database migration if needed

### Frontend Implementation
- [ ] Add regeneration button/option to job progress UI
- [ ] Implement regeneration modal with input override options
- [ ] Update job status handling for CANCELLED status
- [ ] Add confirmation dialogs for cancellation/regeneration
- [ ] Update real-time status polling to handle CANCELLED status
- [ ] Add credit refund notifications

### Testing & Validation
- [ ] Unit tests for cancellation detection
- [ ] Integration tests for regeneration workflow
- [ ] End-to-end tests for credit handling
- [ ] Performance tests for cancellation checkpoints
- [ ] User acceptance testing for regeneration UX

## Success Metrics

- **Cancellation Response Time**: < 5 seconds from user action to processing stop
- **Credit Accuracy**: 100% accurate refunds for cancelled jobs
- **Resource Efficiency**: 90% reduction in wasted API calls for cancelled jobs
- **User Satisfaction**: Seamless regeneration experience with input refinement
- **System Reliability**: Zero data corruption during cancellation/regeneration

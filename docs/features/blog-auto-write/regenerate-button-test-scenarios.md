# Re-generate Button Visibility Control - Test Scenarios

## Test Scenarios for Implementation Verification

### Scenario 1: Progress View (No Regeneration Context)

**Setup:**
- User clicks on AI status badge to view progress
- <PERSON><PERSON> opens with `isExplicitRegeneration = false`

**Expected Behavior:**
- <PERSON><PERSON> opens in progress view
- Only "Close" button is visible
- No re-generate button appears regardless of job status

**Test Cases:**
```javascript
// Test data
const testCases = [
  { status: BlogAutoWriteJobStatus.COMPLETED, shouldShowButton: false },
  { status: BlogAutoWriteJobStatus.FAILED, shouldShowButton: false },
  { status: BlogAutoWriteJobStatus.CANCELLED, shouldShowButton: false },
  { status: BlogAutoWriteJobStatus.PENDING, shouldShowButton: false },
  { status: BlogAutoWriteJobStatus.GENERATING_CONTENT, shouldShowButton: false },
];
```

### Scenario 2: Explicit Regeneration (Completed Job)

**Setup:**
- User clicks regenerate button in AI Blog Generator list
- Job status is `COMPLETED`
- <PERSON><PERSON> opens with `isExplicitRegeneration = true`

**Expected Behavior:**
- Modal opens in form view with pre-populated data
- Re-generate button is visible and enabled
- User can modify settings and regenerate

**Test Cases:**
```javascript
const testCase = {
  status: BlogAutoWriteJobStatus.COMPLETED,
  isExplicitRegeneration: true,
  expectedButtonVisible: true,
  expectedButtonEnabled: true,
};
```

### Scenario 3: Explicit Regeneration (Failed Job)

**Setup:**
- User clicks regenerate button in AI Blog Generator list
- Job status is `FAILED`
- Modal opens with `isExplicitRegeneration = true`

**Expected Behavior:**
- Modal opens in form view with pre-populated data
- Re-generate button is visible and enabled
- User can modify settings and regenerate

### Scenario 4: Explicit Regeneration (Cancelled Job)

**Setup:**
- User clicks regenerate button in AI Blog Generator list
- Job status is `CANCELLED`
- Modal opens with `isExplicitRegeneration = true`

**Expected Behavior:**
- Modal opens in form view with pre-populated data
- Re-generate button is visible and enabled
- User can modify settings and regenerate

### Scenario 5: Ongoing Job (No Regeneration Available)

**Setup:**
- Job status is any in-progress status (PENDING, GENERATING_CONTENT, etc.)
- User views AI Blog Generator list

**Expected Behavior:**
- No regenerate button visible in list item
- Only progress viewing available via AI status badge
- Modal opens in progress view with no regenerate option

**Test Cases:**
```javascript
const inProgressStatuses = [
  BlogAutoWriteJobStatus.PENDING,
  BlogAutoWriteJobStatus.GENERATING_CONTENT,
  BlogAutoWriteJobStatus.CREATING_DRAFT,
  BlogAutoWriteJobStatus.LINKING_ARTICLE,
  BlogAutoWriteJobStatus.ANALYZING_SEO,
  BlogAutoWriteJobStatus.GENERATING_IMAGE,
  BlogAutoWriteJobStatus.UPLOADING_IMAGE,
  BlogAutoWriteJobStatus.UPDATING_ARTICLE,
  BlogAutoWriteJobStatus.FINALIZING_SEO,
  BlogAutoWriteJobStatus.PUBLISHING,
];

inProgressStatuses.forEach(status => {
  // List item should not show regenerate button
  expect(shouldShowRegenerateButtonInList(status)).toBe(false);
  
  // Modal should not show regenerate button even if explicitly opened
  expect(shouldShowRegenerateButton(status, false)).toBe(false);
  expect(shouldShowRegenerateButton(status, true)).toBe(false);
});
```

### Scenario 6: Button State During Regeneration

**Setup:**
- User clicks regenerate button
- Regeneration process starts
- Modal shows loading state

**Expected Behavior:**
- Re-generate button shows loading spinner
- Button is disabled during regeneration
- User cannot click button multiple times

### Scenario 7: Cross-Page Consistency

**Setup:**
- Test same behavior on both AI Blog Generator and Articles pages

**Expected Behavior:**
- Articles page: Only "Generate Blog" button (no regeneration context)
- AI Blog Generator page: Both generation and regeneration contexts work correctly
- Event handling works consistently across pages

## Implementation Verification Checklist

### Code Changes Verification

- [ ] `BlogAutoWriteModal` component has `isExplicitRegeneration` prop
- [ ] `shouldShowRegenerateButton` function checks both context and status
- [ ] `isRegenerateButtonEnabled` function handles loading states
- [ ] Status constants are properly defined and imported
- [ ] AI Blog Generator page passes correct prop values
- [ ] BlogListItem only shows regenerate button for eligible statuses

### Functional Testing

- [ ] Progress view never shows regenerate button
- [ ] Regeneration view shows button only for eligible statuses
- [ ] Button is properly enabled/disabled based on status
- [ ] Loading states work correctly during regeneration
- [ ] Job status updates properly affect button visibility
- [ ] Cross-page behavior is consistent

### Edge Cases

- [ ] Modal opened with invalid job ID
- [ ] Job status changes while modal is open
- [ ] Network errors during job status fetch
- [ ] Rapid clicking on regenerate button
- [ ] Modal closed and reopened quickly

## Expected Function Behavior

### `shouldShowRegenerateButton(jobStatus)`

```javascript
// Should return true only when:
// 1. isExplicitRegeneration === true
// 2. jobStatus is in REGENERATION_ELIGIBLE_STATUSES

const REGENERATION_ELIGIBLE_STATUSES = [
  BlogAutoWriteJobStatus.COMPLETED,
  BlogAutoWriteJobStatus.FAILED,
  BlogAutoWriteJobStatus.CANCELLED,
];

// Test cases
expect(shouldShowRegenerateButton(BlogAutoWriteJobStatus.COMPLETED)).toBe(true);  // if isExplicitRegeneration = true
expect(shouldShowRegenerateButton(BlogAutoWriteJobStatus.FAILED)).toBe(true);     // if isExplicitRegeneration = true
expect(shouldShowRegenerateButton(BlogAutoWriteJobStatus.CANCELLED)).toBe(true);  // if isExplicitRegeneration = true
expect(shouldShowRegenerateButton(BlogAutoWriteJobStatus.PENDING)).toBe(false);   // always false for in-progress
```

### `isRegenerateButtonEnabled(jobStatus)`

```javascript
// Should return true only when:
// 1. shouldShowRegenerateButton(jobStatus) === true
// 2. !isRegeneratingJob && !isRegeneratingLocal

// Test cases
expect(isRegenerateButtonEnabled(BlogAutoWriteJobStatus.COMPLETED)).toBe(true);   // if not loading
expect(isRegenerateButtonEnabled(BlogAutoWriteJobStatus.COMPLETED)).toBe(false);  // if loading
```

## Manual Testing Steps

1. **Test Progress View:**
   - Click AI status badge on any job
   - Verify no regenerate button appears
   - Test with different job statuses

2. **Test Regeneration View:**
   - Click regenerate button on completed job
   - Verify regenerate button appears and is enabled
   - Test form pre-population

3. **Test Status Filtering:**
   - Verify ongoing jobs don't show regenerate button in list
   - Verify only eligible statuses show regenerate button

4. **Test Loading States:**
   - Click regenerate button
   - Verify button shows loading state
   - Verify button is disabled during loading

5. **Test Cross-Page Consistency:**
   - Test same behavior on Articles page
   - Verify event handling works correctly

This comprehensive test plan ensures the re-generate button visibility control works as expected and provides a clear user experience.

---

## New Feature: Edit Blog Button for Completed Jobs

### Scenario 8: Edit Blog Button for Completed Jobs

**Setup:**
- User clicks on AI status badge to view progress of a completed job
- Job status is `COMPLETED`
- Job has linked article with `article_id`
- Modal opens in progress view

**Expected Behavior:**
- Modal shows progress view with job completion status
- "Edit Blog" button appears instead of re-generate button
- Button has BlogIcon and redirects to Shopify admin
- Button uses `handleEditArticle(article_id, { external: true })`

**Test Cases:**
```javascript
const completedJobWithArticle = {
  status: BlogAutoWriteJobStatus.COMPLETED,
  article: {
    article_id: "gid://shopify/Article/123456789"
  }
};

// Should show Edit Blog button
expect(shouldShowEditBlogButton(completedJobWithArticle.status, completedJobWithArticle)).toBe(true);

// Should not show regenerate button in progress view
expect(shouldShowRegenerateButton(completedJobWithArticle.status)).toBe(false); // when isExplicitRegeneration = false
```

### Scenario 9: No Edit Blog Button for Jobs Without Article

**Setup:**
- Job status is `COMPLETED`
- Job does not have linked article (no `article_id`)
- Modal opens in progress view

**Expected Behavior:**
- No "Edit Blog" button appears
- Only "Close" button is visible
- User cannot edit non-existent article

**Test Cases:**
```javascript
const completedJobWithoutArticle = {
  status: BlogAutoWriteJobStatus.COMPLETED,
  article: null // or missing article_id
};

// Should not show Edit Blog button
expect(shouldShowEditBlogButton(completedJobWithoutArticle.status, completedJobWithoutArticle)).toBe(false);
```

### Scenario 10: Edit Blog Button Priority Over Regenerate

**Setup:**
- Job status is `COMPLETED`
- Job has linked article
- Modal opened in progress view (not explicit regeneration)

**Expected Behavior:**
- "Edit Blog" button takes priority over regenerate button
- Only "Edit Blog" button is shown (not both)
- Clear user action: edit the completed blog

**Implementation Logic:**
```javascript
// In getModalButtons function
if (shouldShowEditBlogButton(currentJobStatus, currentJobData)) {
  // Show Edit Blog button
  buttons.push(editBlogButton);
}
// Only show regenerate if Edit Blog is not shown
else if (shouldShowRegenerateButton(currentJobStatus)) {
  // Show Re-generate button
  buttons.push(regenerateButton);
}
```

### Updated Implementation Verification

#### Additional Code Changes Verification

- [ ] `shouldShowEditBlogButton` function checks for COMPLETED status and article_id
- [ ] `handleEditBlog` function uses `handleEditArticle` with external: true
- [ ] Edit Blog button has BlogIcon and proper styling
- [ ] Button priority logic: Edit Blog takes precedence over regenerate
- [ ] Job data is properly tracked and updated via progress updates

#### Additional Functional Testing

- [ ] Edit Blog button appears for completed jobs with articles
- [ ] Edit Blog button redirects to correct Shopify admin URL
- [ ] No Edit Blog button for jobs without linked articles
- [ ] Edit Blog button takes priority over regenerate button
- [ ] Button works correctly across different modal opening contexts

#### Additional Edge Cases

- [ ] Job completes while modal is open (button should appear)
- [ ] Job data missing article information
- [ ] Invalid article_id handling
- [ ] Shopify admin redirect error handling

### Expected Function Behavior Updates

#### `shouldShowEditBlogButton(jobStatus, jobData)`

```javascript
// Should return true only when:
// 1. jobStatus === BlogAutoWriteJobStatus.COMPLETED
// 2. jobData?.article?.article_id exists

// Test cases
expect(shouldShowEditBlogButton(BlogAutoWriteJobStatus.COMPLETED, { article: { article_id: "123" } })).toBe(true);
expect(shouldShowEditBlogButton(BlogAutoWriteJobStatus.COMPLETED, { article: null })).toBe(false);
expect(shouldShowEditBlogButton(BlogAutoWriteJobStatus.FAILED, { article: { article_id: "123" } })).toBe(false);
```

#### Button Priority Logic

```javascript
// Priority order in progress view:
// 1. Edit Blog (for completed jobs with articles)
// 2. Re-generate (for explicit regeneration requests with eligible statuses)
// 3. Close (always present)

const getExpectedButtons = (jobStatus, jobData, isExplicitRegeneration) => {
  if (shouldShowEditBlogButton(jobStatus, jobData)) {
    return ["Edit Blog", "Close"];
  } else if (shouldShowRegenerateButton(jobStatus) && isExplicitRegeneration) {
    return ["Re-generate", "Close"];
  } else {
    return ["Close"];
  }
};
```

This enhanced test plan ensures both the re-generate button visibility control and the new Edit Blog button functionality work correctly together.

# Blog Auto-Write Database Design

## Document Information

| Field | Value |
|-------|-------|
| **Feature Name** | Blog Auto-Write Database Schema |
| **Document Version** | 1.2.0 |
| **Created Date** | 2024-12-19 |
| **Last Updated** | 2025-06-24 |
| **Status** | Production Ready |
| **Owner** | Backend Development Team |

## Database Schema Overview

### "Dummy Article First" Approach

This design uses a "Dummy Article First" strategy where:
1. **Draft articles are created immediately** in the existing `articles` table when users initiate blog generation
2. **BlogAutoWriteJob table tracks the generation process** and references the existing article
3. **Articles are progressively updated** with AI-generated content as the job progresses
4. **No complex union queries needed** - everything uses the familiar articles table structure
5. **Audit trail preserved** - job records maintained even if articles are deleted

### Concurrency Handling Strategy

**Problem**: Multiple users creating blogs simultaneously could cause database constraint violations.

**Solution**: Time-based unique article IDs for dummy articles:
- **Format**: YYYYMMDDHHMMSS (e.g., 20250624143052 for 2025-06-24 14:30:52)
- **Storage**: Negative values to distinguish from real Shopify article IDs
- **Example**: `-20250624143052` (dummy) vs `123456789` (real Shopify ID)
- **Benefits**: No database conflicts, each job gets unique identifiers
- **Handles**: Also use timestamp-based unique handles to prevent conflicts

### Enhanced Articles Table

The existing `articles` table is extended with new fields to support AI generation tracking, eliminating the need for complex data merging.

### Design Decision: Progress Storage

**Progress is stored only in the job table** to avoid data duplication:
- `articles.generation_status` ✅ **Keep** - needed for filtering and business logic
- `articles.is_ai_generated` ✅ **Keep** - needed for categorization
- `articles.generation_progress` ❌ **Remove** - temporary display data, stored in job table only

This reduces data redundancy while maintaining query performance for the most common use cases.

### Table Structure

#### Enhanced Articles Table Structure

```sql
-- Add new columns to existing articles table
ALTER TABLE articles ADD COLUMN generation_status VARCHAR(50) DEFAULT 'manual';
ALTER TABLE articles ADD COLUMN is_ai_generated BOOLEAN DEFAULT false;
-- Note: generation_progress removed - stored only in blog_auto_write_jobs table

-- Create index for AI generation queries
CREATE INDEX idx_articles_generation_status ON articles(generation_status);
CREATE INDEX idx_articles_ai_generated ON articles(is_ai_generated);
```

#### BlogAutoWriteJob Table Structure

```sql
CREATE TABLE blog_auto_write_jobs (
    -- Primary Key
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Shop Context
    shop_id INTEGER NOT NULL,
    
    -- Article Reference (REQUIRED initially, NULL if article deleted)
    article_id INTEGER NULL, -- FK to articles.id (created first, NULL if deleted)
    
    -- Job Status and Progress
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    progress INTEGER NOT NULL DEFAULT 0, -- 0-100 percentage
    
    -- Input Data (JSON)
    input_data JSONB NOT NULL,
    
    -- Credit Management
    estimated_credit_usage JSONB NOT NULL DEFAULT '{}',
    credit_usage JSONB NOT NULL DEFAULT '{}',
    
    -- Error Handling
    error_message TEXT NULL,
    
    -- Timestamps
    processing_started_at TIMESTAMP NULL,
    processing_completed_at TIMESTAMP NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    
    -- Foreign Key Constraints
    CONSTRAINT fk_blog_auto_write_jobs_article 
        FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE SET NULL,
    
    -- Indexes for Performance
    INDEX idx_blog_auto_write_jobs_shop_id (shop_id),
    INDEX idx_blog_auto_write_jobs_status (status),
    INDEX idx_blog_auto_write_jobs_article_id (article_id),
    INDEX idx_blog_auto_write_jobs_created_at (created_at)
);
```

## "Dummy Article First" Workflow

### 1. Article Creation Phase
**When user initiates blog generation:**
- **Article created immediately** in `articles` table with:
  - `title`: "[Generating] {topic}"
  - `body_html`: "<p>Content is being generated...</p>"
  - `generation_status`: 'pending'
  - `is_ai_generated`: true
  - `article_id`: NULL (no Shopify ID yet)
  - `published_at`: NULL (draft state)

### 2. Job Creation Phase
**Immediately after article creation:**
- **BlogAutoWriteJob created** with:
  - `article_id`: References the dummy article (REQUIRED)
  - `status`: 'pending'
  - `progress`: 0
  - `input_data`: User's generation parameters

### 3. Content Generation Phase
**During AI content generation:**
- **Article progressively updated**:
  - `title`: Updated to generated title
  - `body_html`: Updated with generated content
  - `focus_keyword`: Set from generated keywords
  - `generation_status`: 'generating'
- **Job progress updated**:
  - `progress`: 20% → 60% → 80% (in job table only)

### 4. Shopify Publication Phase
**After content generation completes:**
- **Article published to Shopify**:
  - `article_id`: Set to Shopify's article ID
  - `published_at`: Set to publication timestamp
  - `generation_status`: 'published'
- **Job completion**:
  - `progress`: 100 (in job table)
  - `status`: 'completed'

### 5. Completion Phase
**Job and article finalized:**
- **Job status**: 'completed'
- **Article available** for standard SEO optimization
- **Real-time updates** stop, normal article workflow begins

### 6. Automatic Status Synchronization

**Overview:**
The system maintains automatic synchronization between `blog_auto_write_jobs.status` and `articles.generation_status` throughout the entire job lifecycle to ensure data consistency.

**Synchronization Trigger:**
Every time a job status is updated during queue processing, the corresponding article's `generation_status` is automatically synchronized using the `BlogAutoWriteService.updateJob()` method.

**Database Operations:**
```sql
-- Example: Job status update with automatic article sync
BEGIN;

-- 1. Update job status
UPDATE blog_auto_write_jobs
SET status = 'generating_image',
    progress = 75,
    updated_at = NOW()
WHERE id = $1;

-- 2. Automatic article status synchronization (triggered by service layer)
UPDATE articles
SET generation_status = 'content_ready',
    updated_at = NOW()
WHERE id = (SELECT article_id FROM blog_auto_write_jobs WHERE id = $1);

COMMIT;
```

**Status Mapping Rules:**
```sql
-- Job Status → Article Generation Status Mapping
CASE job_status
  WHEN 'pending' THEN 'pending'
  WHEN 'generating_content' THEN 'generating'
  WHEN 'creating_draft' THEN 'generating'
  WHEN 'linking_article' THEN 'draft_created'
  WHEN 'analyzing_seo' THEN 'content_ready'
  WHEN 'generating_image' THEN 'content_ready'
  WHEN 'uploading_image' THEN 'content_ready'
  WHEN 'updating_article' THEN 'content_ready'
  WHEN 'finalizing_seo' THEN 'content_ready'
  WHEN 'publishing' THEN 'publishing'
  WHEN 'completed' THEN CASE
    WHEN auto_publish = true THEN 'published'
    ELSE 'draft_published'
  END
  WHEN 'failed' THEN 'failed'
  WHEN 'cancelled' THEN 'failed'
END
```

**Smart Progression Logic:**
The system prevents status regression using progression order validation:
```sql
-- Status progression order (higher = more advanced)
-- manual(0) → pending(1) → generating(2) → content_ready(3)
-- → draft_created(4) → publishing(5) → published/draft_published(6)

-- Example: Prevent regression from 'published' to 'generating'
UPDATE articles
SET generation_status = $new_status
WHERE id = $article_id
  AND (
    $new_status = 'failed' OR  -- Always allow failure status
    status_order($new_status) >= status_order(generation_status)  -- Prevent regression
  );
```

**Synchronization Benefits:**
- **Data Consistency**: Job and article statuses always stay in sync
- **Query Performance**: Efficient filtering using `articles.generation_status` index
- **Real-time Accuracy**: Status changes reflect immediately in both tables
- **Audit Trail**: Complete tracking of status progression throughout job lifecycle

### 7. Article Deletion Scenario
**If user deletes the article:**
- **Article**: Deleted from articles table
- **Job record**: Preserved with `article_id` set to NULL
- **Audit trail**: Complete generation history maintained
- **Token usage**: Billing data preserved for accounting
- **Analytics**: Usage statistics remain intact

## Database Relationships

### Primary Relationships
1. **BlogAutoWriteJob → Articles** (One-to-One, Optional)
   - `blog_auto_write_jobs.article_id` → `articles.id`
   - **CREATED**: Set immediately when dummy article is created
   - **PRESERVED**: Set to NULL if article is deleted (preserves audit trail)
   - **AUDIT TRAIL**: Job record maintained even after article deletion

2. **BlogAutoWriteJob → Shop** (Many-to-One)
   - `blog_auto_write_jobs.shop_id` → `shops.shop_id`
   - Inherited from existing patterns

3. **BlogAutoWriteJob → Blog** (Many-to-One, Optional)
   - Target blog for publication (stored in article record)

### Audit Trail & Tracking Benefits
1. **Usage Analytics**
   - Track total generation attempts vs completions
   - Monitor user cancellation patterns
   - Measure feature adoption and success rates

2. **Token Accounting**
   - Preserve token usage data even if article deleted
   - Accurate billing regardless of article lifecycle
   - Historical cost analysis and optimization

3. **Support & Debugging**
   - Complete generation history for troubleshooting
   - Error pattern analysis across deleted articles
   - User behavior insights for feature improvement

## JSON Field Structures

### input_data JSONB Structure
```typescript
interface BlogGenerationInput {
  topic: string;
  keywords: string[];
  tone: 'professional' | 'casual' | 'friendly' | 'authoritative';
  wordCount: number; // 500-2000
  blogType: 'how-to' | 'listicle' | 'review' | 'news' | 'guide';
  targetAudience?: string;
  customInstructions?: string;
}
```

### estimated_credit_usage JSONB Structure
```typescript
interface EstimatedCreditUsage {
  totalCredits: number;
  breakdown: object;
  feature: string;
  featureName: string;
  addonGroup: string;
  estimatedCost: number;
}
```

### credit_usage JSONB Structure
```typescript
interface CreditUsage {
  totalCredits: number;        // Updated with actual usage
  breakdown: object;           // Updated during processing
  feature: string;
  featureName: string;
  addonGroup: string;
  estimatedCost: number;       // Updated with actual cost
}
```

## Status Enums

### BlogGenerationStatus (Articles Table)
```typescript
enum BlogGenerationStatus {
  MANUAL = 'manual',           // Regular manually created articles
  PENDING = 'pending',         // AI generation job created, not started
  GENERATING = 'generating',   // AI actively creating content
  CONTENT_READY = 'content_ready', // Content generated, ready for publication
  DRAFT_CREATED = 'draft_created', // Draft article created in Shopify
  PUBLISHING = 'publishing',   // Publishing to Shopify in progress
  PUBLISHED = 'published',     // Successfully published to Shopify (autoPublish: true)
  DRAFT_PUBLISHED = 'draft_published', // Completed as draft (autoPublish: false)
  FAILED = 'failed'           // Generation or publication failed
}
```

### BlogAutoWriteJobStatus (Job Table) - Full 9-Status Approach
```typescript
enum BlogAutoWriteJobStatus {
  PENDING = 'pending',
  GENERATING_CONTENT = 'generating_content',     // Step 1: Content generation
  CREATING_DRAFT = 'creating_draft',             // Step 2: Shopify draft creation
  LINKING_ARTICLE = 'linking_article',           // Step 3: Article linking
  ANALYZING_SEO = 'analyzing_seo',               // Step 4: First SEO analysis
  GENERATING_IMAGE = 'generating_image',         // Step 5: Featured image generation
  UPLOADING_IMAGE = 'uploading_image',           // Step 6: Shopify image upload
  UPDATING_ARTICLE = 'updating_article',        // Step 7: Article update with image
  FINALIZING_SEO = 'finalizing_seo',            // Step 8: Final SEO analysis
  PUBLISHING = 'publishing',                     // Step 9: Shopify article publish
  COMPLETED = 'completed',
  FAILED = 'failed'
}
```

## Query Patterns

### Common Queries

#### 1. Get Job with Article Details (Handles Deleted Articles)
```sql
SELECT
    baw.*,
    a.title as article_title,
    a.handle as article_handle,
    a.score as article_seo_score,
    CASE
        WHEN baw.article_id IS NULL THEN 'Article Deleted'
        WHEN a.id IS NULL THEN 'Article Not Found'
        ELSE 'Article Available'
    END as article_status
FROM blog_auto_write_jobs baw
LEFT JOIN articles a ON baw.article_id = a.id
WHERE baw.id = $1 AND baw.shop_id = $2;
```

#### 2. List User's Blog Generation History (Including Deleted)
```sql
SELECT
    baw.id,
    baw.status,
    baw.input_data->>'topic' as topic,
    baw.created_at,
    baw.credit_usage->>'totalCredits' as credit_cost,
    COALESCE(a.title, '[Article Deleted]') as article_title,
    CASE
        WHEN baw.article_id IS NULL THEN 'deleted'
        WHEN a.id IS NOT NULL THEN 'available'
        ELSE 'missing'
    END as article_status
FROM blog_auto_write_jobs baw
LEFT JOIN articles a ON baw.article_id = a.id
WHERE baw.shop_id = $1
ORDER BY baw.created_at DESC
LIMIT 20;
```

#### 3. Usage Analytics Query
```sql
SELECT
    COUNT(*) as total_attempts,
    COUNT(CASE WHEN baw.status = 'completed' THEN 1 END) as successful_generations,
    COUNT(CASE WHEN baw.article_id IS NULL THEN 1 END) as deleted_articles,
    AVG(CAST(baw.credit_usage->>'totalCredits' AS INTEGER)) as avg_credit_cost,
    DATE_TRUNC('day', baw.created_at) as generation_date
FROM blog_auto_write_jobs baw
WHERE baw.shop_id = $1
    AND baw.created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE_TRUNC('day', baw.created_at)
ORDER BY generation_date DESC;
```

#### 4. Find Job by Article (Handles Deleted Articles)
```sql
-- Find job for existing article
SELECT baw.*
FROM blog_auto_write_jobs baw
WHERE baw.article_id = $1 AND baw.shop_id = $2;

-- Find orphaned jobs (articles deleted)
SELECT baw.*
FROM blog_auto_write_jobs baw
WHERE baw.article_id IS NULL
    AND baw.shop_id = $1
    AND baw.status = 'completed'
ORDER BY baw.created_at DESC;
```

#### 5. Enhanced Articles List (Shows AI Generation Status)
```sql
SELECT
    a.*,
    baw.status as generation_status,
    baw.progress as generation_progress,
    baw.token_usage->>'finalTokenCost' as generation_cost,
    CASE
        WHEN a.is_ai_generated = true AND baw.id IS NOT NULL THEN 'ai_generated'
        WHEN a.is_ai_generated = true AND baw.id IS NULL THEN 'ai_generated_orphaned'
        ELSE 'manual'
    END as article_type
FROM articles a
LEFT JOIN blog_auto_write_jobs baw ON baw.article_id = a.id
WHERE a.shop_id = $1 AND a.is_deleted = false
ORDER BY a.created_at DESC;
```

## Migration Strategy

### Migration File Structure
```javascript
// YYYYMMDDHHMMSS-enhance-articles-for-ai-generation.js
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add new columns to articles table
    await queryInterface.addColumn('articles', 'generation_status', {
      type: Sequelize.STRING(50),
      defaultValue: 'manual',
      allowNull: false
    });

    await queryInterface.addColumn('articles', 'is_ai_generated', {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
      allowNull: false
    });

    await queryInterface.addColumn('articles', 'generation_progress', {
      type: Sequelize.INTEGER,
      defaultValue: 0,
      allowNull: false
    });

    // Create indexes
    await queryInterface.addIndex('articles', ['generation_status']);
    await queryInterface.addIndex('articles', ['is_ai_generated']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeIndex('articles', ['generation_status']);
    await queryInterface.removeIndex('articles', ['is_ai_generated']);
    await queryInterface.removeColumn('articles', 'generation_progress');
    await queryInterface.removeColumn('articles', 'is_ai_generated');
    await queryInterface.removeColumn('articles', 'generation_status');
  }
};
```

```javascript
// YYYYMMDDHHMMSS-create-blog-auto-write-jobs.js
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('blog_auto_write_jobs', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      shop_id: {
        type: Sequelize.INTEGER,
        allowNull: false
      },

      article_id: {
        type: Sequelize.INTEGER,
        allowNull: true, // NULL if article deleted
        references: {
          model: 'articles',
          key: 'id'
        },
        onDelete: 'SET NULL' // Preserve audit trail
      },
      status: {
        type: Sequelize.STRING(50),
        allowNull: false,
        defaultValue: 'pending'
      },
      progress: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      input_data: {
        type: Sequelize.JSONB,
        allowNull: false
      },
      estimated_credit_usage: {
        type: Sequelize.JSONB,
        allowNull: false,
        defaultValue: {}
      },
      credit_usage: {
        type: Sequelize.JSONB,
        allowNull: false,
        defaultValue: {}
      },
      error_message: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      processing_started_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      processing_completed_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Create indexes
    await queryInterface.addIndex('blog_auto_write_jobs', ['shop_id']);
    await queryInterface.addIndex('blog_auto_write_jobs', ['status']);

    await queryInterface.addIndex('blog_auto_write_jobs', ['article_id']);
    await queryInterface.addIndex('blog_auto_write_jobs', ['created_at']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('blog_auto_write_jobs');
  }
};
```

## Performance Considerations

### Indexing Strategy
- **Primary Access**: shop_id + status (most common queries)
- **Shop Queries**: shop_id + status (shop-specific job history)
- **Article Lookup**: article_id (job-to-article relationship)
- **Cleanup**: created_at (for archival/cleanup jobs)
- **AI Generation**: generation_status, is_ai_generated (articles filtering)

### Data Retention
- **Active Jobs**: Keep indefinitely for audit trail
- **Completed Jobs**: Archive after 1 year
- **Failed Jobs**: Keep for 6 months for debugging
- **Orphaned Jobs**: Keep permanently for billing accuracy

## Security Considerations

### Data Protection
- **Input Sanitization**: All user inputs in input_data are sanitized
- **Content Security**: Generated content is validated before storage
- **Access Control**: All queries include shop_id for tenant isolation
- **Token Security**: Token usage data encrypted and access-controlled

### Audit Trail
- **Token Usage**: Complete audit trail of token consumption
- **Status Changes**: All status transitions are logged
- **Error Tracking**: Detailed error information for debugging
- **Deletion Tracking**: Preserve records even after article deletion

## Integration Points

### With Articles Table
- **Creation**: Job creates article record immediately
- **Updates**: Article content updated progressively during generation
- **Deletion**: Article deletion preserves job record for audit trail
- **SEO**: Generated articles available for standard SEO optimization

### With Existing Services
- **ArticleService**: Enhanced to handle AI generation workflow
- **SocketService**: Emits progress updates based on job status
- **CacheService**: Token management integration via job.token_usage
- **ShopifyService**: Publishes generated articles to Shopify

---

## Next Steps

1. **Create Migration Files**: Implement the database schema changes
2. **Create Sequelize Models**: Define enhanced Article and BlogAutoWriteJob models
3. **Update Article Model**: Add new fields and associations
4. **Test Relationships**: Verify all foreign key constraints work correctly
5. **Performance Testing**: Validate query performance with indexes
6. **Audit Trail Testing**: Verify data preservation on article deletion

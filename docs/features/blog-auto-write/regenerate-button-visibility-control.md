# Blog Auto-Write Modal - Re-generate Button Visibility Control

## Document Information

| Field | Value |
|-------|-------|
| **Feature Name** | Re-generate Button Visibility Control |
| **Document Version** | 1.0.0 |
| **Created Date** | 2025-01-23 |
| **Last Updated** | 2025-01-23 |
| **Status** | Pending Implementation |
| **Owner** | Development Team |
| **Reviewers** | Product Team, UX Team |

## Overview

This document outlines the required changes to the blog auto-write modal's re-generate button behavior based on team discussions. The changes aim to improve user experience by providing clearer guidance on when regeneration is appropriate and preventing confusion with ongoing job operations.

## Problem Statement

### Current Issues

1. **Confusing User Experience**: Users can attempt to regenerate ongoing jobs, which is not logical
2. **Inconsistent Button Visibility**: Re-generate button appears in all modal contexts regardless of intent
3. **Unclear Workflow**: No distinction between viewing progress and requesting regeneration
4. **User Confusion**: Users unsure when regeneration is appropriate

### Team Decision

After team discussion, the following requirements were established:
- **Only show re-generate button when opened from AI Blog Generator page's regenerate button click**
- **Only enable button for completed or failed jobs** (not ongoing jobs)
- **Remove regeneration option for ongoing jobs entirely**

## Current Implementation Analysis

### Job Status Categories

```javascript
// In-progress statuses (no regeneration allowed)
const IN_PROGRESS_STATUSES = [
  BlogAutoWriteJobStatus.PENDING,
  BlogAutoWriteJobStatus.GENERATING_CONTENT,
  BlogAutoWriteJobStatus.CREATING_DRAFT,
  BlogAutoWriteJobStatus.LINKING_ARTICLE,
  BlogAutoWriteJobStatus.ANALYZING_SEO,
  BlogAutoWriteJobStatus.GENERATING_IMAGE,
  BlogAutoWriteJobStatus.UPLOADING_IMAGE,
  BlogAutoWriteJobStatus.UPDATING_ARTICLE,
  BlogAutoWriteJobStatus.FINALIZING_SEO,
  BlogAutoWriteJobStatus.PUBLISHING,
];

// Eligible statuses for regeneration
const REGENERATION_ELIGIBLE_STATUSES = [
  BlogAutoWriteJobStatus.COMPLETED,
  BlogAutoWriteJobStatus.FAILED,
  BlogAutoWriteJobStatus.CANCELLED, // Allow regeneration of cancelled jobs
];
```

### Current Behavior (Before Changes)

1. **Modal Progress View**: Re-generate button shows for ALL AI-generated jobs regardless of status
2. **Modal Form View**: Shows "Re-generate" button text when in regeneration mode
3. **AI Blog Generator Page**: All AI-generated articles show regenerate button in list
4. **Button Logic**: `shouldShowRegenerateButton()` returns true for any job with ID

## Required Changes

### 1. BlogAutoWriteModal Component Enhancement

**File**: `web/frontend/components/blog-auto-write/BlogAutoWriteModal.jsx`

#### Add New Prop for Regeneration Context

```javascript
const BlogAutoWriteModal = ({ 
  isOpen, 
  onClose, 
  initialView = null, 
  initialJobId = null, 
  regenerationJobId = null,
  isExplicitRegeneration = false // NEW: Track if opened via regenerate button
}) => {
```

#### Update Button Visibility Logic

```javascript
// Replace existing shouldShowRegenerateButton function
const shouldShowRegenerateButton = (jobStatus) => {
  // Only show if explicitly opened for regeneration
  if (!isExplicitRegeneration) {
    return false;
  }
  
  // Only show for completed, failed, or cancelled jobs
  const eligibleStatuses = [
    BlogAutoWriteJobStatus.COMPLETED,
    BlogAutoWriteJobStatus.FAILED,
    BlogAutoWriteJobStatus.CANCELLED,
  ];
  
  return eligibleStatuses.includes(jobStatus);
};

// Update button enablement logic
const isRegenerateButtonEnabled = (jobStatus) => {
  return shouldShowRegenerateButton(jobStatus) && 
         !isRegeneratingJob && 
         !isRegeneratingLocal;
};
```

#### Update Modal Button Rendering

```javascript
// In getModalButtons function for progress view
if (currentView === "progress") {
  const buttons = [];
  
  // Get current job status from progress component or job data
  const currentJobStatus = jobData?.status || BlogAutoWriteJobStatus.PENDING;
  
  // Add re-generate button only if conditions are met
  if (shouldShowRegenerateButton(currentJobStatus)) {
    buttons.push(
      <button
        key="regenerate"
        variant="primary"
        onClick={handleRegenerate}
        loading={isRegeneratingJob || isRegeneratingLocal}
        disabled={!isRegenerateButtonEnabled(currentJobStatus)}
      >
        {t("Re-generate")}
      </button>
    );
  }
  
  buttons.push(
    <button key="close" onClick={handleClose}>
      {t("Close")}
    </button>
  );
  
  return buttons;
}
```

### 2. AI Blog Generator Page Updates

**File**: `web/frontend/pages/ai-blog-generator.jsx`

#### Update Modal Props

```javascript
{/* Global Blog Auto-Write Modal - For list items */}
{activeModalJobId && (
  <BlogAutoWriteModal
    isOpen={!!activeModalJobId}
    onClose={() => {
      setActiveModalJobId(null);
      setActiveModalType(null);
    }}
    initialView={activeModalType === "regenerate" ? "form" : undefined}
    initialJobId={activeModalType === "progress" ? activeModalJobId : undefined}
    regenerationJobId={activeModalType === "regenerate" ? activeModalJobId : undefined}
    isExplicitRegeneration={activeModalType === "regenerate"} // NEW
  />
)}
```

### 3. BlogListItem Component Updates

**File**: `web/frontend/pages/ai-blog-generator.jsx` (BlogListItem component)

#### Update Regenerate Button Visibility

```javascript
{/* Regenerate button - only for eligible statuses */}
{autoWriteStatus.isAiGenerated && 
 REGENERATION_ELIGIBLE_STATUSES.includes(autoWriteStatus.status) && (
  <TooltipWrapper content="Regenerate blog">
    <Button
      icon={RefreshIcon}
      variant="tertiary"
      size="micro"
      onClick={handleRegenerate}
    />
  </TooltipWrapper>
)}
```

#### Add Status Constants

```javascript
// Add at the top of the file or import from a constants file
const REGENERATION_ELIGIBLE_STATUSES = [
  BlogAutoWriteJobStatus.COMPLETED,
  BlogAutoWriteJobStatus.FAILED,
  BlogAutoWriteJobStatus.CANCELLED,
];

const IN_PROGRESS_STATUSES = [
  BlogAutoWriteJobStatus.PENDING,
  BlogAutoWriteJobStatus.GENERATING_CONTENT,
  BlogAutoWriteJobStatus.CREATING_DRAFT,
  BlogAutoWriteJobStatus.LINKING_ARTICLE,
  BlogAutoWriteJobStatus.ANALYZING_SEO,
  BlogAutoWriteJobStatus.GENERATING_IMAGE,
  BlogAutoWriteJobStatus.UPLOADING_IMAGE,
  BlogAutoWriteJobStatus.UPDATING_ARTICLE,
  BlogAutoWriteJobStatus.FINALIZING_SEO,
  BlogAutoWriteJobStatus.PUBLISHING,
];
```

## User Experience Flow

### Scenario 1: Viewing Progress (No Regeneration)

1. **User Action**: User clicks on AI status badge to view progress
2. **Modal Opens**: Progress view opens with job tracking
3. **Button Visibility**: Only "Close" button visible (no regenerate button)
4. **User Experience**: Clear focus on monitoring progress

### Scenario 2: Explicit Regeneration Request

1. **User Action**: User clicks regenerate button in AI Blog Generator list
2. **Status Check**: System checks if job status allows regeneration
3. **Modal Opens**: Form view opens with original data pre-populated
4. **Button Visibility**: "Re-generate" button visible and enabled for eligible statuses
5. **User Experience**: Clear intent to modify and regenerate content

### Scenario 3: Ongoing Job (No Regeneration Available)

1. **User Action**: User views ongoing job in AI Blog Generator list
2. **Button Visibility**: No regenerate button visible in list
3. **Progress View**: Only progress monitoring available
4. **User Experience**: Clear indication that job is in progress

## Benefits

### User Experience Benefits

1. **Reduced Confusion**: No regeneration options for ongoing jobs
2. **Clear Intent Separation**: Distinct workflows for progress viewing vs. regeneration
3. **Better Visual Hierarchy**: Buttons appear only when contextually appropriate
4. **Improved Guidance**: Users understand when regeneration is possible

### Technical Benefits

1. **Cleaner Logic**: Explicit context tracking for regeneration requests
2. **Better State Management**: Clear separation of modal purposes
3. **Consistent Behavior**: Uniform regeneration logic across components
4. **Error Prevention**: Prevents invalid regeneration attempts

## Implementation Checklist

### Code Changes

- [ ] Add `isExplicitRegeneration` prop to BlogAutoWriteModal
- [ ] Update `shouldShowRegenerateButton` logic with status and context checks
- [ ] Modify modal button rendering in progress view
- [ ] Update AI Blog Generator page modal props
- [ ] Add status constants for regeneration eligibility
- [ ] Update BlogListItem regenerate button visibility

### Testing Requirements

- [ ] Test button visibility for each job status
- [ ] Verify button only shows for explicit regeneration requests
- [ ] Confirm no regeneration options for in-progress jobs
- [ ] Test cross-component consistency
- [ ] Validate edge cases (cancelled, failed, completed jobs)

### Documentation Updates

- [ ] Update component prop documentation
- [ ] Add status-based behavior documentation
- [ ] Update user guide with new workflow
- [ ] Create testing scenarios documentation

## Conclusion

This enhancement significantly improves the user experience by providing clear guidance on when regeneration is appropriate and preventing confusion with ongoing job operations. The changes maintain backward compatibility while introducing more intuitive behavior patterns that align with user expectations and business logic.

---

## Additional Enhancement: Edit Blog Button for Completed Jobs

### New Feature Added (2025-01-23)

**Context**: In addition to the re-generate button visibility control, a new "Edit Blog" button has been added for completed jobs in the progress view to provide direct access to Shopify admin for editing the generated blog.

### Feature Overview

When a blog auto-write job is fully completed and has a linked article, users can now directly edit the blog in Shopify admin from the progress modal.

### Implementation Details

#### New Helper Functions

```javascript
// Helper function to determine if Edit Blog button should be shown
const shouldShowEditBlogButton = (jobStatus, jobData) => {
  // Only show for completed jobs that have a linked article
  return jobStatus === BlogAutoWriteJobStatus.COMPLETED &&
         jobData?.article?.article_id;
};

// Handle Edit Blog button click
const handleEditBlog = useCallback(() => {
  if (currentJobData?.article?.article_id) {
    handleEditArticle(currentJobData.article.article_id, { external: true });
  }
}, [currentJobData, handleEditArticle]);
```

#### Button Priority Logic

```javascript
// In progress view, buttons appear in this priority order:
if (shouldShowEditBlogButton(currentJobStatus, currentJobData)) {
  // Show Edit Blog button for completed jobs with articles
  buttons.push(editBlogButton);
}
// Only show regenerate if Edit Blog is not shown
else if (shouldShowRegenerateButton(currentJobStatus)) {
  // Show Re-generate button for explicit regeneration requests
  buttons.push(regenerateButton);
}
```

#### Job Data Tracking

```javascript
// Track both job status and full job data for button logic
const [currentJobStatus, setCurrentJobStatus] = useState(null);
const [currentJobData, setCurrentJobData] = useState(null);

// Update both status and data from progress updates
const handleJobProgressUpdate = useCallback((_progress, jobData) => {
  if (jobData?.status) {
    setCurrentJobStatus(jobData.status);
    setCurrentJobData(jobData);
  }
}, []);
```

### User Experience Flow

#### Scenario: Completed Job with Article

1. **User Action**: User clicks AI status badge on completed job
2. **Modal Opens**: Progress view shows job completion status
3. **Button Display**: "Edit Blog" button appears with BlogIcon
4. **User Clicks**: Button redirects to Shopify admin article editor
5. **External Navigation**: User can edit the blog directly in Shopify

#### Scenario: Completed Job without Article

1. **User Action**: User clicks AI status badge on completed job
2. **Modal Opens**: Progress view shows job completion status
3. **Button Display**: Only "Close" button appears (no Edit Blog option)
4. **User Experience**: Clear indication that no article is available for editing

### Benefits

1. **Direct Access**: Users can immediately edit completed blogs without navigation
2. **Contextual Action**: Edit button appears only when relevant (completed + article exists)
3. **Priority Logic**: Edit Blog takes precedence over regeneration for completed jobs
4. **Consistent UX**: Uses same Shopify admin redirect pattern as list items
5. **Clear Intent**: Separates editing existing content from regenerating new content

### Technical Integration

#### Required Imports

```javascript
import { useAppBridgeRedirect } from "@/hooks/useAppBridgeRedirect";
import { BlogIcon } from "@shopify/polaris-icons";
```

#### Hook Usage

```javascript
const { handleEditArticle } = useAppBridgeRedirect();
```

#### Button Implementation

```javascript
<button
  key="edit-blog"
  variant="primary"
  onClick={handleEditBlog}
  icon={BlogIcon}
>
  {t("Edit Blog")}
</button>
```

### Testing Considerations

#### Test Scenarios

1. **Completed Job with Article**: Verify Edit Blog button appears and redirects correctly
2. **Completed Job without Article**: Verify no Edit Blog button appears
3. **Button Priority**: Verify Edit Blog takes precedence over regenerate button
4. **Job Data Updates**: Verify button appears when job completes while modal is open
5. **Error Handling**: Verify graceful handling of missing or invalid article IDs

#### Expected Behavior

- Edit Blog button only appears for `BlogAutoWriteJobStatus.COMPLETED`
- Button requires `jobData?.article?.article_id` to be present
- Button redirects to Shopify admin with correct article ID
- Button takes priority over regenerate button in progress view
- Button integrates seamlessly with existing modal button logic

This enhancement provides users with immediate access to edit their completed AI-generated blogs, improving the overall workflow and user experience.

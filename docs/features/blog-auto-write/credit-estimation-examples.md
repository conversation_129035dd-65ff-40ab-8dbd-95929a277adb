# Credit Estimation Service - Namespace API Examples

## Overview

The new `CreditEstimationService` provides a namespace-based API for estimating credits across all AI add-on features. It supports multiple addon groups and is designed for extensibility.

## Namespace Structure

```javascript
const creditEstimationService = require('./services/TokenEstimationService');

// AI_OPTIMIZER addon features
creditEstimationService.blog.autoWrite(input)
creditEstimationService.blog.optimization(input, settings)
creditEstimationService.product.optimization(input, settings)
creditEstimationService.collection.optimization(input, settings)
creditEstimationService.article.optimization(input, settings)
creditEstimationService.image.altText(input, settings)

// IMAGE_OPTIMIZER addon features
creditEstimationService.image.optimization(input, settings)
creditEstimationService.image.bulkOptimization(input, settings)
```

## Usage Examples

### 1. Blog Auto-Write (AI_OPTIMIZER)

```javascript
const blogInput = {
  topic: "SEO Best Practices",
  keyword: "SEO optimization",
  blogType: "Guide",
  wordCount: "800-1200",
  tone: "Formal",
  customInstructions: "Include examples",
  featuredImageDescription: "Professional dashboard"
};

const result = creditEstimationService.blog.autoWrite(blogInput);

console.log(result);
// Output:
// {
//   totalCredits: 112,
//   breakdown: {
//     content: { credits: 12, details: {...} },
//     images: { credits: 100, details: {...} }
//   },
//   feature: 'blog_auto_write',
//   featureName: 'Blog Auto-Write',
//   addonGroup: 'AI_OPTIMIZER',
//   estimatedCost: 0.224
// }
```

### 2. Product Optimization (AI_OPTIMIZER)

```javascript
const productInput = {
  title: "Wireless Headphones",
  description: "High-quality wireless headphones...",
  focusKeyword: "wireless headphones",
  images: [{id: 1, src: "..."}, {id: 2, src: "..."}]
};

const productSettings = {
  meta: true,
  tags: true,
  imageAltText: "allImages"
};

const result = creditEstimationService.product.optimization(productInput, productSettings);

console.log(result);
// Output:
// {
//   totalCredits: 18,
//   breakdown: {
//     content: { credits: 6, details: {...} },
//     images: { credits: 12, details: {...} }
//   },
//   feature: 'product_optimization',
//   featureName: 'Product Content Optimization',
//   addonGroup: 'AI_OPTIMIZER',
//   estimatedCost: 0.036
// }
```

### 3. Image Alt-Text Generation (AI_OPTIMIZER)

```javascript
const imageInput = {
  images: [
    { id: 1, src: "product1.jpg" },
    { id: 2, src: "product2.jpg" }
  ]
};

const imageSettings = {
  imageAltText: "allImages"
};

const result = creditEstimationService.image.altText(imageInput, imageSettings);

console.log(result);
// Output:
// {
//   totalCredits: 12,
//   breakdown: {
//     images: { credits: 12, details: {...} }
//   },
//   feature: 'image_alt_text',
//   featureName: 'Image Alt-Text Generation',
//   addonGroup: 'AI_OPTIMIZER',
//   estimatedCost: 0.024
// }
```

### 4. Image File Optimization (IMAGE_OPTIMIZER)

```javascript
const optimizationInput = {
  imageCount: 10
};

const result = creditEstimationService.image.optimization(optimizationInput);

console.log(result);
// Output:
// {
//   totalCredits: 10,
//   breakdown: {
//     images: { 
//       credits: 10, 
//       details: { 
//         creditsPerImage: 1, 
//         imageCount: 10, 
//         totalCredits: 10 
//       } 
//     }
//   },
//   feature: 'image_optimization',
//   featureName: 'Image File Optimization',
//   addonGroup: 'IMAGE_OPTIMIZER',
//   estimatedCost: 0.02
// }
```

## Backward Compatibility

The service maintains backward compatibility with existing methods:

```javascript
// Old methods still work (deprecated)
const contentCredits = creditEstimationService.estimateBlogContentCredits(input);
const imageCredits = creditEstimationService.estimateBlogImageCredits(input);
const totalCredits = creditEstimationService.estimateTotalBlogCredits(input);

// New namespace methods (recommended)
const newResult = creditEstimationService.blog.autoWrite(input);
```

## Add-on Group Support

The service automatically identifies which addon group each feature belongs to:

- **AI_OPTIMIZER**: Blog auto-write, content optimization, alt-text generation
- **IMAGE_OPTIMIZER**: Image file optimization, bulk optimization

This enables proper credit tracking and billing across different addon subscriptions.

## Extensibility

Adding new features is straightforward:

```javascript
// Add new feature to constants
const ADDON_FEATURES = {
  // ... existing features
  EMAIL_OPTIMIZATION: 'email_optimization'
};

// Add configuration
const FEATURE_CONFIGS = {
  // ... existing configs
  [ADDON_FEATURES.EMAIL_OPTIMIZATION]: {
    name: 'Email Content Optimization',
    addonGroup: 'EMAIL_OPTIMIZER',
    contentCalculation: {
      method: 'aiservice_standard'
    }
  }
};

// Add to namespace in constructor
this.email = {
  optimization: (input, settings) => this.#estimateCredits(ADDON_FEATURES.EMAIL_OPTIMIZATION, input, settings)
};
```

## Error Handling

The service provides clear error messages for invalid configurations:

```javascript
try {
  const result = creditEstimationService.blog.autoWrite(invalidInput);
} catch (error) {
  console.error(error.message); // "Unknown add-on feature: invalid_feature"
}
```

## Integration with Controllers

Use in API controllers:

```javascript
// BlogAutoWriteController.js
const creditEstimation = creditEstimationService.blog.autoWrite(input);

// ProductController.js
const creditEstimation = creditEstimationService.product.optimization(product, settings);

// ImageController.js
const creditEstimation = creditEstimationService.image.optimization(imageData);
```

This namespace approach provides a clean, intuitive API that scales with your add-on feature ecosystem while maintaining backward compatibility and clear separation of concerns.

# Blog Auto-Write Step Tracking Implementation

## Overview

This document details the comprehensive step tracking system implemented for the blog auto-write feature, enabling robust job recovery, detailed progress monitoring, and cost prediction.

## 9-Step Workflow

The blog auto-write process follows this exact sequence:

1. **CONTENT_GENERATION** (30%) - AI generates main blog content using OpenAI
2. **SHOPIFY_DRAFT_CREATION** (10%) - Save generated content to Shopify as draft blog post
3. **ARTICLE_LINKING** (5%) - Link dummy article with Shopify draft blog post
4. **FIRST_SEO_ANALYSIS** (10%) - First round of SEO analysis on content
5. **IMAGE_GENERATION** (20%) - Generate featured image using DALL-E
6. **SHOPIFY_IMAGE_UPLOAD** (5%) - Save featured image in Shopify
7. **SHOPIFY_ARTICLE_UPDATE** (5%) - Update article in Shopify with featured image
8. **FINAL_SEO_ANALYSIS** (10%) - Final SEO analysis including image optimization
9. **SHOPIFY_ARTICLE_PUBLISH** (5%) - Publish article to Shopify (if autopublish enabled)

## Database Schema

### BlogAutoWriteJob Table Fields

```sql
-- Core tracking fields
steps JSONB NOT NULL DEFAULT '[...]'  -- Detailed step tracking array
retry_count INTEGER NOT NULL DEFAULT 0  -- Number of retries attempted
max_retries INTEGER NOT NULL DEFAULT 3  -- Maximum retries allowed

-- Credit usage tracking
estimated_credit_usage JSONB NOT NULL DEFAULT '{}'  -- Cost prediction
credit_usage JSONB NOT NULL DEFAULT '{}'  -- Actual usage tracking
```

### Step Structure

Each step in the `steps` JSONB array contains:

```json
{
  "step": "CONTENT_GENERATION",
  "completed": false,
  "error": null,
  "startedAt": null,
  "completedAt": null
}
```

### Token Usage Structure

Both `estimated_token_usage` and `token_usage` follow this format:

```json
{
  "content_generation": { "tokens": 1500, "cost_usd": 0.045 },
  "image_generation": { "tokens": 1, "cost_usd": 0.040 },
  "seo_analysis": { "tokens": 500, "cost_usd": 0.015 },
  "total_tokens": 2001,
  "total_cost_usd": 0.100
}
```

## Virtual Fields

The model includes computed virtual fields for clean API responses:

- **`currentProgress`** - Real-time progress calculation based on completed steps
- **`nextStep`** - Next step to process (null if all complete)
- **`canRetry`** - Whether job can be retried (retry_count < max_retries)
- **`lastFailedStep`** - Last step that failed (null if no failures)

## Performance Indexes

Optimized database queries with strategic indexes:

```sql
-- Retry monitoring
CREATE INDEX idx_blog_auto_write_jobs_retry_count ON blog_auto_write_jobs (retry_count);
CREATE INDEX idx_blog_auto_write_jobs_status_retry ON blog_auto_write_jobs (status, retry_count);

-- Shop-specific queries
CREATE INDEX idx_blog_auto_write_jobs_shop_status ON blog_auto_write_jobs (shop_id, status);
CREATE INDEX idx_blog_auto_write_jobs_shop_created ON blog_auto_write_jobs (shop_id, created_at);
```

## Job Recovery Capabilities

### Resume from Failed Step

When a job fails, the system can:

1. Identify the exact step that failed via `lastFailedStep`
2. Resume processing from the `nextStep`
3. Track retry attempts with `retry_count`
4. Respect retry limits with `max_retries`

### Progress Calculation

Progress is calculated using weighted step completion:

```javascript
// Example: Content generation complete = 30% progress
const progress = BlogAutoWriteJobSteps.calculateProgress(job.steps);
```

### Error Handling

Each step can track specific errors:

```json
{
  "step": "IMAGE_GENERATION",
  "completed": false,
  "error": "DALL-E API rate limit exceeded",
  "startedAt": "2025-06-24T10:00:00Z",
  "completedAt": null
}
```

## API Integration

### Job Status Response

```json
{
  "id": 123,
  "status": "generating_content",
  "progress": 40,
  "currentProgress": 40,
  "nextStep": "SHOPIFY_DRAFT_CREATION",
  "canRetry": true,
  "lastFailedStep": null,
  "retry_count": 0,
  "max_retries": 3,
  "estimated_token_usage": { ... },
  "token_usage": { ... },
  "steps": [ ... ]
}
```

### Queue Processing

The queue processor can:

1. Check `nextStep` to determine what to process
2. Update step status with timestamps
3. Handle failures by setting error messages
4. Implement retry logic based on `canRetry`

## Cost Management

### Estimation vs Actual

- **`estimated_token_usage`** - Set during job creation for budget planning
- **`token_usage`** - Updated during execution with actual costs
- **Comparison** - Enables budget accuracy improvements and cost overrun detection

### Budget Tracking

```javascript
// Check if job exceeds estimated cost
const estimatedCost = job.estimated_token_usage.total_cost_usd;
const actualCost = job.token_usage.total_cost_usd;
const overrun = actualCost > estimatedCost;
```

## Implementation Files

### Core Files
- `web/packages/storeseo-enums/src/blogAutoWrite/jobSteps.ts` - Step definitions and utilities
- `web/sequelize/attributes/blog-auto-write-job-attributes.js` - Model attributes with virtual fields
- `web/sequelize/models/blogautowritejob.js` - Sequelize model definition
- `web/sequelize/migrations/20250612080345-create-blog-auto-write-jobs.js` - Database migration

### Key Functions
- `BlogAutoWriteJobSteps.getDefaultSteps()` - Initialize new job steps
- `BlogAutoWriteJobSteps.calculateProgress()` - Calculate completion percentage
- `job.currentProgress` - Virtual field for real-time progress
- `job.nextStep` - Virtual field for next processing step

## Frontend Progress Tracking Integration

### Recent Architecture Improvements (2025-07-02)

#### Unified Modal Architecture
The progress tracking system has been consolidated into a single intelligent modal:

```jsx
// BlogAutoWriteModal.jsx - Unified modal with smart view detection
const BlogAutoWriteModal = ({ isOpen, onClose, initialView = null, initialJobId = null }) => {
  // Auto-detect correct initial view
  const getInitialView = useCallback(() => {
    if (initialView) return initialView;     // Explicit override
    if (initialJobId) return "progress";     // Existing job → Progress view
    return "form";                           // Default → Form view
  }, [initialView, initialJobId]);

  const [currentView, setCurrentView] = useState(getInitialView());
  const [createdJobId, setCreatedJobId] = useState(initialJobId);

  return (
    <Modal type="app-bridge" open={isOpen} setOpen={onClose}>
      <Modal.Section>
        {currentView === "form" && <BlogAutoWriteForm />}
        {currentView === "progress" && (createdJobId || initialJobId) && (
          <BlogAutoWriteJobProgress
            jobId={createdJobId || initialJobId}
            refreshInterval={3500}
            autoRefresh={true}
            showHeader={true}
            showCreditInfo={true}
          />
        )}
      </Modal.Section>
    </Modal>
  );
};
```

#### Context-Aware Modal Opening
The modal intelligently opens in the appropriate view based on user interaction:

- **Generate Button**: Opens in form view for new blog creation
- **Article Row Click**: Opens in progress view for existing job tracking
- **Automatic Detection**: No configuration needed, works based on props

#### React Query Integration
Replaced manual polling with proper React Query implementation:

```jsx
// BlogAutoWriteJobProgress.jsx - Enhanced with useAppQuery
const { data, isLoading, error, refetch } = useAppQuery({
  queryKey: ["blog-auto-write-job-progress", jobId],
  queryFn: () => getBlogAutoWriteJobStatus(jobId),
  reactQueryOptions: {
    enabled: !!jobId,
    refetchInterval: (data) => {
      if (!autoRefresh) return false;
      const status = data?.data?.status;
      return isJobInProgress(status) ? refreshInterval : false;
    },
    keepPreviousData: true,
    retry: 3,
  },
});
```

#### Simplified Status Display
Updated status display logic for better user experience:

```jsx
// useBlogAutoWriteStatus.js - Simplified status logic
const getDisplayStatus = () => {
  switch (autoWriteJob.status) {
    case BlogAutoWriteJobStatus.COMPLETED:
      return "AI Generated";
    case BlogAutoWriteJobStatus.FAILED:
      return "Blog Generation: Failed";
    case BlogAutoWriteJobStatus.PENDING:
      return "Blog Generation: Pending";
    default:
      return "Blog Generation: Ongoing";
  }
};
```

## Frontend Progress Tracking Integration

### Progress Popover System
The step tracking system is fully integrated with a real-time progress popover that provides users with detailed feedback during blog generation.

#### BlogAutoWriteProgressPopover Component
```jsx
const BlogAutoWriteProgressPopover = ({ jobId, isOpen, onClose, activator }) => {
  const [jobProgress, setJobProgress] = useState(null);

  // Fetch job progress when popover opens
  useEffect(() => {
    if (isOpen && jobId && !jobProgress) {
      fetchJobProgress();
    }
  }, [isOpen, jobId]);

  // Poll for updates if job is still in progress
  useEffect(() => {
    let interval;
    if (isOpen && jobProgress && isJobInProgress(jobProgress.status)) {
      interval = setInterval(fetchJobProgress, 5000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isOpen, jobProgress]);

  const fetchJobProgress = async () => {
    const response = await fetch(`/api/blog-auto-write/${jobId}`);
    const data = await response.json();
    if (data.success) {
      setJobProgress(data.data);
    }
  };
};
```

#### Articles Table Integration
```jsx
// ArticleRowItem.jsx - Conditional rendering for AI-generated articles
{autoWriteStatus.isAiGenerated ? (
  <BlockStack gap="100">
    <Link url={`/optimize-seo/articles/${article_id}`}>
      <Text fontWeight="medium">{title}</Text>
    </Link>
    <InlineStack gap="200" blockAlign="center">
      <BlogAutoWriteProgressPopover
        jobId={autoWriteStatus.jobId}
        activator={
          <Button variant="plain" size="micro">
            <InlineStack gap="100" blockAlign="center">
              <Icon source={ViewIcon} tone={autoWriteStatus.statusTone} />
              <Text tone={autoWriteStatus.statusTone} fontWeight="medium">
                {autoWriteStatus.displayStatus}
              </Text>
            </InlineStack>
          </Button>
        }
      />
    </InlineStack>
  </BlockStack>
) : (
  // Regular articles: Original horizontal layout
  <InlineStack>
    <TitleColumn title={title} url={url} />
    <AIGenerationStatusBadge />
  </InlineStack>
)}
```

#### Status Detection Hook
```javascript
// useBlogAutoWriteStatus.js - Frontend-backend enum synchronization
export const useBlogAutoWriteStatus = (article) => {
  return useMemo(() => {
    const autoWriteJob = article?.autoWriteJob;
    const isAiGenerated = !!autoWriteJob;
    const isInProgress = isAiGenerated && IN_PROGRESS_STATUSES.includes(autoWriteJob.status);

    const getDisplayStatus = () => {
      if (autoWriteJob.status === BlogAutoWriteJobStatus.COMPLETED) {
        return "AI Generated Blog Post";
      }
      // Use actual backend enum labels
      const statusText = BlogAutoWriteJobStatus.labels[autoWriteJob.status] || "In progress";
      return `Blog generation: ${statusText}`;
    };

    return {
      isAiGenerated,
      isInProgress,
      status: autoWriteJob?.status,
      displayStatus: getDisplayStatus(),
      statusTone: getStatusTone(),
      jobId: autoWriteJob?.id,
      progress: autoWriteJob?.progress || 0,
    };
  }, [article?.autoWriteJob]);
};
```

#### Progress Display Features
- **Real-time Polling**: Updates every 3.5 seconds for active jobs using React Query
- **Unified Modal UI**: Single `BlogAutoWriteModal` with intelligent view detection
- **Context-Aware Opening**: Automatically opens in correct view (form vs progress)
- **Simplified Status**: "Ongoing" for in-progress, "AI Generated" for completed
- **Dynamic Icons**: Magic icon for completed, View icon for others
- **Step Visualization**: Shows each step with completion status and timestamps
- **Credit Tracking**: Displays estimated vs actual credit usage
- **Error Handling**: Graceful fallbacks with retry functionality
- **Performance**: React Query caching, automatic cleanup, conditional rendering
- **Responsive**: Works seamlessly on desktop and mobile
- **Consolidated Architecture**: Single modal component for all interactions

#### Backend API Enhancement
```javascript
// Enhanced GET /blog-auto-write/:jobId response
{
  "success": true,
  "data": {
    "jobId": "job_abc123",
    "status": "generating_content",
    "progress": 30,
    "steps": [
      {
        "step": "CONTENT_GENERATION",
        "completed": false,
        "startedAt": "2024-12-19T10:00:05Z"
      },
      {
        "step": "SHOPIFY_DRAFT_CREATION",
        "completed": false
      }
    ],
    "estimatedCreditUsage": {
      "totalCredits": 112,
      "content": { "credits": 12 },
      "images": { "credits": 100 }
    },
    "creditUsage": {
      "totalCredits": 5,
      "content": { "credits": 5 }
    }
  }
}
```

## Production Benefits

1. **Reliability** - Jobs can recover from any step failure
2. **Transparency** - Detailed progress tracking for users
3. **Cost Control** - Accurate estimation and actual usage tracking
4. **Performance** - Optimized queries for monitoring and recovery
5. **Maintainability** - Clean virtual fields for consistent API responses

This implementation provides a robust foundation for reliable, cost-effective blog auto-write processing with comprehensive monitoring and recovery capabilities.

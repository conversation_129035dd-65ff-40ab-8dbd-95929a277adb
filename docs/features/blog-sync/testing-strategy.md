# Blog Sync - Testing Strategy

## Overview

Comprehensive testing strategy for the blog sync system covering unit tests, integration tests, performance tests, and end-to-end validation.

## Testing Pyramid

```
                    E2E Tests
                   /           \
              Integration Tests
             /                   \
        Unit Tests (Foundation)
```

### Testing Levels
- **Unit Tests (70%)**: Individual components and functions
- **Integration Tests (20%)**: Component interactions and API integration
- **E2E Tests (10%)**: Complete user workflows

## Unit Testing

### BlogSyncQueue Tests
```javascript
// tests/queue/BlogSyncQueue.test.js
import BlogSyncQueue from '../../api/queue/jobs/BlogSyncQueue';
import ShopifyService from '../../api/services/shopify/OnlineStoreService';
import BlogService from '../../api/services/BlogService';

describe('BlogSyncQueue', () => {
  let queueInstance;
  let mockChannel;
  let mockMessage;

  beforeEach(() => {
    queueInstance = new BlogSyncQueue.__proto__.constructor(
      { queueName: 'test-blog-sync', prefetch: 1 },
      { connection: 'mock' }
    );
    
    mockChannel = { ack: jest.fn(), nack: jest.fn() };
    mockMessage = { content: Buffer.from('test') };
    
    jest.clearAllMocks();
  });

  describe('handle()', () => {
    test('should process blogs successfully', async () => {
      const mockBlogs = [
        { id: 'gid://shopify/Blog/1', title: 'Blog 1', handle: 'blog-1' },
        { id: 'gid://shopify/Blog/2', title: 'Blog 2', handle: 'blog-2' }
      ];

      ShopifyService.onlineStore.getBlogs.mockResolvedValue({
        blogs: mockBlogs,
        pageInfo: { hasNextPage: false }
      });

      BlogService.saveOrUpdateBlog.mockResolvedValue({});
      BlogService.deleteNotSyncedBlogs.mockResolvedValue(0);

      const message = {
        shopId: 1,
        shopDomain: 'test.myshopify.com',
        shopUrl: 'https://test.myshopify.com',
        session: { shop: 'test.myshopify.com', accessToken: 'token' },
        cursor: null
      };

      await queueInstance.handle(mockMessage, mockChannel, () => message);

      expect(ShopifyService.onlineStore.getBlogs).toHaveBeenCalledWith(
        'test.myshopify.com',
        expect.objectContaining({ first: 50, after: null })
      );
      expect(BlogService.saveOrUpdateBlog).toHaveBeenCalledTimes(2);
      expect(mockChannel.ack).toHaveBeenCalledWith(mockMessage);
    });

    test('should handle pagination correctly', async () => {
      const mockBlogs = [{ id: 'gid://shopify/Blog/1', title: 'Blog 1' }];
      
      ShopifyService.onlineStore.getBlogs.mockResolvedValue({
        blogs: mockBlogs,
        pageInfo: { hasNextPage: true, endCursor: 'cursor123' }
      });

      const dispatchQueue = jest.fn();
      queueInstance.dispatchQueue = dispatchQueue;

      const message = {
        shopId: 1,
        shopDomain: 'test.myshopify.com',
        session: { shop: 'test.myshopify.com' },
        cursor: null
      };

      await queueInstance.handle(mockMessage, mockChannel, () => message);

      expect(dispatchQueue).toHaveBeenCalledWith({
        queueName: 'test-blog-sync',
        message: expect.objectContaining({ cursor: 'cursor123' })
      });
    });

    test('should handle individual blog errors gracefully', async () => {
      const mockBlogs = [
        { id: 'gid://shopify/Blog/1', title: 'Blog 1' },
        { id: 'gid://shopify/Blog/2', title: 'Blog 2' }
      ];

      ShopifyService.onlineStore.getBlogs.mockResolvedValue({
        blogs: mockBlogs,
        pageInfo: { hasNextPage: false }
      });

      // First blog succeeds, second fails
      BlogService.saveOrUpdateBlog
        .mockResolvedValueOnce({})
        .mockRejectedValueOnce(new Error('Blog save failed'));

      const message = {
        shopId: 1,
        shopDomain: 'test.myshopify.com',
        session: { shop: 'test.myshopify.com' },
        cursor: null
      };

      await queueInstance.handle(mockMessage, mockChannel, () => message);

      // Should continue processing despite individual failure
      expect(BlogService.saveOrUpdateBlog).toHaveBeenCalledTimes(2);
      expect(mockChannel.ack).toHaveBeenCalledWith(mockMessage);
    });

    test('should handle rate limiting', async () => {
      const cache = require('../../api/cache');
      cache.apiRateLimitExceeded.mockResolvedValue(true);

      const dispatchQueue = jest.fn();
      queueInstance.dispatchQueue = dispatchQueue;

      const message = {
        shopId: 1,
        shopDomain: 'test.myshopify.com',
        session: { shop: 'test.myshopify.com' },
        cursor: null
      };

      await queueInstance.handle(mockMessage, mockChannel, () => message);

      expect(dispatchQueue).toHaveBeenCalledWith({
        queueName: 'test-blog-sync',
        message: message,
        ttl: 30000
      });
      expect(mockChannel.ack).toHaveBeenCalledWith(mockMessage);
    });
  });

  describe('#syncBlogs()', () => {
    test('should return correct pagination info', async () => {
      const mockBlogs = [{ id: 'gid://shopify/Blog/1', title: 'Blog 1' }];
      
      ShopifyService.onlineStore.getBlogs.mockResolvedValue({
        blogs: mockBlogs,
        pageInfo: { hasNextPage: true, endCursor: 'next-cursor' }
      });

      BlogService.saveOrUpdateBlog.mockResolvedValue({});

      const result = await queueInstance.syncBlogs({
        shopId: 1,
        shopDomain: 'test.myshopify.com',
        session: { shop: 'test.myshopify.com' },
        cursor: null
      });

      expect(result).toEqual({
        nextCursor: 'next-cursor',
        blogsProcessed: 1
      });
    });

    test('should track success and failure rates', async () => {
      const mockBlogs = [
        { id: 'gid://shopify/Blog/1', title: 'Blog 1' },
        { id: 'gid://shopify/Blog/2', title: 'Blog 2' },
        { id: 'gid://shopify/Blog/3', title: 'Blog 3' }
      ];

      ShopifyService.onlineStore.getBlogs.mockResolvedValue({
        blogs: mockBlogs,
        pageInfo: { hasNextPage: false }
      });

      // 2 succeed, 1 fails
      BlogService.saveOrUpdateBlog
        .mockResolvedValueOnce({})
        .mockResolvedValueOnce({})
        .mockRejectedValueOnce(new Error('Save failed'));

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      await queueInstance.syncBlogs({
        shopId: 1,
        shopDomain: 'test.myshopify.com',
        session: { shop: 'test.myshopify.com' },
        cursor: null
      });

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Processed 2 blogs, failed 1 (66.7% success)')
      );

      consoleSpy.mockRestore();
    });
  });
});
```

### ShopifyService Tests
```javascript
// tests/services/shopify/OnlineStoreService.test.js
describe('OnlineStoreService - Blog Methods', () => {
  describe('getBlogs()', () => {
    test('should fetch blogs with correct GraphQL query', async () => {
      const mockResponse = {
        blogs: {
          edges: [
            { node: { id: 'gid://shopify/Blog/1', title: 'Blog 1' } }
          ],
          pageInfo: { hasNextPage: false }
        }
      };

      const mockGraphQL = jest.fn().mockResolvedValue(mockResponse);
      ShopifyService.graphql = mockGraphQL;

      const result = await ShopifyService.onlineStore.getBlogs('test.myshopify.com', {
        first: 10,
        after: 'cursor123'
      });

      expect(mockGraphQL).toHaveBeenCalledWith(
        'test.myshopify.com',
        expect.stringContaining('query BlogList'),
        { first: 10, after: 'cursor123', metafieldKeys: [] }
      );

      expect(result.blogs).toHaveLength(1);
      expect(result.pageInfo.hasNextPage).toBe(false);
    });

    test('should handle GraphQL errors', async () => {
      const mockGraphQL = jest.fn().mockRejectedValue(new Error('GraphQL Error'));
      ShopifyService.graphql = mockGraphQL;

      await expect(
        ShopifyService.onlineStore.getBlogs('test.myshopify.com')
      ).rejects.toThrow('GraphQL Error');
    });
  });

  describe('getBlog()', () => {
    test('should fetch single blog by ID', async () => {
      const mockResponse = {
        blog: { id: 'gid://shopify/Blog/1', title: 'Blog 1' }
      };

      const mockGraphQL = jest.fn().mockResolvedValue(mockResponse);
      ShopifyService.graphql = mockGraphQL;

      const result = await ShopifyService.onlineStore.getBlog('test.myshopify.com', {
        id: 'gid://shopify/Blog/1'
      });

      expect(mockGraphQL).toHaveBeenCalledWith(
        'test.myshopify.com',
        expect.stringContaining('query BlogDetails'),
        { id: 'gid://shopify/Blog/1', metafieldKeys: [] }
      );

      expect(result.id).toBe('gid://shopify/Blog/1');
    });
  });
});
```

### BlogService Tests
```javascript
// tests/services/BlogService.test.js
describe('BlogService', () => {
  describe('saveOrUpdateBlog()', () => {
    test('should insert new blog', async () => {
      const mockQuery = jest.fn().mockResolvedValue({ insertId: 1 });
      const db = { query: mockQuery };
      
      const blogData = {
        shop_id: 1,
        blog_id: 123,
        title: 'Test Blog',
        handle: 'test-blog',
        is_synced: true
      };

      await BlogService.saveOrUpdateBlog(blogData);

      expect(mockQuery).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO blogs'),
        expect.arrayContaining([1, 123, 'Test Blog', 'test-blog'])
      );
    });

    test('should update existing blog', async () => {
      const mockQuery = jest.fn().mockResolvedValue({ affectedRows: 1 });
      const db = { query: mockQuery };

      const blogData = {
        shop_id: 1,
        blog_id: 123,
        title: 'Updated Blog',
        handle: 'updated-blog',
        is_synced: true
      };

      await BlogService.saveOrUpdateBlog(blogData);

      expect(mockQuery).toHaveBeenCalledWith(
        expect.stringContaining('ON DUPLICATE KEY UPDATE'),
        expect.arrayContaining([1, 123, 'Updated Blog', 'updated-blog'])
      );
    });
  });

  describe('deleteNotSyncedBlogs()', () => {
    test('should delete unsynced blogs', async () => {
      const mockQuery = jest.fn().mockResolvedValue({ affectedRows: 3 });
      const db = { query: mockQuery };

      const result = await BlogService.deleteNotSyncedBlogs(1);

      expect(mockQuery).toHaveBeenCalledWith(
        'DELETE FROM blogs WHERE shop_id = ? AND is_synced = 0',
        [1]
      );
      expect(result).toBe(3);
    });
  });
});
```

## Integration Testing

### Queue Integration Tests
```javascript
// tests/integration/BlogSyncIntegration.test.js
describe('Blog Sync Integration', () => {
  test('should complete full sync flow', async () => {
    // Setup test shop and mock data
    const testShop = await createTestShop();
    const mockBlogs = await createMockShopifyBlogs();

    // Mock Shopify API
    nock('https://test.myshopify.com')
      .post('/admin/api/2023-10/graphql.json')
      .reply(200, { data: { blogs: mockBlogs } });

    // Dispatch blog sync
    await dispatchQueue({
      queueName: QUEUE_NAMES.BLOG_SYNC,
      message: {
        shopId: testShop.id,
        shopDomain: testShop.domain,
        session: testShop.session,
        cursor: null
      }
    });

    // Wait for processing
    await waitForQueueCompletion();

    // Verify results
    const blogs = await Blog.findAll({ where: { shop_id: testShop.id } });
    expect(blogs).toHaveLength(mockBlogs.length);
    expect(blogs.every(blog => blog.is_synced)).toBe(true);
  });

  test('should handle ArticleSync → BlogSync flow', async () => {
    const testShop = await createTestShop();

    // Dispatch article sync (should trigger blog sync)
    await dispatchQueue({
      queueName: QUEUE_NAMES.ARTICLE_SYNC,
      message: {
        shopId: testShop.id,
        shopDomain: testShop.domain,
        session: testShop.session,
        cursor: null
      }
    });

    // Wait for both queues to complete
    await waitForQueueCompletion();

    // Verify both articles and blogs are synced
    const articles = await Article.findAll({ where: { shop_id: testShop.id } });
    const blogs = await Blog.findAll({ where: { shop_id: testShop.id } });

    expect(articles.length).toBeGreaterThan(0);
    expect(blogs.length).toBeGreaterThan(0);
    expect(blogs.every(blog => blog.is_synced)).toBe(true);
  });
});
```

### API Integration Tests
```javascript
// tests/integration/BlogAPI.test.js
describe('Blog API Integration', () => {
  test('should sync blogs via command line utility', async () => {
    const testShop = await createTestShop();
    
    // Run blog sync utility
    const result = await execAsync(
      `node web/cmd/sync/blogSync.js ${testShop.domain} --dry-run`
    );

    expect(result.stdout).toContain('DRY RUN MODE');
    expect(result.stdout).toContain('Actions that would be performed');
    expect(result.exitCode).toBe(0);
  });

  test('should handle force sync option', async () => {
    const testShop = await createTestShop();
    
    // Set sync as ongoing
    await cache.blogSyncOngoing(testShop.domain, true);

    // Run with force flag
    const result = await execAsync(
      `node web/cmd/sync/blogSync.js ${testShop.domain} --force`
    );

    expect(result.stdout).toContain('Blog sync initiated successfully');
    expect(result.exitCode).toBe(0);
  });
});
```

## Performance Testing

### Load Testing
```javascript
// tests/performance/BlogSyncLoad.test.js
describe('Blog Sync Performance', () => {
  test('should handle large blog counts efficiently', async () => {
    const testShop = await createTestShop();
    const largeBlogSet = await createMockBlogs(1000); // 1000 blogs

    const startTime = Date.now();

    await dispatchQueue({
      queueName: QUEUE_NAMES.BLOG_SYNC,
      message: {
        shopId: testShop.id,
        shopDomain: testShop.domain,
        session: testShop.session,
        cursor: null
      }
    });

    await waitForQueueCompletion();

    const endTime = Date.now();
    const processingTime = endTime - startTime;

    // Should complete within reasonable time (adjust based on requirements)
    expect(processingTime).toBeLessThan(60000); // 1 minute

    // Verify all blogs processed
    const blogs = await Blog.findAll({ where: { shop_id: testShop.id } });
    expect(blogs).toHaveLength(1000);
  });

  test('should handle concurrent shop syncs', async () => {
    const testShops = await Promise.all([
      createTestShop(),
      createTestShop(),
      createTestShop()
    ]);

    const syncPromises = testShops.map(shop =>
      dispatchQueue({
        queueName: QUEUE_NAMES.BLOG_SYNC,
        message: {
          shopId: shop.id,
          shopDomain: shop.domain,
          session: shop.session,
          cursor: null
        }
      })
    );

    const startTime = Date.now();
    await Promise.all(syncPromises);
    await waitForQueueCompletion();
    const endTime = Date.now();

    // Should handle concurrent syncs efficiently
    expect(endTime - startTime).toBeLessThan(120000); // 2 minutes

    // Verify all shops synced correctly
    for (const shop of testShops) {
      const blogs = await Blog.findAll({ where: { shop_id: shop.id } });
      expect(blogs.length).toBeGreaterThan(0);
    }
  });
});
```

### Memory Testing
```javascript
// tests/performance/MemoryUsage.test.js
describe('Memory Usage', () => {
  test('should not leak memory during large syncs', async () => {
    const initialMemory = process.memoryUsage();
    
    // Process multiple large syncs
    for (let i = 0; i < 10; i++) {
      const testShop = await createTestShop();
      await createMockBlogs(500);
      
      await dispatchQueue({
        queueName: QUEUE_NAMES.BLOG_SYNC,
        message: {
          shopId: testShop.id,
          shopDomain: testShop.domain,
          session: testShop.session,
          cursor: null
        }
      });
      
      await waitForQueueCompletion();
      
      // Force garbage collection
      if (global.gc) global.gc();
    }

    const finalMemory = process.memoryUsage();
    const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;

    // Memory increase should be reasonable (adjust threshold as needed)
    expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024); // 100MB
  });
});
```

## End-to-End Testing

### User Workflow Tests
```javascript
// tests/e2e/BlogSyncWorkflow.test.js
describe('Blog Sync E2E Workflow', () => {
  test('should complete full user sync workflow', async () => {
    // Setup test environment
    const testShop = await createTestShop();
    await loginAsShopOwner(testShop);

    // Navigate to sync page
    await page.goto('/admin/sync');

    // Click sync button
    await page.click('[data-testid="sync-button"]');

    // Wait for sync to complete
    await page.waitForSelector('[data-testid="sync-complete"]', {
      timeout: 60000
    });

    // Verify success message
    const successMessage = await page.textContent('[data-testid="sync-status"]');
    expect(successMessage).toContain('Blog posts sync complete!');

    // Navigate to auto-write page
    await page.goto('/admin/auto-write');

    // Verify blog selection is available
    const blogSelector = await page.locator('[data-testid="blog-selector"]');
    await expect(blogSelector).toBeVisible();

    // Verify blogs are populated
    const blogOptions = await page.locator('[data-testid="blog-option"]').count();
    expect(blogOptions).toBeGreaterThan(0);
  });

  test('should handle sync errors gracefully', async () => {
    // Setup test with API errors
    await mockShopifyAPIError();

    const testShop = await createTestShop();
    await loginAsShopOwner(testShop);

    await page.goto('/admin/sync');
    await page.click('[data-testid="sync-button"]');

    // Should show error message
    await page.waitForSelector('[data-testid="sync-error"]');
    const errorMessage = await page.textContent('[data-testid="sync-error"]');
    expect(errorMessage).toContain('Sync failed');

    // Retry button should be available
    const retryButton = await page.locator('[data-testid="retry-sync"]');
    await expect(retryButton).toBeVisible();
  });
});
```

## Test Data Management

### Test Fixtures
```javascript
// tests/fixtures/blogFixtures.js
export const createMockShopifyBlogs = (count = 5) => {
  return Array.from({ length: count }, (_, i) => ({
    id: `gid://shopify/Blog/${i + 1}`,
    title: `Test Blog ${i + 1}`,
    handle: `test-blog-${i + 1}`,
    tags: [`tag${i}`, `category${i}`],
    templateSuffix: i % 2 === 0 ? 'custom' : null,
    metafields: {
      edges: []
    }
  }));
};

export const createTestShop = async () => {
  return await Shop.create({
    domain: `test-${Date.now()}.myshopify.com`,
    access_token: 'test-token',
    name: 'Test Shop',
    url: `https://test-${Date.now()}.myshopify.com`
  });
};
```

### Database Cleanup
```javascript
// tests/helpers/testCleanup.js
export const cleanupTestData = async () => {
  // Clean up test blogs
  await Blog.destroy({
    where: {
      shop_id: {
        [Op.in]: await Shop.findAll({
          where: { domain: { [Op.like]: 'test-%' } },
          attributes: ['id']
        }).then(shops => shops.map(s => s.id))
      }
    }
  });

  // Clean up test shops
  await Shop.destroy({
    where: { domain: { [Op.like]: 'test-%' } }
  });
};

// Run cleanup after each test
afterEach(async () => {
  await cleanupTestData();
});
```

## Continuous Integration

### Test Pipeline
```yaml
# .github/workflows/blog-sync-tests.yml
name: Blog Sync Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: test
          MYSQL_DATABASE: storeseo_test
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run unit tests
        run: npm run test:unit -- --testPathPattern=blog-sync
        
      - name: Run integration tests
        run: npm run test:integration -- --testPathPattern=blog-sync
        
      - name: Run performance tests
        run: npm run test:performance -- --testPathPattern=blog-sync
        
      - name: Generate coverage report
        run: npm run test:coverage -- --testPathPattern=blog-sync
        
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v1
```

## Test Coverage Goals

### Coverage Targets
- **Unit Tests**: 90%+ line coverage
- **Integration Tests**: 80%+ feature coverage
- **E2E Tests**: 100% critical path coverage

### Coverage Monitoring
```javascript
// jest.config.js
module.exports = {
  collectCoverageFrom: [
    'web/api/queue/jobs/BlogSyncQueue.js',
    'web/api/services/shopify/OnlineStoreService.js',
    'web/api/services/BlogService.js',
    'web/cmd/sync/blogSync.js'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 90,
      lines: 90,
      statements: 90
    }
  }
};
```

---

**Testing Status**: 📋 **PLANNED**  
**Coverage Target**: 90%+ for critical components  
**Automation**: Full CI/CD integration planned

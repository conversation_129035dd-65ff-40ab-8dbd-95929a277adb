# Blog Sync - API Reference

## Overview

This document provides comprehensive API reference for the blog sync system, including service methods, GraphQL queries, and queue interfaces.

## Service Layer APIs

### ShopifyService.onlineStore

#### getArticles()
Fetches articles from Shopify with pagination support.

```javascript
async getArticles(shop, options = {})
```

**Parameters:**
- `shop` (string): Shop domain (e.g., "shop.myshopify.com")
- `options` (object):
  - `first` (number, default: 5): Number of articles to fetch
  - `after` (string, optional): Pagination cursor
  - `metafieldKeys` (array, default: []): Metafield keys to include

**Returns:**
```javascript
{
  articles: [
    {
      id: "gid://shopify/Article/123456789",
      title: "How to Use Our Product",
      handle: "how-to-use-our-product",
      content: "<p>Article content here...</p>",
      summary: "Brief article summary",
      author: "<PERSON>",
      tags: ["tutorial", "product"],
      publishedAt: "2023-12-01T10:00:00Z",
      createdAt: "2023-11-30T15:30:00Z",
      updatedAt: "2023-12-01T09:45:00Z",
      status: "PUBLISHED",
      blog: {
        id: "gid://shopify/Blog/987654321",
        title: "News Blog",
        handle: "news"
      },
      metafields: [/* metafield objects */]
    }
  ],
  pageInfo: {
    hasNextPage: boolean,
    hasPreviousPage: boolean,
    startCursor: string,
    endCursor: string
  }
}
```

**Example:**
```javascript
const { articles, pageInfo } = await ShopifyService.onlineStore.getArticles("shop.myshopify.com", {
  first: 50,
  after: "eyJsYXN0X2lkIjo...",
  metafieldKeys: ["seo.title", "seo.description"]
});
```

#### getBlogs()
Fetches blogs from Shopify with pagination support.

```javascript
async getBlogs(shop, options = {})
```

**Parameters:**
- `shop` (string): Shop domain (e.g., "shop.myshopify.com")
- `options` (object):
  - `first` (number, default: 5): Number of blogs to fetch
  - `after` (string, optional): Pagination cursor
  - `metafieldKeys` (array, default: []): Metafield keys to include

**Returns:**
```javascript
{
  blogs: [
    {
      id: "gid://shopify/Blog/123456789",
      title: "News Blog",
      handle: "news",
      tags: ["updates", "announcements"],
      templateSuffix: null,
      metafields: [/* metafield objects */]
    }
  ],
  pageInfo: {
    hasNextPage: boolean,
    hasPreviousPage: boolean,
    startCursor: string,
    endCursor: string
  }
}
```

**Example:**
```javascript
const { blogs, pageInfo } = await ShopifyService.onlineStore.getBlogs("shop.myshopify.com", {
  first: 50,
  after: "eyJsYXN0X2lkIjo...",
  metafieldKeys: ["seo.title", "seo.description"]
});
```

#### getBlog()
Fetches a single blog by ID.

```javascript
async getBlog(shop, options = {})
```

**Parameters:**
- `shop` (string): Shop domain
- `options` (object):
  - `id` (string, required): Blog GraphQL ID
  - `metafieldKeys` (array, default: []): Metafield keys to include

**Returns:**
```javascript
{
  id: "gid://shopify/Blog/123456789",
  title: "News Blog",
  handle: "news",
  tags: ["updates", "announcements"],
  templateSuffix: null,
  metafields: [/* metafield objects */]
}
```

**Example:**
```javascript
const blog = await ShopifyService.onlineStore.getBlog("shop.myshopify.com", {
  id: "gid://shopify/Blog/123456789",
  metafieldKeys: ["seo.title"]
});
```

### ArticleService

#### markArticlesAsNotSynced()
Marks all articles for a shop as not synced (preparation for sync).

```javascript
async markArticlesAsNotSynced(shopId)
```

**Parameters:**
- `shopId` (number): Database shop ID

**Returns:** Promise<void>

**Example:**
```javascript
await ArticleService.markArticlesAsNotSynced(123);
```

#### saveOrUpdateArticle()
Saves or updates an article record using upsert pattern.

```javascript
async saveOrUpdateArticle(articleData)
```

**Parameters:**
- `articleData` (object):
  - `shop_id` (number): Database shop ID
  - `blog_id` (number): Database blog ID
  - `article_id` (number): Shopify article ID
  - `title` (string): Article title
  - `handle` (string): URL handle
  - `content` (string): Article content (HTML)
  - `summary` (string): Article summary
  - `author` (string): Article author
  - `tags` (string): Comma-separated tags
  - `published_at` (Date): Publication date
  - `status` (string): Publication status
  - `is_synced` (boolean): Sync status

**Returns:** Promise<object> - Database result

**Example:**
```javascript
const articleData = {
  shop_id: 123,
  blog_id: 456,
  article_id: 987654321,
  title: "How to Use Our Product",
  handle: "how-to-use-our-product",
  content: "<p>Article content...</p>",
  summary: "Brief summary",
  author: "John Doe",
  tags: "tutorial,product",
  published_at: new Date("2023-12-01T10:00:00Z"),
  status: "published",
  is_synced: true
};

await ArticleService.saveOrUpdateArticle(articleData);
```

#### deleteNotSyncedArticles()
Removes articles that weren't found during sync (cleanup phase).

```javascript
async deleteNotSyncedArticles(shopId)
```

**Parameters:**
- `shopId` (number): Database shop ID

**Returns:** Promise<number> - Number of deleted records

**Example:**
```javascript
const deletedCount = await ArticleService.deleteNotSyncedArticles(123);
console.log(`Cleaned up ${deletedCount} old articles`);
```

#### count()
Returns the total number of articles for a shop.

```javascript
async count(shopId)
```

**Parameters:**
- `shopId` (number): Database shop ID

**Returns:** Promise<number> - Total article count

**Example:**
```javascript
const totalArticles = await ArticleService.count(123);
console.log(`Shop has ${totalArticles} articles`);
```

### BlogService

#### markBlogsAsNotSynced()
Marks all blogs for a shop as not synced (preparation for sync).

```javascript
async markBlogsAsNotSynced(shopId)
```

**Parameters:**
- `shopId` (number): Database shop ID

**Returns:** Promise<void>

**Example:**
```javascript
await BlogService.markBlogsAsNotSynced(123);
```

#### saveOrUpdateBlog()
Saves or updates a blog record using upsert pattern.

```javascript
async saveOrUpdateBlog(blogData)
```

**Parameters:**
- `blogData` (object):
  - `shop_id` (number): Database shop ID
  - `blog_id` (number): Shopify blog ID
  - `title` (string): Blog title
  - `handle` (string): URL handle
  - `template_suffix` (string): Theme template
  - `tags` (string): Comma-separated tags
  - `is_synced` (boolean): Sync status

**Returns:** Promise<object> - Database result

**Example:**
```javascript
const blogData = {
  shop_id: 123,
  blog_id: 987654321,
  title: "News Blog",
  handle: "news",
  template_suffix: null,
  tags: "updates,announcements",
  is_synced: true
};

await BlogService.saveOrUpdateBlog(blogData);
```

#### deleteNotSyncedBlogs()
Removes blogs that weren't found during sync (cleanup phase).

```javascript
async deleteNotSyncedBlogs(shopId)
```

**Parameters:**
- `shopId` (number): Database shop ID

**Returns:** Promise<number> - Number of deleted records

**Example:**
```javascript
const deletedCount = await BlogService.deleteNotSyncedBlogs(123);
console.log(`Cleaned up ${deletedCount} old blogs`);
```

### ArticleSerializer

#### serializeShopifyArticleData()
Converts Shopify article data to database format.

```javascript
static serializeShopifyArticleData(shopId, shopifyArticle)
```

**Parameters:**
- `shopId` (number): Database shop ID
- `shopifyArticle` (object): Raw Shopify article data

**Returns:**
```javascript
{
  shop_id: number,
  blog_id: number,
  article_id: number,
  title: string,
  handle: string,
  content: string,
  summary: string,
  author: string,
  tags: string,
  published_at: Date,
  created_at_shopify: Date,
  updated_at_shopify: Date,
  status: string,
  is_synced: boolean
}
```

**Example:**
```javascript
const serializedData = ArticleSerializer.serializeShopifyArticleData(123, {
  id: "gid://shopify/Article/987654321",
  title: "How to Use Our Product",
  handle: "how-to-use-our-product",
  content: "<p>Article content...</p>",
  summary: "Brief summary",
  author: "John Doe",
  tags: ["tutorial", "product"],
  publishedAt: "2023-12-01T10:00:00Z",
  createdAt: "2023-11-30T15:30:00Z",
  updatedAt: "2023-12-01T09:45:00Z",
  status: "PUBLISHED",
  blog: {
    id: "gid://shopify/Blog/456"
  }
});
```

### BlogSerializer

#### serializeShopifyBlogData()
Converts Shopify blog data to database format.

```javascript
static serializeShopifyBlogData(shopId, shopifyBlog)
```

**Parameters:**
- `shopId` (number): Database shop ID
- `shopifyBlog` (object): Raw Shopify blog data

**Returns:**
```javascript
{
  shop_id: number,
  blog_id: number,
  title: string,
  handle: string,
  template_suffix: string,
  tags: string,
  is_synced: boolean
}
```

**Example:**
```javascript
const serializedData = BlogSerializer.serializeShopifyBlogData(123, {
  id: "gid://shopify/Blog/987654321",
  title: "News Blog",
  handle: "news",
  tags: ["updates", "announcements"],
  templateSuffix: null
});
```

## GraphQL Queries

### Article Fragment
**File:** `web/api/queries/onlineStore/fragment.article.gql`

```graphql
fragment ArticleNode on Article {
  id
  title
  handle
  content
  summary
  author
  tags
  publishedAt
  createdAt
  updatedAt
  status
  blog {
    id
    title
    handle
  }
  metafields(first: 50, keys: $metafieldKeys) {
    edges {
      node {
        ...MetafieldNode
      }
    }
  }
}
```

### Articles List Query
**File:** `web/api/queries/onlineStore/query.articles.gql`

```graphql
query ArticleList($first: Int, $after: String, $metafieldKeys: [String!]) {
  articles(first: $first, after: $after) {
    edges {
      node {
        ...ArticleNode
      }
    }
    pageInfo {
      ...PageInfoNode
    }
  }
}
```

**Variables:**
```javascript
{
  "first": 50,
  "after": "eyJsYXN0X2lkIjo...",
  "metafieldKeys": ["seo.title", "seo.description"]
}
```

### Single Article Query
**File:** `web/api/queries/onlineStore/query.article.gql`

```graphql
query ArticleDetails($id: ID!, $metafieldKeys: [String!]) {
  article(id: $id) {
    ...ArticleNode
  }
}
```

**Variables:**
```javascript
{
  "id": "gid://shopify/Article/123456789",
  "metafieldKeys": ["seo.title"]
}
```

### Blog Fragment
**File:** `web/api/queries/onlineStore/fragment.blog.gql`

```graphql
fragment BlogNode on Blog {
  id
  title
  handle
  tags
  templateSuffix
  metafields(first: 50, keys: $metafieldKeys) {
    edges {
      node {
        ...MetafieldNode
      }
    }
  }
}
```

### Blogs List Query
**File:** `web/api/queries/onlineStore/query.blogs.gql`

```graphql
query BlogList($first: Int, $after: String, $metafieldKeys: [String!]) {
  blogs(first: $first, after: $after) {
    edges {
      node {
        ...BlogNode
      }
    }
    pageInfo {
      ...PageInfoNode
    }
  }
}
```

**Variables:**
```javascript
{
  "first": 50,
  "after": "eyJsYXN0X2lkIjo...",
  "metafieldKeys": ["seo.title", "seo.description"]
}
```

### Single Blog Query
**File:** `web/api/queries/onlineStore/query.blog.gql`

```graphql
query BlogDetails($id: ID!, $metafieldKeys: [String!]) {
  blog(id: $id) {
    ...BlogNode
  }
}
```

**Variables:**
```javascript
{
  "id": "gid://shopify/Blog/123456789",
  "metafieldKeys": ["seo.title"]
}
```

## Queue APIs

### BlogSyncQueue

#### Message Format
```javascript
{
  shopId: number,           // Database shop ID
  shopDomain: string,       // Shop domain
  shopUrl: string,          // Full shop URL
  session: {                // Shopify session
    shop: string,
    accessToken: string,
    url: string,
    shopId: number
  },
  cursor: string | null     // Pagination cursor
}
```

#### Dispatch Blog Sync
```javascript
const { dispatchQueue } = require("../api/queue/queueDispatcher");
const { QUEUE_NAMES } = require("../api/queue/config");

dispatchQueue({
  queueName: QUEUE_NAMES.BLOG_SYNC,
  message: {
    shopId: 123,
    shopDomain: "shop.myshopify.com",
    shopUrl: "https://shop.myshopify.com",
    session: {
      shop: "shop.myshopify.com",
      accessToken: "shpat_...",
      url: "https://shop.myshopify.com",
      shopId: 123
    },
    cursor: null
  }
});
```

### ArticleSyncQueue Integration

#### Enhanced Message Handling
The ArticleSyncQueue now automatically dispatches BlogSyncQueue after completion:

```javascript
// After article sync completion
await cache.blogSyncOngoing(shopDomain, false);

dispatchQueue({
  queueName: QUEUE_NAMES.BLOG_SYNC,
  message: { shopId, shopDomain, shopUrl, session, cursor: null }
});
```

## Command Line APIs

### Blog Sync Utility
**File:** `web/cmd/sync/blogSync.js`

#### Basic Usage
```bash
node web/cmd/sync/blogSync.js <shopDomain> [options]
```

#### Options
- `--dry-run`: Show what would be synced without executing
- `--force`: Force sync even if already in progress
- `--verbose`: Show detailed logging

#### Examples
```bash
# Check current blog status
node web/cmd/sync/blogSync.js client-shop.myshopify.com --dry-run

# Run blog sync
node web/cmd/sync/blogSync.js client-shop.myshopify.com

# Force retry stuck sync
node web/cmd/sync/blogSync.js client-shop.myshopify.com --force

# Debug with verbose logging
node web/cmd/sync/blogSync.js client-shop.myshopify.com --verbose
```

### Article Sync Utility
**File:** `web/cmd/sync/articleSync.js`

#### Usage
```bash
node web/cmd/sync/articleSync.js <shopDomain>
```

**Note:** This triggers the full sync flow (Articles → Blogs)

## Cache APIs

### Blog Sync State Management
```javascript
const cache = require("../api/cache");

// Set sync ongoing flag
await cache.blogSyncOngoing(shopDomain, true);

// Clear sync ongoing flag
await cache.blogSyncOngoing(shopDomain, false);

// Check sync status
const isOngoing = await cache.blogSyncOngoing(shopDomain);
```

### Rate Limiting
```javascript
// Check API rate limits
const isLimited = await cache.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API");

if (isLimited) {
  // Handle rate limiting
  console.log("Rate limit exceeded, throttling...");
}
```

## Event APIs

### Completion Events
```javascript
const EventService = require("../api/services/EventService");

// Trigger blog sync completion event
await EventService.handleBlogArticleSyncComplete({
  shop: shopDomain,
  total: articleCount
});
```

## Error Handling APIs

### Error Types
```javascript
// Rate limiting error
class RateLimitError extends Error {
  constructor(message) {
    super(message);
    this.name = 'RateLimitError';
    this.retryable = true;
  }
}

// Blog processing error
class BlogProcessingError extends Error {
  constructor(message, blogId) {
    super(message);
    this.name = 'BlogProcessingError';
    this.blogId = blogId;
    this.retryable = false;
  }
}
```

### Error Handling Patterns
```javascript
try {
  await BlogService.saveOrUpdateBlog(blogData);
} catch (error) {
  if (error.name === 'RateLimitError') {
    // Re-dispatch with delay
    dispatchQueue({ queueName, message, ttl: 30000 });
  } else {
    // Log and continue
    logger.error(error, { blogId, shopDomain });
  }
}
```

## Database Query APIs

### Direct Database Access
```javascript
const { Blog } = require("../sequelize");

// Find all synced blogs for a shop
const blogs = await Blog.findAll({
  where: { 
    shop_id: shopId, 
    is_synced: true 
  },
  order: [['title', 'ASC']]
});

// Count blogs by sync status
const syncedCount = await Blog.count({
  where: { 
    shop_id: shopId, 
    is_synced: true 
  }
});
```

## Response Formats

### Success Response
```javascript
{
  success: true,
  data: {
    blogsProcessed: 25,
    blogsFailed: 2,
    successRate: 92.6,
    processingTime: 15.3,
    nextCursor: "eyJsYXN0X2lkIjo..." || null
  }
}
```

### Error Response
```javascript
{
  success: false,
  error: {
    type: "BlogSyncError",
    message: "Failed to sync blogs",
    details: {
      shopDomain: "shop.myshopify.com",
      cursor: "eyJsYXN0X2lkIjo...",
      retryable: true
    }
  }
}
```

---

**API Status**: ✅ **STABLE**  
**Version**: v2.1.0 Foundation  
**Compatibility**: Backward compatible with existing APIs

# Blog Sync - Frontend Development Plan

## Overview

This document outlines the frontend development plan for v2.1.0 blog sync features, focusing on blog selection UI and AI-generated content management.

## Current State Analysis

### Existing Frontend Components
- **Blog Auto-Write Form**: Basic form for generating AI content
- **Article Listing**: Shows generated articles
- **Sync Button**: Triggers article/blog sync
- **Notification System**: Shows sync completion messages

### Current Limitations
- ❌ No blog selection in auto-write form
- ❌ No dedicated AI-generated blogs page
- ❌ Limited blog metadata display
- ❌ No blog filtering or search capabilities

## v2.1.0 Frontend Goals

### Primary Objectives
1. **Blog Selection UI**: Allow users to choose target blog for auto-write
2. **AI-Generated Blogs Page**: Dedicated page for AI-generated content
3. **Enhanced Blog Management**: Better blog visibility and control
4. **Improved User Experience**: Intuitive blog selection and management

### Success Metrics
- Users can select any blog (including empty ones) for auto-write
- Reduced support tickets related to blog selection
- Increased usage of auto-write feature across different blogs
- Improved user satisfaction with blog management

## Component Architecture

### New Components

#### 1. BlogSelector Component
```jsx
// components/BlogSelector/BlogSelector.jsx
import { Select, Spinner, Text } from '@shopify/polaris';

const BlogSelector = ({ 
  selectedBlog, 
  onBlogChange, 
  loading = false,
  error = null 
}) => {
  const { blogs, loading: blogsLoading } = useBlogList();
  
  const options = blogs.map(blog => ({
    label: blog.title,
    value: blog.id,
    disabled: !blog.is_synced
  }));

  return (
    <Select
      label="Target Blog"
      options={options}
      value={selectedBlog}
      onChange={onBlogChange}
      loading={blogsLoading || loading}
      error={error}
      helpText="Choose which blog to publish the article to"
    />
  );
};
```

#### 2. BlogList Component
```jsx
// components/BlogList/BlogList.jsx
import { ResourceList, Card, Badge, Text } from '@shopify/polaris';

const BlogList = ({ blogs, onBlogSelect, selectedBlog }) => {
  const renderBlogItem = (blog) => (
    <ResourceList.Item
      id={blog.id}
      onClick={() => onBlogSelect(blog)}
      selected={selectedBlog?.id === blog.id}
    >
      <div className="blog-item">
        <Text variant="headingMd">{blog.title}</Text>
        <Text variant="bodyMd" color="subdued">{blog.handle}</Text>
        <div className="blog-meta">
          <Badge status={blog.is_synced ? 'success' : 'attention'}>
            {blog.is_synced ? 'Synced' : 'Not Synced'}
          </Badge>
          {blog.article_count > 0 && (
            <Badge>{blog.article_count} articles</Badge>
          )}
        </div>
      </div>
    </ResourceList.Item>
  );

  return (
    <Card>
      <ResourceList
        items={blogs}
        renderItem={renderBlogItem}
        loading={loading}
        emptyState={<EmptyBlogState />}
      />
    </Card>
  );
};
```

#### 3. AIGeneratedBlogsPage Component
```jsx
// pages/AIGeneratedBlogsPage/AIGeneratedBlogsPage.jsx
import { Page, Layout, Card, Filters } from '@shopify/polaris';

const AIGeneratedBlogsPage = () => {
  const [filters, setFilters] = useState({
    blog: '',
    status: '',
    dateRange: ''
  });

  return (
    <Page
      title="AI-Generated Content"
      subtitle="Manage your AI-generated blog articles"
      primaryAction={{
        content: 'Generate New Article',
        onAction: () => navigate('/auto-write')
      }}
    >
      <Layout>
        <Layout.Section>
          <Card>
            <Filters
              filters={filters}
              onFiltersChange={setFilters}
              appliedFilters={appliedFilters}
              onClearAll={clearAllFilters}
            >
              <BlogFilter />
              <StatusFilter />
              <DateRangeFilter />
            </Filters>
          </Card>
        </Layout.Section>
        
        <Layout.Section>
          <AIGeneratedArticlesList filters={filters} />
        </Layout.Section>
      </Layout>
    </Page>
  );
};
```

### Enhanced Components

#### 1. Enhanced Auto-Write Form
```jsx
// components/AutoWriteForm/AutoWriteForm.jsx (Enhanced)
const AutoWriteForm = () => {
  const [formData, setFormData] = useState({
    title: '',
    keywords: '',
    targetBlog: '', // NEW: Blog selection
    tone: 'professional',
    length: 'medium'
  });

  return (
    <Form onSubmit={handleSubmit}>
      <FormLayout>
        <TextField
          label="Article Title"
          value={formData.title}
          onChange={(value) => setFormData({...formData, title: value})}
        />
        
        <TextField
          label="Focus Keywords"
          value={formData.keywords}
          onChange={(value) => setFormData({...formData, keywords: value})}
        />
        
        {/* NEW: Blog Selection */}
        <BlogSelector
          selectedBlog={formData.targetBlog}
          onBlogChange={(value) => setFormData({...formData, targetBlog: value})}
        />
        
        <Select
          label="Writing Tone"
          options={toneOptions}
          value={formData.tone}
          onChange={(value) => setFormData({...formData, tone: value})}
        />
        
        <Button primary submit loading={loading}>
          Generate Article
        </Button>
      </FormLayout>
    </Form>
  );
};
```

## Data Layer

### API Integration

#### 1. Blog List Hook
```javascript
// hooks/useBlogList.js
import { useQuery } from '@tanstack/react-query';

export const useBlogList = (options = {}) => {
  return useQuery({
    queryKey: ['blogs', options],
    queryFn: async () => {
      const response = await fetch('/api/blogs', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch blogs');
      }
      
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};
```

#### 2. Blog Sync Hook
```javascript
// hooks/useBlogSync.js
import { useMutation, useQueryClient } from '@tanstack/react-query';

export const useBlogSync = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (shopDomain) => {
      const response = await fetch('/api/sync/blogs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ shopDomain })
      });
      
      if (!response.ok) {
        throw new Error('Blog sync failed');
      }
      
      return response.json();
    },
    onSuccess: () => {
      // Invalidate blog list to refresh data
      queryClient.invalidateQueries(['blogs']);
      
      // Show success notification
      toast.success('Blog sync completed successfully');
    },
    onError: (error) => {
      toast.error(`Blog sync failed: ${error.message}`);
    }
  });
};
```

### State Management

#### 1. Blog Selection State
```javascript
// store/blogSlice.js
import { createSlice } from '@reduxjs/toolkit';

const blogSlice = createSlice({
  name: 'blogs',
  initialState: {
    selectedBlog: null,
    blogList: [],
    loading: false,
    error: null,
    syncStatus: 'idle'
  },
  reducers: {
    setSelectedBlog: (state, action) => {
      state.selectedBlog = action.payload;
    },
    setBlogList: (state, action) => {
      state.blogList = action.payload;
    },
    setSyncStatus: (state, action) => {
      state.syncStatus = action.payload;
    }
  }
});

export const { setSelectedBlog, setBlogList, setSyncStatus } = blogSlice.actions;
export default blogSlice.reducer;
```

## User Experience Flow

### Blog Selection Flow
```
1. User opens Auto-Write form
   ↓
2. BlogSelector component loads available blogs
   ↓
3. User sees dropdown with all synced blogs
   ↓
4. User selects target blog
   ↓
5. Form validation ensures blog is selected
   ↓
6. Article generation includes blog selection
   ↓
7. Generated article is published to selected blog
```

### Blog Management Flow
```
1. User navigates to AI-Generated Blogs page
   ↓
2. Page loads all AI-generated articles grouped by blog
   ↓
3. User can filter by blog, status, date range
   ↓
4. User can view article details and performance
   ↓
5. User can regenerate or edit articles
   ↓
6. User can bulk manage articles across blogs
```

## Responsive Design

### Mobile Considerations
```scss
// styles/BlogSelector.scss
.blog-selector {
  @media (max-width: 768px) {
    .blog-dropdown {
      width: 100%;
      font-size: 16px; // Prevent zoom on iOS
    }
    
    .blog-meta {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }
  }
}
```

### Accessibility
```jsx
// Accessible blog selection
<Select
  label="Target Blog"
  options={options}
  value={selectedBlog}
  onChange={onBlogChange}
  aria-describedby="blog-help-text"
  aria-required="true"
/>
<Text id="blog-help-text" variant="bodyMd" color="subdued">
  Choose which blog to publish the article to. Only synced blogs are available.
</Text>
```

## Performance Optimization

### Code Splitting
```javascript
// Lazy load AI-Generated Blogs page
const AIGeneratedBlogsPage = lazy(() => 
  import('../pages/AIGeneratedBlogsPage/AIGeneratedBlogsPage')
);

// Route configuration
<Route 
  path="/ai-generated-blogs" 
  element={
    <Suspense fallback={<PageSkeleton />}>
      <AIGeneratedBlogsPage />
    </Suspense>
  } 
/>
```

### Data Caching
```javascript
// React Query configuration for blogs
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      refetchOnWindowFocus: false,
      retry: 2
    }
  }
});
```

## Testing Strategy

### Component Testing
```javascript
// BlogSelector.test.jsx
import { render, screen, fireEvent } from '@testing-library/react';
import { BlogSelector } from './BlogSelector';

describe('BlogSelector', () => {
  const mockBlogs = [
    { id: 1, title: 'News Blog', is_synced: true },
    { id: 2, title: 'Updates', is_synced: false }
  ];

  test('renders blog options correctly', () => {
    render(
      <BlogSelector 
        blogs={mockBlogs}
        selectedBlog=""
        onBlogChange={jest.fn()}
      />
    );
    
    expect(screen.getByText('News Blog')).toBeInTheDocument();
    expect(screen.getByText('Updates')).toBeInTheDocument();
  });

  test('disables unsynced blogs', () => {
    render(<BlogSelector blogs={mockBlogs} />);
    
    const updatesOption = screen.getByText('Updates').closest('option');
    expect(updatesOption).toBeDisabled();
  });
});
```

### Integration Testing
```javascript
// AutoWriteForm.integration.test.jsx
describe('Auto-Write Form Integration', () => {
  test('submits form with selected blog', async () => {
    const mockSubmit = jest.fn();
    
    render(<AutoWriteForm onSubmit={mockSubmit} />);
    
    // Fill form
    fireEvent.change(screen.getByLabelText('Article Title'), {
      target: { value: 'Test Article' }
    });
    
    // Select blog
    fireEvent.change(screen.getByLabelText('Target Blog'), {
      target: { value: '1' }
    });
    
    // Submit
    fireEvent.click(screen.getByText('Generate Article'));
    
    await waitFor(() => {
      expect(mockSubmit).toHaveBeenCalledWith({
        title: 'Test Article',
        targetBlog: '1',
        // ... other form data
      });
    });
  });
});
```

## Implementation Timeline

### Phase 1: Core Components (Week 1-2)
- [ ] BlogSelector component
- [ ] Enhanced Auto-Write form
- [ ] Blog list API integration
- [ ] Basic blog selection functionality

### Phase 2: Blog Management (Week 3-4)
- [ ] AI-Generated Blogs page
- [ ] Blog filtering and search
- [ ] Article listing by blog
- [ ] Bulk operations interface

### Phase 3: Polish & Testing (Week 5-6)
- [ ] Responsive design implementation
- [ ] Accessibility improvements
- [ ] Performance optimization
- [ ] Comprehensive testing
- [ ] User acceptance testing

## Migration Strategy

### Backward Compatibility
- Existing auto-write functionality continues to work
- Default blog selection for users without preference
- Gradual rollout of new features

### Data Migration
- No database changes required
- Frontend state migration for existing forms
- User preference storage for blog selection

---

**Frontend Status**: 📋 **PLANNED**  
**Target Release**: v2.1.0  
**Dependencies**: Blog sync backend (✅ Complete)

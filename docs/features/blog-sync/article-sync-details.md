# Article Sync - Detailed Implementation

## Overview

This document provides comprehensive details about the article synchronization system, which forms Phase 1 of the complete blog sync process.

## Article Sync Process

### High-Level Flow
```
User Triggers Sync
    ↓
ArticleSyncQueue Dispatched
    ↓
Preparation Phase
├── Mark all articles as not synced
└── Mark all blogs as not synced
    ↓
Processing Phase (Paginated)
├── Fetch articles from Shopify (50 per batch)
├── For each article:
│   ├── Serialize article data
│   ├── Save/update article in database
│   ├── Extract blog information
│   └── Save/update associated blog
└── Continue until all articles processed
    ↓
Cleanup Phase
├── Delete unsynced articles
├── Clear sync ongoing flag
└── Dispatch BlogSyncQueue
```

### Detailed Implementation

#### 1. Preparation Phase
```javascript
// Mark all existing articles as not synced
await ArticleService.markArticlesAsNotSynced(shopId);
// SQL: UPDATE articles SET is_synced = 0 WHERE shop_id = ?

// Mark all existing blogs as not synced  
await BlogService.markBlogsAsNotSynced(shopId);
// SQL: UPDATE blogs SET is_synced = 0 WHERE shop_id = ?
```

**Purpose**: Prepare for cleanup phase by marking existing records for potential deletion.

#### 2. Processing Phase
```javascript
let cursor = message.cursor || null;
let totalArticlesProcessed = 0;

do {
  // Fetch articles from Shopify (paginated)
  const { articles, pageInfo } = await ShopifyService.onlineStore.getArticles(session.shop, {
    first: 50,
    after: cursor,
    metafieldKeys: metafieldKeysFilterArray
  });

  // Process each article in the batch
  for (let shopifyArticle of articles) {
    try {
      // 1. Serialize article data
      const articleData = ArticleSerializer.serializeShopifyArticleData(shopId, shopifyArticle);
      
      // 2. Save/update article
      await ArticleService.saveOrUpdateArticle(articleData);
      
      // 3. Process associated blog
      if (shopifyArticle.blog) {
        const blogData = BlogSerializer.serializeShopifyBlogData(shopId, shopifyArticle.blog);
        await BlogService.saveOrUpdateBlog(blogData);
      }
      
      totalArticlesProcessed++;
    } catch (articleError) {
      // Individual article errors don't stop the batch
      logger.error(articleError, { 
        articleId: shopifyArticle.id, 
        shopDomain 
      });
      continue;
    }
  }

  // Handle pagination
  cursor = pageInfo?.hasNextPage ? pageInfo.endCursor : null;
  
  if (cursor) {
    // Dispatch next batch
    dispatchQueue({
      queueName: QUEUE_NAMES.ARTICLE_SYNC,
      message: { ...originalMessage, cursor }
    });
    return; // Exit current handler
  }
  
} while (cursor);
```

#### 3. Cleanup Phase
```javascript
// Remove articles that weren't found in Shopify
const deletedArticles = await ArticleService.deleteNotSyncedArticles(shopId);
console.log(`Cleaned up ${deletedArticles} old articles`);

// Clear sync flag immediately (allows user retry)
await cache.blogSyncOngoing(shopDomain, false);

// Dispatch blog sync for complete coverage
dispatchQueue({
  queueName: QUEUE_NAMES.BLOG_SYNC,
  message: { shopId, shopDomain, shopUrl, session, cursor: null }
});
```

## Article Data Processing

### Shopify Article Structure
```javascript
// Raw Shopify article data
{
  id: "gid://shopify/Article/123456789",
  title: "How to Use Our Product",
  handle: "how-to-use-our-product",
  content: "<p>Detailed article content with HTML formatting...</p>",
  summary: "Brief article summary for previews",
  author: "John Doe",
  tags: ["tutorial", "product", "how-to"],
  publishedAt: "2023-12-01T10:00:00Z",
  createdAt: "2023-11-30T15:30:00Z",
  updatedAt: "2023-12-01T09:45:00Z",
  status: "PUBLISHED",
  blog: {
    id: "gid://shopify/Blog/987654321",
    title: "News Blog",
    handle: "news"
  },
  metafields: {
    edges: [
      {
        node: {
          key: "seo_title",
          value: "Custom SEO Title",
          namespace: "seo"
        }
      }
    ]
  }
}
```

### Article Serialization
```javascript
// ArticleSerializer.serializeShopifyArticleData()
static serializeShopifyArticleData(shopId, shopifyArticle) {
  return {
    shop_id: parseInt(shopId),
    blog_id: shopifyArticle.blog ? 
      parseInt(shopifyArticle.blog.id.replace('gid://shopify/Blog/', '')) : null,
    article_id: parseInt(shopifyArticle.id.replace('gid://shopify/Article/', '')),
    title: shopifyArticle.title?.substring(0, 255) || null,
    handle: shopifyArticle.handle?.substring(0, 255) || null,
    content: shopifyArticle.content || null,
    summary: shopifyArticle.summary || null,
    author: shopifyArticle.author?.substring(0, 255) || null,
    tags: Array.isArray(shopifyArticle.tags) ? shopifyArticle.tags.join(',') : null,
    published_at: shopifyArticle.publishedAt ? new Date(shopifyArticle.publishedAt) : null,
    created_at_shopify: shopifyArticle.createdAt ? new Date(shopifyArticle.createdAt) : null,
    updated_at_shopify: shopifyArticle.updatedAt ? new Date(shopifyArticle.updatedAt) : null,
    status: this.mapShopifyStatus(shopifyArticle.status),
    is_synced: true
  };
}

static mapShopifyStatus(shopifyStatus) {
  const statusMap = {
    'PUBLISHED': 'published',
    'DRAFT': 'draft',
    'UNPUBLISHED': 'unpublished'
  };
  return statusMap[shopifyStatus] || 'draft';
}
```

### Database Operations
```javascript
// ArticleService.saveOrUpdateArticle()
async saveOrUpdateArticle(articleData) {
  const query = `
    INSERT INTO articles (
      shop_id, blog_id, article_id, title, handle, content, summary,
      author, tags, published_at, created_at_shopify, updated_at_shopify,
      status, is_synced
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ON DUPLICATE KEY UPDATE
      blog_id = VALUES(blog_id),
      title = VALUES(title),
      handle = VALUES(handle),
      content = VALUES(content),
      summary = VALUES(summary),
      author = VALUES(author),
      tags = VALUES(tags),
      published_at = VALUES(published_at),
      created_at_shopify = VALUES(created_at_shopify),
      updated_at_shopify = VALUES(updated_at_shopify),
      status = VALUES(status),
      is_synced = VALUES(is_synced),
      updated_at = CURRENT_TIMESTAMP
  `;

  const values = [
    articleData.shop_id,
    articleData.blog_id,
    articleData.article_id,
    articleData.title,
    articleData.handle,
    articleData.content,
    articleData.summary,
    articleData.author,
    articleData.tags,
    articleData.published_at,
    articleData.created_at_shopify,
    articleData.updated_at_shopify,
    articleData.status,
    articleData.is_synced
  ];

  return await db.query(query, values);
}
```

## Performance Characteristics

### Pagination Strategy
- **Batch Size**: 50 articles per GraphQL request
- **Cursor-Based**: Uses Shopify's cursor pagination for reliability
- **Memory Efficient**: Processes batches sequentially, not in parallel

### Processing Metrics
```javascript
// Example processing output
console.log(`[ArticleSyncQueue] - Processing batch for shop ${shopDomain}`);
console.log(`Articles in batch: ${articles.length}`);
console.log(`Total processed: ${totalArticlesProcessed}`);
console.log(`Processing rate: ${articlesPerSecond.toFixed(2)} articles/second`);
```

### Scalability
- **Large Shops**: Handles shops with 10,000+ articles efficiently
- **Memory Usage**: Constant memory usage regardless of total article count
- **Error Isolation**: Individual article failures don't stop processing

## Error Handling

### Individual Article Errors
```javascript
for (let shopifyArticle of articles) {
  try {
    await this.processArticle(shopifyArticle, shopId);
    articlesProcessed++;
  } catch (articleError) {
    // Log error but continue processing
    logger.error(articleError, {
      articleId: shopifyArticle.id,
      articleTitle: shopifyArticle.title,
      shopDomain,
      errorType: articleError.constructor.name
    });
    articlesFailed++;
    continue; // Don't stop batch processing
  }
}
```

### Batch Processing Errors
```javascript
try {
  const result = await this.processBatch(articles, shopId);
  return result;
} catch (batchError) {
  if (this.isRetryableError(batchError)) {
    // Re-dispatch with delay
    dispatchQueue({
      queueName: this.config.queueName,
      message: originalMessage,
      ttl: this.throttledDelay
    });
  } else {
    // Log and fail
    logger.error(batchError, { shopDomain, cursor });
    throw batchError;
  }
}
```

### Rate Limiting
```javascript
// Check rate limits before processing
if (await cache.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API")) {
  console.log(`Rate limit exceeded for ${shopDomain}, throttling...`);
  
  dispatchQueue({
    queueName: this.config.queueName,
    message: originalMessage,
    ttl: this.throttledDelay // 30 seconds
  });
  
  channel.ack(message);
  return;
}
```

## Integration with Blog Sync

### Blog Data Extraction
```javascript
// Extract blog information from article
if (shopifyArticle.blog) {
  const blogData = {
    shop_id: shopId,
    blog_id: parseInt(shopifyArticle.blog.id.replace('gid://shopify/Blog/', '')),
    title: shopifyArticle.blog.title,
    handle: shopifyArticle.blog.handle,
    is_synced: true
  };
  
  await BlogService.saveOrUpdateBlog(blogData);
}
```

### Transition to Blog Sync
```javascript
// After all articles processed
await ArticleService.deleteNotSyncedArticles(shopId);
console.log("Article sync complete");

// Clear sync flag (user can retry immediately)
await cache.blogSyncOngoing(shopDomain, false);

// Dispatch comprehensive blog sync
dispatchQueue({
  queueName: QUEUE_NAMES.BLOG_SYNC,
  message: { shopId, shopDomain, shopUrl, session, cursor: null }
});
```

## Monitoring and Logging

### Progress Tracking
```javascript
console.log(`[${this.config.queueName}] - Article sync started for shop ${shopDomain}`);
console.log(`Processing batch ${batchNumber}: ${articles.length} articles`);
console.log(`Total articles processed: ${totalArticlesProcessed}`);
console.log(`Success rate: ${successRate.toFixed(1)}%`);
console.log(`Processing rate: ${articlesPerSecond.toFixed(2)} articles/second`);
```

### Error Logging
```javascript
logger.error(error, {
  queueName: 'ArticleSyncQueue',
  shopDomain,
  shopId,
  cursor,
  articleId: shopifyArticle?.id,
  batchSize: articles?.length,
  errorType: error.constructor.name,
  errorMessage: error.message,
  retryable: this.isRetryableError(error)
});
```

## Testing Considerations

### Unit Testing
- Mock Shopify API responses
- Test pagination handling
- Verify error isolation
- Check data serialization

### Integration Testing
- End-to-end article sync flow
- Database state verification
- Error recovery scenarios
- Performance benchmarks

### Load Testing
- Large article counts (10,000+)
- Concurrent shop processing
- Memory usage monitoring
- Rate limit handling

---

**Article Sync Status**: ✅ **PRODUCTION READY**  
**Performance**: Handles 10,000+ articles efficiently  
**Reliability**: Individual error isolation with automatic recovery

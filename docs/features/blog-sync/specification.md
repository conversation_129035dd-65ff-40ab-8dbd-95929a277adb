# Blog Sync Feature Specification

## Overview

Comprehensive blog synchronization system that handles both articles and blogs from Shopify, providing complete coverage for content management and auto-write functionality.

## Feature Description

### What is Blog Sync?

Blog Sync is a comprehensive two-phase synchronization system that:

1. **Article Sync (Phase 1)**:
   - Syncs all blog articles from Shopify
   - Syncs blogs associated with those articles
   - Uses cursor-based pagination for scalability
   - Handles article content, metadata, and publishing status

2. **Blog Sync (Phase 2)**:
   - Syncs ALL blogs from Shopify, including empty ones
   - Ensures complete blog coverage beyond just article-associated blogs
   - Provides foundation for blog selection features

### Business Value

- **Complete Blog Coverage**: Access to all blogs for auto-write target selection
- **Enhanced User Experience**: Users can publish to any blog, not just those with existing articles
- **Foundation for v2.1.0**: Enables blog selection features in auto-write functionality

## Current vs Previous Behavior

### Previous Behavior (Before Enhancement)
```
User triggers "Blog Sync" → ArticleSyncQueue only
├── Phase 1: Article Processing
│   ├── Fetch articles from Shopify (paginated)
│   ├── Save articles to database
│   ├── Save associated blogs (only those with articles)
│   └── Clean up unsynced articles
└── Completion: Limited blog coverage
```

**Limitations**:
- ❌ Only blogs with articles were synced
- ❌ Empty blogs were not available for selection
- ❌ Auto-write could only target blogs with existing content
- ❌ Incomplete blog metadata for selection purposes

### Current Behavior (After Enhancement)
```
User triggers "Blog Sync" → ArticleSyncQueue → BlogSyncQueue
├── Phase 1: ArticleSyncQueue (Enhanced)
│   ├── Mark articles/blogs as not synced (preparation)
│   ├── Fetch articles from Shopify (cursor-based pagination)
│   ├── For each article:
│   │   ├── Save article with full content and metadata
│   │   └── Save associated blog information
│   ├── Clean up unsynced articles
│   ├── Clear ongoing sync flag (user can retry immediately)
│   └── Dispatch BlogSyncQueue
├── Phase 2: BlogSyncQueue (NEW)
│   ├── Fetch ALL blogs from Shopify (cursor-based pagination)
│   ├── Save all blogs (including empty ones)
│   ├── Clean up unsynced blogs
│   └── Trigger final completion event
└── Result: Complete article + blog coverage
```

**Benefits**:
- ✅ ALL blogs are synced (with and without articles)
- ✅ Complete blog selection for auto-write
- ✅ Users never get stuck unable to retry
- ✅ Robust error handling and recovery

## Technical Architecture

### Queue Flow
```
ArticleSyncQueue (existing)
    ↓ (on completion)
cache.blogSyncOngoing(false) ← User can retry from here
    ↓ (dispatch)
BlogSyncQueue (new)
    ↓ (on completion)
Final completion event
```

### Key Components

#### 1. BlogSyncQueue
- **Purpose**: Dedicated queue for comprehensive blog synchronization
- **Trigger**: Automatically dispatched after ArticleSyncQueue completion
- **Features**: Pagination, error isolation, progress tracking

#### 2. Enhanced ShopifyService
- **New Methods**: `onlineStore.getBlogs()`, `onlineStore.getBlog()`
- **GraphQL Queries**: Dedicated blog queries with metafield support

#### 3. Production Utilities
- **blogSync.js**: Production-ready utility for engineers
- **articleSync.js**: Renamed from confusing "blogSync.js"

## User Experience

### For End Users
1. **Trigger Sync**: Click "Sync" button in admin
2. **Article Phase**: Articles sync first (existing behavior)
3. **Blog Phase**: All blogs sync automatically (new)
4. **Completion**: "Blog posts sync complete!" notification
5. **Result**: All blogs available for auto-write selection

### For Engineers (Client Support)
```bash
# Check current blog status (safe)
node web/cmd/sync/blogSync.js client-shop.myshopify.com --dry-run

# Fix client blog sync issues
node web/cmd/sync/blogSync.js client-shop.myshopify.com

# Force retry stuck sync
node web/cmd/sync/blogSync.js client-shop.myshopify.com --force
```

## Data Model

### Article Entity
```javascript
{
  shop_id: number,           // Shop identifier
  blog_id: number,           // Parent blog ID
  article_id: number,        // Shopify article ID
  title: string,             // Article title
  handle: string,            // URL-friendly identifier
  content: string,           // Full article content (HTML)
  summary: string,           // Article excerpt
  author: string,            // Article author name
  tags: string,              // Comma-separated tags
  published_at: Date,        // Publication timestamp
  created_at_shopify: Date,  // Shopify creation time
  updated_at_shopify: Date,  // Shopify last update time
  status: string,            // published, draft, unpublished
  is_synced: boolean,        // Sync status
  // ... SEO and analysis fields
}
```

### Blog Entity
```javascript
{
  shop_id: number,           // Shop identifier
  blog_id: number,           // Shopify blog ID
  title: string,             // Blog title
  handle: string,            // URL handle
  template_suffix: string,   // Theme template
  tags: string[],            // Blog tags
  is_synced: boolean,        // Sync status
  // ... SEO and analysis fields
}
```

### Sync Status Tracking

#### Articles
- `is_synced: true` - Article is current and synced
- `is_synced: false` - Article marked for cleanup (will be deleted if not found in Shopify)

#### Blogs
- `is_synced: true` - Blog is current and synced
- `is_synced: false` - Blog marked for cleanup (will be deleted if not found in Shopify)

## Error Handling

### Multi-Level Error Strategy
1. **Individual Blog Errors**: Continue processing other blogs
2. **Batch Errors**: Retry with exponential backoff
3. **Queue Errors**: Re-dispatch with throttling
4. **User Recovery**: Force retry options available

### Safety Mechanisms
- **Early Flag Clearing**: Users can retry after article sync completion
- **Dry Run Mode**: Test without making changes
- **Progress Monitoring**: Success/failure rate tracking
- **Graceful Degradation**: Article sync success is primary goal

## Performance Characteristics

### Scalability
- **Batch Processing**: 50 blogs per API call
- **Cursor Pagination**: Handles large blog lists efficiently
- **Memory Efficient**: Sequential batch processing

### Rate Limiting
- **Shopify API Compliance**: Respects GraphQL API limits
- **Automatic Throttling**: Re-dispatch on rate limit hits
- **Cache Integration**: Uses existing rate limit tracking

## Integration Points

### Current Integrations
- **Article Sync**: Seamless transition from article to blog sync
- **Event System**: Maintains existing completion events
- **Cache System**: Uses existing blog sync flags
- **Notification System**: Preserves user notifications

### Future Integrations (v2.1.0)
- **Auto-Write Blog Selection**: Choose target blog from all synced blogs
- **AI-Generated Blogs Page**: Filter and display AI-generated content
- **Enhanced Blog Management**: Bulk operations on synced blogs

## Success Metrics

### Technical Metrics
- ✅ **100% Blog Coverage**: All Shopify blogs synced
- ✅ **Error Isolation**: Individual failures don't stop sync
- ✅ **User Recovery**: No stuck sync states
- ✅ **Performance**: Efficient pagination and processing

### Business Metrics
- ✅ **Enhanced Auto-Write**: Users can select any blog as target
- ✅ **Reduced Support**: Self-service troubleshooting tools
- ✅ **Foundation Ready**: v2.1.0 features can be built

## Security Considerations

### Data Protection
- **Shop Isolation**: Blogs are scoped to individual shops
- **Access Control**: Uses existing Shopify access tokens
- **Data Validation**: Input sanitization and validation

### API Security
- **Rate Limiting**: Prevents API abuse
- **Error Logging**: Secure logging without sensitive data
- **Token Management**: Secure handling of access tokens

## Deployment Requirements

### Prerequisites
- Queue processor must support BlogSyncQueue
- Database schema supports existing blog structure
- GraphQL queries are valid for Shopify API

### Rollback Plan
- Remove BlogSyncQueue from kernel registration
- Revert ArticleSyncQueue integration changes
- Restore original completion event logic

---

**Status**: ✅ **IMPLEMENTED & TESTED**  
**Version**: v2.1.0 Foundation  
**Next Phase**: UI enhancements for blog selection

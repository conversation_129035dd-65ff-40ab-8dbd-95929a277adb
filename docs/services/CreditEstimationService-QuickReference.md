# Credit Estimation Service - Quick Reference

## 🚀 Quick Start

```javascript
const CreditEstimationService = require('./services/CreditEstimationService');

// Blog auto-write (fully implemented)
const blogCredits = CreditEstimationService.blog.autoWrite({
  topic: "SEO Guide",
  wordCount: "800-1200",
  blogType: "Guide",
  featuredImageDescription: "Dashboard"
});

console.log(`Total Credits: ${blogCredits.totalCredits}`);
console.log(`Estimated Cost: $${blogCredits.estimatedCost}`);
```

## 📋 Implementation Status

| Feature | Namespace | Status | Addon Group |
|---------|-----------|--------|-------------|
| **Blog Auto-Write** | `blog.autoWrite()` | ✅ **Production Ready** | AI_OPTIMIZER |
| **Image Alt-Text** | `image.altText()` | ✅ **Implemented** | AI_OPTIMIZER |
| **Image Optimization** | `image.optimization()` | ✅ **Implemented** | IMAGE_OPTIMIZER |
| **Bulk Image Optimization** | `image.bulkOptimization()` | ✅ **Implemented** | IMAGE_OPTIMIZER |
| Blog Optimization | `blog.optimization()` | 🚧 **Placeholder** | AI_OPTIMIZER |
| Product Optimization | `product.optimization()` | 🚧 **Basic Structure** | AI_OPTIMIZER |
| Collection Optimization | `collection.optimization()` | 🚧 **Basic Structure** | AI_OPTIMIZER |
| Article Optimization | `article.optimization()` | 🚧 **Basic Structure** | AI_OPTIMIZER |

## 🎯 Fully Implemented Features

### Blog Auto-Write
```javascript
// Input
const input = {
  topic: "SEO Best Practices",
  keyword: "SEO optimization",        // Optional
  wordCount: "800-1200",             // Required
  blogType: "Guide",                 // Required
  tone: "Professional",              // Optional
  customInstructions: "Include examples", // Optional
  featuredImageDescription: "Dashboard"   // Optional
};

// Usage
const result = CreditEstimationService.blog.autoWrite(input);

// Output
{
  totalCredits: 112,
  feature: "blog_auto_write",
  featureName: "Blog Auto-Write",
  addonGroup: "AI_OPTIMIZER",
  estimatedCost: 0.224,
  breakdown: {
    content: { credits: 12, details: {...} },
    images: { credits: 100, details: {...} }  // Only if image description provided
  }
}
```

### Image Features
```javascript
// Alt-text generation
const altText = CreditEstimationService.image.altText({
  images: [{ url: "img1.jpg" }, { url: "img2.jpg" }]
});

// File optimization
const optimization = CreditEstimationService.image.optimization({
  imageCount: 5
});

// Bulk optimization
const bulk = CreditEstimationService.image.bulkOptimization({
  imageCount: 100
});
```

## 🚧 Placeholder Features (Need Implementation)

### Content Optimization Features
```javascript
// These return placeholder credits - need proper implementation
CreditEstimationService.blog.optimization(input, settings);      // Returns 0
CreditEstimationService.product.optimization(input, settings);   // Returns 5 + alt-text
CreditEstimationService.collection.optimization(input, settings); // Returns 4 + alt-text
CreditEstimationService.article.optimization(input, settings);   // Returns 6 + alt-text
```

## 💰 Credit Pricing

| Feature | Cost Calculation | Example |
|---------|------------------|---------|
| **Blog Content** | OpenAI tokens × 10x markup | 12 credits |
| **Blog Image** | DALL-E $0.08 × 2.5x markup | 100 credits |
| **Image Alt-Text** | AiService IMAGE calculation | Variable |
| **Image Optimization** | 1 credit per image | 1 credit |

**USD Conversion**: 5000 credits = $10 (1 credit = $0.002)

## 🏗️ Response Format

All methods return this consistent structure:

```javascript
{
  totalCredits: number,           // Total credits required
  breakdown: {                    // Detailed breakdown
    content?: { credits, details },
    images?: { credits, details }
  },
  feature: string,                // Feature identifier
  featureName: string,            // Human-readable name
  addonGroup: string,             // AI_OPTIMIZER or IMAGE_OPTIMIZER
  estimatedCost: number           // USD cost estimate
}
```

## 🔧 Integration Patterns

### Service Integration
```javascript
// In your service
class MyFeatureService {
  getCreditEstimate(input) {
    return CreditEstimationService.myFeature.action(input);
  }
}
```

### Controller Integration
```javascript
// In your controller
async getCreditEstimate(req, res) {
  const estimation = MyFeatureService.getCreditEstimate(req.body);
  const usage = await MyFeatureService.getCreditUsage(req.user.shopDomain);
  
  res.json({
    success: true,
    data: {
      estimatedCredits: estimation.totalCredits,
      availableCredits: usage.availableCredits,
      canProceed: usage.availableCredits >= estimation.totalCredits,
      feature: estimation.feature,
      addonGroup: estimation.addonGroup,
      breakdown: estimation.breakdown
    }
  });
}
```

## 🧪 Testing

```javascript
// Test structure
describe('Credit Estimation', () => {
  it('should estimate credits correctly', () => {
    const result = CreditEstimationService.blog.autoWrite(input);
    
    expect(result.totalCredits).toBeGreaterThan(0);
    expect(result.feature).toBe('blog_auto_write');
    expect(result.addonGroup).toBe('AI_OPTIMIZER');
  });
});
```

## ⚠️ Important Notes

1. **Blog Auto-Write**: Only fully implemented feature - production ready
2. **No Backward Compatibility**: Clean API for new features
3. **Addon Groups**: Properly classify features for billing
4. **Placeholder Values**: Other features return placeholder credits
5. **USD Estimation**: All responses include cost estimation

## 🔮 Next Steps for Development

1. **Implement Content Optimization**: Define scope and credit calculation
2. **Enhance Product Features**: Add detailed product optimization logic
3. **Add Settings Support**: Implement feature-specific configuration
4. **Performance Optimization**: Add caching and bulk processing

## 📞 Need Help?

- **Documentation**: See `docs/services/CreditEstimationService.md`
- **Tests**: Check `web/api/tests/integration/` for examples
- **Issues**: Contact development team for feature requests

---

**Last Updated**: 2025-01-23
**Version**: 1.0.0

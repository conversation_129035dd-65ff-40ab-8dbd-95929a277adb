# Credit Estimation Service Documentation

## Overview

The `CreditEstimationService` provides accurate credit usage estimation for all AI-powered add-on features in the StoreSEO application. It uses a namespace-based API design with explicit, detailed methods for better maintainability and developer experience.

**Version**: 1.0.0
**Architecture**: Simplified namespace-based
**Date**: 2025-01-23

## Design Philosophy

- **Namespace-based API**: Intuitive `service.feature.action` structure
- **Explicit Methods**: Detailed, specific implementations over abstract configurations
- **Addon Group Support**: Proper classification of AI_OPTIMIZER vs IMAGE_OPTIMIZER features
- **No Backward Compatibility**: Clean implementation for new features (blog auto-write)
- **Transparent Pricing**: Clear cost breakdown and USD estimation

## API Structure

### Namespace Organization

```javascript
const creditEstimationService = require('./services/CreditEstimationService');

// Blog features (AI_OPTIMIZER)
creditEstimationService.blog.autoWrite(input)
creditEstimationService.blog.optimization(input, settings)

// Product features (AI_OPTIMIZER)
creditEstimationService.product.optimization(input, settings)

// Collection features (AI_OPTIMIZER)
creditEstimationService.collection.optimization(input, settings)

// Article features (AI_OPTIMIZER)
creditEstimationService.article.optimization(input, settings)

// Image features
creditEstimationService.image.altText(input, settings)        // AI_OPTIMIZER
creditEstimationService.image.optimization(input, settings)  // IMAGE_OPTIMIZER
creditEstimationService.image.bulkOptimization(input, settings) // IMAGE_OPTIMIZER
```

## Currently Implemented Features

### ✅ Blog Auto-Write (AI_OPTIMIZER)

**Status**: ✅ **Fully Implemented & Production Ready**

```javascript
const result = creditEstimationService.blog.autoWrite({
  topic: "SEO Best Practices",
  keyword: "SEO optimization",
  wordCount: "800-1200",
  blogType: "Guide",
  tone: "Professional",
  customInstructions: "Include examples",
  featuredImageDescription: "Dashboard screenshot"
});
```

**Response Structure**:
```javascript
{
  totalCredits: 112,
  breakdown: {
    content: {
      credits: 12,
      details: {
        tokenEstimation: { promptTokens: 850, completionTokens: 1350 },
        openAiUsage: { prompt_tokens: 850, completion_tokens: 1350 },
        userCredits: 12
      }
    },
    images: {
      credits: 100,
      details: {
        dalleActualCost: 0.08,
        userCredits: 100
      }
    }
  },
  feature: "blog_auto_write",
  featureName: "Blog Auto-Write",
  addonGroup: "AI_OPTIMIZER",
  estimatedCost: 0.224
}
```

**Credit Calculation**:
- **Content**: Uses OpenAI token estimation with 10x markup (matches existing AiService)
- **Images**: DALL-E 3 pricing ($0.08 for 1792x1024) with 2.5x markup = 100 credits
- **Total**: Content credits + Image credits (if image description provided)

### ✅ Image File Optimization (IMAGE_OPTIMIZER)

**Status**: ✅ **Fully Implemented**

```javascript
// Single image optimization
const result = creditEstimationService.image.optimization({
  imageCount: 1
});

// Bulk image optimization
const bulkResult = creditEstimationService.image.bulkOptimization({
  imageCount: 50
});
```

**Credit Calculation**: 1 credit per image optimization

### ✅ Image Alt-Text Generation (AI_OPTIMIZER)

**Status**: ✅ **Fully Implemented**

```javascript
const result = creditEstimationService.image.altText({
  images: [{ url: "image1.jpg" }, { url: "image2.jpg" }]
});
```

**Credit Calculation**: Uses existing AiService IMAGE calculation

## Placeholder Implementations (Future Development)

### 🚧 Blog Content Optimization (AI_OPTIMIZER)

**Status**: 🚧 **Placeholder - Needs Implementation**

```javascript
// Currently returns 0 credits
const result = creditEstimationService.blog.optimization(input, settings);
```

**TODO**:
- Define optimization scope (SEO, readability, etc.)
- Implement credit calculation logic
- Add settings support

### 🚧 Product Content Optimization (AI_OPTIMIZER)

**Status**: 🚧 **Basic Structure - Needs Enhancement**

```javascript
// Currently returns basic placeholder credits
const result = creditEstimationService.product.optimization({
  title: "Product Title",
  description: "Product Description",
  images: [...]
}, {
  imageAltText: true
});
```

**Current Implementation**:
- Base content optimization: 5 credits (placeholder)
- Image alt-text: Uses existing calculation if enabled

**TODO**:
- Define product optimization features (title, description, tags, etc.)
- Implement proper credit calculation based on content length
- Add more granular settings

### 🚧 Collection Content Optimization (AI_OPTIMIZER)

**Status**: 🚧 **Basic Structure - Needs Enhancement**

```javascript
// Currently returns basic placeholder credits
const result = creditEstimationService.collection.optimization(input, settings);
```

**Current Implementation**:
- Base content optimization: 4 credits (placeholder)
- Image alt-text: Uses existing calculation if enabled

**TODO**:
- Define collection optimization scope
- Implement proper credit calculation
- Add collection-specific settings

### 🚧 Article Content Optimization (AI_OPTIMIZER)

**Status**: 🚧 **Basic Structure - Needs Enhancement**

```javascript
// Currently returns basic placeholder credits
const result = creditEstimationService.article.optimization(input, settings);
```

**Current Implementation**:
- Base content optimization: 6 credits (placeholder)
- Image alt-text: Uses existing calculation if enabled

**TODO**:
- Define article optimization features
- Implement proper credit calculation
- Add article-specific settings

## Addon Group Classification

### AI_OPTIMIZER Addon Group
- Blog auto-write ✅
- Blog optimization 🚧
- Product optimization 🚧
- Collection optimization 🚧
- Article optimization 🚧
- Image alt-text generation ✅

### IMAGE_OPTIMIZER Addon Group
- Image file optimization ✅
- Bulk image optimization ✅

## Utility Methods

### Credit to USD Conversion
```javascript
convertCreditsToUSD(credits) {
  // 5000 credits = $10, so 1 credit = $0.002
  return (credits * 10) / 5000;
}
```

### Image Alt-Text Credit Calculation
```javascript
calculateImageAltTextCredits(images, settings) {
  // Uses existing AiService IMAGE calculation
  const creditsPerImage = this.calculateCreditUsage({}, IMAGE);
  return images.length * creditsPerImage;
}
```

## Integration Examples

### Blog Auto-Write Service Integration

```javascript
// In BlogAutoWriteService.js
getCreditEstimate(inputData) {
  return CreditEstimationService.blog.autoWrite(inputData);
}

// In BlogAutoWriteController.js
async getCreditEstimate(req, res) {
  const creditEstimation = BlogAutoWriteService.getCreditEstimate(inputData);
  const creditUsage = await BlogAutoWriteService.getCreditUsage(shopDomain);
  
  res.json({
    success: true,
    data: {
      estimatedCredits: creditEstimation.totalCredits,
      availableCredits: creditUsage.availableCredits,
      canProceed: creditUsage.availableCredits >= creditEstimation.totalCredits,
      feature: creditEstimation.feature,
      addonGroup: creditEstimation.addonGroup,
      breakdown: creditEstimation.breakdown
    }
  });
}
```

## Future Development Roadmap

### Phase 1: Content Optimization Features
- [ ] Implement blog content optimization
- [ ] Enhance product optimization with detailed features
- [ ] Implement collection optimization
- [ ] Implement article optimization

### Phase 2: Advanced Features
- [ ] Batch processing support
- [ ] Custom pricing tiers
- [ ] Feature-specific settings
- [ ] Usage analytics integration

### Phase 3: Performance & Scaling
- [ ] Caching layer for estimations
- [ ] Bulk estimation APIs
- [ ] Real-time usage tracking
- [ ] Cost optimization algorithms

## Testing

### Test Coverage
- ✅ Blog auto-write integration tests
- ✅ Namespace API tests
- ✅ Credit calculation accuracy tests
- ✅ Addon group classification tests
- ✅ Error handling tests

### Test Files
- `web/api/tests/integration/blog-auto-write-clean-integration.test.js`
- `web/api/tests/integration/blog-auto-write-integration.test.js`

## Migration Notes

### From TokenEstimationService
- ✅ Renamed service file
- ✅ Updated all imports
- ✅ Migrated detailed calculation methods
- ✅ Updated blog auto-write integration
- ✅ Removed backward compatibility for new features

### Breaking Changes
- Service name changed from `TokenEstimationService` to `CreditEstimationService`
- API structure changed from direct methods to namespace-based
- Response format enhanced with addon group and feature information
- Blog auto-write uses credit terminology instead of token terminology

## Configuration

### Credit Pricing
```javascript
// From web/api/config/ai.js
const creditUnit = 1000;           // Credits per unit
const inputOutputRatio = 3;        // Output token weight
const perImageCredit = 1000;       // Credits per image alt-text

// Pricing
const creditsPerDollar = 500;      // 5000 credits = $10
const imageMarkup = 2.5;           // 2.5x markup for DALL-E
```

### Addon Groups
```javascript
const AI_OPTIMIZER = "AI_OPTIMIZER";
const IMAGE_OPTIMIZER = "IMAGE_OPTIMIZER";
```

## Support & Maintenance

**Maintainer**: Development Team
**Last Updated**: 2025-01-23
**Next Review**: Q2 2025

For questions or feature requests, please refer to the development team or create an issue in the project repository.

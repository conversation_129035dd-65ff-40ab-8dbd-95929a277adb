//@ts-check
const { Product, Op } = require("../../sequelize");
const ProductImageService = require("../services/ProductImageService");
const ArticleImageService = require("../services/ArticleImageService");
const { capitalize } = require("lodash");

/**
 * Calculate actual image alt text counts based on optimization dates within bulk operation timeframe
 * @param {number} shopId - Shop ID
 * @param {Date} startDate - Bulk operation start date
 * @param {Date} endDate - Bulk operation end date
 * @param {string} resourceType - Resource type (PRODUCT or ARTICLE)
 * @returns {Promise<{featuredImageCount: number, productImageCount: number}>} - Object containing featuredImageCount and productImageCount
 */
async function calculateImageAltTextCounts(shopId, startDate, endDate, resourceType) {
  let featuredImageCount = 0;
  let productImageCount = 0;

  const calculateProductImagesCount = async () => {
    const conditions = {
      shop_id: shopId,
      alt_text_optimized_at: {
        [Op.between]: [startDate, endDate],
      },
    };

    const additionalIncludes = [
      {
        model: Product,
        as: "featured",
        attributes: ["id", "featured_media_id"],
        where: {
          shop_id: shopId,
        },
        required: false,
      },
    ];

    const optimizedProductImages = await ProductImageService.getImagesByConditions(
      conditions,
      undefined, // fields - use default
      additionalIncludes
    );

    // Count total product images optimized
    productImageCount = optimizedProductImages.length;

    // Count featured images (images that have an associated product via featuredImage)
    featuredImageCount = optimizedProductImages.filter((productImage) => {
      return productImage.featured !== null;
    }).length;
  };

  const calculateArticleImagesCount = async () => {
    const conditions = {
      shop_id: shopId,
      alt_text_optimized_at: {
        [Op.between]: [startDate, endDate],
      },
    };

    const optimizedArticleImages = await ArticleImageService.getImagesByConditions(conditions);

    featuredImageCount = optimizedArticleImages.length;
  };

  switch (resourceType) {
    case "PRODUCT":
      await calculateProductImagesCount();
      break;
    case "ARTICLE":
      await calculateArticleImagesCount();
      break;
  }

  return {
    featuredImageCount,
    productImageCount,
  };
}

/**
 * Extract image optimization data for bulk operation email
 * @param {number} shopId - Shop ID
 * @param {string} resourceType - Resource type (PRODUCT_IMAGE, COLLECTION_IMAGE, ARTICLE_IMAGE)
 * @param {Date} startDate - Bulk operation start date
 * @param {Date} endDate - Bulk operation end date
 * @returns {Promise<object>} Image optimization data
 */
async function extractImageOptimizationData(shopId, resourceType, startDate, endDate) {
  let optimizedImages = [];
  const defaultData = {
    compressionType: "None",
    resizeOption: "Didn't change",
    totalImagesOptimized: 0,
  };

  const conditions = {
    shop_id: shopId,
    optimized_at: {
      [Op.between]: [startDate, endDate],
    },
  };

  try {
    switch (resourceType) {
      case "PRODUCT_IMAGE":
        const ProductImageService = require("../services/ProductImageService");
        optimizedImages = await ProductImageService.getImagesByConditions(conditions, ["optimization_setting"]);
        break;
      case "COLLECTION_IMAGE":
        const CollectionImageService = require("../services/collections/CollectionImageService");
        optimizedImages = await CollectionImageService.getImagesByConditions(conditions, undefined, [
          "resourceOptimizationMeta",
        ]);
        break;
      case "ARTICLE_IMAGE":
        const ArticleImageService = require("../services/ArticleImageService");
        optimizedImages = await ArticleImageService.getImagesByConditions(conditions, undefined, [
          "resourceOptimizationMeta",
        ]);
        break;
      default:
        console.warn(`Unsupported resource type for image optimization data: ${resourceType}`);
        return defaultData;
    }
    // Set total images optimized count
    defaultData.totalImagesOptimized = optimizedImages.length;

    // Get optimization settings from the first record that has settings
    const recordWithSettings = optimizedImages.find((record) => record.optimization_setting);
    if (recordWithSettings && recordWithSettings.optimization_setting) {
      const optimizationSetting = recordWithSettings.optimization_setting;

      const compressionTypeRaw = optimizationSetting.compression_type;
      if (compressionTypeRaw) {
        defaultData.compressionType = capitalize(compressionTypeRaw);
      }

      const resizeOptionRaw = optimizationSetting.target_width;
      if (resizeOptionRaw) {
        defaultData.resizeOption =
          resizeOptionRaw === "2048" ? `${resizeOptionRaw} px (Recommended by Shopify)` : `${resizeOptionRaw} px`;
      }
    }

    return defaultData;
  } catch (error) {
    console.error(`Failed to get image optimization details for shop ${shopId}, resource type ${resourceType}:`, error);
    return defaultData;
  }
}

module.exports = {
  calculateImageAltTextCounts,
  extractImageOptimizationData,
};

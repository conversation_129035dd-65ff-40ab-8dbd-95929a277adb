const TrialStatus = require("storeseo-enums/trialStatus");
const dayjs = require("dayjs");

/**
 * Create initial trial data structure
 * @param {number} trialDays - Number of trial days
 * @param {import("../../../types").TrialRules} trialRules - Trial usage limits
 * @param {import("../../../types").TrialData} trialData - Existing trial data
 * @returns {import("../../../types").TrialData} Initial trial data object
 */
const serializeTrialData = (trialDays = 0, trialRules = {}, trialData = null) => {
  if (trialData?.status === TrialStatus.ACTIVE) {
    return {
      ...trialData,
      status: TrialStatus.CONVERTED,
    };
  }

  const now = dayjs();
  const expiresAt = now.add(trialDays, "day");

  return {
    status: trialDays > 0 ? TrialStatus.ACTIVE : now.isSame(expiresAt) ? TrialStatus.EXPIRED : TrialStatus.INACTIVE,
    startedAt: now.format(),
    expiresAt: expiresAt.format(),
    trialDays: trialDays,
    isTrialUser: trialDays > 0,
    limits: {
      ...trialRules,
    },
  };
};

/**
 * Check if trial is currently active
 * @param {Object} trialData - Trial data from shop.trial_data
 * @returns {boolean} True if trial is active
 */
const isTrialActive = (trialData) => {
  if (!trialData || trialData.status !== TrialStatus.ACTIVE) {
    return false;
  }

  const now = new Date();
  const expiresAt = new Date(trialData.expiresAt);

  return now < expiresAt;
};

/**
 * Check if trial has expired
 * @param {Object} trialData - Trial data from shop.trial_data
 * @returns {boolean} True if trial has expired
 */
const isTrialExpired = (trialData) => {
  if (!trialData || trialData.status !== TrialStatus.ACTIVE) {
    return false;
  }

  const now = new Date();
  const expiresAt = new Date(trialData.expiresAt);

  return now >= expiresAt;
};

/**
 * Check if user has already used a trial
 * @param {Object} trialData - Trial data from shop.trial_data
 * @returns {boolean} True if user has used trial before
 */
const isTrialUser = (trialData) => {
  return trialData && trialData.isTrialUser === true;
};

/**
 * Calculate remaining trial days
 * @param {Object} trialData - Trial data from shop.trial_data
 * @returns {number} Number of days remaining (0 if expired)
 */

const getRemainingTrialDays = (trialData) => {
  if (!trialData || !isTrialActive(trialData)) {
    return 0;
  }

  const now = dayjs();
  const expiresAt = dayjs(trialData.expiresAt);

  // Compare only the date part (ignore time)
  const diffDays = expiresAt.startOf("day").diff(now.startOf("day"), "day");

  return Math.max(0, diffDays);
};

/**
 * Update trial data status
 * @param {Object} trialData - Current trial data
 * @param {string} newStatus - New status from TrialStatus enum
 * @returns {Object} Updated trial data
 */
const updateTrialStatus = (trialData, newStatus) => {
  if (!trialData) {
    return null;
  }

  return {
    ...trialData,
    status: newStatus,
    updatedAt: new Date().toISOString(),
  };
};

module.exports = {
  serializeTrialData,
  isTrialActive,
  isTrialExpired,
  isTrialUser,
  getRemainingTrialDays,
  updateTrialStatus,
};

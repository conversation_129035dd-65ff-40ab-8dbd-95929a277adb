const BlogAutoWriteService = require("../services/BlogAutoWriteService");
const { dispatchQueue } = require("../queue/queueDispatcher");
const { QUEUE_NAMES } = require("../queue/config");
const { BlogAutoWriteJob } = require("../../sequelize");
const cache = require("../cache");
const BlogAutoWriteJobStatus = require("storeseo-enums/blogAutoWrite/jobStatus");

/**
 * Blog Auto-Write Controller
 * Handles API endpoints for blog auto-generation
 */
class BlogAutoWriteController {
  /**
   * Create a new blog auto-write job
   * POST /api/blog-auto-write
   * Note: Input validation and credit validation are handled by middleware
   * Note: Uses shop's default blog (no blogId required)
   */
  async createJob(req, res) {
    try {
      const { shopId, shop: shopDomain } = req.user;
      const {
        topic,
        keyword,
        targetBlog,
        tone,
        wordCount,
        blogType,
        customInstructions,
        generateFeaturedImage,
        featuredImageDescription,
        autoPublish,
      } = req.body;

      // Credit estimation and availability already validated by middleware
      const creditEstimation = req.creditEstimation;
      const availableCredits = req.availableCredits;

      const inputData = {
        topic,
        keyword,
        targetBlog,
        tone,
        wordCount,
        blogType,
        customInstructions,
        generateFeaturedImage,
        featuredImageDescription,
        autoPublish: autoPublish !== undefined ? autoPublish : false, // Default to draft for current release
      };

      // Create job and dummy article (uses shop's default blog)
      const result = await BlogAutoWriteService.createBlogAutoWriteJob({
        shopId,
        shopDomain,
        inputData,
      });

      // Dispatch to queue for processing
      await dispatchQueue({
        queueName: QUEUE_NAMES.BLOG_AUTO_WRITE_QUEUE,
        message: {
          jobId: result.job.id,
          shopDomain,
        },
      });

      res.status(201).json({
        success: true,
        message: "Blog auto-write job created successfully",
        data: {
          jobId: result.job.id,
          articleId: result.article.id,
          status: result.job.status,
          progress: result.job.progress,
          estimatedCredits: creditEstimation.totalCredits,
          availableCredits: availableCredits - creditEstimation.totalCredits,
          feature: creditEstimation.feature,
          featureName: creditEstimation.featureName,
          addonGroup: creditEstimation.addonGroup,
        },
      });
    } catch (error) {
      console.error("Create blog auto-write job error:", error);

      res.status(500).json({
        success: false,
        message: "Failed to create blog auto-write job",
        error: {
          code: "JOB_CREATION_FAILED",
          details: error.message,
        },
      });
    }
  }

  /**
   * Get job details by ID
   * GET /api/blog-auto-write/:jobId
   */
  async getJob(req, res) {
    try {
      const { shopId } = req.user;
      const { jobId } = req.params;

      const job = await BlogAutoWriteService.getJobById(jobId, shopId);
      if (!job) {
        return res.status(404).json({
          success: false,
          error: "Job not found",
        });
      }

      res.json({
        success: true,
        data: {
          jobId: job.id,
          status: job.status,
          progress: job.progress,
          steps: job.steps,
          articleId: job.article_id,
          article: job.article,
          inputData: job.input_data,
          estimatedCreditUsage: job.estimated_credit_usage,
          creditUsage: job.credit_usage,
          errorMessage: job.error_message,
          createdAt: job.created_at,
          updatedAt: job.updated_at,
          processingStartedAt: job.processing_started_at,
          processingCompletedAt: job.processing_completed_at,
        },
      });
    } catch (error) {
      console.error("Error getting job status:", error);
      res.status(500).json({
        success: false,
        error: error.message,
      });
    }
  }

  /**
   * Get credit usage estimate for blog auto-write
   * POST /api/blog-auto-write/credit-estimate
   * Note: Input validation is handled by middleware
   */
  async getCreditEstimate(req, res) {
    try {
      const { shopDomain } = req.user;
      const inputData = req.body; // Already validated and sanitized by middleware

      // Use new namespace API directly
      const creditEstimation = BlogAutoWriteService.getCreditEstimate(inputData);
      const creditUsage = await BlogAutoWriteService.getCreditUsage(shopDomain);

      res.json({
        success: true,
        data: {
          estimatedCredits: creditEstimation.totalCredits,
          availableCredits: creditUsage.availableCredits,
          currentUsage: creditUsage.currentUsage,
          usageLimit: creditUsage.usageLimit,
          canProceed: creditUsage.availableCredits >= creditEstimation.totalCredits,
          feature: creditEstimation.feature,
          featureName: creditEstimation.featureName,
          addonGroup: creditEstimation.addonGroup,
          estimatedCost: creditEstimation.estimatedCost,
          breakdown: creditEstimation.breakdown,
        },
      });
    } catch (error) {
      console.error("Error getting credit estimate:", error);
      res.status(500).json({
        success: false,
        message: "Failed to get credit estimate",
        error: {
          code: "CREDIT_ESTIMATION_FAILED",
          details: error.message,
        },
      });
    }
  }

  /**
   * Get current credit usage for AI_OPTIMIZER addon
   * GET /api/blog-auto-write/credit-usage
   */
  async getCreditUsage(req, res) {
    try {
      const { shopDomain } = req.user;
      const creditUsage = await BlogAutoWriteService.getCreditUsage(shopDomain);

      res.json({
        success: true,
        data: creditUsage,
      });
    } catch (error) {
      console.error("Error getting credit usage:", error);
      res.status(500).json({
        success: false,
        error: error.message,
      });
    }
  }

  /**
   * List jobs for shop
   * GET /api/blog-auto-write/jobs
   */
  async listJobs(req, res) {
    try {
      const { shopId } = req.user;
      const { page = 1, limit = 20, status } = req.query;

      const whereClause = { shop_id: shopId };
      if (status) {
        whereClause.status = status;
      }

      const offset = (page - 1) * limit;

      const { rows: jobs, count: total } = await BlogAutoWriteJob.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: require("../sequelize/models").Article,
            as: "article",
            required: false,
          },
        ],
        order: [["created_at", "DESC"]],
        limit: parseInt(limit),
        offset: parseInt(offset),
      });

      res.json({
        success: true,
        data: {
          jobs: jobs.map((job) => job.toJSON()),
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            totalPages: Math.ceil(total / limit),
          },
        },
      });
    } catch (error) {
      console.error("Error listing jobs:", error);
      res.status(500).json({
        success: false,
        error: error.message,
      });
    }
  }

  /**
   * Cancel/delete a job
   * DELETE /api/blog-auto-write/:jobId
   */
  async cancelJob(req, res) {
    try {
      const { shopId, shopDomain } = req.user;
      const { jobId } = req.params;

      const job = await BlogAutoWriteService.getJobById(jobId, shopId);
      if (!job) {
        return res.status(404).json({
          success: false,
          error: "Job not found",
        });
      }

      // Only allow cancellation of pending or failed jobs
      if (![BlogAutoWriteJobStatus.PENDING, BlogAutoWriteJobStatus.FAILED].includes(job.status)) {
        return res.status(400).json({
          success: false,
          error: "Cannot cancel job in current status",
        });
      }

      // Return reserved credits if job is pending
      if (job.status === BlogAutoWriteJobStatus.PENDING && job.token_usage) {
        // Support both new credit structure and old token structure
        const reservedAmount = job.token_usage.reservedCredits || job.token_usage.reservedTokens || 0;

        if (reservedAmount > 0) {
          await cache.addons.decrementUsageCount(shopDomain, {
            addon: "AI_OPTIMIZER",
            decrementBy: reservedAmount,
          });
        }
      }

      // Update job status to cancelled
      await BlogAutoWriteService.updateJob(jobId, {
        status: BlogAutoWriteJobStatus.FAILED,
        error_message: "Cancelled by user",
      });

      res.json({
        success: true,
        message: "Job cancelled successfully",
      });
    } catch (error) {
      console.error("Error cancelling job:", error);
      res.status(500).json({
        success: false,
        error: error.message,
      });
    }
  }

  /**
   * Regenerate job with optional input overrides
   * POST /api/blog-auto-write/:jobId/regenerate
   * @note Validation is handled by validateRegenerateCredits middleware
   */
  async regenerateJob(req, res) {
    try {
      const { shopId, shop: shopDomain } = req.user;
      const { jobId } = req.params;

      // Data already validated and attached by middleware
      const { originalJob, targetArticle, mergedInputData, creditEstimation } = req;

      // Cancel original job if it's not already cancelled/failed
      let refundedCredits = 0;
      if (
        originalJob.status !== BlogAutoWriteJobStatus.CANCELLED &&
        originalJob.status !== BlogAutoWriteJobStatus.FAILED
      ) {
        // Return reserved credits if job had any
        if (originalJob.token_usage) {
          const reservedAmount = originalJob.token_usage.reservedCredits || originalJob.token_usage.reservedTokens || 0;
          if (reservedAmount > 0) {
            await cache.addons.decrementUsageCount(shopDomain, {
              addon: "AI_OPTIMIZER",
              decrementBy: reservedAmount,
            });
            refundedCredits = reservedAmount;
          }
        }

        // Update original job status to cancelled
        await BlogAutoWriteService.updateJob(jobId, {
          status: BlogAutoWriteJobStatus.CANCELLED,
          error_message: "Cancelled for regeneration",
        });
      }

      // Create new job with merged input data
      const result = await BlogAutoWriteService.createBlogAutoWriteJob({
        shopId,
        shopDomain,
        inputData: mergedInputData,
        targetArticleId: targetArticle.id, // Reuse same article
      });

      // Dispatch new job to queue
      await dispatchQueue({
        queueName: QUEUE_NAMES.BLOG_AUTO_WRITE_QUEUE,
        message: {
          jobId: result.job.id,
          shopDomain,
        },
      });

      // Get updated credit usage after new job creation
      const updatedCreditUsage = await BlogAutoWriteService.getCreditUsage(shopDomain);

      res.status(201).json({
        success: true,
        message: "Job regenerated successfully",
        data: {
          // Core fields (same structure as create job)
          jobId: result.job.id,
          articleId: targetArticle.id,
          status: result.job.status,
          progress: result.job.progress,
          estimatedCredits: creditEstimation.totalCredits,
          availableCredits: updatedCreditUsage.availableCredits,
          feature: creditEstimation.feature,
          featureName: creditEstimation.featureName,
          addonGroup: creditEstimation.addonGroup,

          // Regeneration-specific metadata
          regeneration: {
            cancelledJobId: jobId,
            refundedCredits: refundedCredits > 0 ? refundedCredits : undefined,
          },
        },
      });
    } catch (error) {
      console.error("Regenerate blog auto-write job error:", error);

      res.status(500).json({
        success: false,
        message: "Failed to regenerate blog auto-write job",
        error: {
          code: "REGENERATION_FAILED",
          details: error.message,
        },
      });
    }
  }
}

module.exports = new BlogAutoWriteController();

const { RABBIT_MQ_CONNECTION } = require("..");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const BaseQueue = require("./BaseQueue");
const logger = require("storeseo-logger");
const cache = require("../../cache");
const { dispatchQueue } = require("../queueDispatcher");
const ShopifyService = require("../../services/ShopifyService");
const BlogSerializer = require("../../serializers/BlogSerializer");
const { metafieldKeysFilterArray } = require("storeseo-enums/metafields");
const BlogService = require("../../services/BlogService");
const ArticleService = require("../../services/ArticleService");
const EventService = require("../../services/EventService");

class BlogSyncQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { shopId, shopDomain, shopUrl, session, cursor = null } = decodedMessage;

    try {
      // Check for API rate limiting
      if (await cache.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API")) {
        dispatchQueue({
          queueName: this.config.queueName,
          message: decodedMessage,
          ttl: this.throttledDelay,
        });
        return;
      }

      console.log(`[${this.config.queueName}] - Syncing blogs for shop: ${shopDomain}. Cursor: ${cursor}`);

      const { nextCursor, blogsProcessed } = await this.#syncBlogs({ shopId, shopDomain, shopUrl, session, cursor });

      if (nextCursor) {
        // Continue pagination - dispatch next batch
        dispatchQueue({
          queueName: this.config.queueName,
          message: {
            ...decodedMessage,
            cursor: nextCursor,
          },
        });
        console.log(
          `[${this.config.queueName}] - Processed ${blogsProcessed} blogs, continuing with cursor: ${nextCursor}`
        );
      } else {
        // Blog sync complete - cleanup and notify
        await BlogService.deleteNotSyncedBlogs(shopId);

        // Get total article count for the completion event (articles + blogs are synced together)
        const total = await ArticleService.count(shopId);

        console.log(`[${this.config.queueName}] - Blog sync complete for shop ${shopDomain}.`);

        // Trigger the final blog article sync complete event
        await EventService.handleBlogArticleSyncComplete({
          shop: shopDomain,
          total,
        });

        // Note: cache.blogSyncOngoing flag is already cleared by ArticleSyncQueue
        // This ensures users can retry sync even if blog sync fails

        console.log(
          `[${this.config.queueName}] - Complete blog article sync finished for shop ${shopDomain}. Total articles: ${total}`
        );
      }
    } catch (err) {
      if (this.isThrottled(err)) {
        // Re-dispatch for throttling
        await cache.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API", true, 90);
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.log(`[${shopDomain}] ${this.config.queueName} redispatched. Wait ${this.throttledDelay / 1000}s.`);
        return;
      }

      console.error(`[${this.config.queueName}] Critical error syncing blogs for shop ${shopDomain}:`, {
        error: err.message,
        cursor,
        shopId,
        queueName: this.config.queueName,
      });

      await this.handleError(err, message);

      logger.error(err, {
        description: `Critical failure in blog sync queue - unable to process blogs`,
        dispatchedMessage: decodedMessage,
        transaction: "BlogSync",
        domain: shopDomain,
        shopId,
        cursor,
        queueName: this.config.queueName,
        errorType: err.name || "Unknown",
        stack: err.stack,
      });
    } finally {
      channel.ack(message);
    }
  }

  /**
   * Sync blogs from Shopify
   * @param {Object} params - Sync parameters
   * @param {number} params.shopId - Shop ID
   * @param {string} params.shopDomain - Shop domain
   * @param {string} params.shopUrl - Shop URL
   * @param {Object} params.session - Shopify session
   * @param {string|null} params.cursor - Pagination cursor
   * @returns {Promise<{nextCursor: string | null, blogsProcessed: number}>}
   */
  async #syncBlogs({ shopId, shopDomain, shopUrl, session, cursor }) {
    // Note: shopUrl parameter is kept for consistency with other sync queues but not used in blog sync
    let blogsProcessed = 0;
    let blogsFailed = 0;

    try {
      console.log(`[BlogSync] Fetching blogs for shop ${shopDomain}, cursor: ${cursor || "initial"}`);

      // Fetch blogs from Shopify using the new onlineStore.getBlogs method
      const { blogs: shopifyBlogs, pageInfo } = await ShopifyService.onlineStore.getBlogs(session.shop, {
        first: 50, // Fetch 50 blogs per batch
        after: cursor,
        metafieldKeys: metafieldKeysFilterArray,
      });

      if (!shopifyBlogs || shopifyBlogs.length === 0) {
        console.log(`[BlogSync] No blogs found for shop ${shopDomain}`);
        return { nextCursor: null, blogsProcessed: 0 };
      }

      console.log(`[BlogSync] Retrieved ${shopifyBlogs.length} blogs from Shopify for shop ${shopDomain}`);

      // Process each blog with enhanced error handling
      for (let shopifyBlog of shopifyBlogs) {
        try {
          // Validate blog data
          if (!shopifyBlog || !shopifyBlog.id || !shopifyBlog.title) {
            console.warn(`[BlogSync] Skipping invalid blog data for shop ${shopDomain}:`, shopifyBlog);
            blogsFailed++;
            continue;
          }

          // Serialize blog data for database storage
          const serializedBlogData = BlogSerializer.serializeShopifyBlogData(shopId, shopifyBlog);

          // Validate serialized data
          if (!serializedBlogData || !serializedBlogData.blog_id) {
            console.warn(`[BlogSync] Skipping blog with invalid serialized data: ${shopifyBlog.id}`);
            blogsFailed++;
            continue;
          }

          // Save or update blog in database
          const savedBlog = await BlogService.saveOrUpdateBlog(serializedBlogData);

          if (savedBlog) {
            blogsProcessed++;
            console.log(`[BlogSync] ✓ Processed blog: "${shopifyBlog.title}" (ID: ${shopifyBlog.id})`);
          } else {
            console.warn(`[BlogSync] ⚠ Failed to save blog: "${shopifyBlog.title}" (ID: ${shopifyBlog.id})`);
            blogsFailed++;
          }
        } catch (blogError) {
          blogsFailed++;
          console.error(`[BlogSync] ✗ Error processing blog ${shopifyBlog.id} for shop ${shopDomain}:`, {
            error: blogError.message,
            blogTitle: shopifyBlog.title,
            shopDomain,
          });

          // Log detailed error for debugging
          logger.error(blogError, {
            description: `Failed to process individual blog during sync`,
            blogId: shopifyBlog.id,
            blogTitle: shopifyBlog.title,
            shopDomain,
            transaction: "BlogSync",
          });

          // Continue processing other blogs even if one fails
        }
      }

      const successRate = (blogsProcessed / (blogsProcessed + blogsFailed)) * 100;
      console.log(
        `[BlogSync] Batch complete for shop ${shopDomain}: ${blogsProcessed} processed, ${blogsFailed} failed (${successRate.toFixed(1)}% success rate)`
      );

      // Log warning if success rate is low
      if (successRate < 80 && blogsProcessed + blogsFailed > 0) {
        console.warn(
          `[BlogSync] Low success rate (${successRate.toFixed(1)}%) for shop ${shopDomain} - investigate potential issues`
        );
      }

      // Return pagination info
      return {
        nextCursor: pageInfo.hasNextPage ? pageInfo.endCursor : null,
        blogsProcessed,
      };
    } catch (error) {
      console.error(`[BlogSync] Critical error fetching blogs for shop ${shopDomain}:`, {
        error: error.message,
        cursor,
        blogsProcessed,
        blogsFailed,
      });

      // Log detailed error for debugging
      logger.error(error, {
        description: `Critical error during blog sync`,
        shopDomain,
        cursor,
        blogsProcessed,
        blogsFailed,
        transaction: "BlogSync",
      });

      throw error; // Re-throw to be handled by the main error handler
    }
  }
}

module.exports = new BlogSyncQueue(
  {
    queueName: QUEUE_NAMES.BLOG_SYNC,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.BLOG_SYNC],
  },
  RABBIT_MQ_CONNECTION
);

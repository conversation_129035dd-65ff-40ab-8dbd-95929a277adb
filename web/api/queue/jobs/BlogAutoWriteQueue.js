const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const { RABBIT_MQ_CONNECTION } = require("../index");
const { dispatchQueue } = require("../queueDispatcher");
const BlogAutoWriteService = require("../../services/BlogAutoWriteService");
const FeatureFlagService = require("../../services/FeatureFlagService");
const OpenAiService = require("../../services/openai/OpenAiService");
const CreditEstimationService = require("../../services/CreditEstimationService");
const ShopifyService = require("../../services/ShopifyService");
const ArticleService = require("../../services/ArticleService");
const ArticleMetaService = require("../../services/ArticleMetaService");
const AnalysisService = require("../../services/AnalysisService");
const ShopService = require("../../services/ShopService");
const cache = require("../../cache");
const { AI_OPTIMIZER } = require("storeseo-enums/subscriptionAddonGroup");
const { CONTENT } = require("storeseo-enums/aiContentTypes");
const { METAFIELD_KEYS } = require("storeseo-enums/metafields");
const BlogGenerationStatus = require("storeseo-enums/blogGenerationStatus");
const BlogAutoWriteJobStatus = require("storeseo-enums/blogAutoWrite/jobStatus");
const BlogAutoWriteJobSteps = require("storeseo-enums/blogAutoWrite/jobSteps");
const BlogType = require("storeseo-enums/blogAutoWrite/blogType");
const ToneType = require("storeseo-enums/blogAutoWrite/toneType");
const WordCountRange = require("storeseo-enums/blogAutoWrite/wordCountRange"); // Still used for default values
const { validateStepCompletion, getStatusForStep } = require("storeseo-enums/blogAutoWrite/jobSteps");

/**
 * Custom error class for job cancellation handling
 * Provides specific error identification for graceful cancellation processing
 */
class JobCancelledException extends Error {
  constructor(message, jobId = null) {
    super(message);
    this.name = "JobCancelledException";
    this.code = "JOB_CANCELLED";
    this.jobId = jobId;
    this.timestamp = new Date().toISOString();

    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, JobCancelledException);
    }
  }

  /**
   * Convert error to JSON for logging and debugging
   */
  toJSON() {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      jobId: this.jobId,
      timestamp: this.timestamp,
      stack: this.stack,
    };
  }

  /**
   * Check if an error is a JobCancelledException
   */
  static isJobCancelledException(error) {
    return error instanceof JobCancelledException || error.code === "JOB_CANCELLED";
  }
}

/**
 * Blog Auto-Write Queue
 * Handles AI blog generation with progressive article updates and real-time cancellation detection
 */
class BlogAutoWriteQueue extends BaseQueue {
  /**
   * Get the next step from job's actual steps (feature flag aware)
   * @param {Array} steps - Job steps array
   * @returns {string|null} Next step to process or null if all completed
   */
  getNextStepFromJob(steps) {
    // Find the first step that is not completed
    for (const stepData of steps) {
      if (!stepData.completed) {
        return stepData.step;
      }
    }
    return null; // All steps completed
  }

  /**
   * Lightweight method to check if a job has been cancelled
   * Uses minimal database query for performance optimization
   * @param {string} jobId - The job ID to check
   * @throws {JobCancelledException} If the job has been cancelled
   */
  async checkJobCancellation(jobId) {
    try {
      // Use BlogAutoWriteService to get job with minimal data (internal method)
      const job = await BlogAutoWriteService.getJobByIdInternal(jobId);

      if (!job) {
        throw new Error(`Job ${jobId} not found`);
      }

      if (job.status === BlogAutoWriteJobStatus.CANCELLED) {
        throw new JobCancelledException(`Job ${jobId} has been cancelled by user`, jobId);
      }

      // Job is active, continue processing
      return true;
    } catch (error) {
      // Re-throw JobCancelledException as-is
      if (JobCancelledException.isJobCancelledException(error)) {
        throw error;
      }

      // Handle other database errors
      console.error(`Error checking job cancellation for ${jobId}:`, error);
      throw new Error(`Failed to check job cancellation status: ${error.message}`);
    }
  }

  /**
   * Analyze job steps and determine the current processing state
   * @param {Object} job - The job object with steps array
   * @returns {Object} Current step analysis with resume information
   */
  getJobCurrentStep(job) {
    try {
      if (!job || !job.steps || !Array.isArray(job.steps)) {
        return {
          canResume: false,
          error: "Job steps data is missing or invalid",
          nextStep: null,
          lastCompletedStep: null,
          completedSteps: 0,
        };
      }

      // Validate step completion order (skip validation when feature flag creates filtered steps)
      if (FeatureFlagService.blogs.autoWrite.isImageGenerationEnabled()) {
        // Feature enabled: Use original validation (expects all 9 steps)
        const validation = validateStepCompletion(job.steps);
        if (!validation.isValid) {
          return {
            canResume: false,
            error: `Invalid step completion order: ${validation.errors.join(", ")}`,
            nextStep: null,
            lastCompletedStep: null,
            completedSteps: 0,
          };
        }
      } else {
        // Feature disabled: Skip validation since job has filtered steps
        // The job was created with only enabled steps, so validation against all steps would fail
        console.info(`[${job.id}] Skipping step validation - job created with filtered steps due to feature flag`);
      }

      // Find the next step to process (use job's actual steps, not hardcoded orderedSteps)
      const nextStep = this.getNextStepFromJob(job.steps);

      // Find the last completed step
      const completedSteps = job.steps.filter((step) => step.completed);
      const lastCompletedStep = completedSteps.length > 0 ? completedSteps[completedSteps.length - 1] : null;

      return {
        canResume: true,
        error: null,
        nextStep: nextStep,
        lastCompletedStep: lastCompletedStep?.step || null,
        completedSteps: completedSteps.length,
        totalSteps: job.steps.length, // Use actual job steps count (feature flag aware)
        isComplete: nextStep === null,
        resumeFromStep: nextStep,
      };
    } catch (error) {
      console.error("Error analyzing job current step:", error);
      return {
        canResume: false,
        error: `Failed to analyze job steps: ${error.message}`,
        nextStep: null,
        lastCompletedStep: null,
        completedSteps: 0,
      };
    }
  }

  /**
   * Validate if a job can be safely resumed from a specific step
   * @param {Object} job - The job object with steps and status
   * @param {string} targetStep - The step to resume from
   * @returns {Object} Validation result with resume eligibility
   */
  canResumeFromStep(job, targetStep) {
    try {
      // Check if job exists and has valid structure
      if (!job) {
        return {
          canResume: false,
          error: "Job not found",
          reason: "MISSING_JOB",
        };
      }

      // Check if job is in a resumable status
      const resumableStatuses = [
        BlogAutoWriteJobStatus.FAILED,
        BlogAutoWriteJobStatus.PENDING,
        // Add other resumable statuses as needed
      ];

      if (!resumableStatuses.includes(job.status)) {
        return {
          canResume: false,
          error: `Job status '${job.status}' is not resumable`,
          reason: "INVALID_STATUS",
        };
      }

      // Check if target step is valid (use job's actual steps, not hardcoded orderedSteps)
      const jobStepNames = job.steps.map((s) => s.step);
      if (!targetStep || !jobStepNames.includes(targetStep)) {
        return {
          canResume: false,
          error: `Invalid target step: ${targetStep}`,
          reason: "INVALID_STEP",
        };
      }

      // Analyze current job state
      const currentState = this.getJobCurrentStep(job);
      if (!currentState.canResume) {
        return {
          canResume: false,
          error: currentState.error,
          reason: "INVALID_STEP_STATE",
        };
      }

      // Check if target step is the expected next step
      if (currentState.nextStep !== targetStep) {
        return {
          canResume: false,
          error: `Target step '${targetStep}' does not match expected next step '${currentState.nextStep}'`,
          reason: "STEP_MISMATCH",
        };
      }

      // Check if job has been cancelled
      if (job.status === BlogAutoWriteJobStatus.CANCELLED) {
        return {
          canResume: false,
          error: "Cannot resume cancelled job",
          reason: "JOB_CANCELLED",
        };
      }

      // All validations passed
      return {
        canResume: true,
        error: null,
        reason: null,
        targetStep: targetStep,
        currentState: currentState,
      };
    } catch (error) {
      console.error("Error validating job resumption:", error);
      return {
        canResume: false,
        error: `Validation failed: ${error.message}`,
        reason: "VALIDATION_ERROR",
      };
    }
  }

  /**
   * Resume job processing from a specific step
   * Main resumption logic that restarts processing from the last incomplete step
   * @param {string} jobId - The job ID to resume
   * @param {string} shopDomain - The shop domain
   * @param {string} fromStep - Optional specific step to resume from
   * @returns {Promise<Object>} Resumption result
   */
  async resumeJobFromStep(jobId, shopDomain, fromStep = null) {
    try {
      console.info(`[${jobId}] Attempting to resume job for shop ${shopDomain}`);

      // Get the current job state
      const job = await BlogAutoWriteService.getJobById(jobId);
      if (!job) {
        throw new Error(`Job ${jobId} not found`);
      }

      // Analyze current job state
      const currentState = this.getJobCurrentStep(job);
      if (!currentState.canResume) {
        throw new Error(`Job cannot be resumed: ${currentState.error}`);
      }

      // Determine the step to resume from
      const targetStep = fromStep || currentState.resumeFromStep;
      if (!targetStep) {
        console.info(`[${jobId}] Job is already complete, no resumption needed`);
        return {
          success: true,
          resumed: false,
          reason: "Job already complete",
          currentState,
        };
      }

      // Validate resumption eligibility
      const validation = this.canResumeFromStep(job, targetStep);
      if (!validation.canResume) {
        throw new Error(`Cannot resume from step ${targetStep}: ${validation.error}`);
      }

      console.info(
        `[${jobId}] Resuming from step: ${targetStep} (completed: ${currentState.completedSteps}/${currentState.totalSteps})`
      );

      // Update job status to indicate resumption
      await BlogAutoWriteService.updateJob(jobId, { status: BlogAutoWriteJobStatus.PENDING });

      // TODO: Implement actual step processing logic here
      // This will be implemented when we add individual step implementations
      console.info(
        `[${jobId}] Job resumption prepared successfully. Actual processing will be implemented in step implementations.`
      );

      return {
        success: true,
        resumed: true,
        resumedFromStep: targetStep,
        previouslyCompleted: currentState.completedSteps,
        totalSteps: currentState.totalSteps,
        currentState,
      };
    } catch (error) {
      console.error(`[${jobId}] Failed to resume job:`, error);

      // Update job status back to failed if resumption fails
      try {
        await BlogAutoWriteService.updateJob(jobId, {
          status: BlogAutoWriteJobStatus.FAILED,
          error_message: `Resumption failed: ${error.message}`,
        });
      } catch (statusUpdateError) {
        console.error(`[${jobId}] Failed to update job status after resumption failure:`, statusUpdateError);
      }

      return {
        success: false,
        resumed: false,
        error: error.message,
        reason: "RESUMPTION_FAILED",
      };
    }
  }

  /**
   * Mark a step as started with timestamp tracking
   * @param {string} jobId - The job ID
   * @param {string} stepName - The step being started
   * @returns {Promise<void>}
   */
  async markStepAsStarted(jobId, stepName) {
    try {
      const job = await BlogAutoWriteService.getJobByIdInternal(jobId);
      if (!job || !job.steps) {
        throw new Error(`Job ${jobId} or steps not found`);
      }

      // Check if job was cancelled before updating steps
      if (job.status === BlogAutoWriteJobStatus.CANCELLED) {
        console.info(`[${jobId}] Job was cancelled, skipping step start tracking`);
        throw new JobCancelledException(`Job ${jobId} was cancelled before step start`, jobId);
      }

      // Update the specific step with start timestamp
      const updatedSteps = job.steps.map((step) => {
        if (step.step === stepName) {
          return {
            ...step,
            startedAt: new Date().toISOString(),
            error: null, // Clear any previous errors
          };
        }
        return step;
      });

      // Update job with step start tracking
      await BlogAutoWriteService.updateJob(jobId, { steps: updatedSteps });
      console.info(`[${jobId}] Step ${stepName} marked as started`);
    } catch (error) {
      console.error(`[${jobId}] Failed to mark step ${stepName} as started:`, error);
      // Don't throw here - step tracking shouldn't break the main flow
    }
  }

  /**
   * Execute Step 1: Content Generation
   * Generates blog content using OpenAI API with error handling and basic credit tracking
   * @param {string} jobId - The job ID
   * @param {Object} job - The job object with input data
   * @param {string} shopDomain - The shop domain
   * @returns {Promise<Object>} Content generation result
   */
  async executeContentGeneration(jobId, job, shopDomain) {
    try {
      console.info(`[${jobId}] Starting content generation for topic: ${job.input_data?.topic}`);

      // Validate input data
      if (!job.input_data || !job.input_data.topic) {
        throw new Error("Missing required input data: topic");
      }

      const inputData = job.input_data;

      // Prepare OpenAI generation parameters
      const generationParams = {
        topic: inputData.topic,
        keyword: inputData.keyword || "",
        blogType: inputData.blogType || BlogType.GUIDE,
        wordCount: inputData.wordCount || WordCountRange.RANGE_500_800,
        tone: inputData.tone || ToneType.FORMAL,
        customInstructions: inputData.customInstructions || "",
        language: inputData.language || "English",
      };

      console.info(`[${jobId}] Calling OpenAI with params:`, {
        topic: generationParams.topic,
        blogType: generationParams.blogType,
        wordCount: generationParams.wordCount,
        tone: generationParams.tone,
      });

      // Call OpenAI service for content generation
      const generationResult = await OpenAiService.generateBlogContent(generationParams);

      if (!generationResult || !generationResult.output) {
        throw new Error("OpenAI service returned invalid response");
      }

      const { output, usage } = generationResult;

      // Validate generated content quality
      const validation = this.validateGeneratedContent(output);
      if (!validation.isValid) {
        throw new Error(`Generated content validation failed: ${validation.errors.join(", ")}`);
      }

      // Calculate actual credit usage using CreditEstimationService (centralized credit logic)
      const creditsUsed = CreditEstimationService.calculateCreditUsage(usage, CONTENT);
      console.info(`[${jobId}] Content generation completed. Credits used: ${creditsUsed}`);

      // Note: Credit usage tracked in job data only
      // Final credit settlement (estimated vs actual) happens at job completion/cancellation/failure
      // This avoids multiple cache updates and provides better user experience

      // Store generated content in step result for use in Shopify draft creation step
      // Don't update dummy article yet - that happens in the draft creation step
      console.info(`[${jobId}] Content generated and stored in job step data`);

      return {
        success: true,
        usage: usage,
        creditsUsed: creditsUsed,
        stepResult: {
          // Metadata for tracking and validation
          contentGenerated: true,
          wordCount: output.content?.length || 0,
          hasTitle: !!output.title,
          hasContent: !!output.content,
          hasMetaData: !!(output.meta_title && output.meta_description),

          // Generated content for next step (single source of truth)
          content: {
            title: output.title,
            body: output.content, // Use 'body' to match Shopify GraphQL field name
            meta_title: output.meta_title,
            meta_description: output.meta_description,
            tags: output.tags || [],
            focus_keyword: output.focus_keyword || "",
          },
        },
      };
    } catch (error) {
      console.error(`[${jobId}] Content generation failed:`, error);
      throw new Error(`Content generation failed: ${error.message}`);
    }
  }

  /**
   * Validate generated content quality and completeness
   * @param {Object} content - Generated content from OpenAI
   * @returns {Object} Validation result
   */
  validateGeneratedContent(content) {
    const errors = [];

    try {
      // Check required fields
      if (!content.title || typeof content.title !== "string" || content.title.trim().length === 0) {
        errors.push("Missing or invalid title");
      }

      if (!content.content || typeof content.content !== "string" || content.content.trim().length === 0) {
        errors.push("Missing or invalid content");
      }

      // Note: Content length validation removed to allow variance during initial development
      // Can be re-added later if needed with more refined tolerance levels

      // Check SEO fields if they should be present
      if (!content.meta_title || content.meta_title.trim().length === 0) {
        errors.push("Missing meta title");
      }

      if (!content.meta_description || content.meta_description.trim().length === 0) {
        errors.push("Missing meta description");
      }

      // Note: SEO length validation and keyword inclusion validation removed for flexibility
      // Just checking field presence is sufficient during initial development
      // Can be re-added later with more refined rules based on real usage data

      return {
        isValid: errors.length === 0,
        errors: errors,
        warnings: [], // Could add warnings for non-critical issues
        stats: {
          titleLength: content.title?.length || 0,
          contentWordCount: content.content ? content.content.split(/\s+/).length : 0,
          metaTitleLength: content.meta_title?.length || 0,
          metaDescriptionLength: content.meta_description?.length || 0,
        },
      };
    } catch (error) {
      console.error("Error during content validation:", error);
      return {
        isValid: false,
        errors: [`Validation error: ${error.message}`],
        warnings: [],
        stats: {},
      };
    }
  }

  /**
   * Execute Step 2: Shopify Draft Creation
   * Retrieves content from Step 1, creates Shopify draft, and sets meta fields
   * @param {string} jobId - The job ID
   * @param {Object} job - The job object with steps data
   * @param {string} shopDomain - The shop domain
   * @returns {Promise<Object>} Shopify draft creation result
   */
  async executeShopifyDraftCreation(jobId, job, shopDomain) {
    try {
      console.info(`[${jobId}] Starting Shopify draft creation for shop: ${shopDomain}`);

      // Step 2.1: Retrieve generated content from Step 1 job data
      const contentGenerationStep = job.steps.find((step) => step.step === BlogAutoWriteJobSteps.CONTENT_GENERATION);
      if (!contentGenerationStep || !contentGenerationStep.completed || !contentGenerationStep.result) {
        throw new Error("Content generation step not completed or missing result data");
      }

      const generatedContent = contentGenerationStep.result.stepResult?.content;
      if (!generatedContent) {
        throw new Error("Generated content not found in Step 1 result data");
      }

      console.info(`[${jobId}] Retrieved content from Step 1: title="${generatedContent.title}"`);

      // Step 2.2: Create blog handle from title
      const blogHandle = this.createBlogHandle(generatedContent.title);
      console.info(`[${jobId}] Generated blog handle: "${blogHandle}"`);

      // Step 2.3: Get target blog information (from job input data or fallback to default)
      const BlogService = require("../../services/BlogService");
      let blog;

      if (job.input_data?.targetBlog) {
        // Use the selected blog from job input
        blog = await BlogService.getBlog(job.shop_id, parseInt(job.input_data.targetBlog));
        if (!blog) {
          console.warn(`[${jobId}] Selected blog ${job.input_data.targetBlog} not found, falling back to default blog`);
          blog = await BlogService.getDefaultBlog(job.shop_id);
        }
      } else {
        // Fallback to default blog for backward compatibility
        blog = await BlogService.getDefaultBlog(job.shop_id);
      }

      if (!blog) {
        throw new Error("No target blog found for article creation");
      }

      console.info(`[${jobId}] Using blog: "${blog.title}" (ID: ${blog.id}, Shopify ID: ${blog.blog_id})`);

      // Verify the blog is synced
      if (!blog.is_synced) {
        throw new Error(`Target blog "${blog.title}" is not synced. Please sync the blog first.`);
      }

      // Check if this is a regeneration scenario (article has positive ID = already exists in Shopify)
      const ArticleService = require("../../services/ArticleService");
      const currentArticle = await ArticleService.getArticle(job.shop_id, job.article_id);
      if (!currentArticle) {
        throw new Error("Article not found in database");
      }

      const isRegenerationScenario = currentArticle.article_id > 0; // Positive ID means Shopify article exists
      console.info(
        `[${jobId}] Article scenario: ${isRegenerationScenario ? "UPDATE existing Shopify article" : "CREATE new Shopify article"} (article_id: ${currentArticle.article_id})`
      );

      // Step 2.4: Create or update Shopify draft article WITHOUT meta title/description
      const articleInput = {
        blogId: `gid://shopify/Blog/${blog.blog_id}`,
        title: generatedContent.title,
        author: {
          name: "StoreSEO AI", // Default author name for AI-generated content
        },
        body: generatedContent.body,
        handle: blogHandle,
        tags: Array.isArray(generatedContent.tags) ? generatedContent.tags : [],
        isPublished: false, // Create as draft
        // Explicitly exclude meta_title and meta_description - will be set via metafields
      };

      let blogCreationResponse;

      if (isRegenerationScenario) {
        // Update existing Shopify article
        console.info(
          `[${jobId}] Updating existing Shopify article...`,
          JSON.stringify(
            {
              id: currentArticle.shopify_gql_id,
              article: articleInput,
              metafieldKeys: [], // We'll set metafields separately
            },
            null,
            2
          )
        );

        blogCreationResponse = await ShopifyService.onlineStore.updateArticle(shopDomain, {
          id: currentArticle.shopify_gql_id,
          article: articleInput,
          metafieldKeys: [], // We'll set metafields separately
        });

        if (!blogCreationResponse) {
          throw new Error("Failed to update existing Shopify article - invalid response");
        }
      } else {
        // Create new Shopify draft article
        console.info(
          `[${jobId}] Creating new Shopify draft article...`,
          JSON.stringify(
            {
              article: articleInput,
              metafieldKeys: [], // We'll set metafields separately
            },
            null,
            2
          )
        );

        blogCreationResponse = await ShopifyService.onlineStore.createArticle(shopDomain, {
          article: articleInput,
          metafieldKeys: [], // We'll set metafields separately
        });

        if (!blogCreationResponse) {
          throw new Error("Failed to create new Shopify draft article - invalid response");
        }
      }

      // Extract ID from GraphQL ID format (gid://shopify/Article/123456789)
      const shopifyArticleGqlId = blogCreationResponse.id;
      const shopifyArticleId = shopifyArticleGqlId.split("/").pop();
      const shopifyHandle = blogCreationResponse.handle;
      console.info(
        `[${jobId}] Shopify article ${isRegenerationScenario ? "updated" : "created"} successfully: GQL ID=${shopifyArticleGqlId}, handle="${shopifyHandle}"`
      );

      // Step 2.5: Set meta title and description via Shopify metafield API
      console.info(`[${jobId}] Setting meta fields via Shopify API...`);

      const metafieldsToSet = [
        {
          ownerId: shopifyArticleGqlId,
          key: METAFIELD_KEYS.TITLE_TAG, // Using enum key
          value: generatedContent.meta_title,
        },
        {
          ownerId: shopifyArticleGqlId,
          key: METAFIELD_KEYS.DESCRIPTION_TAG, // Using enum key
          value: generatedContent.meta_description,
        },
      ];

      const metafieldResponse = await ShopifyService.setMetafields(shopDomain, metafieldsToSet);
      console.info(`[${jobId}] Meta fields set successfully`);

      // Step 2.6: Return comprehensive step result (Article update will be handled in Step 3)
      return {
        success: true,
        creditsUsed: 0, // No credits used for Shopify operations
        usage: {}, // No AI usage for this step
        stepResult: {
          draftCreated: true,
          shopifyArticleId: shopifyArticleId,
          shopifyArticleGqlId: shopifyArticleGqlId,
          articleHandle: shopifyHandle,
          blogId: blog.blog_id,
          blogCreationResponse: blogCreationResponse,
          metaFieldResponse: metafieldResponse,
        },
      };
    } catch (error) {
      console.error(`[${jobId}] Shopify draft creation failed:`, error);
      throw new Error(`Shopify draft creation failed: ${error.message}`);
    }
  }

  /**
   * Execute Step 3: Article Linking
   * Links the dummy article with the Shopify draft created in Step 2
   * @param {string} jobId - The job ID
   * @param {Object} job - The job data
   * @param {string} shopDomain - The shop domain
   * @returns {Promise<Object>} Article linking result
   */
  async executeArticleLinking(jobId, job, shopDomain) {
    try {
      console.info(`[${jobId}] Starting Step 3: Article Linking`);

      // Step 3.1: Retrieve Step 2 data
      const shopifyDraftStep = job.steps.find((step) => step.step === BlogAutoWriteJobSteps.SHOPIFY_DRAFT_CREATION);

      if (!shopifyDraftStep || !shopifyDraftStep.completed) {
        throw new Error("Shopify draft creation step not completed");
      }

      const step2Result = shopifyDraftStep.result.stepResult;
      if (!step2Result || !step2Result.draftCreated) {
        throw new Error("Shopify draft creation failed - no valid result data");
      }

      console.info(
        `[${jobId}] Retrieved Step 2 data: Article ID=${step2Result.shopifyArticleId}, Handle="${step2Result.articleHandle}"`
      );

      // Step 3.2: Validate Step 2 data
      const requiredFields = ["shopifyArticleId", "shopifyArticleGqlId", "articleHandle", "blogId"];
      const missingFields = requiredFields.filter((field) => !step2Result[field]);

      if (missingFields.length > 0) {
        throw new Error(`Missing required Step 2 data: ${missingFields.join(", ")}`);
      }

      // Step 3.3: Get content from Step 1 for complete article update
      const contentGenerationStep = job.steps.find((step) => step.step === BlogAutoWriteJobSteps.CONTENT_GENERATION);
      if (!contentGenerationStep || !contentGenerationStep.completed) {
        throw new Error("Content generation step not completed");
      }

      const generatedContent = contentGenerationStep.result.stepResult?.content;
      if (!generatedContent) {
        throw new Error("Generated content not found in Step 1 result");
      }

      console.info(`[${jobId}] Updating article in database with complete content and Shopify data...`);

      const articleUpdate = {
        // Shopify data from Step 2
        article_id: step2Result.shopifyArticleId,
        handle: step2Result.articleHandle,
        shopify_blog_id: step2Result.blogId,
        author: step2Result.blogCreationResponse?.author?.name || "StoreSEO AI",

        // Content data from Step 1
        title: generatedContent.title,
        body_html: generatedContent.body,
        focus_keyword: generatedContent.focus_keyword,
        tags: generatedContent.tags,

        // Status and sync data
        generation_status: BlogGenerationStatus.DRAFT_CREATED,
        is_synced: true,
        updated_at: new Date(),
        synced_at: new Date(),
      };

      await ArticleService.updateArticle(job.article_id, articleUpdate);
      console.info(`[${jobId}] Article updated successfully with Shopify data`);

      // Step 3.4: Process metafield data after article update
      const metaFieldResponse = step2Result.metaFieldResponse;
      let metafieldSyncStatus = "none";
      let savedMetafields = [];

      if (metaFieldResponse && metaFieldResponse.metafieldsSet && metaFieldResponse.metafieldsSet.metafields) {
        console.info(`[${jobId}] Processing metafield data...`);

        const metafields = metaFieldResponse.metafieldsSet.metafields;
        console.info(`[${jobId}] Found ${metafields.length} metafields to process`);

        // Get the updated article to get blog_id for metafield insertion
        const updatedArticle = await ArticleService.getArticle(job.shop_id, job.article_id);
        if (!updatedArticle) {
          throw new Error("Updated article not found for metafield processing");
        }

        // Delete existing metafields for this article (clean slate)
        await ArticleMetaService.deleteMeta(job.article_id);
        console.info(`[${jobId}] Cleared existing metafields for article`);

        // Process metafields to extract numeric IDs
        const processedMetafields = metafields.map((metafield) => ({
          ...metafield,
          id: metafield.id.split("/").pop(), // Extract numeric portion from GraphQL ID
        }));

        console.info(`[${jobId}] Processed ${processedMetafields.length} metafields with numeric IDs`);

        // Insert metafields into article_metas table
        savedMetafields = await ArticleMetaService.upsertMetas(
          job.shop_id,
          updatedArticle.blog_id,
          job.article_id,
          processedMetafields
        );

        console.info(`[${jobId}] Saved ${savedMetafields.length} metafields to database`);
        metafieldSyncStatus = savedMetafields.length > 0 ? "complete" : "partial";

        // Update article with metafield sync status
        await ArticleService.updateArticle(job.article_id, {
          metafield_sync_status: metafieldSyncStatus,
        });
        console.info(`[${jobId}] Updated article metafield sync status: ${metafieldSyncStatus}`);
      } else {
        console.warn(`[${jobId}] No metafield data found in Step 2 result`);
      }

      // Step 3.5: Validate linking
      const updatedArticle = await ArticleService.getArticle(job.shop_id, job.article_id);
      if (!updatedArticle || updatedArticle.article_id !== step2Result.shopifyArticleId) {
        throw new Error("Article linking validation failed - data mismatch");
      }

      console.info(`[${jobId}] Article linking validation successful`);

      // Step 3.6: Create step result
      return {
        success: true,
        creditsUsed: 0, // No credits used for article linking
        usage: {}, // No AI usage for this step
        stepResult: {
          articleLinked: true,
          metafieldsProcessed: metafieldSyncStatus !== "none",

          // Article data (Shopify + Content)
          articleId: job.article_id,
          shopifyArticleId: step2Result.shopifyArticleId,
          shopifyArticleGqlId: step2Result.shopifyArticleGqlId,
          articleHandle: step2Result.articleHandle,

          // Content data stored
          title: generatedContent.title,
          bodyHtml: generatedContent.body,
          focusKeyword: generatedContent.focus_keyword,
          tags: generatedContent.tags,
          author: step2Result.blogCreationResponse?.author?.name || "StoreSEO AI",

          // Metafield data
          metafieldsCount: savedMetafields.length,
          metafieldSyncStatus: metafieldSyncStatus,
          savedMetafields: savedMetafields.map((m) => ({
            id: m.id,
            gql_id: m.gql_id,
            key: m.key,
            namespace: m.namespace,
            value: m.value,
            type: m.type,
          })),

          // Status tracking
          generationStatus: BlogGenerationStatus.DRAFT_CREATED,
          isSynced: true,
          linkingTimestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      console.error(`[${jobId}] Article linking failed:`, error);
      throw new Error(`Article linking failed: ${error.message}`);
    }
  }

  /**
   * Execute Step 4: SEO Analysis
   * Performs comprehensive SEO analysis on the generated content and stores results
   * @param {string} jobId - The job ID
   * @param {Object} job - The job object
   * @param {string} shopDomain - The shop domain
   * @returns {Promise<Object>} SEO analysis result
   */
  async executeSeoAnalysis(jobId, job, shopDomain) {
    try {
      console.info(`[${jobId}] Starting Step 4: SEO Analysis`);

      // Step 4.1: Retrieve article data for analysis
      console.info(`[${jobId}] Retrieving article data for SEO analysis...`);

      const article = await ArticleService.getArticle(job.shop_id, job.article_id);
      if (!article) {
        throw new Error("Article not found for SEO analysis");
      }

      // Validate that article has the required content for analysis
      if (!article.title || !article.body_html || !article.focus_keyword) {
        throw new Error("Article missing required content for SEO analysis (title, body_html, or focus_keyword)");
      }

      console.info(
        `[${jobId}] Article data retrieved: title="${article.title}", focus_keyword="${article.focus_keyword}"`
      );

      // Step 4.2: Perform SEO analysis and store results using existing AnalysisService
      console.info(`[${jobId}] Performing comprehensive SEO analysis and storing results...`);

      const updatedArticle = await AnalysisService.analyseEachArticle({
        shopId: job.shop_id,
        article: article,
        shopURL: `https://${shopDomain}`,
      });

      console.info(
        `[${jobId}] SEO analysis completed and stored: score=${updatedArticle.score}, issues=${updatedArticle.issues}, passed=${updatedArticle.passed}`
      );

      // Step 4.3: Create step result
      return {
        success: true,
        creditsUsed: 0, // No credits used for SEO analysis
        usage: {}, // No AI usage for this step
        stepResult: {
          analysisCompleted: true,

          // Article data
          articleId: job.article_id,
          articleTitle: article.title,
          focusKeyword: article.focus_keyword,

          // SEO analysis results
          seoScore: updatedArticle.score,
          totalIssues: updatedArticle.issues,
          totalPassed: updatedArticle.passed,
          isOptimized: updatedArticle.is_optimized,
          isAnalysed: updatedArticle.is_analysed,

          // Status tracking
          analysisTimestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      console.error(`[${jobId}] SEO analysis failed:`, error);
      throw new Error(`SEO analysis failed: ${error.message}`);
    }
  }

  /**
   * Execute Step 5: Image Generation
   * Conditionally generates featured image using DALL-E and tracks credit usage
   * @param {string} jobId - The job ID
   * @param {Object} job - The job object
   * @param {string} shopDomain - The shop domain
   * @returns {Promise<Object>} Image generation result
   */
  async executeImageGeneration(jobId, job, shopDomain) {
    try {
      console.info(`[${jobId}] Starting Step 5: Image Generation`);

      // Step 5.1: Check generation condition
      const generateFeaturedImage = job.input_data?.generateFeaturedImage;
      console.info(`[${jobId}] Image generation flag: ${generateFeaturedImage}`);

      if (!generateFeaturedImage) {
        console.info(`[${jobId}] Image generation skipped - disabled by user`);
        return {
          success: true,
          skipped: true,
          creditsUsed: 0,
          stepResult: {
            imageGenerated: false,
            reason: "Image generation disabled by user",
          },
        };
      }

      // Step 5.2: Get article data and create image prompt
      console.info(`[${jobId}] Retrieving article data for image generation...`);

      const article = await ArticleService.getArticle(job.shop_id, job.article_id);
      if (!article) {
        throw new Error("Article not found for image generation");
      }

      // Get content from Step 1 for better prompt creation
      const contentGenerationStep = job.steps.find((step) => step.step === BlogAutoWriteJobSteps.CONTENT_GENERATION);
      const generatedContent = contentGenerationStep?.result?.stepResult?.content;

      const imagePrompt = this.createImagePrompt({
        title: article.title,
        focusKeyword: article.focus_keyword,
        topic: job.input_data?.topic,
        blogType: job.input_data?.blogType || "Guide",
        featuredImageDescription: job.input_data?.featuredImageDescription || generatedContent?.meta_description,
      });

      console.info(`[${jobId}] Created image prompt: "${imagePrompt}"`);

      // Step 5.3: Generate image using OpenAI DALL-E
      console.info(`[${jobId}] Generating image with DALL-E...`);

      const imageResult = await OpenAiService.generateBlogImage({
        featuredImageDescription: imagePrompt,
        topic: job.input_data?.topic,
        blogType: job.input_data?.blogType || "Guide",
        size: "1792x1024",
        quality: "standard",
      });

      console.info(`[${jobId}] Image generated successfully: ${imageResult.imageUrl}`);

      // Step 5.4: Calculate credit costs using proper DALL-E image generation logic
      console.info(`[${jobId}] Calculating credit costs...`);

      // Use the correct blog image credit estimation (not alt-text generation)
      const imageCredits = CreditEstimationService.estimateBlogImageCredits({
        generateFeaturedImage: true,
        featuredImageDescription: imagePrompt,
      });

      const creditsUsed = imageCredits.userCredits;
      console.info(
        `[${jobId}] Image generation completed. Credits used: ${creditsUsed} (DALL-E cost: $${imageCredits.dalleActualCost})`
      );

      // Create usage object for consistency with Step 1 pattern
      const imageUsage = {
        prompt_tokens: Math.ceil(imagePrompt.length / 4), // Approximate
        completion_tokens: 0, // Images don't have completion tokens
        total_tokens: Math.ceil(imagePrompt.length / 4),
        total_requests: 1,
        dalle_cost: imageCredits.dalleActualCost,
      };

      // Note: Credit usage tracked in job data only
      // Final credit settlement (estimated vs actual) happens at job completion/cancellation/failure
      // This avoids multiple cache updates and provides better user experience

      // Step 5.6: Prepare image data for next step
      console.info(`[${jobId}] Preparing image data for article linking...`);

      const imageAltText = `${article.title} - ${article.focus_keyword}`;

      console.info(`[${jobId}] Image data prepared for next step`);

      // Step 5.7: Create step result
      return {
        success: true,
        usage: imageUsage,
        creditsUsed: creditsUsed,
        stepResult: {
          // Metadata for tracking and validation
          imageGenerated: true,

          // Generated image data (for potential future steps)
          imageUrl: imageResult.imageUrl,
          imageAlt: imageAltText,
          imagePrompt,
          revisedPrompt: imageResult.revisedPrompt,
        },
      };
    } catch (error) {
      console.error(`[${jobId}] Image generation failed:`, error);
      throw new Error(`Image generation failed: ${error.message}`);
    }
  }

  /**
   * Execute Step 6: Shopify Article Image Update
   * Updates Shopify article with generated image from Step 5
   * @param {string} jobId - The job ID
   * @param {Object} job - The job object
   * @param {string} shopDomain - The shop domain
   * @returns {Promise<Object>} Shopify image update result
   */
  async executeShopifyImageUpdate(jobId, job, shopDomain) {
    try {
      console.info(`[${jobId}] Starting Step 6: Shopify Article Image Update`);

      // Step 6.1: Get image data from Step 5 result
      const step5Result = job.steps.find((step) => step.step === BlogAutoWriteJobSteps.IMAGE_GENERATION);

      if (!step5Result || !step5Result.result) {
        throw new Error("Step 5 (Image Generation) result not found");
      }

      // Check if Step 5 was skipped
      if (step5Result.result.skipped) {
        console.info(`[${jobId}] Image update skipped - no image was generated in Step 5`);
        return {
          success: true,
          skipped: true,
          stepResult: {
            imageUpdated: false,
            reason: "No image generated in Step 5",
          },
        };
      }

      // Get image data from Step 5
      const imageData = step5Result.result.stepResult;
      if (!imageData.imageGenerated || !imageData.imageUrl) {
        throw new Error("Invalid image data from Step 5 - missing imageUrl");
      }

      console.info(`[${jobId}] Retrieved image data from Step 5: ${imageData.imageUrl}`);

      // Step 6.2: Get Shopify article ID from previous steps
      const step2Result = job.steps.find((step) => step.step === BlogAutoWriteJobSteps.SHOPIFY_DRAFT_CREATION);
      if (!step2Result || !step2Result.result?.stepResult?.shopifyArticleGqlId) {
        throw new Error("Shopify article ID not found from Step 2");
      }

      const shopifyArticleGqlId = step2Result.result.stepResult.shopifyArticleGqlId;
      console.info(`[${jobId}] Using Shopify article ID: ${shopifyArticleGqlId}`);

      // Step 6.3: Update Shopify article with image
      console.info(`[${jobId}] Updating Shopify article with image...`);

      const updateData = {
        image: {
          url: imageData.imageUrl,
          altText: imageData.imageAlt,
        },
      };

      const shopifyResponse = await ShopifyService.onlineStore.updateArticle(shopDomain, {
        id: shopifyArticleGqlId,
        article: updateData,
        metafieldKeys: [],
      });

      console.info(`[${jobId}] Shopify article updated successfully with image`);

      // Step 6.4: Extract featured image data from Shopify response (using serialization logic)
      const featuredImageData = shopifyResponse.image
        ? {
            media_id: shopifyResponse.image.id.replace(/.*\//gim, ""),
            src: shopifyResponse.image.url,
            alt_text: shopifyResponse.image.altText,
          }
        : null;

      console.info(`[${jobId}] Featured image data extracted:`, featuredImageData);

      // Step 6.5: Create step result
      return {
        success: true,
        creditsUsed: 0, // No credits used for Shopify operations
        usage: {}, // No AI usage for this step
        stepResult: {
          imageUpdated: true,

          // Featured image data for Step 7 database update
          featuredImageData: featuredImageData,
          shopifyArticleId: shopifyArticleGqlId,

          // Original image data for reference
          originalImageUrl: imageData.imageUrl,
          originalImageAlt: imageData.imageAlt,

          // Update metadata
          updateTimestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      console.error(`[${jobId}] Shopify image update failed:`, error);
      throw new Error(`Shopify image update failed: ${error.message}`);
    }
  }

  /**
   * Execute Step 7: Article Database Updates
   * Updates local article database with Shopify image data from Step 6
   * @param {string} jobId - The job ID
   * @param {Object} job - The job object
   * @param {string} shopDomain - The shop domain
   * @returns {Promise<Object>} Article database update result
   */
  async executeArticleDatabaseUpdate(jobId, job, shopDomain) {
    try {
      console.info(`[${jobId}] Starting Step 7: Article Database Updates`);

      // Step 7.1: Get Shopify image data from Step 6 result
      const step6Result = job.steps.find((step) => step.step === BlogAutoWriteJobSteps.SHOPIFY_IMAGE_UPLOAD);

      if (!step6Result || !step6Result.result) {
        throw new Error("Step 6 (Shopify Image Upload) result not found");
      }

      // Check if Step 6 was skipped
      if (step6Result.result.skipped) {
        console.info(`[${jobId}] Database update skipped - no image was uploaded in Step 6`);
        return {
          success: true,
          skipped: true,
          stepResult: {
            databaseUpdated: false,
            reason: "No image uploaded in Step 6",
          },
        };
      }

      // Get featured image data from Step 6
      const step6Data = step6Result.result.stepResult;
      const featuredImageData = step6Data.featuredImageData;

      if (!step6Data.imageUpdated || !featuredImageData) {
        throw new Error("Invalid image data from Step 6 - missing featuredImageData");
      }

      console.info(`[${jobId}] Retrieved featured image data from Step 6:`, featuredImageData);

      // Step 7.2: Validate article exists
      const article = await ArticleService.getArticle(job.shop_id, job.article_id);
      if (!article) {
        throw new Error(`Article not found: shop_id=${job.shop_id}, article_id=${job.article_id}`);
      }

      console.info(`[${jobId}] Article found: ${article.title} (ID: ${article.id})`);

      // Step 7.3: Update article database with image data (following ArticleSyncQueue pattern)
      console.info(`[${jobId}] Updating article database with featured image data...`);

      // First update the article generation status
      const articleUpdateData = {
        generation_status: BlogGenerationStatus.CONTENT_READY,
        updated_at: new Date(),
      };

      const updatedArticle = await ArticleService.updateArticle(article.id, articleUpdateData);

      if (!updatedArticle) {
        throw new Error("Failed to update article in database");
      }

      console.info(`[${jobId}] Article updated successfully`);

      // First, delete any existing images for this article (for regeneration scenarios)
      console.info(`[${jobId}] Checking for existing images to delete...`);

      const ArticleImageService = require("../../services/ArticleImageService");
      const existingImages = await ArticleImageService.getAll(job.shop_id, article.id);
      if (existingImages && existingImages.length > 0) {
        console.info(`[${jobId}] Found ${existingImages.length} existing image(s), deleting them...`);

        // Delete all existing images for this article
        await ArticleImageService.deleteByArticleId(article.id, job.shop_id);
        console.info(`[${jobId}] Deleted all existing images for article`);
      } else {
        console.info(`[${jobId}] No existing images found for article`);
      }

      // Then upsert the new featured image (following ArticleService.saveOrUpdateArticle pattern)
      console.info(`[${jobId}] Upserting new featured image in ArticleImage table...`);

      const upsertedImage = await ArticleService.upsertImage(
        job.shop_id,
        article.blog_id,
        article.id,
        featuredImageData
      );

      if (!upsertedImage) {
        throw new Error("Failed to upsert featured image in database");
      }

      console.info(`[${jobId}] Article database and featured image updated successfully`);

      // Step 7.4: Create step result
      return {
        success: true,
        creditsUsed: 0, // No credits used for database updates
        usage: {}, // No AI usage for this step
        stepResult: {
          databaseUpdated: true,

          // Updated article data
          articleId: article.id,
          imageData: featuredImageData,
          imageId: upsertedImage.id,
          generationStatus: BlogGenerationStatus.CONTENT_READY,

          // Update metadata
          updateTimestamp: new Date().toISOString(),

          // Validation
          articleExists: true,
          imageDataValid: true,
          statusUpdated: true,
          imageUpserted: true,
        },
      };
    } catch (error) {
      console.error(`[${jobId}] Article database update failed:`, error);
      throw new Error(`Article database update failed: ${error.message}`);
    }
  }

  /**
   * Execute Step 8: Final SEO Analysis
   * Performs comprehensive SEO analysis on complete article with featured image
   * @param {string} jobId - The job ID
   * @param {Object} job - The job object
   * @param {string} shopDomain - The shop domain
   * @returns {Promise<Object>} Final SEO analysis result
   */
  async executeFinalSeoAnalysis(jobId, job, shopDomain) {
    try {
      console.info(`[${jobId}] Starting Step 8: Final SEO Analysis`);

      // Step 8.1: Get complete article with featured image
      const article = await ArticleService.getArticle(job.shop_id, job.article_id);
      if (!article) {
        throw new Error(`Article not found: shop_id=${job.shop_id}, article_id=${job.article_id}`);
      }

      console.info(`[${jobId}] Article found: ${article.title} (ID: ${article.id})`);

      // Step 8.2: Validate article has required content for analysis
      if (!article.title || !article.body_html || !article.focus_keyword) {
        throw new Error("Article missing required content for SEO analysis (title, body_html, focus_keyword)");
      }

      console.info(`[${jobId}] Performing final SEO analysis on complete article with image...`);

      // Step 8.3: Perform comprehensive SEO analysis (same as Step 4 but on complete article)
      const analysisResult = await AnalysisService.analyseEachArticle({
        shopId: job.shop_id,
        article: article,
        shopURL: shopDomain,
      });

      if (!analysisResult) {
        throw new Error("SEO analysis failed to return results");
      }

      console.info(`[${jobId}] Final SEO analysis completed successfully`);
      console.info(
        `[${jobId}] SEO Score: ${analysisResult.score}/100, Issues: ${analysisResult.issues}, Passed: ${analysisResult.passed}`
      );

      // Step 8.4: Create step result (simplified - detailed analysis already stored in Step 4)
      return {
        success: true,
        creditsUsed: 0, // No credits used for SEO analysis
        usage: {}, // No AI usage for this step
        stepResult: {
          finalAnalysisCompleted: true,

          // Final SEO score (may be different from Step 4 due to image optimization)
          finalSeoScore: analysisResult.score,

          // Article completion status
          articleId: job.article_id,
          hasImage: !!article.img,
          isOptimized: analysisResult.is_optimized,

          // Final analysis metadata
          analysisTimestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      console.error(`[${jobId}] Final SEO analysis failed:`, error);
      throw new Error(`Final SEO analysis failed: ${error.message}`);
    }
  }

  /**
   * Execute Step 9: Article Publishing
   * Conditionally publishes article to Shopify if autoPublish is enabled
   * @param {string} jobId - The job ID
   * @param {Object} job - The job object
   * @param {string} shopDomain - The shop domain
   * @returns {Promise<Object>} Article publishing result
   */
  async executeArticlePublishing(jobId, job, shopDomain) {
    try {
      console.info(`[${jobId}] Starting Step 9: Article Publishing`);

      // Step 9.1: Check autoPublish setting
      const autoPublish = job.input_data?.autoPublish;
      console.info(`[${jobId}] Auto-publish setting: ${autoPublish}`);

      if (!autoPublish) {
        console.info(`[${jobId}] Auto-publish disabled - article remains as draft`);
        return {
          success: true,
          stepResult: {
            publishingCompleted: true,
            published: false,
            reason: "Auto-publish disabled - article saved as draft",
            articleId: job.article_id,
            completionTimestamp: new Date().toISOString(),
          },
        };
      }

      // Step 9.2: Get Shopify article ID from Step 2
      const step2Result = job.steps.find((step) => step.step === BlogAutoWriteJobSteps.SHOPIFY_DRAFT_CREATION);
      if (!step2Result || !step2Result.result?.stepResult?.shopifyArticleGqlId) {
        throw new Error("Shopify article ID not found from Step 2");
      }

      const shopifyArticleGqlId = step2Result.result.stepResult.shopifyArticleGqlId;
      console.info(`[${jobId}] Publishing Shopify article: ${shopifyArticleGqlId}`);

      // Step 9.3: Update Shopify article to published
      console.info(`[${jobId}] Setting article as published in Shopify...`);

      const updateData = {
        isPublished: true,
      };

      const shopifyResponse = await ShopifyService.onlineStore.updateArticle(shopDomain, {
        id: shopifyArticleGqlId,
        article: updateData,
        metafieldKeys: [],
      });

      console.info(`[${jobId}] Article published successfully in Shopify`);

      // Step 9.4: Create step result
      return {
        success: true,
        creditsUsed: 0, // No credits used for publishing
        usage: {}, // No AI usage for this step
        stepResult: {
          publishingCompleted: true,
          published: true,

          // Publishing data
          articleId: job.article_id,
          shopifyArticleId: shopifyArticleGqlId,
          publishedAt: shopifyResponse.publishedAt,

          // Completion metadata
          completionTimestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      console.error(`[${jobId}] Article publishing failed:`, error);
      throw new Error(`Article publishing failed: ${error.message}`);
    }
  }

  /**
   * Create image generation prompt based on article content
   * @param {Object} data - Article data for prompt creation
   * @returns {string} Generated image prompt
   */
  createImagePrompt(data) {
    const { title, focusKeyword, topic, blogType, featuredImageDescription } = data;

    // Use provided description if available, otherwise create from article data
    if (featuredImageDescription && featuredImageDescription.trim()) {
      return featuredImageDescription.trim();
    }

    // Create prompt from article data
    let prompt = `Professional ${blogType.toLowerCase()} illustration`;

    if (focusKeyword) {
      prompt += ` about ${focusKeyword}`;
    }

    if (topic && topic !== title) {
      prompt += `, related to ${topic}`;
    }

    prompt += ". Clean, modern design suitable for blog featured image. High quality, professional appearance.";

    return prompt;
  }

  /**
   * Create a Shopify-compatible blog handle from title
   * @param {string} title - The blog title
   * @returns {string} Shopify-compatible handle
   */
  createBlogHandle(title) {
    if (!title || typeof title !== "string") {
      throw new Error("Invalid title provided for handle generation");
    }

    // Convert to lowercase and replace spaces with hyphens
    let handle = title
      .toLowerCase()
      .trim()
      // Replace multiple spaces with single space
      .replace(/\s+/g, " ")
      // Replace spaces with hyphens
      .replace(/\s/g, "-")
      // Remove special characters except hyphens and alphanumeric
      .replace(/[^a-z0-9-]/g, "")
      // Remove multiple consecutive hyphens
      .replace(/-+/g, "-")
      // Remove leading/trailing hyphens
      .replace(/^-+|-+$/g, "");

    // Ensure handle is not empty
    if (!handle) {
      handle = `blog-post-${Date.now()}`;
    }

    // Ensure handle meets Shopify length requirements (max 255 characters)
    if (handle.length > 255) {
      handle = handle.substring(0, 255).replace(/-+$/, "");
    }

    return handle;
  }

  /**
   * Update step result in steps array
   * @param {Array} steps - Current job steps
   * @param {string} completedStep - The step that was completed
   * @param {Object} stepResult - Result data from the completed step
   * @returns {Array} Updated steps array
   */
  updateStepResult(steps, completedStep, stepResult) {
    return steps.map((step) => {
      if (step.step === completedStep) {
        return {
          ...step,
          completed: true,
          completedAt: new Date().toISOString(),
          error: null,
          result: stepResult,
        };
      }
      return step;
    });
  }

  /**
   * Complete step and update job status in a single atomic operation
   * Combines step completion tracking with dynamic status updates for efficiency
   * @param {string} jobId - The job ID
   * @param {string} completedStep - The step that was just completed
   * @param {Object} stepResult - Result data from the completed step
   * @returns {Promise<void>}
   */
  async completeStepAndUpdateStatus(jobId, completedStep, stepResult) {
    try {
      console.info(`[${jobId}] Completing step ${completedStep} and updating job status atomically`);

      // Get current job to analyze step state
      const job = await BlogAutoWriteService.getJobByIdInternal(jobId);
      if (!job) {
        throw new Error(`Job ${jobId} not found`);
      }

      // Always update the step result first (for credit tracking)
      const updatedSteps = this.updateStepResult(job.steps, completedStep, stepResult);

      // Check if job was cancelled - save step result but don't proceed with status update
      if (job.status === BlogAutoWriteJobStatus.CANCELLED) {
        console.info(`[${jobId}] Job was cancelled during step completion, saving step result for credit tracking`);
        await BlogAutoWriteService.updateJob(jobId, { steps: updatedSteps });
        console.info(`[${jobId}] Step result saved for cancelled job to preserve credit usage`);
        throw new JobCancelledException(`Job ${jobId} was cancelled during step completion`, jobId);
      }

      // Determine the next status based on completed step (use job's actual steps)
      const jobStepNames = job.steps.map((s) => s.step);
      const currentStepIndex = jobStepNames.indexOf(completedStep);
      if (currentStepIndex === -1) {
        throw new Error(`Completed step ${completedStep} not found in job steps`);
      }

      let nextStatus;
      let nextStep = null;

      // Check if this was the last step
      if (currentStepIndex === jobStepNames.length - 1) {
        // All steps completed
        nextStatus = BlogAutoWriteJobStatus.COMPLETED;
        console.info(`[${jobId}] Last step completed, job will be marked as COMPLETED`);
      } else {
        // Get the next step and its corresponding status
        nextStep = jobStepNames[currentStepIndex + 1];
        nextStatus = getStatusForStep(nextStep);

        if (!nextStatus) {
          throw new Error(`No status mapping found for next step: ${nextStep}`);
        }
        console.info(`[${jobId}] Next step: ${nextStep}, status will be: ${nextStatus}`);
      }

      // Prepare all update data locally in the queue
      const updateData = {
        steps: updatedSteps,
        status: nextStatus,
      };

      // Calculate progress based on actually completed steps
      if (FeatureFlagService.blogs.autoWrite.isImageGenerationEnabled()) {
        // Feature enabled: Use original static weights (all 9 steps)
        updateData.progress = BlogAutoWriteJobSteps.calculateProgress(updatedSteps);
      } else {
        // Feature disabled: Use dynamic weights for filtered steps
        const dynamicWeights = FeatureFlagService.blogs.autoWrite.getStepWeights();
        updateData.progress = BlogAutoWriteJobSteps.calculateProgressWithWeights(updatedSteps, dynamicWeights);
      }

      // Add processing timestamps based on status
      if (nextStatus === BlogAutoWriteJobStatus.GENERATING_CONTENT && !job.processing_started_at) {
        updateData.processing_started_at = new Date();
      }

      if (nextStatus === BlogAutoWriteJobStatus.COMPLETED || nextStatus === BlogAutoWriteJobStatus.FAILED) {
        updateData.processing_completed_at = new Date();
      }

      // Single atomic database update with all data
      await BlogAutoWriteService.updateJob(jobId, updateData);

      console.info(
        `[${jobId}] Step ${completedStep} completed and job status updated to ${nextStatus}${nextStep ? ` for next step ${nextStep}` : ""}`
      );
    } catch (error) {
      console.error(`[${jobId}] Failed to complete step ${completedStep} and update status:`, error);
      throw error; // Re-throw to be handled by main error handler
    }
  }

  /**
   * Execute a step with automatic cancellation detection
   * Wraps step execution with pre-step cancellation checks and error handling
   * @param {string} jobId - The job ID being processed
   * @param {string} stepName - Name of the step being executed
   * @param {Function} stepFunction - The async function to execute for this step
   * @param {Object} context - Context object containing job data and dependencies
   * @returns {Promise<any>} Result of the step execution
   */
  async executeStepWithCancellationCheck(jobId, stepName, stepFunction, context = {}) {
    try {
      // Pre-step cancellation check
      console.info(`[${jobId}] Checking cancellation before executing step: ${stepName}`);
      await this.checkJobCancellation(jobId);

      // Execute the step
      console.info(`[${jobId}] Executing step: ${stepName}`);
      const stepStartTime = Date.now();

      const result = await stepFunction(context);

      const stepDuration = Date.now() - stepStartTime;
      console.info(`[${jobId}] Step ${stepName} completed in ${stepDuration}ms`);

      return result;
    } catch (error) {
      // Handle cancellation gracefully
      if (JobCancelledException.isJobCancelledException(error)) {
        console.info(`[${jobId}] Step ${stepName} cancelled: ${error.message}`);
        throw error; // Re-throw to be handled by main error handler
      }

      // Handle other step execution errors
      console.error(`[${jobId}] Step ${stepName} failed:`, error);
      throw new Error(`Step ${stepName} execution failed: ${error.message}`);
    }
  }

  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { jobId, shopDomain } = decodedMessage;

    try {
      console.info(`[${this.config.queueName}] Processing job ${jobId} for shop ${shopDomain}`);

      // Initial cancellation check before starting any processing
      await this.checkJobCancellation(jobId);

      // Get the job details
      const job = await BlogAutoWriteService.getJobByIdInternal(jobId);
      if (!job) {
        throw new Error(`Job ${jobId} not found`);
      }

      // Analyze current job state to determine next step
      const currentState = this.getJobCurrentStep(job);
      if (!currentState.canResume) {
        throw new Error(`Job cannot be processed: ${currentState.error}`);
      }

      if (currentState.isComplete) {
        console.info(`[${jobId}] Job is already complete`);
        const result = { status: "completed", message: "Job already completed" };
        channel.ack(message);
        console.info(
          `[${this.config.queueName}](${shopDomain}) Job ${jobId} processed successfully. Status: ${result.status}`
        );
        return;
      }

      const nextStep = currentState.nextStep;
      console.info(`[${jobId}] Processing step: ${nextStep}`);

      // Mark step as started before execution
      await this.markStepAsStarted(jobId, nextStep);

      // Execute the next step with cancellation protection
      let stepResult;
      switch (nextStep) {
        case BlogAutoWriteJobSteps.CONTENT_GENERATION:
          stepResult = await this.executeStepWithCancellationCheck(
            jobId,
            BlogAutoWriteJobSteps.CONTENT_GENERATION,
            async () => await this.executeContentGeneration(jobId, job, shopDomain),
            { job, shopDomain }
          );
          break;

        case BlogAutoWriteJobSteps.SHOPIFY_DRAFT_CREATION:
          stepResult = await this.executeStepWithCancellationCheck(
            jobId,
            BlogAutoWriteJobSteps.SHOPIFY_DRAFT_CREATION,
            async () => await this.executeShopifyDraftCreation(jobId, job, shopDomain),
            { job, shopDomain }
          );
          break;

        case BlogAutoWriteJobSteps.ARTICLE_LINKING:
          stepResult = await this.executeStepWithCancellationCheck(
            jobId,
            BlogAutoWriteJobSteps.ARTICLE_LINKING,
            async () => await this.executeArticleLinking(jobId, job, shopDomain),
            { job, shopDomain }
          );
          break;

        case BlogAutoWriteJobSteps.FIRST_SEO_ANALYSIS:
          stepResult = await this.executeStepWithCancellationCheck(
            jobId,
            BlogAutoWriteJobSteps.FIRST_SEO_ANALYSIS,
            async () => await this.executeSeoAnalysis(jobId, job, shopDomain),
            { job, shopDomain }
          );
          break;

        case BlogAutoWriteJobSteps.IMAGE_GENERATION:
          stepResult = await this.executeStepWithCancellationCheck(
            jobId,
            BlogAutoWriteJobSteps.IMAGE_GENERATION,
            async () => await this.executeImageGeneration(jobId, job, shopDomain),
            { job, shopDomain }
          );
          break;

        case BlogAutoWriteJobSteps.SHOPIFY_IMAGE_UPLOAD:
          stepResult = await this.executeStepWithCancellationCheck(
            jobId,
            BlogAutoWriteJobSteps.SHOPIFY_IMAGE_UPLOAD,
            async () => await this.executeShopifyImageUpdate(jobId, job, shopDomain),
            { job, shopDomain }
          );
          break;

        case BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_UPDATE:
          stepResult = await this.executeStepWithCancellationCheck(
            jobId,
            BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_UPDATE,
            async () => await this.executeArticleDatabaseUpdate(jobId, job, shopDomain),
            { job, shopDomain }
          );
          break;

        case BlogAutoWriteJobSteps.FINAL_SEO_ANALYSIS:
          stepResult = await this.executeStepWithCancellationCheck(
            jobId,
            BlogAutoWriteJobSteps.FINAL_SEO_ANALYSIS,
            async () => await this.executeFinalSeoAnalysis(jobId, job, shopDomain),
            { job, shopDomain }
          );
          break;

        case BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_PUBLISH:
          stepResult = await this.executeStepWithCancellationCheck(
            jobId,
            BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_PUBLISH,
            async () => await this.executeArticlePublishing(jobId, job, shopDomain),
            { job, shopDomain }
          );
          break;

        default:
          throw new Error(`Unknown step: ${nextStep}. Step implementation not yet available.`);
      }

      // Mark step as completed AND update job status in a single atomic operation
      await this.completeStepAndUpdateStatus(jobId, nextStep, stepResult);

      // Check if there are more steps to process
      const updatedJob = await BlogAutoWriteService.getJobByIdInternal(jobId);
      const nextStepAfterCompletion = this.getNextStepFromJob(updatedJob.steps);

      if (nextStepAfterCompletion) {
        // Queue the next step
        await this.queueNextStep(jobId, shopDomain);
        console.info(`[${jobId}] Step ${nextStep} completed, queued next step: ${nextStepAfterCompletion}`);
      } else {
        // All steps completed, mark job as complete
        await this.completeJob(jobId);
        console.info(`[${jobId}] All steps completed, job marked as COMPLETED`);
      }

      const result = {
        status: nextStepAfterCompletion ? "step_completed_next_queued" : "job_completed",
        step: nextStep,
        nextStep: nextStepAfterCompletion,
        stepResult: stepResult,
        message: `Step ${nextStep} completed successfully${nextStepAfterCompletion ? ", next step queued" : ", job completed"}`,
      };

      channel.ack(message);
      console.info(
        `[${this.config.queueName}](${shopDomain}) Job ${jobId} processed successfully. Status: ${result.status}`
      );
    } catch (error) {
      channel.ack(message);

      // Handle job cancellation gracefully
      if (JobCancelledException.isJobCancelledException(error)) {
        console.info(`[${shopDomain}] Job ${jobId} was cancelled: ${error.message}`);

        // Job is already marked as CANCELLED by the API, no need to update status
        // Just perform cleanup and exit gracefully
        await this.handleJobCancellation(jobId, shopDomain, error);
        return;
      }

      // Handle other errors
      console.error(`[${shopDomain}] ${this.config.queueName} failed for job ${jobId}. Error: ${error.message}`, error);

      // Perform credit settlement for failed job before marking as failed
      try {
        await this.performFinalCreditSettlement(jobId, shopDomain, "failure");
      } catch (settlementError) {
        console.error(`[${jobId}] Credit settlement failed for failed job:`, settlementError);
        // Continue with job failure even if settlement fails
      }

      // Update job status to failed
      await BlogAutoWriteService.updateJob(jobId, {
        status: BlogAutoWriteJobStatus.FAILED,
        error_message: error.message,
        processing_completed_at: new Date(),
      });

      await this.handleError(error, message);
    }
  }

  /**
   * Queue the next step for processing
   * @param {string} jobId - The job ID
   * @param {string} shopDomain - The shop domain
   */
  async queueNextStep(jobId, shopDomain) {
    try {
      // Sleep for 1 seconds before dispatching next step
      console.info(`[${jobId}] Waiting 2 seconds before dispatching next step...`);
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const message = {
        jobId,
        shopDomain,
      };

      await dispatchQueue({
        queueName: this.config.queueName,
        message: message,
        ttl: 3000,
      });
      console.info(`[${jobId}] Next step message dispatched to queue for shop ${shopDomain}`);
    } catch (error) {
      console.error(`[${jobId}] Failed to queue next step:`, error);
      throw new Error(`Failed to queue next step: ${error.message}`);
    }
  }

  /**
   * Mark job as completed when all steps are finished
   * @param {string} jobId - The job ID
   */
  async completeJob(jobId) {
    try {
      // Check if job was cancelled before marking as complete
      const job = await BlogAutoWriteService.getJobByIdInternal(jobId);
      if (job.status === BlogAutoWriteJobStatus.CANCELLED) {
        console.info(`[${jobId}] Job was cancelled, skipping completion`);
        throw new JobCancelledException(`Job ${jobId} was cancelled before completion`, jobId);
      }

      // Perform final credit settlement before marking as complete
      const shopDomain = await this.getShopDomainFromJob(job);
      await this.performFinalCreditSettlement(jobId, shopDomain, "completion");

      await BlogAutoWriteService.updateJob(jobId, {
        status: BlogAutoWriteJobStatus.COMPLETED,
        progress: 100,
        processing_completed_at: new Date(),
      });
      console.info(`[${jobId}] Job marked as COMPLETED with credit settlement`);
    } catch (error) {
      console.error(`[${jobId}] Failed to mark job as completed:`, error);
      throw new Error(`Failed to complete job: ${error.message}`);
    }
  }

  /**
   * Handle job cancellation with proper cleanup
   * @param {string} jobId - The cancelled job ID
   * @param {string} shopDomain - The shop domain
   * @param {JobCancelledException} cancellationError - The cancellation error
   */
  async handleJobCancellation(jobId, shopDomain, cancellationError) {
    try {
      console.info(`[${jobId}] Performing cancellation cleanup for shop ${shopDomain}`);
      console.info(`[${jobId}] Cancellation reason: ${cancellationError.message}`);

      // Perform credit refund for cancelled job
      await this.performFinalCreditSettlement(jobId, shopDomain, "cancellation");

      console.info(`[${jobId}] Cancellation cleanup completed with credit refund`);
    } catch (cleanupError) {
      console.error(`[${jobId}] Error during cancellation cleanup:`, cleanupError);
      // Don't throw here - we want to exit gracefully even if cleanup fails
    }
  }

  /**
   * Get shop domain from job data
   * @param {Object} job - The job object
   * @returns {Promise<string>} Shop domain
   */
  async getShopDomainFromJob(job) {
    try {
      // Use ShopService to get shop by ID (not domain)
      const shop = await ShopService.getShopById(job.shop_id);

      if (!shop) {
        throw new Error(`Shop not found for job ${job.id}`);
      }

      return shop.domain;
    } catch (error) {
      console.error(`Failed to get shop domain for job ${job.id}:`, error);
      throw error;
    }
  }

  /**
   * Perform final credit settlement for job completion, cancellation, or failure
   * @param {string} jobId - The job ID
   * @param {string} shopDomain - The shop domain
   * @param {string} settlementType - Type of settlement: 'completion', 'cancellation', or 'failure'
   */
  async performFinalCreditSettlement(jobId, shopDomain, settlementType) {
    try {
      console.info(`[${jobId}] Starting credit settlement for ${settlementType}`);

      // Get the current job with all step data
      const job = await BlogAutoWriteService.getJobByIdInternal(jobId);
      if (!job) {
        throw new Error(`Job ${jobId} not found for credit settlement`);
      }

      // Calculate actual credit usage from completed steps
      const actualCreditUsage = this.calculateActualCreditUsage(job.steps);
      const estimatedCredits = job.estimated_credit_usage?.totalCredits || 0;

      console.info(
        `[${jobId}] Credit settlement - Estimated: ${estimatedCredits}, Actual: ${actualCreditUsage.totalCredits}`
      );

      // Calculate settlement amount (positive = refund, negative = additional charge)
      const settlementAmount = estimatedCredits - actualCreditUsage.totalCredits;

      // Update job with actual credit usage for audit trail
      await this.updateJobCreditUsage(jobId, actualCreditUsage, settlementType);

      // Apply credit adjustment if needed
      if (Math.abs(settlementAmount) > 0.01) {
        // Only adjust if difference is significant
        await this.applyCreditAdjustment(shopDomain, settlementAmount, settlementType, jobId);
      }

      console.info(`[${jobId}] Credit settlement completed - Settlement: ${settlementAmount} credits`);
    } catch (error) {
      console.error(`[${jobId}] Credit settlement failed:`, error);
      throw error;
    }
  }

  /**
   * Calculate actual credit usage from completed job steps
   * @param {Array} steps - Array of job steps
   * @returns {Object} Actual credit usage breakdown
   */
  calculateActualCreditUsage(steps) {
    let totalCredits = 0;
    const breakdown = {
      content: { credits: 0, usage: null },
      images: { credits: 0, usage: null },
    };

    // Process each completed step to aggregate credit usage
    steps.forEach((step) => {
      if (step.completed && step.result) {
        const creditsUsed = step.result.creditsUsed || 0;
        const usage = step.result.usage || {};

        totalCredits += creditsUsed;

        // Categorize credits by step type for breakdown
        if (step.step === "CONTENT_GENERATION" && creditsUsed > 0) {
          breakdown.content.credits += creditsUsed;
          breakdown.content.usage = usage;
        } else if (step.step === "IMAGE_GENERATION" && creditsUsed > 0) {
          breakdown.images.credits += creditsUsed;
          breakdown.images.usage = usage;
        }
      }
    });

    // Clean up breakdown - remove categories with no credits
    if (breakdown.content.credits === 0) {
      delete breakdown.content;
    }
    if (breakdown.images.credits === 0) {
      delete breakdown.images;
    }

    return {
      totalCredits: Number(totalCredits.toFixed(2)),
      breakdown,
      calculatedAt: new Date().toISOString(),
    };
  }

  /**
   * Update job with actual credit usage for audit trail
   * @param {string} jobId - The job ID
   * @param {Object} actualCreditUsage - Calculated actual credit usage
   * @param {string} settlementType - Type of settlement
   */
  async updateJobCreditUsage(jobId, actualCreditUsage, settlementType) {
    try {
      // Get job data for audit trail
      const job = await BlogAutoWriteService.getJobByIdInternal(jobId);
      const estimatedCredits = job.estimated_credit_usage?.totalCredits || 0;
      const settlementAmount = estimatedCredits - actualCreditUsage.totalCredits;

      const updateData = {
        credit_usage: {
          ...actualCreditUsage,
          feature: "blog_auto_write",
          featureName: "Blog Auto-Write",
          addonGroup: "AI_OPTIMIZER",
          estimatedCost: Number((actualCreditUsage.totalCredits * 0.002).toFixed(4)), // Approximate cost
          settlementType,
          settledAt: new Date().toISOString(),

          // Enhanced audit trail
          auditTrail: {
            estimatedCredits,
            actualCredits: actualCreditUsage.totalCredits,
            settlementAmount,
            settlementType,
            completedSteps: job.steps?.filter((step) => step.completed)?.length || 0,
            totalSteps: job.steps?.length || 0,
            jobDuration: job.processing_started_at ? new Date() - new Date(job.processing_started_at) : null,
            stepBreakdown:
              job.steps
                ?.filter((step) => step.completed)
                .map((step) => ({
                  step: step.step,
                  completedAt: step.completedAt,
                  creditsUsed: step.result?.creditsUsed || 0,
                  usage: step.result?.usage || step.result?.imageUsage || null,
                })) || [],
          },
        },
      };

      await BlogAutoWriteService.updateJob(jobId, updateData);
      console.info(`[${jobId}] Job credit usage updated for ${settlementType} with comprehensive audit trail`);
    } catch (error) {
      console.error(`[${jobId}] Failed to update job credit usage:`, error);
      throw error;
    }
  }

  /**
   * Apply credit adjustment (refund or additional charge) to shop's usage
   * @param {string} shopDomain - The shop domain
   * @param {number} settlementAmount - Amount to adjust (positive = refund, negative = charge)
   * @param {string} settlementType - Type of settlement
   * @param {string} jobId - The job ID for logging
   */
  async applyCreditAdjustment(shopDomain, settlementAmount, settlementType, jobId) {
    try {
      if (settlementAmount > 0) {
        // Refund credits (decrement usage)
        await cache.addons.decrementUsageCount(shopDomain, {
          addon: AI_OPTIMIZER,
          decrementBy: settlementAmount,
        });

        await cache.addons.decrementTotalUsageCount(shopDomain, {
          addon: AI_OPTIMIZER,
          decrementBy: settlementAmount,
        });

        await cache.addons.decrementTotalAppUsageCount(AI_OPTIMIZER, settlementAmount);

        console.info(`[${jobId}] Credit refund applied: ${settlementAmount} credits for ${settlementType}`);
      } else {
        // Additional charge (increment usage)
        const additionalCharge = Math.abs(settlementAmount);
        await cache.addons.incrementUsageCount(shopDomain, {
          addon: AI_OPTIMIZER,
          incrementBy: additionalCharge,
        });

        await cache.addons.incrementTotalUsageCount(shopDomain, {
          addon: AI_OPTIMIZER,
          incrementBy: additionalCharge,
        });

        await cache.addons.incrementTotalAppUsageCount(AI_OPTIMIZER, additionalCharge);

        console.info(`[${jobId}] Additional credit charge applied: ${additionalCharge} credits for ${settlementType}`);
      }
    } catch (error) {
      console.error(`[${jobId}] Failed to apply credit adjustment:`, error);
      throw error;
    }
  }
}

module.exports = new BlogAutoWriteQueue(
  {
    queueName: QUEUE_NAMES.BLOG_AUTO_WRITE_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.BLOG_AUTO_WRITE_QUEUE],
  },
  RABBIT_MQ_CONNECTION
);


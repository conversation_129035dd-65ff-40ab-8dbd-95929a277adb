const { RABBIT_MQ_CONNECTION } = require("../..");
const ShopService = require("../../../services/ShopService");
const ShopifyService = require("../../../services/ShopifyService");
const SubscriptionPlanService = require("../../../services/SubscriptionPlanService");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../../config");
const BaseQueue = require("../BaseQueue");
const logger = require("storeseo-logger");
const { getRemainingTrialDays, updateTrialStatus, isTrialActive } = require("../../../utils/trialHelpers");
const { serializeShopPlanData } = require("../../../serializers/SubscriptionSerializer");
const TrialStatus = require("storeseo-enums/trialStatus");
const appSubscriptionStatus = require("storeseo-enums/appSubscriptionStatus");
const SubscriptionAddonService = require("../../../services/SubscriptionAddonService");
const AppSubscriptionStatus = require("storeseo-enums/appSubscriptionStatus");
const AddonUsageService = require("../../../services/AddonUsageService");

class TrialExpirationQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { shopDomain } = decodedMessage;

    try {
      console.log(`[${this.config.queueName}] Processing trial expiration for shop: ${shopDomain}`);

      // 1. Fetch shop data with trial information
      const shop = await ShopService.getShop(shopDomain);

      if (!shop) {
        console.warn(`[${this.config.queueName}] Shop not found: ${shopDomain}`);
        channel.ack(message);
        return;
      }

      // 2. Validate trial data exists and is active
      if (!shop.trial_data || !isTrialActive(shop.trial_data)) {
        console.warn(`[${this.config.queueName}] No active trial found for shop: ${shopDomain}`);
        channel.ack(message);
        return;
      }

      // 3. Double-check trial expiration (safety check)
      const remainingDays = getRemainingTrialDays(shop.trial_data);
      if (remainingDays > 0) {
        console.warn(
          `[${this.config.queueName}] Trial not yet expired for shop: ${shopDomain}, remaining days: ${remainingDays}`
        );
        channel.ack(message);
        return;
      }

      // 4. Create session for Shopify API calls
      const session = {
        shop: shopDomain,
        accessToken: shop.access_token,
      };

      // 5. Check current Shopify subscription status
      const activeSubscription = await ShopifyService.getActiveSubscription(session.shop);

      // 6. Handle based on subscription status
      if (activeSubscription && activeSubscription.status === appSubscriptionStatus.ACTIVE) {
        await this.handleTrialConversion(shop, activeSubscription);
        logger.debug(`Trial converted to paid subscription for shop: ${shopDomain}`, { domain: shopDomain });
      } else {
        await this.handleTrialExpiration(shop);
        logger.debug(`Trial expired for shop: ${shopDomain}`, { domain: shopDomain });
      }

      channel.ack(message);
      console.info(`[${this.config.queueName}] Successfully processed trial expiration for shop: ${shopDomain}`);
    } catch (error) {
      channel.ack(message);
      logger.error(error, {
        queueName: this.config.queueName,
        domain: shopDomain,
        message: "Trial expiration processing failed",
      });

      console.error(
        `[${this.config.queueName}] Failed to process trial expiration for shop: ${shopDomain}`,
        error.message
      );
    }
  }

  /**
   * Handle trial conversion when subscription is active
   * @param {Object} shop - Shop data
   * @param {Object} activeSubscription - Active subscription from Shopify
   */
  async handleTrialConversion(shop, activeSubscription) {
    try {
      // Update trial status to converted
      const updatedTrialData = updateTrialStatus(shop.trial_data, TrialStatus.CONVERTED);

      const url = new URL(activeSubscription.returnUrl);
      const searchParams = url.searchParams;

      const planSlug = searchParams.get("plan");
      const addonIds = searchParams.get("addons");

      // Get subscription plan data from the active subscription
      const subscriptionPlan = await SubscriptionPlanService.getSubscriptionPlanBySlug(planSlug);

      if (!subscriptionPlan) {
        throw new Error(`Unable to find subscription plan for active subscription: ${activeSubscription.name}`);
      }

      const addons = await SubscriptionAddonService.getSelectedAddons(addonIds.split(","));

      // Serialize shop plan data with the active subscription
      const updateData = serializeShopPlanData(shop, subscriptionPlan, activeSubscription, addons);

      // Update shop with converted trial and subscription data
      await ShopService.updateShop(shop.id, {
        trial_data: updatedTrialData,
        plan_rules: updateData.plan_rules,
        plan_status: activeSubscription.status,
        appSubscriptionId: activeSubscription.id,
        appSubscriptionData: activeSubscription,
        plan_validity: updateData.plan_validity,
        subscribed_at: updateData.subscribed_at,
      });

      await AddonUsageService.updateRecurringUsagePermissions({
        shopId: shop.id,
        shopDomain: shop.domain,
        purchaseDate: updateData.subscribed_at,
        addons,
      });

      console.log(`Trial converted for shop: ${shop.domain}, subscription: ${activeSubscription.name}`);
    } catch (error) {
      console.error(`Error handling trial conversion for shop: ${shop.domain}`, error.message);
      throw error;
    }
  }

  /**
   * Handle trial expiration when no active subscription
   * @param {Object} shop - Shop data
   */
  async handleTrialExpiration(shop) {
    try {
      // Update trial status to expired
      const updatedTrialData = updateTrialStatus(shop.trial_data, TrialStatus.EXPIRED);

      // Update shop with expired trial and plan status
      await ShopService.updateShop(shop.id, {
        trial_data: updatedTrialData,
        plan_status: AppSubscriptionStatus.EXPIRED,
      });

      console.log(`Trial expired for shop: ${shop.domain}`);
    } catch (error) {
      console.error(`Error handling trial expiration for shop: ${shop.domain}`, error.message);
      throw error;
    }
  }
}

module.exports = new TrialExpirationQueue(
  {
    queueName: QUEUE_NAMES.TRIAL_EXPIRATION_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.TRIAL_EXPIRATION_QUEUE],
  },
  RABBIT_MQ_CONNECTION
);

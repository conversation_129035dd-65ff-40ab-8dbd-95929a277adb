# Blog Auto-Write Feature Implementation

## Overview

This document describes the implementation of the Blog Auto-Write feature using the "Dummy Article First" approach. The feature allows users to generate AI-powered blog content with SEO optimization and automatic publishing to Shopify.

## Architecture

### "Dummy Article First" Approach

1. **Immediate Article Creation**: When a user initiates blog generation, a draft article is created immediately in the `articles` table
2. **Job Tracking**: A `BlogAutoWriteJob` record tracks the generation process and references the article
3. **Progressive Updates**: The article is updated with AI-generated content as the job progresses
4. **Token Management**: Tokens are reserved before generation and settled after completion

## Database Schema

### Enhanced Articles Table

New fields added to support AI generation:

```sql
-- AI Generation fields
generation_status VARCHAR(50) DEFAULT 'manual' NOT NULL,
is_ai_generated BOOLEAN DEFAULT false NOT NULL,
```

### BlogAutoWriteJob Table

```sql
CREATE TABLE blog_auto_write_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    shop_id INTEGER NOT NULL,
    article_id INTEGER NULL, -- FK to articles.id (NULL if article deleted)
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    progress INTEGER NOT NULL DEFAULT 0,
    input_data JSONB NOT NULL,
    estimated_credit_usage JSONB NOT NULL DEFAULT '{}',
    credit_usage JSONB NOT NULL DEFAULT '{}',
    error_message TEXT NULL,
    processing_started_at TIMESTAMP NULL,
    processing_completed_at TIMESTAMP NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

## Services

### TokenEstimationService

Provides conservative token estimation with 20% buffer:

```javascript
const estimation = TokenEstimationService.estimateTokenUsage({
  topic: "SEO Best Practices",
  keywords: ["SEO", "optimization"],
  tone: "professional",
  wordCount: 1000,
  blogType: "how_to"
});
```

### BlogAutoWriteService

Core business logic for job management:

```javascript
// Create job with dummy article
const result = await BlogAutoWriteService.createBlogAutoWriteJob({
  shopId,
  shopDomain,
  blogId,
  inputData
});

// Get job status
const job = await BlogAutoWriteService.getJobById(jobId, shopId);
```

### OpenAI Service Extension

Extended with blog generation capability:

```javascript
const { output, usage } = await OpenAiService.generateBlogContent({
  topic: "SEO Tips",
  keywords: ["SEO", "optimization"],
  tone: "professional",
  wordCount: 1000,
  blogType: "how_to"
});
```

## Queue System

### BlogAutoWriteQueue

Handles background processing:

1. **Credit Validation**: Checks reserved credits
2. **Content Generation**: Calls OpenAI API
3. **Progressive Updates**: Updates article with generated content
4. **Shopify Publishing**: Publishes to Shopify
5. **Credit Settlement**: Returns unused credits or charges additional

## API Endpoints

### Create Job
```http
POST /api/blog-auto-write
Content-Type: application/json

{
  "blogId": 123,
  "topic": "Best SEO Practices",
  "keywords": ["SEO", "optimization"],
  "tone": "professional",
  "wordCount": 1000,
  "blogType": "how_to"
}
```

### Get Job Status
```http
GET /api/blog-auto-write/{jobId}
```

### Token Estimation
```http
POST /api/blog-auto-write/token-estimate
Content-Type: application/json

{
  "topic": "SEO Tips",
  "keywords": ["SEO"],
  "tone": "professional",
  "wordCount": 1000,
  "blogType": "how_to"
}
```

### Token Usage
```http
GET /api/blog-auto-write/token-usage
```

## Token Management

### Reservation Flow

1. **Estimate**: Calculate required tokens with 20% buffer
2. **Check**: Verify sufficient tokens available
3. **Reserve**: Increment usage count in Redis
4. **Process**: Generate content with OpenAI
5. **Settle**: Return unused or charge additional tokens

### Cache Integration

Uses existing `cache.addons` system with `AI_OPTIMIZER` addon group:

```javascript
// Check available tokens
const currentUsage = await cache.addons.usageCount(shop, { addon: 'AI_OPTIMIZER' });
const usageLimit = await cache.addons.usageLimit(shop, { addon: 'AI_OPTIMIZER' });

// Reserve tokens
await cache.addons.incrementUsageCount(shop, {
  addon: 'AI_OPTIMIZER',
  incrementBy: estimatedTokens
});
```

## Status Flow

### Generation Status (Articles)
- `manual` → `pending` → `generating` → `content_ready` → `publishing` → `published`

### Job Status (BlogAutoWriteJob)
- `pending` → `generating_outline` → `generating_content` → `optimizing_seo` → `publishing_to_shopify` → `completed`

## Error Handling

- **Insufficient Tokens**: Returns error before job creation
- **Generation Failures**: Updates job status to `failed`, returns reserved tokens
- **API Errors**: Comprehensive error logging and user-friendly messages

## Testing

Unit tests included for:
- Token estimation accuracy
- Service method functionality
- Error handling scenarios

## Migration

Run migrations to set up the database schema:

```bash
npx sequelize-cli db:migrate
```

## Configuration

Add to queue kernel:
```javascript
articles: {
  instances: 1,
  queues: [..., BlogAutoWriteQueue],
}
```

## Future Enhancements

- Real-time progress updates via WebSocket
- Content preview and editing
- Bulk blog generation
- Multi-language support
- Advanced SEO optimization

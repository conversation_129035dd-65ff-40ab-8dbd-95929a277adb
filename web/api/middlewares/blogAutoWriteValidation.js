// @ts-check
const CreditEstimationService = require("../services/CreditEstimationService");
const BlogAutoWriteService = require("../services/BlogAutoWriteService");
const BlogService = require("../services/BlogService");
const FeatureFlagService = require("../services/FeatureFlagService");
const cache = require("../cache");
const { AI_OPTIMIZER } = require("storeseo-enums/subscriptionAddonGroup");
const BlogAutoWriteJobStatus = require("storeseo-enums/blogAutoWrite/jobStatus");

// Import shared validation schemas from storeseo-schema package
const { blogAutoWriteInputSchema, blogAutoWriteCreditEstimationSchema } = require("storeseo-schema/blog/autoWrite");

// Use shared schemas from storeseo-schema package
const blogAutoWriteValidationSchema = blogAutoWriteInputSchema;
const creditEstimateValidationSchema = blogAutoWriteCreditEstimationSchema;

/**
 * Validation middleware for blog auto-write creation
 */
const validateBlogAutoWriteInput = async (req, res, next) => {
  try {
    // Validate the request body
    const validatedData = await blogAutoWriteValidationSchema.validate(req.body, {
      abortEarly: false, // Collect all validation errors
      stripUnknown: true, // Remove unknown fields
    });

    // Apply feature flag overrides only when feature is disabled
    if (!FeatureFlagService.blogs.autoWrite.isImageGenerationEnabled()) {
      // Feature disabled: Override image generation settings regardless of user input
      validatedData.generateFeaturedImage = false;
      validatedData.featuredImageDescription = null;

      console.log("[BlogAutoWrite] Image generation disabled by feature flag - overriding user input");
    }
    // When feature is enabled: Pass through user's choice without modification

    // Replace request body with validated and sanitized data
    req.body = validatedData;

    next();
  } catch (error) {
    // Format validation errors in the expected detailed format
    const errors = error.inner.map((err) => ({
      field: err.path,
      message: err.message,
      code: getErrorCode(err.type, err.path),
    }));

    return res.status(400).json({
      success: false,
      message: "Validation failed",
      errors,
    });
  }
};

/**
 * Middleware to validate that the target blog belongs to the authenticated shop
 * This provides defense-in-depth security validation
 */
const validateBlogOwnership = async (req, res, next) => {
  try {
    const { shopId } = req.user;
    const { targetBlog } = req.body;

    // Skip validation if no targetBlog specified (for backward compatibility)
    if (!targetBlog) {
      return next();
    }

    // Validate that the blog exists and belongs to the shop
    const blog = await BlogService.getBlog(shopId, parseInt(targetBlog));
    if (!blog) {
      return res.status(400).json({
        success: false,
        message: "Invalid blog selection",
        errors: [
          {
            field: "targetBlog",
            message: "Selected blog not found or does not belong to your shop",
            code: "INVALID_BLOG_OWNERSHIP",
          },
        ],
      });
    }

    // Validate that the blog is synced
    if (!blog.is_synced) {
      return res.status(400).json({
        success: false,
        message: "Blog not synced",
        errors: [
          {
            field: "targetBlog",
            message: `Selected blog "${blog.title}" is not synced. Please sync the blog first.`,
            code: "BLOG_NOT_SYNCED",
          },
        ],
      });
    }

    // Attach validated blog to request for controller use
    req.validatedBlog = blog;
    next();
  } catch (error) {
    console.error("Blog ownership validation error:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to validate blog ownership",
      error: {
        code: "BLOG_VALIDATION_ERROR",
        details: error.message,
      },
    });
  }
};

/**
 * Validation middleware for credit estimation endpoint
 */
const validateCreditEstimateInput = async (req, res, next) => {
  try {
    // Validate the request body
    const validatedData = await creditEstimateValidationSchema.validate(req.body, {
      abortEarly: false, // Collect all validation errors
      stripUnknown: true, // Remove unknown fields
    });

    // Apply feature flag overrides for credit estimation only when feature is disabled
    if (!FeatureFlagService.blogs.autoWrite.isImageGenerationEnabled()) {
      // Feature disabled: Override image generation settings regardless of user input
      validatedData.generateFeaturedImage = false;
      validatedData.featuredImageDescription = null;

      console.log("[CreditEstimate] Image generation disabled by feature flag - overriding user input");
    }
    // When feature is enabled: Pass through user's choice for accurate credit estimation

    // Replace request body with validated and sanitized data
    req.body = validatedData;

    next();
  } catch (error) {
    // Format validation errors in the expected detailed format
    const errors = error.inner.map((err) => ({
      field: err.path,
      message: err.message,
      code: getErrorCode(err.type, err.path),
    }));

    return res.status(400).json({
      success: false,
      message: "Validation failed",
      errors,
    });
  }
};

/**
 * Credit pre-validation middleware - checks if user has sufficient credits
 */
const validateSufficientCredits = async (req, res, next) => {
  try {
    const { shop } = req.user;
    const inputData = req.body;

    // Get credit estimation directly from CreditEstimationService
    const creditEstimation = CreditEstimationService.blog.autoWrite(inputData);

    // Check available credits
    const currentUsage = await cache.addons.usageCount(shop, { addon: AI_OPTIMIZER });
    const usageLimit = await cache.addons.usageLimit(shop, { addon: AI_OPTIMIZER });
    const availableCredits = usageLimit - currentUsage;

    if (availableCredits < creditEstimation.totalCredits) {
      return res.status(402).json({
        success: false,
        message: "Insufficient credits",
        error: {
          code: "INSUFFICIENT_CREDITS",
          required: creditEstimation.totalCredits,
          available: availableCredits,
          shortfall: creditEstimation.totalCredits - availableCredits,
        },
        creditInfo: {
          currentUsage,
          usageLimit,
          availableCredits,
          estimatedCost: creditEstimation.totalCredits,
        },
      });
    }

    // Attach credit info to request for use in controller
    req.creditEstimation = creditEstimation;
    req.availableCredits = availableCredits;

    next();
  } catch (error) {
    console.error("Credit validation error:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to validate credits",
      error: {
        code: "CREDIT_VALIDATION_ERROR",
        details: error.message,
      },
    });
  }
};

/**
 * Generate error codes based on validation type and field
 * @param {string} type - Yup validation type
 * @param {string} field - Field name
 * @returns {string} Error code
 */
const getErrorCode = (type, field) => {
  switch (type) {
    case "required":
      return "FIELD_REQUIRED";
    case "min":
      return "FIELD_TOO_SHORT";
    case "max":
      return "FIELD_TOO_LONG";
    case "oneOf":
      return "INVALID_ENUM_VALUE";
    case "matches":
      return "INVALID_FORMAT";
    case "integer":
      return "INVALID_INTEGER";
    case "positive":
      return "INVALID_POSITIVE_NUMBER";
    default:
      return "VALIDATION_ERROR";
  }
};

/**
 * Validation middleware for blog auto-write regeneration
 * Validates original job, merges input data, and checks credit availability
 */
const validateRegenerateCredits = async (req, res, next) => {
  try {
    const { shopId, shop: shopDomain } = req.user;
    const { jobId } = req.params;
    const inputOverrides = req.body || {};

    // Get original job
    const originalJob = await BlogAutoWriteService.getJobById(jobId, shopId);
    if (!originalJob) {
      return res.status(404).json({
        success: false,
        error: {
          code: "JOB_NOT_FOUND",
          message: "Original job not found",
        },
      });
    }

    // Allow regeneration for all job statuses
    // Previously restricted completed jobs, but now we allow regeneration at all status levels

    // Get target article
    const targetArticle = originalJob.article;
    if (!targetArticle) {
      return res.status(400).json({
        success: false,
        error: {
          code: "ARTICLE_NOT_FOUND",
          message: "Target article not found",
        },
      });
    }

    // Merge original input data with overrides
    const mergedInputData = {
      ...originalJob.input_data,
      ...inputOverrides,
    };

    // Apply feature flag overrides for regeneration only when feature is disabled
    if (!FeatureFlagService.blogs.autoWrite.isImageGenerationEnabled()) {
      // Feature disabled: Override image generation settings regardless of user input
      mergedInputData.generateFeaturedImage = false;
      mergedInputData.featuredImageDescription = null;

      console.log("[RegenerateJob] Image generation disabled by feature flag - overriding merged input");
    }
    // When feature is enabled: Use merged input data as-is (original + overrides)

    // Get credit estimation for merged input data
    const creditEstimation = BlogAutoWriteService.getCreditEstimate(mergedInputData);
    const creditUsage = await BlogAutoWriteService.getCreditUsage(shopDomain);

    // Check if user has sufficient credits
    if (creditUsage.availableCredits < creditEstimation.totalCredits) {
      return res.status(402).json({
        success: false,
        error: {
          code: "INSUFFICIENT_CREDITS",
          message: "Insufficient credits for regeneration",
          required: creditEstimation.totalCredits,
          available: creditUsage.availableCredits,
        },
        creditInfo: creditUsage,
      });
    }

    // Attach validated data to request for controller use
    req.originalJob = originalJob;
    req.targetArticle = targetArticle;
    req.mergedInputData = mergedInputData;
    req.creditEstimation = creditEstimation;
    req.creditUsage = creditUsage;

    next();
  } catch (error) {
    console.error("Regenerate credit validation error:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "VALIDATION_ERROR",
        message: "Failed to validate credits for regeneration",
        details: error.message,
      },
    });
  }
};

module.exports = {
  validateBlogAutoWriteInput,
  validateBlogOwnership,
  validateCreditEstimateInput,
  validateSufficientCredits,
  validateRegenerateCredits,
  // Export shared schemas for consistency
  blogAutoWriteValidationSchema: blogAutoWriteInputSchema,
  creditEstimateValidationSchema: blogAutoWriteCreditEstimationSchema,
};

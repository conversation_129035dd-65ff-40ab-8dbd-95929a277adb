/**
 * Label mappings for optimized count data
 */
const OPTIMIZED_COUNT_LABELS = {
  optimizedProductsCount: {
    key: "optimized_products",
    label: "Products",
  },
  optimizedCollectionsCount: {
    key: "optimized_collections",
    label: "Collections",
  },
  optimizedArticlesCount: {
    key: "optimized_articles",
    label: "Blog Posts",
  },
  optimizedImageAltTextsCount: {
    key: "optimized_image_alt_texts",
    label: "Image Alt-Text",
  },
};

/**
 * Label mappings for fixed issues count data
 */
const FIXED_ISSUES_LABELS = {
  // done
  unique_focus_keyword: {
    key: "focus_keyword_is_unique",
    label: "Focus keyword is unique",
  },
  // done
  focus_keyword_in_meta_title: {
    key: "focus_keyword_in_meta_title",
    label: "Focus keyword is used in the meta title",
  },
  // done
  meta_desc_within_char_limit: {
    key: "meta_desc_within_char_limit",
    label: "Meta description is within character limit",
  },
  // done
  meta_title_within_char_limit: {
    key: "meta_title_within_char_limit",
    label: "Meta title is within character limit",
  },
  // done
  unique_title: {
    key: "unique_title",
    label: "Product title is unique",
  },
  // done
  focus_keyword_in_img_alt_text: {
    key: "focus_keyword_in_img_alt_text",
    label: "Focus keyword is used in image alt text",
  },
  // done
  focus_keyword_in_title: {
    key: "focus_keyword_in_title",
    label: "Focus keyword is used in the title",
  },
  // done
  focus_keyword_in_meta_desc: {
    key: "focus_keyword_in_meta_desc",
    label: "Focus keyword found in meta description",
  },
  // done
  focus_keyword_in_url: {
    key: "focus_keyword_in_url",
    label: "Focus keyword is used in the URL",
  },
  // done
  focus_keyword_at_the_beginning_of_meta_title: {
    key: "focus_keyword_beginning_of_meta_title",
    label: "Focus keyword is at the beginning of meta title",
  },
  // done
  keyword_density_in_desc: {
    key: "focus_keyword_density_in_desc",
    label: "Focus keyword density in description is optimal",
  },
  // done
  desc_min_word_count_300: {
    key: "product_desc_min_word_count",
    label: "Product description meets minimum word count",
  },
  // done
  alt_text_in_all_img: {
    key: "alt_text_in_all_img",
    label: "Add alt text to all images",
  },
  // done
  optimized_all_images: {
    key: "optimized_all_images",
    label: "All images are optimized",
  },
  // done
  product_description_is_unique: {
    key: "product_description_is_unique",
    label: "Product description is unique",
  },
  // done
  focus_keyword_in_introduction: {
    key: "focus_keyword_in_introduction",
    label: "Focus keyword is used in the introduction",
  },
  // done
  content_more_then_800_words: {
    key: "content_more_then_800_words",
    label: "Content should be more than 800 words",
  },
  // done
  focus_keyword_density: {
    key: "focus_keyword_density",
    label: "Focus keyword density is optimal",
  },
  // done
  focus_keyword_in_subheading: {
    key: "focus_keyword_in_subheading",
    label: "Focus keyword found in subheadings",
  },
  // done
  meta_desc_within_160_char: {
    key: "meta_desc_within_160_char",
    label: "Meta description is within 160 characters",
  },
  // done
  internal_link_in_content: {
    key: "internal_link_in_content",
    label: "Internal link found in content",
  },
  // done
  external_link_in_content: {
    key: "external_link_in_content",
    label: "External link found in content",
  },
};

const serializePrevWeekData = (existingEmail) => {
  if (!existingEmail || !existingEmail?.data?.data) {
    return { previousOptimizedCount: {}, previousFixedIssuesCount: {} };
  }

  const { reportOverview, fixedIssuesReportOverview } = existingEmail.data.data;

  // Initialize previous count objects
  const previousOptimizedCount = {
    optimizedProductsCount: 0,
    optimizedCollectionsCount: 0,
    optimizedArticlesCount: 0,
    optimizedImageAltTextsCount: 0,
  };

  const previousFixedIssuesCount = {};

  if (reportOverview) {
    reportOverview.forEach((item) => {
      const dbKey = Object.keys(OPTIMIZED_COUNT_LABELS).find((key) => {
        const mapping = OPTIMIZED_COUNT_LABELS[key];
        return mapping && mapping.key === item.key;
      });

      if (dbKey) {
        previousOptimizedCount[dbKey] = item.count || 0;
      }
    });
  }

  if (fixedIssuesReportOverview) {
    fixedIssuesReportOverview.forEach((item) => {
      const dbKey = Object.keys(FIXED_ISSUES_LABELS).find((key) => {
        const mapping = FIXED_ISSUES_LABELS[key];
        return mapping && mapping.key === item.key;
      });

      if (dbKey) {
        previousFixedIssuesCount[dbKey] = item.count || 0;
      }
    });
  }

  return { previousOptimizedCount, previousFixedIssuesCount };
};

/**
 * Transform optimized count data to array format
 * @param {Object} optimizedCount - Current week optimized count data
 * @param {Object} previousWeekData - Previous week optimized count data
 * @returns {Array} Array of objects with key, label, count, and percentageChangedComparedToLastWeek
 */
const serializeOptimizedCountToArray = (optimizedCount, previousWeekData = {}) => {
  return Object.entries(optimizedCount)
    .map(([key, count]) => {
      const mapping = OPTIMIZED_COUNT_LABELS[key];
      if (!mapping) {
        console.warn(`No label mapping found for optimized count key: ${key}`);
        return null;
      }

      const prevCount = previousWeekData[key];

      return {
        key: mapping.key,
        label: mapping.label,
        count: count,
        percentageChangedComparedToLastWeek: calculateGrowthPercentageForArray(prevCount, count),
      };
    })
    .filter(Boolean); // Remove null entries
};

/**
 * Transform fixed issues count data to array format
 * @param {Object} fixedIssuesCount - Current week fixed issues count data
 * @param {Object} previousWeekData - Previous week fixed issues count data
 * @returns {Array} Array of objects with key, label, count, and percentageChangedComparedToLastWeek
 */
const serializeFixedIssuesCountToArray = (fixedIssuesCount, previousWeekData = {}) => {
  return Object.entries(fixedIssuesCount)
    .map(([key, count]) => {
      const mapping = FIXED_ISSUES_LABELS[key];
      if (!mapping) {
        console.warn(`No label mapping found for fixed issues key: ${key}`);
        return null;
      }

      // console.log("value", previousWeekData, "value", previousWeekData[key]);
      const prevCount = previousWeekData[key];

      return {
        key: mapping.key,
        label: mapping.label,
        count: count,
        percentageChangedComparedToLastWeek: calculateGrowthPercentageForArray(prevCount, count),
      };
    })
    .filter(Boolean); // Remove null entries
};

/**
 * Calculate growth percentage for array format according to requirements:
 * - When existing data is not found (prevCount = 0), return 100% if currentCount >= 0, otherwise 0%
 * - When email data exists, calculate percentage change (can be negative)
 * @param {number} prevCount - Previous week count
 * @param {number} currentCount - Current week count
 * @returns {number} Growth percentage as a simple number (can be negative)
 */
const calculateGrowthPercentageForArray = (prevCount, currentCount) => {
  if (prevCount === undefined || prevCount === null) {
    return 100;
  }

  // Handle case where no existing data is found (previous count is 0)
  if (prevCount === 0) {
    return currentCount > 0 ? 100 : 0;
  }

  const growth = ((currentCount - prevCount) / prevCount) * 100;
  return Number(growth.toFixed(2));
};

module.exports = {
  serializePrevWeekData,
  serializeOptimizedCountToArray,
  serializeFixedIssuesCountToArray,
  calculateGrowthPercentageForArray,
};

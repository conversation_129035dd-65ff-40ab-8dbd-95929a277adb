/**
 * Feature Flag Service
 * Centralized service for managing feature flags across the application
 */

const featureFlagsConfig = require('../config/featureFlags');
const BlogAutoWriteJobSteps = require("storeseo-enums/blogAutoWrite/jobSteps");

/**
 * Feature Flag Service Class
 * Provides methods for checking feature flags with logging and validation
 */
class FeatureFlagService {
  /**
   * Get feature flag value with fallback
   * @param {string} flagPath - Dot notation path to flag (e.g., 'BLOG_AUTO_WRITE.IMAGE_GENERATION')
   * @param {boolean} defaultValue - Default value if flag not found
   * @returns {boolean} Feature flag value
   */
  static get(flagPath, defaultValue = false) {
    try {
      const pathParts = flagPath.split(".");
      let current = featureFlagsConfig.featureFlags;

      for (const part of pathParts) {
        if (current[part] === undefined) {
          console.warn(`Feature flag ${flagPath} not found, using default:`, defaultValue);
          return defaultValue;
        }
        current = current[part];
      }

      return current;
    } catch (error) {
      console.error(`Error accessing feature flag ${flagPath}:`, error);
      return defaultValue;
    }
  }

  /**
   * Check if a feature is enabled
   * @param {string} flagPath - Dot notation path to flag
   * @returns {boolean} True if feature is enabled
   */
  static isEnabled(flagPath) {
    return this.get(flagPath, false);
  }

  /**
   * Check if a feature is disabled
   * @param {string} flagPath - Dot notation path to flag
   * @returns {boolean} True if feature is disabled
   */
  static isDisabled(flagPath) {
    return !this.isEnabled(flagPath);
  }

  /**
   * Get all flags for a category
   * @param {string} category - Category name (e.g., 'BLOG_AUTO_WRITE')
   * @returns {Object} All flags in the category
   */
  static getCategory(category) {
    return featureFlagsConfig.featureFlags[category] || {};
  }

  /**
   * Get all feature flags (for debugging/admin)
   * @returns {Object} All feature flags
   */
  static getAll() {
    return featureFlagsConfig.featureFlags;
  }

  /**
   * Log feature flag usage (for analytics)
   * @param {string} flagPath - Flag that was checked
   * @param {boolean} enabled - Whether flag was enabled
   * @param {string} context - Additional context
   */
  static logUsage(flagPath, enabled, context = "") {
    if (process.env.NODE_ENV === "development") {
      console.log(`[FeatureFlag] ${flagPath}: ${enabled ? "ENABLED" : "DISABLED"}${context ? ` (${context})` : ""}`);
    }

    // TODO: Add analytics tracking here
    // analytics.track('feature_flag_used', {
    //   flag: flagPath,
    //   enabled,
    //   context,
    //   timestamp: new Date().toISOString()
    // });
  }

  /**
   * Get flags formatted for frontend API response
   * @returns {Object} Frontend-safe feature flags
   */
  static getFrontendFlags() {
    return {
      BLOG_AUTO_WRITE: {
        IMAGE_GENERATION: this.blogs.autoWrite.isImageGenerationEnabled(),
        BULK_GENERATION: this.blogs.autoWrite.isBulkGenerationEnabled(),
        SCHEDULING: this.blogs.autoWrite.isSchedulingEnabled(),
        TEMPLATES: this.blogs.autoWrite.isTemplatesEnabled(),
      },
    };
  }

  /**
   * Check multiple flags at once
   * @param {string[]} flagPaths - Array of flag paths to check
   * @returns {Object} Object with flag paths as keys and boolean values
   */
  static checkMultiple(flagPaths) {
    const result = {};
    flagPaths.forEach((flagPath) => {
      result[flagPath] = this.isEnabled(flagPath);
    });
    return result;
  }

  /**
   * Validate flag path format
   * @param {string} flagPath - Flag path to validate
   * @returns {boolean} True if valid format
   */
  static isValidFlagPath(flagPath) {
    if (typeof flagPath !== "string") return false;

    const parts = flagPath.split(".");
    if (parts.length < 2) return false;

    // Check if category exists
    const category = parts[0];
    return featureFlagsConfig.featureFlags[category] !== undefined;
  }

  /**
   * Get flag metadata (for admin/debugging)
   * @param {string} flagPath - Flag path
   * @returns {Object} Flag metadata
   */
  static getFlagMetadata(flagPath) {
    return {
      path: flagPath,
      enabled: this.isEnabled(flagPath),
      category: flagPath.split(".")[0],
      valid: this.isValidFlagPath(flagPath),
      timestamp: new Date().toISOString(),
    };
  }

  // =============================================================================
  // NAMESPACE-BASED ORGANIZATION
  // =============================================================================

  /**
   * Blog-related feature flags and methods
   */
  static blogs = {
    autoWrite: {
      /**
       * Check if blog auto-write feature is enabled
       * @returns {boolean}
       */
      isEnabled: () => FeatureFlagService.isEnabled("BLOG_AUTO_WRITE.IMAGE_GENERATION"),

      /**
       * Check if image generation is enabled
       * @returns {boolean}
       */
      isImageGenerationEnabled: () => FeatureFlagService.isEnabled("BLOG_AUTO_WRITE.IMAGE_GENERATION"),

      /**
       * Check if bulk generation is enabled
       * @returns {boolean}
       */
      isBulkGenerationEnabled: () => FeatureFlagService.isEnabled("BLOG_AUTO_WRITE.BULK_GENERATION"),

      /**
       * Check if scheduling is enabled
       * @returns {boolean}
       */
      isSchedulingEnabled: () => FeatureFlagService.isEnabled("BLOG_AUTO_WRITE.SCHEDULING"),

      /**
       * Check if templates are enabled
       * @returns {boolean}
       */
      isTemplatesEnabled: () => FeatureFlagService.isEnabled("BLOG_AUTO_WRITE.TEMPLATES"),

      /**
       * Get enabled blog auto-write steps based on feature flags
       * @returns {BlogAutoWriteJobSteps[]} Array of enabled step enums
       */
      getEnabledSteps: () => {
        const baseSteps = [
          BlogAutoWriteJobSteps.CONTENT_GENERATION,
          BlogAutoWriteJobSteps.SHOPIFY_DRAFT_CREATION,
          BlogAutoWriteJobSteps.ARTICLE_LINKING,
          BlogAutoWriteJobSteps.FIRST_SEO_ANALYSIS,
        ];

        // Add image-related steps if enabled
        if (FeatureFlagService.blogs.autoWrite.isImageGenerationEnabled()) {
          baseSteps.push(
            BlogAutoWriteJobSteps.IMAGE_GENERATION,
            BlogAutoWriteJobSteps.SHOPIFY_IMAGE_UPLOAD,
            BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_UPDATE
          );
        }

        // Always include final steps
        baseSteps.push(BlogAutoWriteJobSteps.FINAL_SEO_ANALYSIS, BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_PUBLISH);

        return baseSteps;
      },

      /**
       * Check if a specific step is enabled
       * @param {BlogAutoWriteJobSteps} stepName - Step enum to check
       * @returns {boolean}
       */
      isStepEnabled: (stepName) => {
        const enabledSteps = FeatureFlagService.blogs.autoWrite.getEnabledSteps();
        return enabledSteps.includes(stepName);
      },

      /**
       * Get step weights for progress calculation (excluding disabled steps)
       * @returns {Object} Step weights object with normalized percentages
       */
      getStepWeights: () => {
        const enabledSteps = FeatureFlagService.blogs.autoWrite.getEnabledSteps();
        const enabledWeights = {};
        let totalWeight = 0;

        // Get weights for enabled steps from enum
        enabledSteps.forEach((step) => {
          if (BlogAutoWriteJobSteps.weights[step]) {
            enabledWeights[step] = BlogAutoWriteJobSteps.weights[step];
            totalWeight += BlogAutoWriteJobSteps.weights[step];
          }
        });

        // Normalize weights to total 100%
        const normalizedWeights = {};
        Object.keys(enabledWeights).forEach((step) => {
          normalizedWeights[step] = Math.round((enabledWeights[step] / totalWeight) * 100);
        });

        return normalizedWeights;
      },

      /**
       * Get all available steps (from enum)
       * @returns {BlogAutoWriteJobSteps[]} All possible steps
       */
      getAllSteps: () => {
        return BlogAutoWriteJobSteps.orderedSteps;
      },

      /**
       * Get step labels for UI display
       * @returns {Object} Step labels mapping
       */
      getStepLabels: () => {
        return BlogAutoWriteJobSteps.labels;
      },

      /**
       * Get step descriptions
       * @returns {Object} Step descriptions mapping
       */
      getStepDescriptions: () => {
        return BlogAutoWriteJobSteps.descriptions;
      },
    },
  };
}

module.exports = FeatureFlagService;

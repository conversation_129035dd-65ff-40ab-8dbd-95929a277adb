const { BlogAutoWriteJob, Article } = require("../../sequelize");
const ArticleService = require("./ArticleService");
const BlogService = require("./BlogService");
const FeatureFlagService = require("./FeatureFlagService");
const CreditEstimationService = require("./CreditEstimationService");
const cache = require("../cache");
const { AI_OPTIMIZER } = require("storeseo-enums/subscriptionAddonGroup");
const BlogGenerationStatus = require("storeseo-enums/blogGenerationStatus");
const BlogAutoWriteJobStatus = require("storeseo-enums/blogAutoWrite/jobStatus");
const BlogAutoWriteJobSteps = require("storeseo-enums/blogAutoWrite/jobSteps");

/**
 * Blog Auto-Write Service
 * Handles the "Dummy Article First" approach for AI blog generation
 */
class BlogAutoWriteService {
  /**
   * Map BlogAutoWriteJobStatus to corresponding BlogGenerationStatus
   * This ensures synchronization between job status and article generation status
   * @param {string} jobStatus - BlogAutoWriteJobStatus value
   * @returns {string} Corresponding BlogGenerationStatus value
   */
  static mapJobStatusToGenerationStatus(jobStatus) {
    const statusMapping = {
      [BlogAutoWriteJobStatus.PENDING]: BlogGenerationStatus.PENDING,
      [BlogAutoWriteJobStatus.GENERATING_CONTENT]: BlogGenerationStatus.GENERATING,
      [BlogAutoWriteJobStatus.CREATING_DRAFT]: BlogGenerationStatus.GENERATING,
      [BlogAutoWriteJobStatus.LINKING_ARTICLE]: BlogGenerationStatus.DRAFT_CREATED, // Article linked to Shopify draft
      [BlogAutoWriteJobStatus.ANALYZING_SEO]: BlogGenerationStatus.CONTENT_READY,
      [BlogAutoWriteJobStatus.GENERATING_IMAGE]: BlogGenerationStatus.CONTENT_READY,
      [BlogAutoWriteJobStatus.UPLOADING_IMAGE]: BlogGenerationStatus.CONTENT_READY,
      [BlogAutoWriteJobStatus.UPDATING_ARTICLE]: BlogGenerationStatus.CONTENT_READY,
      [BlogAutoWriteJobStatus.FINALIZING_SEO]: BlogGenerationStatus.CONTENT_READY,
      [BlogAutoWriteJobStatus.PUBLISHING]: BlogGenerationStatus.PUBLISHING,
      [BlogAutoWriteJobStatus.COMPLETED]: BlogGenerationStatus.DRAFT_PUBLISHED, // Default to draft, will be updated based on autoPublish setting
      [BlogAutoWriteJobStatus.FAILED]: BlogGenerationStatus.FAILED,
      [BlogAutoWriteJobStatus.CANCELLED]: BlogGenerationStatus.FAILED, // Treat cancelled as failed for article status
    };

    return statusMapping[jobStatus] || BlogGenerationStatus.PENDING;
  }
  /**
   * Get enabled steps for job creation based on feature flags
   * NOTE: Only used when FEATURE_BLOG_AUTO_WRITE_IMAGE_GENERATION is disabled
   * When feature is stable and enabled permanently, this method can be removed
   * @returns {Array} Steps array with only enabled steps (same format as getDefaultSteps)
   */
  getEnabledStepsForJob() {
    // Get enabled steps from feature flag service
    const enabledSteps = FeatureFlagService.blogs.autoWrite.getEnabledSteps();

    // Create steps array with only enabled steps (same format as getDefaultSteps)
    const stepsArray = enabledSteps.map((step) => ({
      step,
      completed: false,
      error: null,
      startedAt: null,
      completedAt: null,
    }));

    console.log("[BlogAutoWriteService] Created job with enabled steps:", enabledSteps);
    return stepsArray;
  }

  /**
   * Create a new blog auto-write job with dummy article
   * @param {Object} params - Job creation parameters
   * @param {number} params.shopId - Shop ID
   * @param {string} params.shopDomain - Shop domain
   * @param {Object} params.inputData - Blog generation input
   * @param {number} [params.targetArticleId] - Optional existing article ID to reuse (for regeneration)
   * @returns {Promise<Object>} Created job and article
   */
  async createBlogAutoWriteJob({ shopId, shopDomain, inputData, targetArticleId }) {
    try {
      // 1. Get target blog (either selected blog or default blog)
      let targetBlog;
      if (inputData.targetBlog) {
        // Use selected blog
        targetBlog = await BlogService.getBlog(shopId, parseInt(inputData.targetBlog));
        if (!targetBlog) {
          throw new Error(`Selected blog with ID ${inputData.targetBlog} not found.`);
        }
        if (!targetBlog.is_synced) {
          throw new Error(`Selected blog "${targetBlog.title}" is not synced. Please sync the blog first.`);
        }
      } else {
        // Fallback to default blog for backward compatibility
        targetBlog = await BlogService.getDefaultBlog(shopId);
        if (!targetBlog) {
          throw new Error("No blog selected and no default blog found. Please select a blog or sync blogs first.");
        }
      }

      // 2. Estimate credit usage using new namespace API
      const creditEstimation = CreditEstimationService.blog.autoWrite(inputData);

      // Basic validation - ensure we have a valid estimation
      if (!creditEstimation || creditEstimation.totalCredits <= 0) {
        throw new Error("Invalid credit estimation for blog auto-write");
      }

      // 3. Check available credits
      const currentUsage = await cache.addons.usageCount(shopDomain, { addon: AI_OPTIMIZER });
      const usageLimit = await cache.addons.usageLimit(shopDomain, { addon: AI_OPTIMIZER });
      const availableCredits = usageLimit - currentUsage;

      if (availableCredits < creditEstimation.totalCredits) {
        throw new Error(
          `Insufficient credits. Required: ${creditEstimation.totalCredits}, Available: ${availableCredits}`
        );
      }

      // 4. Reserve credits
      await cache.addons.incrementUsageCount(shopDomain, {
        addon: AI_OPTIMIZER,
        incrementBy: creditEstimation.totalCredits,
      });

      // 5. Get or create article
      let targetArticle;
      if (targetArticleId) {
        // Reuse existing article (for regeneration)
        targetArticle = await Article.findOne({
          where: {
            id: targetArticleId,
            shop_id: shopId,
          },
        });

        if (!targetArticle) {
          throw new Error(`Target article with ID ${targetArticleId} not found`);
        }

        // Update article title to show regeneration status
        await targetArticle.update({
          title: `[Regenerating] ${inputData.topic}`,
          generation_status: BlogGenerationStatus.PENDING, // Reset to pending for regeneration
        });
      } else {
        // Create new dummy article in target blog
        targetArticle = await this.createDummyArticle({
          shopId,
          blogId: targetBlog.id,
          topic: inputData.topic,
        });
      }

      // 6. Create blog auto-write job with proper token tracking
      const jobData = {
        shop_id: shopId,
        article_id: targetArticle.id,
        status: BlogAutoWriteJobStatus.PENDING,
        progress: 0,
        input_data: inputData,

        // Immutable estimation (follows CreditEstimationService structure exactly)
        estimated_credit_usage: creditEstimation,

        // Mutable runtime tracking (identical structure to estimation, values updated during processing)
        credit_usage: {
          totalCredits: 0, // Updated with actual total credits used
          breakdown: {}, // Updated with actual usage breakdown during processing
          feature: creditEstimation.feature,
          featureName: creditEstimation.featureName,
          addonGroup: creditEstimation.addonGroup,
          estimatedCost: 0, // Updated with actual cost
        },

        // Step tracking for job recovery and detailed progress monitoring
        steps: FeatureFlagService.blogs.autoWrite.isImageGenerationEnabled()
          ? BlogAutoWriteJobSteps.getDefaultSteps() // Feature enabled: Use all 9 steps
          : this.getEnabledStepsForJob(), // Feature disabled: Use filtered steps
        retry_count: 0,
        max_retries: 3,
      };

      const job = await BlogAutoWriteJob.create(jobData);

      return {
        job: job.toJSON(),
        article: targetArticle,
        targetBlog: targetBlog,
        creditEstimation,
        availableCredits: availableCredits - creditEstimation.totalCredits,
      };
    } catch (error) {
      console.error("Error creating blog auto-write job:", error);
      throw error;
    }
  }

  /**
   * Generate a unique time-based article ID for dummy articles
   * Format: -YYYYMMDDHHMMSS (negative to distinguish from real Shopify IDs)
   * @returns {number} Unique negative article ID
   */
  generateTimeBasedArticleId() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");
    const hours = String(now.getHours()).padStart(2, "0");
    const minutes = String(now.getMinutes()).padStart(2, "0");
    const seconds = String(now.getSeconds()).padStart(2, "0");

    // Format: YYYYMMDDHHMMSS
    const timeBasedId = `${year}${month}${day}${hours}${minutes}${seconds}`;

    // Return as negative number to distinguish from real Shopify article IDs
    return -parseInt(timeBasedId);
  }

  /**
   * Create a dummy article for AI generation
   * @param {Object} params - Article creation parameters
   * @param {number} params.shopId - Shop ID
   * @param {number} params.blogId - Blog ID
   * @param {string} params.topic - Blog topic
   * @returns {Promise<Object>} Created dummy article
   */
  async createDummyArticle({ shopId, blogId, topic }) {
    const dummyArticleData = {
      shop_id: shopId,
      blog_id: blogId,
      article_id: this.generateTimeBasedArticleId(), // Time-based unique ID
      title: `[Generating] ${topic}`,
      handle: this.generateUniqueHandle(topic),
      author: "AI Assistant",
      body_html: "<p>Content is being generated...</p>",
      tags: [],
      score: 0,
      passed: 0,
      issues: 0,
      focus_keyword: "",
      is_analysed: false,
      is_optimized: false,
      published_at: null, // Draft state
      is_deleted: false,
      is_synced: false, // Not synced to Shopify yet
      generation_status: BlogGenerationStatus.PENDING,
      is_ai_generated: true,
    };

    return await ArticleService.saveArticle(dummyArticleData);
  }

  /**
   * Generate a URL handle from topic
   * @param {string} topic - Blog topic
   * @returns {string} URL-friendly handle
   */
  generateHandle(topic) {
    return topic
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim("-")
      .substring(0, 100);
  }

  /**
   * Generate a unique URL handle from topic with timestamp
   * @param {string} topic - Blog topic
   * @returns {string} URL-friendly unique handle
   */
  generateUniqueHandle(topic) {
    const baseHandle = topic
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim("-")
      .substring(0, 85); // Leave room for timestamp

    // Add timestamp to ensure uniqueness (YYYYMMDDHHMMSS format)
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");
    const hours = String(now.getHours()).padStart(2, "0");
    const minutes = String(now.getMinutes()).padStart(2, "0");
    const seconds = String(now.getSeconds()).padStart(2, "0");

    const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`;
    return `${baseHandle}-${timestamp}`;
  }

  /**
   * Get job by ID with article details (API use - includes shop security filtering)
   * @param {string} jobId - Job ID
   * @param {number} shopId - Shop ID (for security filtering)
   * @returns {Promise<Object|null>} Job with article details
   */
  async getJobById(jobId, shopId) {
    try {
      const job = await BlogAutoWriteJob.findOne({
        where: { id: jobId, shop_id: shopId },
        include: [
          {
            model: Article,
            as: "article",
            required: false,
          },
        ],
      });

      return job ? job.toJSON() : null;
    } catch (error) {
      console.error("Error getting job by ID:", error);
      return null;
    }
  }

  /**
   * Get job by ID for internal/system use (no shop filtering)
   * @param {string} jobId - Job ID (primary key lookup)
   * @returns {Promise<Object|null>} Job with article details
   */
  async getJobByIdInternal(jobId) {
    try {
      const job = await BlogAutoWriteJob.findByPk(jobId, {
        include: [
          {
            model: Article,
            as: "article",
            required: false,
          },
        ],
      });

      return job ? job.toJSON() : null;
    } catch (error) {
      console.error("Error getting job by ID (internal):", error);
      return null;
    }
  }

  /**
   * Generic job update method - updates any job fields in a single atomic operation
   * Also synchronizes the corresponding article's generation_status when job status changes
   * @param {string} jobId - Job ID
   * @param {Object} updateData - Data to update (steps, status, progress, etc.)
   * @returns {Promise<Object|null>} Updated job
   */
  async updateJob(jobId, updateData) {
    try {
      // First, update the job
      const [updatedRowsCount] = await BlogAutoWriteJob.update(updateData, {
        where: { id: jobId },
      });

      if (updatedRowsCount > 0) {
        const updatedJob = await BlogAutoWriteJob.findByPk(jobId);

        // If job status was updated, synchronize the article's generation_status
        if (updateData.status && updatedJob?.article_id) {
          await this.syncArticleGenerationStatus(updatedJob.article_id, updateData.status, updatedJob);
        }

        return updatedJob?.toJSON() || null;
      }

      return null;
    } catch (error) {
      console.error("Error updating job:", error);
      return null;
    }
  }

  /**
   * Synchronize article generation_status with job status
   * Only updates if the new status represents progression, not regression
   * @param {number} articleId - Article database ID
   * @param {string} jobStatus - BlogAutoWriteJobStatus value
   * @param {Object} job - Full job object for context
   * @returns {Promise<void>}
   */
  async syncArticleGenerationStatus(articleId, jobStatus, job) {
    try {
      // Get current article to check existing status
      const currentArticle = await ArticleService.getArticle(job?.shop_id, articleId);
      if (!currentArticle) {
        console.warn(`[${job?.id || "unknown"}] Article ${articleId} not found for status sync`);
        return;
      }

      let generationStatus = BlogAutoWriteService.mapJobStatusToGenerationStatus(jobStatus);

      // Special handling for COMPLETED status based on autoPublish setting
      if (jobStatus === BlogAutoWriteJobStatus.COMPLETED) {
        const autoPublish = job?.input_data?.autoPublish;
        generationStatus = autoPublish ? BlogGenerationStatus.PUBLISHED : BlogGenerationStatus.DRAFT_PUBLISHED;
      }

      // Don't override more specific statuses with general ones
      // For example, don't change DRAFT_LINKED back to CONTENT_READY
      if (this.shouldSkipStatusUpdate(currentArticle.generation_status, generationStatus)) {
        console.info(`[${job?.id || "unknown"}] Skipping article ${articleId} status sync: ${currentArticle.generation_status} -> ${generationStatus} (would be regression)`);
        return;
      }

      // Update the article's generation status
      await ArticleService.updateArticle(articleId, {
        generation_status: generationStatus,
        updated_at: new Date(),
      });

      console.info(`[${job?.id || "unknown"}] Article ${articleId} generation_status synchronized: ${currentArticle.generation_status} -> ${generationStatus}`);
    } catch (error) {
      console.error(`Error synchronizing article generation status for article ${articleId}:`, error);
      // Don't throw here - article sync failure shouldn't break job processing
    }
  }

  /**
   * Determine if status update should be skipped to prevent regression
   * @param {string} currentStatus - Current article generation status
   * @param {string} newStatus - Proposed new status
   * @returns {boolean} True if update should be skipped
   */
  shouldSkipStatusUpdate(currentStatus, newStatus) {
    // Define status progression order (higher number = more advanced)
    const statusOrder = {
      [BlogGenerationStatus.MANUAL]: 0,
      [BlogGenerationStatus.PENDING]: 1,
      [BlogGenerationStatus.GENERATING]: 2,
      [BlogGenerationStatus.CONTENT_READY]: 3,
      [BlogGenerationStatus.DRAFT_CREATED]: 4,
      [BlogGenerationStatus.PUBLISHING]: 5,
      [BlogGenerationStatus.PUBLISHED]: 6,
      [BlogGenerationStatus.DRAFT_PUBLISHED]: 6, // Same level as PUBLISHED
      [BlogGenerationStatus.FAILED]: -1, // Special case - can always be set
    };

    const currentOrder = statusOrder[currentStatus] || 0;
    const newOrder = statusOrder[newStatus] || 0;

    // Allow updates to FAILED status (for job failures/cancellations)
    if (newStatus === BlogGenerationStatus.FAILED) {
      return false;
    }

    // Skip if new status would be a regression (lower order)
    return newOrder < currentOrder;
  }

  /**
   * Get credit usage estimate for input using new namespace API
   * @param {Object} inputData - Blog generation input
   * @returns {Object} Credit estimation
   */
  getCreditEstimate(inputData) {
    return CreditEstimationService.blog.autoWrite(inputData);
  }

  /**
   * Get current credit usage for shop
   * @param {string} shopDomain - Shop domain
   * @returns {Promise<Object>} Credit usage information
   */
  async getCreditUsage(shopDomain) {
    try {
      const currentUsage = await cache.addons.usageCount(shopDomain, { addon: AI_OPTIMIZER });
      const usageLimit = await cache.addons.usageLimit(shopDomain, { addon: AI_OPTIMIZER });
      const totalUsage = await cache.addons.totalUsageCount(shopDomain, { addon: AI_OPTIMIZER });

      return {
        currentUsage: Number(currentUsage) || 0,
        usageLimit: Number(usageLimit) || 0,
        availableCredits: Math.max(0, (Number(usageLimit) || 0) - (Number(currentUsage) || 0)),
        totalUsageThisMonth: Number(totalUsage) || 0,
        addonGroup: AI_OPTIMIZER,
        lastUpdated: new Date(),
      };
    } catch (error) {
      console.error("Error getting credit usage:", error);
      return {
        currentUsage: 0,
        usageLimit: 0,
        availableCredits: 0,
        totalUsageThisMonth: 0,
        addonGroup: AI_OPTIMIZER,
        lastUpdated: new Date(),
      };
    }
  }

  /**
   * Check if an article is a dummy article (not yet created in Shopify)
   * @param {Object} article - Article object
   * @returns {boolean} True if dummy article
   */
  static isDummyArticle(article) {
    return article.article_id < 0 || article.generation_status === BlogGenerationStatus.PENDING;
  }

  /**
   * Get the real Shopify article ID (null if dummy)
   * @param {Object} article - Article object
   * @returns {number|null} Shopify article ID or null if dummy
   */
  static getShopifyArticleId(article) {
    return this.isDummyArticle(article) ? null : article.article_id;
  }

  /**
   * Update dummy article with real Shopify article ID
   * @param {number} articleDbId - Database article ID
   * @param {number} shopifyArticleId - Real Shopify article ID
   * @returns {Promise<Object|null>} Updated article
   */
  async updateDummyWithShopifyId(articleDbId, shopifyArticleId) {
    try {
      return await ArticleService.updateArticle(articleDbId, {
        article_id: shopifyArticleId,
        is_synced: true,
        generation_status: BlogGenerationStatus.PUBLISHED,
      });
    } catch (error) {
      console.error("Error updating dummy article with Shopify ID:", error);
      return null;
    }
  }
}

module.exports = new BlogAutoWriteService();

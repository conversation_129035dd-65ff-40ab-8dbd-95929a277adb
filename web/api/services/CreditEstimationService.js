/**
 * Credit Estimation Service for AI Add-on Features
 * 
 * Provides accurate credit usage estimation for all AI-powered add-on features.
 * Uses namespace-based API design with explicit, detailed methods for better maintainability.
 *
 * Usage Examples:
 * - creditEstimationService.blog.autoWrite(input)           // AI_OPTIMIZER addon
 * - creditEstimationService.product.optimization(input)    // AI_OPTIMIZER addon
 * - creditEstimationService.image.altText(input)           // AI_OPTIMIZER addon
 * - creditEstimationService.image.optimization(input)      // IMAGE_OPTIMIZER addon
 *
 * <AUTHOR> Team
 * @version 3.0.0 - Simplified namespace-based architecture
 * @since 2024-12-22
 */

const { CONTENT, IMAGE } = require("storeseo-enums/aiContentTypes");
const { creditUnit, inputOutputRatio, perImageCredit } = require("../config/ai");

class CreditEstimationService {
  constructor() {
    // Initialize namespace objects with explicit method references
    this.blog = {
      autoWrite: (input) => this.estimateBlogAutoWriteCredits(input),
      optimization: (input, settings) => this.estimateBlogOptimizationCredits(input, settings),
    };

    this.product = {
      optimization: (input, settings) => this.estimateProductOptimizationCredits(input, settings),
    };

    this.collection = {
      optimization: (input, settings) => this.estimateCollectionOptimizationCredits(input, settings),
    };

    this.article = {
      optimization: (input, settings) => this.estimateArticleOptimizationCredits(input, settings),
    };

    this.image = {
      // AI_OPTIMIZER addon features
      altText: (input, settings) => this.estimateImageAltTextCredits(input, settings),

      // IMAGE_OPTIMIZER addon features
      optimization: (input, settings) => this.estimateImageOptimizationCredits(input, settings),
      bulkOptimization: (input, settings) => this.estimateBulkImageOptimizationCredits(input, settings),
    };
  }

  // ========================================
  // BLOG AUTO-WRITE METHODS (AI_OPTIMIZER)
  // ========================================

  /**
   * Estimate credits for blog auto-write feature
   * @param {Object} input - Blog auto-write input parameters
   * @returns {Object} Credit estimation result with addon group info
   */
  estimateBlogAutoWriteCredits(input) {
    let totalCredits = 0;
    const breakdown = {};

    // Calculate content credits using existing detailed logic
    const contentResult = this.estimateBlogContentCredits(input);
    totalCredits += contentResult.userCredits;
    breakdown.content = {
      credits: contentResult.userCredits,
      details: contentResult.breakdown,
    };

    // Calculate image credits using existing detailed logic
    const imageResult = this.estimateBlogImageCredits(input);
    if (imageResult.userCredits > 0) {
      totalCredits += imageResult.userCredits;
      breakdown.images = {
        credits: imageResult.userCredits,
        details: imageResult.breakdown,
      };
    }

    return {
      totalCredits,
      breakdown,
      feature: "blog_auto_write",
      featureName: "Blog Auto-Write",
      addonGroup: "AI_OPTIMIZER",
      estimatedCost: this.convertCreditsToUSD(totalCredits),
    };
  }

  /**
   * Estimate credits for blog optimization feature (placeholder)
   * @param {Object} input - Blog optimization input
   * @param {Object} settings - Optimization settings
   * @returns {Object} Credit estimation result
   */
  estimateBlogOptimizationCredits(input, settings = {}) {
    // Placeholder for future blog optimization feature
    return {
      totalCredits: 0,
      breakdown: {},
      feature: "blog_optimization",
      featureName: "Blog Content Optimization",
      addonGroup: "AI_OPTIMIZER",
      estimatedCost: 0,
    };
  }

  // ========================================
  // PRODUCT OPTIMIZATION METHODS (AI_OPTIMIZER)
  // ========================================

  /**
   * Estimate credits for product optimization feature
   * @param {Object} input - Product optimization input
   * @param {Object} settings - Optimization settings
   * @returns {Object} Credit estimation result
   */
  estimateProductOptimizationCredits(input, settings = {}) {
    // This would integrate with existing product optimization logic
    // For now, return a basic structure that can be expanded
    let totalCredits = 0;
    const breakdown = {};

    // Content optimization credits (placeholder - integrate with existing logic)
    const contentCredits = 5; // Example: base content optimization
    totalCredits += contentCredits;
    breakdown.content = {
      credits: contentCredits,
      details: { method: "product_content_optimization" },
    };

    // Image alt-text credits if enabled
    if (settings.imageAltText && input.images?.length > 0) {
      const imageCredits = this.calculateImageAltTextCredits(input.images, settings);
      totalCredits += imageCredits;
      breakdown.images = {
        credits: imageCredits,
        details: { imageCount: input.images.length, creditsPerImage: imageCredits / input.images.length },
      };
    }

    return {
      totalCredits,
      breakdown,
      feature: "product_optimization",
      featureName: "Product Content Optimization",
      addonGroup: "AI_OPTIMIZER",
      estimatedCost: this.convertCreditsToUSD(totalCredits),
    };
  }

  // ========================================
  // SHARED UTILITY METHODS
  // ========================================

  /**
   * Calculate image alt-text credits using existing AiService logic
   * @param {Array} images - Array of images
   * @param {Object} settings - Alt-text settings
   * @returns {number} Total credits for alt-text generation
   */
  calculateImageAltTextCredits(images, settings) {
    if (!images || images.length === 0) return 0;

    // Use existing AiService IMAGE calculation
    const creditsPerImage = this.calculateCreditUsage({}, IMAGE);
    return images.length * creditsPerImage;
  }

  /**
   * Convert credits to USD
   * @param {number} credits - Credit amount
   * @returns {number} USD amount
   */
  convertCreditsToUSD(credits) {
    // 5000 credits = $10, so 1 credit = $0.002
    return (credits * 10) / 5000;
  }

  // ========================================
  // DETAILED IMPLEMENTATION METHODS
  // (Used internally by namespace methods)
  // ========================================

  /**
   * Estimate token usage for blog content generation (detailed implementation)
   * @param {Object} input - Blog generation input
   * @returns {Object} Token usage estimation
   */
  estimateTokenUsage(input) {
    const {
      topic = "",
      keyword = "",
      blogType = "Guide",
      wordCount = "800-1200",
      tone = "Formal",
      customInstructions = "",
    } = input;

    // System prompt tokens (Version 2 template)
    const systemPromptTokens = 650;

    // User prompt tokens (detailed calculation)
    const userPromptTokens = this.estimateUserPromptTokens(input);

    // Custom instructions tokens
    const customInstructionsTokens = customInstructions ? Math.ceil(customInstructions.length / 4) : 0;

    // JSON format tokens
    const jsonFormatTokens = 50;

    // Total prompt tokens
    const promptTokens = systemPromptTokens + userPromptTokens + customInstructionsTokens + jsonFormatTokens;

    // Completion tokens based on word count
    const completionTokens = this.estimateCompletionTokens(wordCount);

    return {
      promptTokens,
      completionTokens,
      totalTokens: promptTokens + completionTokens,
      breakdown: {
        systemPrompt: systemPromptTokens,
        userPrompt: userPromptTokens,
        customInstructions: customInstructionsTokens,
        jsonFormat: jsonFormatTokens,
        completion: completionTokens,
      },
    };
  }

  /**
   * Estimate user prompt tokens for Version 2 template
   * @param {Object} input - Input parameters
   * @returns {number} Estimated user prompt tokens
   */
  estimateUserPromptTokens(input) {
    const { topic = "", keyword = "", blogType = "Guide", wordCount = "800-1200", tone = "Formal" } = input;

    let tokens = 0;

    // Base template tokens
    tokens += 100;

    // Topic tokens
    tokens += Math.ceil(topic.length / 4);

    // Keyword tokens (if provided)
    if (keyword) {
      tokens += Math.ceil(keyword.length / 4);
    }

    // Blog type tokens
    tokens += Math.ceil(blogType.length / 4);

    // Word count tokens
    tokens += Math.ceil(wordCount.toString().length / 4);

    // Tone tokens
    tokens += Math.ceil(tone.length / 4);

    // SEO optimization tokens
    tokens += 250;

    // Structure tokens
    tokens += 150;

    return tokens;
  }

  /**
   * Estimate completion tokens based on word count
   * @param {string|number} wordCount - Target word count
   * @returns {number} Estimated completion tokens
   */
  estimateCompletionTokens(wordCount) {
    const wordCountMap = {
      "Up to 500": 625,
      "500-800": 900,
      "800-1200": 1350,
      "1200-1500": 1688,
      "1500-2000": 2250,
      "2000+": 2700,
    };

    if (typeof wordCount === "string" && wordCountMap[wordCount]) {
      return wordCountMap[wordCount];
    }

    if (typeof wordCount === "number") {
      return Math.ceil(wordCount * 1.125);
    }

    return wordCountMap["800-1200"];
  }

  /**
   * Calculate user credit usage based on OpenAI token usage (with markup for AI_OPTIMIZER)
   * This matches the AiService.calculateCreditUsage method for CONTENT type only
   * Note: IMAGE type in AiService is for alt-text generation, not DALL-E image generation
   * @param {{prompt_tokens: number, completion_tokens: number, total_tokens: number}} usage - OpenAI token usage
   * @param {string} type - Content type (CONTENT or IMAGE)
   * @returns {number} Credit usage for AI_OPTIMIZER addon group
   */
  calculateCreditUsage(usage, type = CONTENT) {
    let creditUsage = 0;
    if (type === CONTENT) {
      creditUsage = ((usage.prompt_tokens + usage.completion_tokens * inputOutputRatio) / creditUnit).toFixed(1);
    } else if (type === IMAGE) {
      // Note: This IMAGE type is for alt-text generation, not used in blog auto-write
      creditUsage = (perImageCredit / creditUnit).toFixed(1);
    }

    return Number(creditUsage);
  }

  /**
   * Calculate approximate credit usage for blog content generation
   * @param {Object} input - Blog generation input parameters
   * @returns {Object} Content credit usage estimation
   */
  estimateBlogContentCredits(input) {
    const tokenEstimation = this.estimateTokenUsage(input);

    // Convert to OpenAI usage format
    const openAiUsage = {
      prompt_tokens: tokenEstimation.promptTokens,
      completion_tokens: tokenEstimation.completionTokens,
      total_tokens: tokenEstimation.promptTokens + tokenEstimation.completionTokens,
    };

    // Calculate user credits (with markup via AiService calculation)
    const userCredits = this.calculateCreditUsage(openAiUsage, CONTENT);

    return {
      userCredits,
      tokenEstimation,
      openAiUsage,
      breakdown: {
        tokenEstimation,
        openAiUsage,
        userCredits,
      },
    };
  }

  /**
   * Calculate approximate credit usage for blog image generation
   * Note: This is for DALL-E image generation (1792x1024), not alt-text generation
   * Uses DALL-E 3 pricing ($0.08 for 1792x1024) with 2.5x markup = 100 user credits
   * @param {Object} input - Image generation input parameters
   * @returns {Object} Image credit usage estimation
   */
  estimateBlogImageCredits(input) {
    const { generateFeaturedImage, featuredImageDescription } = input;

    // Only calculate image credits if user wants to generate featured image
    if (!generateFeaturedImage) {
      return {
        userCredits: 0,
        dalleActualCost: 0,
        breakdown: {
          dalleActualCost: 0,
          userCredits: 0,
          reason: "Featured image generation disabled",
        },
      };
    }

    // DALL-E 3 pricing for 1792x1024 (actual size we call OpenAI API with)
    // Standard quality: $0.08 per image
    const dalleActualCostUSD = 0.08;

    // Convert to user credits with 2.5x markup for image generation
    // Logic: We sell 5000 AI optimizer credits for $10
    // So $1 = 500 credits, $0.08 = 40 credits
    // Apply 2.5x markup: 40 * 2.5 = 100 credits
    const creditsPerDollar = 500; // 5000 credits / $10
    const imageMarkup = 2.5; // 2.5x markup for image generation
    const userCredits = Math.ceil(dalleActualCostUSD * creditsPerDollar * imageMarkup);

    // Determine reason for billing
    const hasDescription = featuredImageDescription && featuredImageDescription.trim();
    const reason = hasDescription ? "Custom image description provided" : "Using topic as image description";

    return {
      userCredits,
      dalleActualCost: dalleActualCostUSD,
      breakdown: {
        dalleActualCost: dalleActualCostUSD,
        userCredits: userCredits,
        reason,
      },
    };
  }

  /**
   * Calculate total user credits for complete blog auto-write job
   * @param {Object} input - Complete blog generation input
   * @returns {Object} Total credit usage estimation
   */
  estimateTotalBlogCredits(input) {
    const contentCredits = this.estimateBlogContentCredits(input);
    const imageCredits = this.estimateBlogImageCredits(input);

    const totalUserCredits = contentCredits.userCredits + imageCredits.userCredits;

    return {
      totalUserCredits,
      contentCredits: contentCredits.userCredits,
      imageCredits: imageCredits.userCredits,
      breakdown: {
        content: contentCredits.breakdown,
        image: imageCredits.breakdown,
        total: {
          userCredits: totalUserCredits,
        },
      },
    };
  }
}

module.exports = new CreditEstimationService();

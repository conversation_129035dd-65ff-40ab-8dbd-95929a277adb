/**
 * Feature Flags Configuration
 * Simple configuration file for feature flag definitions
 * 
 * Environment Variables Pattern:
 * - FEATURE_{CATEGORY}_{FEATURE_NAME}=true/false
 * - Example: FEATURE_BLOG_AUTO_WRITE_IMAGE_GENERATION=false
 */

// Helper function to get environment variable with fallback
const getEnvFlag = (envVar, defaultValue = false) => {
  const value = process.env[envVar];
  if (value === undefined) return defaultValue;
  return value.toLowerCase() === 'true';
};

/**
 * Feature Flags Configuration Object
 * This is the single source of truth for all feature flags
 */
const featureFlags = {
  // Blog Auto-Write Feature Flags
  BLOG_AUTO_WRITE: {
    // Image generation feature (Steps 5-7)
    IMAGE_GENERATION: getEnvFlag("FEATURE_BLOG_AUTO_WRITE_IMAGE_GENERATION", false),

    // Future blog features
    BULK_GENERATION: getEnvFlag("FEATURE_BLOG_AUTO_WRITE_BULK_GENERATION", false),
    SCHEDULING: getEnvFlag("FEATURE_BLOG_AUTO_WRITE_SCHEDULING", false),
    TEMPLATES: getEnvFlag("FEATURE_BLOG_AUTO_WRITE_TEMPLATES", false),
  },
};

// Note: Business logic functions moved to FeatureFlagService
// This config file now only contains raw configuration data

module.exports = {
  featureFlags,
  getEnvFlag,

  // Export specific categories for convenience
  BLOG_AUTO_WRITE: featureFlags.BLOG_AUTO_WRITE,
};

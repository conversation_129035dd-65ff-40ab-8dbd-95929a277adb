const express = require("express");
const router = express.Router();
const BlogAutoWriteController = require("../controllers/BlogAutoWriteController");

// Import validation middleware
const {
  validateBlogAutoWriteInput,
  validateBlogOwnership,
  validateCreditEstimateInput,
  validateSufficientCredits,
  validateRegenerateCredits,
} = require("../middlewares/blogAutoWriteValidation");

/**
 * @route POST /api/blog-auto-write
 * @desc Create a new blog auto-write job in user-selected blog
 * @access Private (Shop authenticated)
 * @middleware Input validation, blog ownership validation, credit validation
 * @body {
 *   topic: string,
 *   targetBlog: string,
 *   keyword?: string,
 *   tone?: string,
 *   wordCount: string,
 *   blogType: string,
 *   customInstructions?: string,
 *   generateFeaturedImage?: boolean (default: false),
 *   featuredImageDescription?: string,
 *   autoPublish?: boolean (default: false)
 * }
 */
router.post(
  "/",
  validateBlogAutoWriteInput,
  validateBlogOwnership,
  validateSufficientCredits,
  BlogAutoWriteController.createJob
);

/**
 * @route GET /api/blog-auto-write/:jobId
 * @desc Get job details by ID (includes status, progress, steps, credit usage)
 * @access Private (Shop authenticated)
 * @param {string} jobId - Job UUID
 * @returns {
 *   jobId: string,
 *   status: string,
 *   progress: number,
 *   steps: array,
 *   articleId: number,
 *   article: object,
 *   inputData: object,
 *   estimatedCreditUsage: object,
 *   creditUsage: object,
 *   errorMessage: string,
 *   createdAt: Date,
 *   updatedAt: Date,
 *   processingStartedAt: Date,
 *   processingCompletedAt: Date
 * }
 */
router.get("/:jobId", BlogAutoWriteController.getJob);

/**
 * @route POST /api/blog-auto-write/credit-estimate
 * @desc Get credit usage estimate for blog auto-write generation
 * @access Private (Shop authenticated)
 * @middleware Input validation
 * @body {
 *   topic: string,
 *   keyword?: string,
 *   tone?: string,
 *   wordCount: string,
 *   blogType: string,
 *   customInstructions?: string,
 *   generateFeaturedImage?: boolean (default: false),
 *   featuredImageDescription?: string
 * }
 * @returns {
 *   estimatedCredits: number,
 *   availableCredits: number,
 *   currentUsage: number,
 *   usageLimit: number,
 *   canProceed: boolean,
 *   feature: string,
 *   featureName: string,
 *   addonGroup: string,
 *   estimatedCost: number,
 *   breakdown: object
 * }
 */
router.post("/credit-estimate", validateCreditEstimateInput, BlogAutoWriteController.getCreditEstimate);

/**
 * @route GET /api/blog-auto-write/credit-usage
 * @desc Get current credit usage for AI_OPTIMIZER addon
 * @access Private (Shop authenticated)
 * @returns {
 *   currentUsage: number,
 *   usageLimit: number,
 *   availableCredits: number,
 *   totalUsageThisMonth: number,
 *   addonGroup: string,
 *   lastUpdated: Date
 * }
 */
router.get("/credit-usage", BlogAutoWriteController.getCreditUsage);

/**
 * @route GET /api/blog-auto-write/jobs
 * @desc List jobs for shop with pagination
 * @access Private (Shop authenticated)
 * @query {
 *   page?: number,
 *   limit?: number,
 *   status?: string
 * }
 */
router.get("/jobs", BlogAutoWriteController.listJobs);

/**
 * @route DELETE /api/blog-auto-write/:jobId
 * @desc Cancel/delete a job
 * @access Private (Shop authenticated)
 * @param {string} jobId - Job UUID
 */
router.delete("/:jobId", BlogAutoWriteController.cancelJob);

/**
 * @route POST /api/blog-auto-write/:jobId/regenerate
 * @desc Regenerate job with optional input overrides
 * @access Private (Shop authenticated)
 * @middleware validateRegenerateCredits - Validates job, merges input data, and checks credits
 * @param {string} jobId - Original job UUID to regenerate
 * @body {Object} inputOverrides - Optional input data overrides (same format as create job)
 * @returns {
 *   jobId: string,
 *   articleId: number,
 *   status: string,
 *   progress: number,
 *   estimatedCredits: number,
 *   availableCredits: number,
 *   feature: string,
 *   featureName: string,
 *   addonGroup: string,
 *   regeneration: {
 *     cancelledJobId: string,
 *     refundedCredits?: number
 *   }
 * }
 * @note Response structure matches create job for consistency, with additional regeneration metadata
 * @note Can regenerate jobs in any status including completed
 */
router.post("/:jobId/regenerate", validateRegenerateCredits, BlogAutoWriteController.regenerateJob);

module.exports = router;

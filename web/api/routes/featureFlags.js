const express = require('express');
const FeatureFlagService = require("../services/FeatureFlagService");
const router = express.Router();

/**
 * Feature Flags API Routes
 * Provides frontend access to backend feature flag configuration
 */

/**
 * @route GET /api/feature-flags
 * @desc Get current feature flags for the authenticated shop
 * @access Private (Shop authenticated)
 * @returns {Object} Feature flags object
 */
router.get("/", (req, res) => {
  try {
    // Get frontend-safe feature flags from service
    const frontendFlags = FeatureFlagService.getFrontendFlags();

    // Log flag access for analytics (in development)
    if (process.env.NODE_ENV === "development") {
      console.log("[FeatureFlags API] Flags requested by shop:", req.user?.shop || "unknown");
    }

    res.json({
      success: true,
      flags: frontendFlags,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    // console.error("Error fetching feature flags:", error);

    // Return safe defaults on error
    const safeDefaults = {
      BLOG_AUTO_WRITE: {
        IMAGE_GENERATION: false,
        BULK_GENERATION: false,
        SCHEDULING: false,
        TEMPLATES: false,
      },
      AI: {
        SUGGESTIONS: false,
        AUTO_OPTIMIZATION: true, // Safe to keep enabled
        CONTENT_ANALYSIS: true, // Safe to keep enabled
      },
      UI: {
        NEW_DASHBOARD: false,
        DARK_MODE: false,
        ADVANCED_FILTERS: true, // Safe to keep enabled
      },
      INTEGRATIONS: {
        SOCIAL_MEDIA: false,
        ANALYTICS: true, // Safe to keep enabled
        WEBHOOKS: false,
      },
    };

    res.status(500).json({
      success: false,
      error: "Failed to fetch feature flags",
      flags: safeDefaults, // Provide fallback flags
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * @route GET /api/feature-flags/:category
 * @desc Get feature flags for a specific category
 * @access Private (Shop authenticated)
 * @param {string} category - Feature flag category (e.g., 'BLOG_AUTO_WRITE')
 * @returns {Object} Category-specific feature flags
 */
router.get("/:category", (req, res) => {
  try {
    const { category } = req.params;
    const categoryUpper = category.toUpperCase();

    // Validate category
    const validCategories = ["BLOG_AUTO_WRITE", "AI", "UI", "INTEGRATIONS"];
    if (!validCategories.includes(categoryUpper)) {
      return res.status(400).json({
        success: false,
        error: `Invalid category. Valid categories: ${validCategories.join(", ")}`,
      });
    }

    const categoryFlags = FeatureFlagService.getCategory(categoryUpper);

    res.json({
      success: true,
      category: categoryUpper,
      flags: categoryFlags,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error(`Error fetching feature flags for category ${req.params.category}:`, error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch category feature flags",
      flags: {},
    });
  }
});

/**
 * @route POST /api/feature-flags/check
 * @desc Check multiple feature flags at once
 * @access Private (Shop authenticated)
 * @body {string[]} flags - Array of flag paths to check
 * @returns {Object} Flag states
 */
router.post("/check", (req, res) => {
  try {
    const { flags } = req.body;

    if (!Array.isArray(flags)) {
      return res.status(400).json({
        success: false,
        error: "Flags must be an array of flag paths",
      });
    }

    const flagStates = FeatureFlagService.checkMultiple(flags);

    res.json({
      success: true,
      flags: flagStates,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error checking feature flags:", error);
    res.status(500).json({
      success: false,
      error: "Failed to check feature flags",
    });
  }
});

module.exports = router;

/**
 * Clean Integration Test for Blog Auto-Write with New Credit Estimation Service
 * Tests the clean integration without backward compatibility
 */

const BlogAutoWriteService = require("../../services/BlogAutoWriteService");
const CreditEstimationService = require("../../services/CreditEstimationService");

describe("Blog Auto-Write Clean Integration (No Backward Compatibility)", () => {
  const sampleInput = {
    topic: "SEO Best Practices for E-commerce",
    keyword: "SEO optimization",
    blogType: "Guide",
    wordCount: "800-1200",
    tone: "Professional",
    customInstructions: "Include practical examples and case studies",
    featuredImageDescription: "Modern e-commerce dashboard showing SEO metrics"
  };

  describe("BlogAutoWriteService - New Methods Only", () => {
    it("should use new getCreditEstimate method", () => {
      const creditEstimation = BlogAutoWriteService.getCreditEstimate(sampleInput);
      
      expect(creditEstimation).toHaveProperty("totalCredits");
      expect(creditEstimation).toHaveProperty("feature", "blog_auto_write");
      expect(creditEstimation).toHaveProperty("featureName", "Blog Auto-Write");
      expect(creditEstimation).toHaveProperty("addonGroup", "AI_OPTIMIZER");
      expect(creditEstimation).toHaveProperty("estimatedCost");
      expect(creditEstimation).toHaveProperty("breakdown");
      
      expect(creditEstimation.totalCredits).toBeGreaterThan(0);
      expect(typeof creditEstimation.estimatedCost).toBe("number");
    });

    it("should have detailed breakdown structure", () => {
      const creditEstimation = BlogAutoWriteService.getCreditEstimate(sampleInput);
      
      expect(creditEstimation.breakdown).toHaveProperty("content");
      expect(creditEstimation.breakdown).toHaveProperty("images");
      
      expect(creditEstimation.breakdown.content).toHaveProperty("credits");
      expect(creditEstimation.breakdown.content).toHaveProperty("details");
      
      expect(creditEstimation.breakdown.images).toHaveProperty("credits");
      expect(creditEstimation.breakdown.images).toHaveProperty("details");
    });

    it("should calculate image credits correctly for DALL-E", () => {
      const creditEstimation = BlogAutoWriteService.getCreditEstimate(sampleInput);
      
      // Should have 100 credits for DALL-E image (2.5x markup on $0.08)
      expect(creditEstimation.breakdown.images.credits).toBe(100);
      expect(creditEstimation.breakdown.images.details.dalleActualCost).toBe(0.08);
    });

    it("should handle input without image description", () => {
      const inputWithoutImage = { ...sampleInput };
      delete inputWithoutImage.featuredImageDescription;
      
      const creditEstimation = BlogAutoWriteService.getCreditEstimate(inputWithoutImage);
      
      expect(creditEstimation.breakdown.content).toBeDefined();
      expect(creditEstimation.breakdown.images).toBeUndefined();
      expect(creditEstimation.totalCredits).toBe(creditEstimation.breakdown.content.credits);
    });
  });

  describe("Direct CreditEstimationService - Namespace API", () => {
    it("should work with blog.autoWrite namespace", () => {
      const result = CreditEstimationService.blog.autoWrite(sampleInput);
      
      expect(result).toHaveProperty("totalCredits");
      expect(result).toHaveProperty("breakdown");
      expect(result).toHaveProperty("feature", "blog_auto_write");
      expect(result).toHaveProperty("featureName", "Blog Auto-Write");
      expect(result).toHaveProperty("addonGroup", "AI_OPTIMIZER");
      expect(result.totalCredits).toBeGreaterThan(0);
    });

    it("should work with other namespace methods", () => {
      // Test product optimization
      const productResult = CreditEstimationService.product.optimization({ title: "Test Product" });
      expect(productResult.addonGroup).toBe("AI_OPTIMIZER");
      
      // Test image optimization
      const imageResult = CreditEstimationService.image.optimization({ imageCount: 3 });
      expect(imageResult.addonGroup).toBe("IMAGE_OPTIMIZER");
      expect(imageResult.totalCredits).toBe(3);
      
      // Test image alt-text
      const altTextResult = CreditEstimationService.image.altText({ images: [{}, {}] });
      expect(altTextResult.addonGroup).toBe("AI_OPTIMIZER");
    });
  });

  describe("Credit Calculation Accuracy", () => {
    it("should have consistent total credits", () => {
      const result = CreditEstimationService.blog.autoWrite(sampleInput);
      const expectedTotal = result.breakdown.content.credits + result.breakdown.images.credits;
      
      expect(result.totalCredits).toBe(expectedTotal);
    });

    it("should calculate USD cost correctly", () => {
      const result = CreditEstimationService.blog.autoWrite(sampleInput);
      const expectedCost = (result.totalCredits * 10) / 5000; // 5000 credits = $10
      
      expect(result.estimatedCost).toBeCloseTo(expectedCost, 4);
    });

    it("should handle different word counts", () => {
      const shortInput = { ...sampleInput, wordCount: "Up to 500" };
      const longInput = { ...sampleInput, wordCount: "2000+" };
      
      const shortResult = CreditEstimationService.blog.autoWrite(shortInput);
      const longResult = CreditEstimationService.blog.autoWrite(longInput);
      
      expect(longResult.breakdown.content.credits).toBeGreaterThan(shortResult.breakdown.content.credits);
    });
  });

  describe("Addon Group Classification", () => {
    it("should correctly classify AI_OPTIMIZER features", () => {
      const blogResult = CreditEstimationService.blog.autoWrite(sampleInput);
      const productResult = CreditEstimationService.product.optimization({});
      const altTextResult = CreditEstimationService.image.altText({ images: [{}] });
      
      expect(blogResult.addonGroup).toBe("AI_OPTIMIZER");
      expect(productResult.addonGroup).toBe("AI_OPTIMIZER");
      expect(altTextResult.addonGroup).toBe("AI_OPTIMIZER");
    });

    it("should correctly classify IMAGE_OPTIMIZER features", () => {
      const optimizationResult = CreditEstimationService.image.optimization({ imageCount: 1 });
      const bulkResult = CreditEstimationService.image.bulkOptimization({ imageCount: 10 });
      
      expect(optimizationResult.addonGroup).toBe("IMAGE_OPTIMIZER");
      expect(bulkResult.addonGroup).toBe("IMAGE_OPTIMIZER");
    });
  });

  describe("Error Handling and Edge Cases", () => {
    it("should handle empty input gracefully", () => {
      const result = CreditEstimationService.blog.autoWrite({});
      
      expect(result).toHaveProperty("totalCredits");
      expect(result.totalCredits).toBeGreaterThanOrEqual(0);
      expect(result.feature).toBe("blog_auto_write");
    });

    it("should handle minimal valid input", () => {
      const minimalInput = {
        topic: "Test",
        blogType: "Guide",
        wordCount: "500-800"
      };
      
      const result = CreditEstimationService.blog.autoWrite(minimalInput);
      
      expect(result.totalCredits).toBeGreaterThan(0);
      expect(result.breakdown.content).toBeDefined();
    });

    it("should handle zero image count", () => {
      const result = CreditEstimationService.image.optimization({ imageCount: 0 });
      
      expect(result.totalCredits).toBe(0);
      expect(result.addonGroup).toBe("IMAGE_OPTIMIZER");
    });
  });

  describe("Service Integration Consistency", () => {
    it("should have consistent results between service and direct calls", () => {
      const serviceResult = BlogAutoWriteService.getCreditEstimate(sampleInput);
      const directResult = CreditEstimationService.blog.autoWrite(sampleInput);
      
      expect(serviceResult.totalCredits).toBe(directResult.totalCredits);
      expect(serviceResult.feature).toBe(directResult.feature);
      expect(serviceResult.addonGroup).toBe(directResult.addonGroup);
    });
  });
});

describe("Controller Response Format Validation", () => {
  it("should provide expected response structure for frontend", () => {
    const creditEstimation = BlogAutoWriteService.getCreditEstimate({
      topic: "Test Topic",
      wordCount: "800-1200",
      blogType: "Guide",
      featuredImageDescription: "Test image"
    });

    // Simulate controller response structure
    const controllerResponse = {
      estimatedCredits: creditEstimation.totalCredits,
      feature: creditEstimation.feature,
      featureName: creditEstimation.featureName,
      addonGroup: creditEstimation.addonGroup,
      estimatedCost: creditEstimation.estimatedCost,
      breakdown: creditEstimation.breakdown
    };

    expect(controllerResponse).toHaveProperty("estimatedCredits");
    expect(controllerResponse).toHaveProperty("feature", "blog_auto_write");
    expect(controllerResponse).toHaveProperty("featureName", "Blog Auto-Write");
    expect(controllerResponse).toHaveProperty("addonGroup", "AI_OPTIMIZER");
    expect(controllerResponse).toHaveProperty("estimatedCost");
    expect(controllerResponse).toHaveProperty("breakdown");
    
    expect(typeof controllerResponse.estimatedCredits).toBe("number");
    expect(typeof controllerResponse.estimatedCost).toBe("number");
    expect(controllerResponse.estimatedCredits).toBeGreaterThan(0);
  });
});

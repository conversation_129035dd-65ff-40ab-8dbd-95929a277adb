/**
 * Integration Test for Blog Auto-Write with New Credit Estimation Service
 * Tests the integration between BlogAutoWriteService and CreditEstimationService
 */

const BlogAutoWriteService = require("../../services/BlogAutoWriteService");
const CreditEstimationService = require("../../services/CreditEstimationService");

describe("Blog Auto-Write Integration with New Credit Estimation", () => {
  const sampleInput = {
    topic: "SEO Best Practices",
    keyword: "SEO optimization",
    blogType: "Guide",
    wordCount: "800-1200",
    tone: "Formal",
    customInstructions: "Include examples",
    featuredImageDescription: "Professional dashboard"
  };

  describe("BlogAutoWriteService integration", () => {
    it("should use new namespace API correctly", () => {
      // Test new method
      const creditEstimation = BlogAutoWriteService.getCreditEstimate(sampleInput);
      
      expect(creditEstimation).toHaveProperty("totalCredits");
      expect(creditEstimation).toHaveProperty("feature", "blog_auto_write");
      expect(creditEstimation).toHaveProperty("featureName", "Blog Auto-Write");
      expect(creditEstimation).toHaveProperty("addonGroup", "AI_OPTIMIZER");
      expect(creditEstimation.totalCredits).toBeGreaterThan(0);
    });

    it("should maintain backward compatibility", () => {
      // Test old method still works
      const tokenEstimation = BlogAutoWriteService.getTokenEstimate(sampleInput);
      
      expect(tokenEstimation).toHaveProperty("totalEstimated");
      expect(tokenEstimation).toHaveProperty("breakdown");
      expect(tokenEstimation.totalEstimated).toBeGreaterThan(0);
    });

    it("should have consistent results between new and old methods", () => {
      const creditEstimation = BlogAutoWriteService.getCreditEstimate(sampleInput);
      const tokenEstimation = BlogAutoWriteService.getTokenEstimate(sampleInput);
      
      // Should return the same total credits/tokens
      expect(creditEstimation.totalCredits).toBe(tokenEstimation.totalEstimated);
    });
  });

  describe("Direct CreditEstimationService usage", () => {
    it("should work with namespace API", () => {
      const result = CreditEstimationService.blog.autoWrite(sampleInput);
      
      expect(result).toHaveProperty("totalCredits");
      expect(result).toHaveProperty("breakdown");
      expect(result).toHaveProperty("feature", "blog_auto_write");
      expect(result).toHaveProperty("addonGroup", "AI_OPTIMIZER");
      expect(result.totalCredits).toBeGreaterThan(0);
    });

    it("should work with backward compatibility methods", () => {
      const contentCredits = CreditEstimationService.estimateBlogContentCredits(sampleInput);
      const imageCredits = CreditEstimationService.estimateBlogImageCredits(sampleInput);
      const totalCredits = CreditEstimationService.estimateTotalBlogCredits(sampleInput);
      
      expect(contentCredits).toHaveProperty("userCredits");
      expect(imageCredits).toHaveProperty("userCredits");
      expect(totalCredits).toHaveProperty("totalUserCredits");
      
      expect(totalCredits.totalUserCredits).toBe(
        contentCredits.userCredits + imageCredits.userCredits
      );
    });
  });

  describe("Credit calculation accuracy", () => {
    it("should calculate content credits correctly", () => {
      const result = CreditEstimationService.blog.autoWrite(sampleInput);
      
      expect(result.breakdown.content).toBeDefined();
      expect(result.breakdown.content.credits).toBeGreaterThan(0);
      expect(result.breakdown.content.details).toBeDefined();
    });

    it("should calculate image credits correctly for DALL-E", () => {
      const result = CreditEstimationService.blog.autoWrite(sampleInput);
      
      expect(result.breakdown.images).toBeDefined();
      expect(result.breakdown.images.credits).toBe(100); // 2.5x markup on $0.08
      expect(result.breakdown.images.details.dalleActualCost).toBe(0.08);
    });

    it("should handle input without image description", () => {
      const inputWithoutImage = { ...sampleInput };
      delete inputWithoutImage.featuredImageDescription;
      
      const result = CreditEstimationService.blog.autoWrite(inputWithoutImage);
      
      expect(result.breakdown.content).toBeDefined();
      expect(result.breakdown.images).toBeUndefined();
      expect(result.totalCredits).toBe(result.breakdown.content.credits);
    });
  });

  describe("Addon group support", () => {
    it("should correctly identify AI_OPTIMIZER addon group", () => {
      const result = CreditEstimationService.blog.autoWrite(sampleInput);
      expect(result.addonGroup).toBe("AI_OPTIMIZER");
    });

    it("should support IMAGE_OPTIMIZER addon group", () => {
      const result = CreditEstimationService.image.optimization({ imageCount: 5 });
      expect(result.addonGroup).toBe("IMAGE_OPTIMIZER");
      expect(result.totalCredits).toBe(5); // 1 credit per image
    });
  });

  describe("Error handling", () => {
    it("should handle invalid input gracefully", () => {
      const result = CreditEstimationService.blog.autoWrite({});
      
      expect(result).toHaveProperty("totalCredits");
      expect(result.totalCredits).toBeGreaterThanOrEqual(0);
    });

    it("should handle missing optional fields", () => {
      const minimalInput = {
        topic: "Test Topic",
        blogType: "Guide",
        wordCount: "500-800"
      };
      
      const result = CreditEstimationService.blog.autoWrite(minimalInput);
      
      expect(result).toHaveProperty("totalCredits");
      expect(result.totalCredits).toBeGreaterThan(0);
    });
  });
});

describe("Controller Integration Simulation", () => {
  it("should work with controller-style input", () => {
    // Simulate controller input format
    const controllerInput = {
      topic: "SEO Guide",
      keywords: ["SEO", "optimization"], // Array format from controller
      tone: "Formal",
      wordCount: "800-1200",
      blogType: "Guide",
      targetAudience: "Developers",
      customInstructions: "Include code examples"
    };

    // Convert to service format (like controller does)
    const serviceInput = {
      topic: controllerInput.topic,
      keyword: Array.isArray(controllerInput.keywords) ? controllerInput.keywords[0] : controllerInput.keywords,
      tone: controllerInput.tone,
      wordCount: controllerInput.wordCount,
      blogType: controllerInput.blogType,
      customInstructions: controllerInput.customInstructions
    };

    // Test backward compatibility method (what controller currently uses)
    const estimation = BlogAutoWriteService.getTokenEstimate(serviceInput);
    
    expect(estimation).toHaveProperty("totalEstimated");
    expect(estimation.totalEstimated).toBeGreaterThan(0);
  });
});

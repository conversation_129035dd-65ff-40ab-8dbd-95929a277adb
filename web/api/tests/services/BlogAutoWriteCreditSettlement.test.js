/**
 * Blog Auto-Write Credit Settlement Tests
 * Comprehensive tests for credit usage, tracking, and settlement functionality
 */

const BlogAutoWriteQueue = require("../../queue/jobs/BlogAutoWriteQueue");
const BlogAutoWriteService = require("../../services/BlogAutoWriteService");
const CreditEstimationService = require("../../services/CreditEstimationService");
const cache = require("../../cache");
const { AI_OPTIMIZER } = require("storeseo-enums/subscriptionAddonGroup");
const BlogAutoWriteJobStatus = require("storeseo-enums/blogAutoWrite/jobStatus");

// Mock dependencies
jest.mock("../../cache");
jest.mock("../../services/BlogAutoWriteService");
jest.mock("../../../sequelize", () => ({
  Shop: {
    findByPk: jest.fn()
  }
}));

describe("BlogAutoWriteQueue - Credit Settlement", () => {
  let queueInstance;
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Create a test instance of the queue
    queueInstance = new BlogAutoWriteQueue.__proto__.constructor(
      { queueName: "test-queue", prefetch: 1 },
      { connection: "mock" }
    );
  });

  describe("calculateActualCreditUsage", () => {
    it("should calculate credits from completed content generation step", () => {
      const steps = [
        {
          step: "CONTENT_GENERATION",
          completed: true,
          result: {
            creditsUsed: 12.5,
            usage: {
              prompt_tokens: 1000,
              completion_tokens: 2000,
              total_tokens: 3000
            }
          }
        },
        {
          step: "SHOPIFY_DRAFT_CREATION",
          completed: true,
          result: { draftCreated: true }
        }
      ];

      const result = queueInstance.calculateActualCreditUsage(steps);

      expect(result.totalCredits).toBe(12.5);
      expect(result.breakdown.content).toBeDefined();
      expect(result.breakdown.content.credits).toBe(12.5);
      expect(result.breakdown.content.usage.total_tokens).toBe(3000);
    });

    it("should calculate credits from both content and image generation steps", () => {
      const steps = [
        {
          step: "CONTENT_GENERATION",
          completed: true,
          result: {
            creditsUsed: 12.5,
            usage: {
              prompt_tokens: 1000,
              completion_tokens: 2000,
              total_tokens: 3000
            }
          }
        },
        {
          step: "IMAGE_GENERATION",
          completed: true,
          result: {
            creditsUsed: 100,
            imageUsage: {
              dalle_cost: 0.04,
              total_requests: 1
            }
          }
        }
      ];

      const result = queueInstance.calculateActualCreditUsage(steps);

      expect(result.totalCredits).toBe(112.5);
      expect(result.breakdown.content.credits).toBe(12.5);
      expect(result.breakdown.images.credits).toBe(100);
    });

    it("should handle steps without credit usage", () => {
      const steps = [
        {
          step: "SHOPIFY_DRAFT_CREATION",
          completed: true,
          result: { draftCreated: true }
        },
        {
          step: "ARTICLE_LINKING",
          completed: true,
          result: { linked: true }
        }
      ];

      const result = queueInstance.calculateActualCreditUsage(steps);

      expect(result.totalCredits).toBe(0);
      expect(result.breakdown).toEqual({});
    });

    it("should ignore incomplete steps", () => {
      const steps = [
        {
          step: "CONTENT_GENERATION",
          completed: true,
          result: { creditsUsed: 12.5 }
        },
        {
          step: "IMAGE_GENERATION",
          completed: false,
          result: { creditsUsed: 100 }
        }
      ];

      const result = queueInstance.calculateActualCreditUsage(steps);

      expect(result.totalCredits).toBe(12.5);
      expect(result.breakdown.images).toBeUndefined();
    });
  });

  describe("applyCreditAdjustment", () => {
    beforeEach(() => {
      cache.addons.decrementUsageCount = jest.fn();
      cache.addons.decrementTotalUsageCount = jest.fn();
      cache.addons.decrementTotalAppUsageCount = jest.fn();
      cache.addons.incrementUsageCount = jest.fn();
      cache.addons.incrementTotalUsageCount = jest.fn();
      cache.addons.incrementTotalAppUsageCount = jest.fn();
    });

    it("should apply credit refund for positive settlement amount", async () => {
      const shopDomain = "test-shop.myshopify.com";
      const settlementAmount = 25.5; // Refund
      const settlementType = "completion";
      const jobId = "test-job-123";

      await queueInstance.applyCreditAdjustment(shopDomain, settlementAmount, settlementType, jobId);

      expect(cache.addons.decrementUsageCount).toHaveBeenCalledWith(shopDomain, {
        addon: AI_OPTIMIZER,
        decrementBy: 25.5
      });
      expect(cache.addons.decrementTotalUsageCount).toHaveBeenCalledWith(shopDomain, {
        addon: AI_OPTIMIZER,
        decrementBy: 25.5
      });
      expect(cache.addons.decrementTotalAppUsageCount).toHaveBeenCalledWith(AI_OPTIMIZER, 25.5);
    });

    it("should apply additional charge for negative settlement amount", async () => {
      const shopDomain = "test-shop.myshopify.com";
      const settlementAmount = -15.3; // Additional charge
      const settlementType = "completion";
      const jobId = "test-job-123";

      await queueInstance.applyCreditAdjustment(shopDomain, settlementAmount, settlementType, jobId);

      expect(cache.addons.incrementUsageCount).toHaveBeenCalledWith(shopDomain, {
        addon: AI_OPTIMIZER,
        incrementBy: 15.3
      });
      expect(cache.addons.incrementTotalUsageCount).toHaveBeenCalledWith(shopDomain, {
        addon: AI_OPTIMIZER,
        incrementBy: 15.3
      });
      expect(cache.addons.incrementTotalAppUsageCount).toHaveBeenCalledWith(AI_OPTIMIZER, 15.3);
    });
  });

  describe("updateJobCreditUsage", () => {
    beforeEach(() => {
      BlogAutoWriteService.getJobByIdInternal = jest.fn();
      BlogAutoWriteService.updateJob = jest.fn();
    });

    it("should update job with comprehensive audit trail", async () => {
      const jobId = "test-job-123";
      const actualCreditUsage = {
        totalCredits: 87.5,
        breakdown: {
          content: { credits: 12.5 },
          images: { credits: 75 }
        },
        calculatedAt: "2024-01-01T00:00:00.000Z"
      };
      const settlementType = "completion";

      const mockJob = {
        estimated_credit_usage: { totalCredits: 100 },
        processing_started_at: "2023-12-31T23:00:00.000Z",
        steps: [
          {
            step: "CONTENT_GENERATION",
            completed: true,
            completedAt: "2023-12-31T23:05:00.000Z",
            result: { creditsUsed: 12.5 }
          },
          {
            step: "IMAGE_GENERATION",
            completed: true,
            completedAt: "2023-12-31T23:10:00.000Z",
            result: { creditsUsed: 75 }
          }
        ]
      };

      BlogAutoWriteService.getJobByIdInternal.mockResolvedValue(mockJob);

      await queueInstance.updateJobCreditUsage(jobId, actualCreditUsage, settlementType);

      expect(BlogAutoWriteService.updateJob).toHaveBeenCalledWith(jobId, {
        credit_usage: expect.objectContaining({
          totalCredits: 87.5,
          feature: "blog_auto_write",
          featureName: "Blog Auto-Write",
          addonGroup: "AI_OPTIMIZER",
          settlementType: "completion",
          auditTrail: expect.objectContaining({
            estimatedCredits: 100,
            actualCredits: 87.5,
            settlementAmount: 12.5,
            completedSteps: 2,
            totalSteps: 2,
            stepBreakdown: expect.arrayContaining([
              expect.objectContaining({
                step: "CONTENT_GENERATION",
                creditsUsed: 12.5
              }),
              expect.objectContaining({
                step: "IMAGE_GENERATION",
                creditsUsed: 75
              })
            ])
          })
        })
      });
    });
  });

  describe("performFinalCreditSettlement - Integration", () => {
    beforeEach(() => {
      BlogAutoWriteService.getJobByIdInternal = jest.fn();
      BlogAutoWriteService.updateJob = jest.fn();
      cache.addons.decrementUsageCount = jest.fn();
      cache.addons.decrementTotalUsageCount = jest.fn();
      cache.addons.decrementTotalAppUsageCount = jest.fn();
      cache.addons.incrementUsageCount = jest.fn();
      cache.addons.incrementTotalUsageCount = jest.fn();
      cache.addons.incrementTotalAppUsageCount = jest.fn();
    });

    it("should perform complete settlement for successful job completion", async () => {
      const jobId = "test-job-123";
      const shopDomain = "test-shop.myshopify.com";
      const settlementType = "completion";

      const mockJob = {
        id: jobId,
        estimated_credit_usage: { totalCredits: 100 },
        steps: [
          {
            step: "CONTENT_GENERATION",
            completed: true,
            result: { creditsUsed: 10, usage: { total_tokens: 1000 } }
          },
          {
            step: "IMAGE_GENERATION",
            completed: true,
            result: { creditsUsed: 80, imageUsage: { dalle_cost: 0.032 } }
          }
        ]
      };

      BlogAutoWriteService.getJobByIdInternal.mockResolvedValue(mockJob);

      await queueInstance.performFinalCreditSettlement(jobId, shopDomain, settlementType);

      // Should calculate actual usage (10 + 80 = 90)
      // Settlement amount should be 100 - 90 = 10 (refund)
      expect(cache.addons.decrementUsageCount).toHaveBeenCalledWith(shopDomain, {
        addon: AI_OPTIMIZER,
        decrementBy: 10
      });

      expect(BlogAutoWriteService.updateJob).toHaveBeenCalledWith(jobId, {
        credit_usage: expect.objectContaining({
          totalCredits: 90,
          settlementType: "completion"
        })
      });
    });

    it("should handle cancellation with partial refund", async () => {
      const jobId = "test-job-456";
      const shopDomain = "test-shop.myshopify.com";
      const settlementType = "cancellation";

      const mockJob = {
        id: jobId,
        estimated_credit_usage: { totalCredits: 100 },
        steps: [
          {
            step: "CONTENT_GENERATION",
            completed: true,
            result: { creditsUsed: 15, usage: { total_tokens: 1500 } }
          },
          {
            step: "IMAGE_GENERATION",
            completed: false, // Not completed due to cancellation
            result: null
          }
        ]
      };

      BlogAutoWriteService.getJobByIdInternal.mockResolvedValue(mockJob);

      await queueInstance.performFinalCreditSettlement(jobId, shopDomain, settlementType);

      // Should calculate actual usage (15 + 0 = 15)
      // Settlement amount should be 100 - 15 = 85 (large refund)
      expect(cache.addons.decrementUsageCount).toHaveBeenCalledWith(shopDomain, {
        addon: AI_OPTIMIZER,
        decrementBy: 85
      });

      expect(BlogAutoWriteService.updateJob).toHaveBeenCalledWith(jobId, {
        credit_usage: expect.objectContaining({
          totalCredits: 15,
          settlementType: "cancellation"
        })
      });
    });

    it("should handle failure with appropriate settlement", async () => {
      const jobId = "test-job-789";
      const shopDomain = "test-shop.myshopify.com";
      const settlementType = "failure";

      const mockJob = {
        id: jobId,
        estimated_credit_usage: { totalCredits: 50 },
        steps: [
          {
            step: "CONTENT_GENERATION",
            completed: true,
            result: { creditsUsed: 8, usage: { total_tokens: 800 } }
          },
          {
            step: "SHOPIFY_DRAFT_CREATION",
            completed: false, // Failed
            result: null
          }
        ]
      };

      BlogAutoWriteService.getJobByIdInternal.mockResolvedValue(mockJob);

      await queueInstance.performFinalCreditSettlement(jobId, shopDomain, settlementType);

      // Should calculate actual usage (8 + 0 = 8)
      // Settlement amount should be 50 - 8 = 42 (refund)
      expect(cache.addons.decrementUsageCount).toHaveBeenCalledWith(shopDomain, {
        addon: AI_OPTIMIZER,
        decrementBy: 42
      });

      expect(BlogAutoWriteService.updateJob).toHaveBeenCalledWith(jobId, {
        credit_usage: expect.objectContaining({
          totalCredits: 8,
          settlementType: "failure"
        })
      });
    });

    it("should handle case where actual usage exceeds estimate", async () => {
      const jobId = "test-job-999";
      const shopDomain = "test-shop.myshopify.com";
      const settlementType = "completion";

      const mockJob = {
        id: jobId,
        estimated_credit_usage: { totalCredits: 50 },
        steps: [
          {
            step: "CONTENT_GENERATION",
            completed: true,
            result: { creditsUsed: 35, usage: { total_tokens: 3500 } }
          },
          {
            step: "IMAGE_GENERATION",
            completed: true,
            result: { creditsUsed: 25, imageUsage: { dalle_cost: 0.01 } }
          }
        ]
      };

      BlogAutoWriteService.getJobByIdInternal.mockResolvedValue(mockJob);

      await queueInstance.performFinalCreditSettlement(jobId, shopDomain, settlementType);

      // Should calculate actual usage (35 + 25 = 60)
      // Settlement amount should be 50 - 60 = -10 (additional charge)
      expect(cache.addons.incrementUsageCount).toHaveBeenCalledWith(shopDomain, {
        addon: AI_OPTIMIZER,
        incrementBy: 10
      });

      expect(BlogAutoWriteService.updateJob).toHaveBeenCalledWith(jobId, {
        credit_usage: expect.objectContaining({
          totalCredits: 60,
          settlementType: "completion"
        })
      });
    });

    it("should skip adjustment for minimal differences", async () => {
      const jobId = "test-job-minimal";
      const shopDomain = "test-shop.myshopify.com";
      const settlementType = "completion";

      const mockJob = {
        id: jobId,
        estimated_credit_usage: { totalCredits: 50 },
        steps: [
          {
            step: "CONTENT_GENERATION",
            completed: true,
            result: { creditsUsed: 49.99, usage: { total_tokens: 4999 } }
          }
        ]
      };

      BlogAutoWriteService.getJobByIdInternal.mockResolvedValue(mockJob);

      await queueInstance.performFinalCreditSettlement(jobId, shopDomain, settlementType);

      // Settlement amount is 0.01, which is below the 0.01 threshold
      expect(cache.addons.decrementUsageCount).not.toHaveBeenCalled();
      expect(cache.addons.incrementUsageCount).not.toHaveBeenCalled();

      expect(BlogAutoWriteService.updateJob).toHaveBeenCalledWith(jobId, {
        credit_usage: expect.objectContaining({
          totalCredits: 49.99,
          settlementType: "completion"
        })
      });
    });
  });
});

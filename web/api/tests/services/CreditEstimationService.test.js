/**
 * Credit Estimation Service Tests
 * Tests for the new namespace-based API and add-on feature support
 */

const CreditEstimationService = require("../../services/CreditEstimationService");

describe("CreditEstimationService - Namespace API", () => {
  describe("blog namespace", () => {
    it("should estimate credits for blog auto-write", () => {
      const input = {
        topic: "SEO Best Practices",
        keyword: "SEO optimization",
        blogType: "Guide",
        wordCount: "800-1200",
        tone: "Formal",
        customInstructions: "Include examples",
        featuredImageDescription: "Professional dashboard"
      };

      const result = CreditEstimationService.blog.autoWrite(input);
      
      expect(result).toHaveProperty("totalCredits");
      expect(result).toHaveProperty("breakdown");
      expect(result).toHaveProperty("feature");
      expect(result).toHaveProperty("featureName");
      expect(result).toHaveProperty("addonGroup");
      expect(result.feature).toBe("blog_auto_write");
      expect(result.featureName).toBe("Blog Auto-Write");
      expect(result.addonGroup).toBe("AI_OPTIMIZER");
      expect(result.totalCredits).toBeGreaterThan(0);
    });

    it("should handle blog auto-write without image", () => {
      const input = {
        topic: "SEO Guide",
        blogType: "Guide",
        wordCount: "500-800",
        tone: "Casual"
      };

      const result = CreditEstimationService.blog.autoWrite(input);
      
      expect(result.totalCredits).toBeGreaterThan(0);
      expect(result.breakdown.content).toBeDefined();
      expect(result.breakdown.images?.credits || 0).toBe(0);
    });
  });

  describe("product namespace", () => {
    it("should estimate credits for product optimization", () => {
      const input = {
        title: "Wireless Headphones",
        description: "High-quality wireless headphones",
        focusKeyword: "wireless headphones"
      };

      const settings = {
        meta: true,
        tags: true,
        imageAltText: "featuredImage"
      };

      const result = CreditEstimationService.product.optimization(input, settings);
      
      expect(result).toHaveProperty("totalCredits");
      expect(result.feature).toBe("product_optimization");
      expect(result.featureName).toBe("Product Content Optimization");
      expect(result.addonGroup).toBe("AI_OPTIMIZER");
    });
  });

  describe("image namespace", () => {
    it("should estimate credits for alt-text generation", () => {
      const input = {
        images: [{ id: 1, src: "image1.jpg" }, { id: 2, src: "image2.jpg" }]
      };

      const settings = {
        imageAltText: "allImages"
      };

      const result = CreditEstimationService.image.altText(input, settings);
      
      expect(result).toHaveProperty("totalCredits");
      expect(result.feature).toBe("image_alt_text");
      expect(result.featureName).toBe("Image Alt-Text Generation");
      expect(result.addonGroup).toBe("AI_OPTIMIZER");
    });

    it("should estimate credits for image file optimization", () => {
      const input = {
        imageCount: 5
      };

      const result = CreditEstimationService.image.optimization(input);
      
      expect(result).toHaveProperty("totalCredits");
      expect(result.feature).toBe("image_optimization");
      expect(result.featureName).toBe("Image File Optimization");
      expect(result.addonGroup).toBe("IMAGE_OPTIMIZER");
      expect(result.totalCredits).toBe(5); // 1 credit per image
    });
  });

  describe("backward compatibility", () => {
    it("should maintain backward compatibility for blog methods", () => {
      const input = {
        topic: "SEO Guide",
        blogType: "Guide",
        wordCount: "800-1200",
        featuredImageDescription: "Dashboard"
      };

      // Test old methods still work
      const contentCredits = CreditEstimationService.estimateBlogContentCredits(input);
      const imageCredits = CreditEstimationService.estimateBlogImageCredits(input);
      const totalCredits = CreditEstimationService.estimateTotalBlogCredits(input);

      expect(contentCredits).toHaveProperty("userCredits");
      expect(imageCredits).toHaveProperty("userCredits");
      expect(totalCredits).toHaveProperty("totalUserCredits");
    });
  });

  describe("error handling", () => {
    it("should throw error for unknown feature", () => {
      expect(() => {
        CreditEstimationService.blog.autoWrite._estimateCredits("unknown_feature", {});
      }).toThrow("Unknown add-on feature: unknown_feature");
    });
  });
});

describe("CreditEstimationService - Feature Configuration", () => {
  it("should have correct addon group mappings", () => {
    const blogResult = CreditEstimationService.blog.autoWrite({
      topic: "Test",
      blogType: "Guide",
      wordCount: "500-800"
    });

    const imageOptResult = CreditEstimationService.image.optimization({
      imageCount: 1
    });

    expect(blogResult.addonGroup).toBe("AI_OPTIMIZER");
    expect(imageOptResult.addonGroup).toBe("IMAGE_OPTIMIZER");
  });

  it("should provide cost estimation in USD", () => {
    const result = CreditEstimationService.blog.autoWrite({
      topic: "Test",
      blogType: "Guide", 
      wordCount: "500-800"
    });

    expect(result.estimatedCost).toBeGreaterThan(0);
    expect(typeof result.estimatedCost).toBe("number");
  });
});

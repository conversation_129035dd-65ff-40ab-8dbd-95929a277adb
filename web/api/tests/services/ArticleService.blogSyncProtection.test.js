/**
 * Test for Blog Sync Protection of AI-Generated Articles
 * Ensures that AI-generated articles are protected from deletion during blog sync
 */

const ArticleService = require("../../services/ArticleService");
const BlogGenerationStatus = require("storeseo-enums/blogGenerationStatus");

describe("ArticleService - Blog Sync Protection for AI-Generated Articles", () => {
  const mockShopId = 123;

  // Mock the Article model and its methods
  const mockArticle = {
    update: jest.fn(),
    count: jest.fn(),
    findAll: jest.fn(),
    destroy: jest.fn()
  };

  // Mock the required modules
  jest.mock("../../sequelize", () => ({
    Article: mockArticle,
    Op: {
      or: Symbol("or"),
      and: Symbol("and"),
      ne: Symbol("ne"),
      notIn: Symbol("notIn"),
      in: Symbol("in")
    }
  }));

  beforeEach(() => {
    jest.clearAllMocks();
    console.log = jest.fn(); // Mock console.log to avoid test output noise
  });

  describe("markArticlesAsNotSynced", () => {
    it("should protect AI-generated articles from being marked as not synced", async () => {
      // Mock the count query for protected articles
      mockArticle.count.mockResolvedValue(3); // 3 AI-generated articles to protect

      // Mock the update operation
      mockArticle.update.mockResolvedValue([2]); // 2 articles updated

      await ArticleService.markArticlesAsNotSynced(mockShopId);

      // Verify that count was called to check protected articles
      expect(mockArticle.count).toHaveBeenCalledWith({
        where: {
          shop_id: mockShopId,
          [Symbol.for("and")]: [
            { generation_status: { [Symbol.for("ne")]: BlogGenerationStatus.MANUAL } },
            { generation_status: { [Symbol.for("notIn")]: [BlogGenerationStatus.PUBLISHED, BlogGenerationStatus.DRAFT_PUBLISHED] } }
          ]
        }
      });

      // Verify that update was called with protection logic
      expect(mockArticle.update).toHaveBeenCalledWith(
        { is_synced: false },
        {
          where: {
            shop_id: mockShopId,
            [Symbol.for("or")]: [
              { generation_status: BlogGenerationStatus.MANUAL },
              { generation_status: BlogGenerationStatus.PUBLISHED },
              { generation_status: BlogGenerationStatus.DRAFT_PUBLISHED }
            ]
          }
        }
      );

      // Verify logging
      expect(console.log).toHaveBeenCalledWith(
        `[BlogSync] Protecting 3 AI-generated articles from sync deletion for shop ${mockShopId}`
      );
    });

    it("should not log protection message when no AI articles to protect", async () => {
      mockArticle.count.mockResolvedValue(0); // No AI articles to protect
      mockArticle.update.mockResolvedValue([5]); // 5 articles updated

      await ArticleService.markArticlesAsNotSynced(mockShopId);

      // Verify no protection message was logged
      expect(console.log).not.toHaveBeenCalledWith(
        expect.stringContaining("Protecting")
      );
    });
  });

  describe("deleteNotSyncedArticles", () => {
    it("should protect AI-generated articles from deletion", async () => {
      const mockArticlesToDelete = [
        { toJSON: () => ({ id: 1, blog_id: 10 }) },
        { toJSON: () => ({ id: 2, blog_id: 10 }) },
        { destroy: jest.fn() }
      ];

      // Mock total not synced count
      mockArticle.count.mockResolvedValue(5); // 5 total not synced

      // Mock articles to delete (only safe ones)
      mockArticle.findAll.mockResolvedValue(mockArticlesToDelete);

      // Mock the required services
      const mockAnalysisService = { deleteArticleAnalysis: jest.fn() };
      const mockArticleMetaService = { deleteMeta: jest.fn() };
      const mockArticleImageService = { deleteByArticleId: jest.fn() };

      jest.doMock("../../services/AnalysisService", () => mockAnalysisService);
      jest.doMock("../../services/ArticleMetaService", () => mockArticleMetaService);
      jest.doMock("../../services/ArticleImageService", () => mockArticleImageService);

      await ArticleService.deleteNotSyncedArticles(mockShopId);

      // Verify that findAll was called with protection logic
      expect(mockArticle.findAll).toHaveBeenCalledWith({
        where: {
          shop_id: mockShopId,
          is_synced: false,
          [Symbol.for("or")]: [
            { generation_status: BlogGenerationStatus.MANUAL },
            { generation_status: BlogGenerationStatus.PUBLISHED },
            { generation_status: BlogGenerationStatus.DRAFT_PUBLISHED },

          ]
        }
      });

      // Verify protection logging (5 total - 2 to delete = 3 protected)
      expect(console.log).toHaveBeenCalledWith(
        `[BlogSync] Protected 3 AI-generated articles from deletion for shop ${mockShopId}`
      );

      // Verify deletion logging
      expect(console.log).toHaveBeenCalledWith(
        `[BlogSync] Deleting 2 not-synced articles for shop ${mockShopId}`
      );
    });
  });

  describe("temproarilyMarkAllArticlesAsDeleted", () => {
    it("should protect AI-generated articles from being marked as deleted", async () => {
      mockArticle.count.mockResolvedValue(2); // 2 AI articles to protect
      mockArticle.update.mockResolvedValue([3]); // 3 articles updated

      await ArticleService.temproarilyMarkAllArticlesAsDeleted(mockShopId);

      // Verify protection logic was applied
      expect(mockArticle.update).toHaveBeenCalledWith(
        { is_deleted: true },
        {
          where: {
            shop_id: mockShopId,
            [Symbol.for("or")]: [
              { generation_status: BlogGenerationStatus.MANUAL },
              { generation_status: BlogGenerationStatus.PUBLISHED },
              { generation_status: BlogGenerationStatus.DRAFT_PUBLISHED },

            ]
          }
        }
      );

      // Verify protection logging
      expect(console.log).toHaveBeenCalledWith(
        `[BlogSync] Protecting 2 AI-generated articles from temporary deletion for shop ${mockShopId}`
      );
    });
  });

  describe("Protected vs Deletable Status Logic", () => {
    const protectedStatuses = [
      BlogGenerationStatus.PENDING,
      BlogGenerationStatus.GENERATING,
      BlogGenerationStatus.CONTENT_READY,
      BlogGenerationStatus.DRAFT_CREATED,
      BlogGenerationStatus.PUBLISHING,
      BlogGenerationStatus.FAILED // Failed articles should be protected for retry
    ];

    const deletableStatuses = [
      BlogGenerationStatus.MANUAL,
      BlogGenerationStatus.PUBLISHED,
      BlogGenerationStatus.DRAFT_PUBLISHED
    ];

    it("should protect in-progress AI generation statuses", () => {
      protectedStatuses.forEach(status => {
        expect([
          BlogGenerationStatus.MANUAL,
          BlogGenerationStatus.PUBLISHED,
          BlogGenerationStatus.DRAFT_PUBLISHED
        ]).not.toContain(status);
      });
    });

    it("should allow deletion of completed statuses only", () => {
      deletableStatuses.forEach(status => {
        expect([
          BlogGenerationStatus.MANUAL,
          BlogGenerationStatus.PUBLISHED,
          BlogGenerationStatus.DRAFT_PUBLISHED
        ]).toContain(status);
      });
    });
  });
});

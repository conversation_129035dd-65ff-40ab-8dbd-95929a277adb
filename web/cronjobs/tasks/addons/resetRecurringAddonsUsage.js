const subscriptionAddonGroup = require("storeseo-enums/subscriptionAddonGroup");
const subscriptionAddonType = require("storeseo-enums/subscriptionAddonType");
const moment = require("moment");
const { sequelize, Shop, Op } = require("../../../sequelize");
const AddonUsageService = require("../../../api/services/AddonUsageService");
const SubscriptionAddonService = require("../../../api/services/SubscriptionAddonService");
const cache = require("../../../api/cache");

const RESET_INTERVAL_IN_DAYS = "30";
const DATE_FORMAT = "MMM Do YYYY";

const resetRecurringAddonsUsage = async () => {
  try {
    const currentDate = moment();

    const recurringAddons = await SubscriptionAddonService.getRecurringAddons();
    const recurringAddonIds = recurringAddons.map((addon) => addon.id);
    const shopsWithRecurringAddons = await Shop.findAll({
      where: {
        plan_id: {
          [Op.gt]: 1,
        },
      },
      include: [
        {
          association: "addons",
          where: {
            is_active: true,
            addon_id: {
              [Op.in]: recurringAddonIds,
            },
            [Op.and]: sequelize.literal(`((CURRENT_DATE::date - last_reset_date::date) >= ${RESET_INTERVAL_IN_DAYS})`),
          },
        },
      ],
    });

    console.log(
      `\n---\n[${currentDate.format(DATE_FORMAT)}] - Found ${
        shopsWithRecurringAddons.length
      } shops that are eligible for recurring add-on reset...\n---\n`
    );

    for (let i = 0; i < shopsWithRecurringAddons.length; i++) {
      try {
        const shop = shopsWithRecurringAddons[i].toJSON();

        for (let addon of shop.addons) {
          /** @type {import("../../../sequelize/models/addonusage").AddonUsage} */
          const currentlyActiveRecord = addon;
          /** @type {import("../../../sequelize/models/subscriptionaddon").SubscriptionAddon} */
          const subscriptionAddon = await SubscriptionAddonService.getAddonById(currentlyActiveRecord.addon_id);

          const newRecord = await AddonUsageService.registerUsageRecord({
            shopId: shop.id,
            purchaseDate: currentlyActiveRecord.purchase_date,
            lastResetDate: currentDate,
            addon: subscriptionAddon,
            activeRecord: currentlyActiveRecord,
          });

          await AddonUsageService.deactivateRecordById(currentlyActiveRecord.id);
          await AddonUsageService.updateUsageLimitInCache(shop.domain, shop.id, newRecord.addon_group);

          // For recurring addons reset, handle over-usage by carrying it forward as penalty
          // Over-usage = current_cache_usage - (lifetime_limit + monthly_limit)

          // Get current cache usage
          const currentCacheUsage = await cache.addons.usageCount(shop.domain, { addon: newRecord.addon_group });

          // Get all active addon records for this group to find lifetime addons
          const allActiveRecords = await AddonUsageService.getActiveRecordsByAddonGroup(shop.id, newRecord.addon_group);

          // Find lifetime addon record and get its usage
          let lifetimeUsage = 0;
          let lifetimeLimit = 0;
          for (const record of allActiveRecords) {
            // If this addon is not in the recurring list, it's a lifetime addon
            if (!recurringAddonIds.includes(Number(record.addon_id))) {
              lifetimeUsage += record.current_usage;
              lifetimeLimit += record.current_limit;
            }
          }

          // Calculate over-usage: current cache usage - (lifetime limit + monthly limit)
          const monthlyLimit = currentlyActiveRecord.current_limit;
          const totalAllowedLimit = lifetimeLimit + monthlyLimit;
          const overUsage = Math.max(0, Number(currentCacheUsage) - totalAllowedLimit);

          // Update the new monthly record to start with over-usage (penalty for next month)
          if (overUsage > 0) {
            await AddonUsageService.updateRecordById(newRecord.id, { current_usage: overUsage });
          }

          // Set cache to lifetime usage + over-usage penalty (no fresh monthly usage yet)
          const newCacheUsage = lifetimeUsage + overUsage;
          await cache.addons.usageCount(shop.domain, {
            addon: newRecord.addon_group,
            count: newCacheUsage,
          });

          //  resetting the last sent email usage to null
          await cache.addons.lastEmailSentForUsagePercentage(shop.domain, {
            addon: newRecord.addon_group,
            usagePerecentage: "",
          });
        }

        console.log(
          `[${currentDate.format(DATE_FORMAT)}][Shop: ${i + 1}/${shopsWithRecurringAddons.length}] - Shop id: ${
            shop.id
          }, domain: ${shop.domain} recurring add-on reset successful!`
        );
      } catch (err) {
        console.log(
          `[${currentDate.format(DATE_FORMAT)}][Shop: ${i + 1}/${shopsWithRecurringAddons.length}] - Shop id: ${
            shopsWithRecurringAddons[i]?.id
          }, domain: ${shopsWithRecurringAddons[i]?.domain} recurring add-on reset FAILED!`,
          err
        );
      }
    }
  } catch (err) {
    console.log(moment().format(DATE_FORMAT), "Error in 'recurring addon reset' crontask: ", err);
  }
};

module.exports = resetRecurringAddonsUsage;

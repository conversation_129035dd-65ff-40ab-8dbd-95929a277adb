require("dotenv").config();
const { QueryTypes } = require("sequelize");
const { sequelize } = require("../../../sequelize");
const logger = require("storeseo-logger");
const SubscriptionService = require("../../../api/services/SubscriptionService");
const { ACTIVE } = require("storeseo-enums/shopStatus");
const TrialStatus = require("storeseo-enums/trialStatus");
const { sleep } = require("../../../api/utils/helper");

module.exports = async () => {
  console.time("Trial Expiration Check");
  try {
    console.log("==================================================================");
    console.log("Starting Trial Expiration Check at:", new Date().toISOString());
    console.log("==================================================================");

    // Raw query to get only shops with active trials
    const query = `
      SELECT 
        id,
        domain,
        trial_data
      FROM shops 
      WHERE 
        status = :status
        AND trial_data IS NOT NULL 
        AND trial_data->>'status' = :trialStatus
        AND trial_data->>'isTrialUser' = 'true'
      ORDER BY id ASC
    `;

    const shops = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      replacements: {
        status: ACTIVE,
        trialStatus: TrialStatus.ACTIVE,
      },
    });

    console.log(`Found ${shops.length} shops with active trials`);

    if (shops.length === 0) {
      console.log("No shops with active trials found. Exiting...");
      return;
    }

    let processedCount = 0;
    let expiredTrialsCount = 0;
    let skippedCount = 0;
    let shopsList = [];

    for (const shop of shops) {
      try {
        processedCount++;
        shopsList.push(shop.domain);

        console.log(`[${processedCount}/${shops.length}] Checking ${shop.domain}...`);

        // Use SubscriptionService to check trial expiration
        const result = await SubscriptionService.checkTrialExpiration(shop.domain, false);

        if (!result.processed) {
          console.log(`[${processedCount}/${shops.length}] Error processing ${shop.domain}: ${result.reason}`);
          skippedCount++;
          continue;
        }

        if (result.dispatched) {
          console.log(`🔴 Trial expiration queue dispatched for shop: ${shop.domain} (${result.reason})`);
          expiredTrialsCount++;
        } else {
          console.log(`🟢 ${shop.domain}: ${result.reason}`);
          skippedCount++;
        }

        // Small delay to prevent overwhelming the system
        if (processedCount % 10 === 0) {
          await sleep(100, false);
        }
      } catch (error) {
        console.error(`Error processing shop ${shop.domain}:`, error.message);
        logger.error(error, {
          domain: shop.domain,
          context: "trial-expiration-check",
        });
        skippedCount++;
      }
    }

    // Summary
    console.log("==================================================================");
    console.log("Trial Expiration Check Summary:");
    console.log(`  Total shops processed: ${processedCount}`);
    console.log(`  Expired trials dispatched: ${expiredTrialsCount}`);
    console.log(`  Shops skipped: ${skippedCount}`);
    console.log(`  List of shops: ${shopsList.join(", ")}`);
    console.log("==================================================================");

    if (expiredTrialsCount > 0) {
      logger.info("Trial expiration check completed", {
        totalProcessed: processedCount,
        expiredTrials: expiredTrialsCount,
        skipped: skippedCount,
        shopsList,
      });
    }
  } catch (error) {
    console.error("Trial Expiration Check failed:", error);
    logger.error(error, { context: "trial-expiration-check" });
  } finally {
    console.timeEnd("Trial Expiration Check");
    console.log("Trial Expiration Check completed at:", new Date().toISOString());
  }
};

const { Op } = require("sequelize");
const moment = require("moment");
const logger = require("storeseo-logger");
const { dispatchQueue } = require("../../../api/queue/queueDispatcher");
const { QUEUE_NAMES } = require("../../../api/queue/config");
const ShopService = require("../../../api/services/ShopService");
const LlmsTxtGeneratorService = require("../../../api/services/LlmsTxtGeneratorService");
const { ACTIVE } = require("storeseo-enums/shopStatus");
const { sleep } = require("../../../api/utils/helper");
const redisClient = require("../../../api/cache/client");
const settingKeys = require("storeseo-enums/settingKeys");

module.exports = async () => {
  console.time("Daily Store Data Check");
  try {
    console.log("==================================================================");
    const currentDate = moment();
    console.log("Checking Store Data for =", currentDate.format());

    for await (let shops of ShopService.iterateOverAllShops({
      plan_id: { [Op.gt]: 0 },
      status: ACTIVE,
    })) {
      for (let shop of shops) {
        console.log("Dispatching email notification trigger queue for shop: ", shop?.domain);
        dispatchQueue({
          queueName: QUEUE_NAMES.EMAIL_NOTIFICATION_TRIGGER_QUEUE,
          message: { shopDomain: shop?.domain },
        });

        console.log("Dispatching LLMs.txt schedule check for shop: ", shop?.domain);
        LlmsTxtGeneratorService.dispatchScheduleCheck(shop?.domain);

        await storeRedisDataToDb(shop?.id, shop?.domain);
        await sleep(1000);
      }
    }
  } catch (error) {
    logger.error(error);
  } finally {
    console.log("==================================================================");
    console.log("Daily Store Data Check completed");
    console.timeEnd("Daily Store Data Check");
    console.log("==================================================================");
  }
};

async function storeRedisDataToDb(shopId, shopDomain) {
  try {
    console.log(`Storing Redis data to DB for shop: ${shopDomain}`);

    const keys = await redisClient.keys(`${shopDomain}:*`);

    if (keys.length === 0) {
      console.error(`no keys found to update redis data of ${shopDomain}`);
      return;
    }

    const values = await redisClient.mGet(keys);
    const data = keys.map((key, index) => ({
      [key]: values[index],
    }));

    await ShopService.updateShopSetting(shopId, {
      key: settingKeys.REDIS_DATA,
      value: JSON.stringify(data),
      value_type: "json",
    });

    console.info(`updated redis data of ${shopDomain}`);
  } catch (error) {
    console.error(`failed to update redis data of ${shopDomain}`);
  }
}

const moment = require("moment");
const SlackService = require("../../../api/services/SlackService");
const logger = require("storeseo-logger");

const sendAppUsageReport = async (date = undefined) => {
  try {
    console.log("==================================================================");
    date = moment(date).subtract(1, "d").format("YYYY-MM-DD");
    console.log("Sending App Usage Report of Date =", date);

    await SlackService.sendAppUsageReport(date);
  } catch (error) {
    logger.error(error);
  }
};

module.exports = sendAppUsageReport;

const cache = require("../../../api/cache");
const { dispatchQueue } = require("../../../api/queue/queueDispatcher");
const { QUEUE_NAMES } = require("../../../api/queue/config");
const moment = require("moment");

const DATE_FORMAT = "MMM Do YYYY, HH:mm:ss";

/**
 * Sleep for a specified number of milliseconds
 * @param {number} ms Milliseconds to sleep
 * @returns {Promise<void>}
 */
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Process pending webhook registrations by dispatching them to the queue
 * @returns {Promise<void>}
 */
const processPendingWebhookRegistrations = async () => {
  try {
    const currentDate = moment();
    console.log(`\n---\n🔄 [${currentDate.format(DATE_FORMAT)}] - Processing pending webhook registrations\n---\n`);

    // Maximum number of shops to process in one run
    const MAX_SHOPS_TO_PROCESS = 10;
    // Delay between each dispatch (125ms)
    const DELAY_BETWEEN_DISPATCHES = 125;

    let processedCount = 0;

    for (let i = 0; i < MAX_SHOPS_TO_PROCESS; i++) {
      const shopDomain = await cache.webhooks.getNextShopFromPendingWebhookRegistrationList();
      
      if (!shopDomain) {
        console.log(`No more shops in the pending webhook registration list`);
        break;
      }

      try {
        console.log(`Dispatching webhook registration for shop: ${shopDomain}`);
        
        // Dispatch to the update webhook registration queue
        dispatchQueue({
          queueName: QUEUE_NAMES.UPDATE_WEBHOOKS_REGISTRATION_QUEUE,
          message: { shopDomain },
        });
        
        console.log(`Successfully dispatched webhook registration for shop: ${shopDomain}`);
        processedCount++;
        
        // Sleep for 125ms before the next dispatch
        await sleep(DELAY_BETWEEN_DISPATCHES);
      } catch (error) {
        console.error(`Error dispatching webhook registration for shop ${shopDomain}:`, error);
        
        // If there was an error, add the shop back to the list to try again later
        await cache.webhooks.addShopToPendingWebhookRegistrationList(shopDomain);
        console.log(`Added ${shopDomain} back to the pending webhook registration list for retry`);
      }
    }

    console.log(`\n---\n✅ [${currentDate.format(DATE_FORMAT)}] - Processed ${processedCount} pending webhook registrations\n---\n`);
  } catch (error) {
    console.error(`[${moment().format(DATE_FORMAT)}] Error processing pending webhook registrations:`, error);
  }
};

module.exports = processPendingWebhookRegistrations;

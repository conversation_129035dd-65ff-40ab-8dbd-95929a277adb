# Feature Flags Configuration
# Copy this file to .env.local and modify as needed

# =============================================================================
# BLOG AUTO-WRITE FEATURES
# =============================================================================

# Image Generation (Steps 5-7: IMAGE_GENERATION, SHOPIFY_IMAGE_UPLOAD, SHOPIFY_ARTICLE_UPDATE)
# Set to false to temporarily disable image generation
FEATURE_BLOG_AUTO_WRITE_IMAGE_GENERATION=false

# Future Blog Features
FEATURE_BLOG_AUTO_WRITE_BULK_GENERATION=false
FEATURE_BLOG_AUTO_WRITE_SCHEDULING=false
FEATURE_BLOG_AUTO_WRITE_TEMPLATES=false

# =============================================================================
# AI FEATURES
# =============================================================================

# AI-powered suggestions and recommendations
FEATURE_AI_SUGGESTIONS=false

# Auto-optimization features (default: enabled)
FEATURE_AI_AUTO_OPTIMIZATION=true

# Content analysis features (default: enabled)
FEATURE_AI_CONTENT_ANALYSIS=true

# =============================================================================
# UI/UX FEATURES
# =============================================================================

# New dashboard design
FEATURE_UI_NEW_DASHBOARD=false

# Dark mode support
FEATURE_UI_DARK_MODE=false

# Advanced filtering options (default: enabled)
FEATURE_UI_ADVANCED_FILTERS=true

# =============================================================================
# INTEGRATION FEATURES
# =============================================================================

# Social media integrations
FEATURE_INTEGRATIONS_SOCIAL_MEDIA=false

# Analytics integrations (default: enabled)
FEATURE_INTEGRATIONS_ANALYTICS=true

# Webhook support
FEATURE_INTEGRATIONS_WEBHOOKS=false

# =============================================================================
# FRONTEND FEATURE FLAGS
# =============================================================================

# Frontend now fetches feature flags from backend API at runtime
# No duplication needed - single source of truth in backend
# This eliminates the need for REACT_APP_* environment variables

# =============================================================================
# ENVIRONMENT-SPECIFIC OVERRIDES
# =============================================================================

# Development Environment
# - Enable experimental features
# - Enable debugging features
# - More permissive settings

# Staging Environment  
# - Mirror production but allow some experimental features
# - Enable features for testing

# Production Environment
# - Conservative settings
# - Only stable, tested features enabled
# - Performance-critical features only

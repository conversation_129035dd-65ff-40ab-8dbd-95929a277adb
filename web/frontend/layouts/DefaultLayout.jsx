import Footer from "@/components/common/Footer";
import DashboardLoader from "@/components/loader/DashboardLoader";
import DummyPageSkeleton from "@/components/loader/DummyPageSkeleton";
import DummyProductFix from "@/components/loader/DummyProductFix.jsx";
import HtmlSitemapLoader from "@/components/loader/HtmlSitemapLoader";
import { ImageOptimizerLoader } from "@/components/loader/ImageOptimizerLoader";
import ReportLoader from "@/components/loader/ReportLoader";
import SubscriptionLoader from "@/components/loader/SubscriptionLoader";
import { rootFlexStyle } from "@/styles/common";
import { Suspense } from "react";
import { Outlet, useLocation } from "react-router-dom";

export default function DefaultLayout() {
  const { pathname } = useLocation();

  return (
    <div style={rootFlexStyle}>
      {/*<Header />*/}
      <div style={{ flex: 1 }}>
        <Suspense
          fallback={
            pathname.match(/(?:(?:products)|(?:pages)|(?:articles))\/[0-9].*/gim) ? (
              <DummyProductFix />
            ) : pathname.match(/image-optimizer/gim) ? (
              <ImageOptimizerLoader />
            ) : pathname.match(/reports/gim) ? (
              <ReportLoader />
            ) : pathname.match(/html-sitemap/gim) ? (
              <HtmlSitemapLoader />
            ) : pathname.match(/subscription/gim) ? (
              <SubscriptionLoader />
            ) : pathname === "/" ? (
              <DashboardLoader />
            ) : (
              <DummyPageSkeleton />
            )
          }
        >
          <Outlet />
        </Suspense>
      </div>
      <Footer />
    </div>
  );
}

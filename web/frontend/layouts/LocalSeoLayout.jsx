import DummySettingsSkeleton from "@/components/loader/DummySettingsSkeleton";
import { BlockStack, Button, Card, InlineStack, Navigation, Page, Text } from "@shopify/polaris";
import { Suspense, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { rootFlexStyle } from "../styles/common";

import Footer from "@/components/common/Footer";
import ProFeatureBanner from "@/components/common/ProFeatureBanner";
import { MenuIcon, XIcon } from "@shopify/polaris-icons";
import { Outlet, useNavigate, useSearchParams } from "react-router-dom";

const initialMenus = [
  { key: "breadcrumb-schema", url: "/local-seo", title: "Breadcrumb Schema", isLocked: false },
  { key: "collection-schema", url: "/local-seo/collection-schema", title: "Collection Schema", isLocked: false },
  { key: "product-schema", url: "/local-seo/product-schema", title: "Product Schema", isLocked: false },
  {
    key: "product-merchant-schema",
    url: "/local-seo/product-merchant-schema",
    title: "Product Merchant Schema",
    isLocked: false,
  },
  {
    key: "local-business-schema",
    url: "/local-seo/local-business-schema",
    title: "Local Business Schema",
    isLocked: false,
  },
  { key: "article-schema", url: "/local-seo/article-schema", title: "Article Schema", isLocked: false },
  { key: "blog-schema", url: "/local-seo/blog-schema", title: "Blog Schema", isLocked: false },
  { key: "organization-schema", url: "/local-seo/organization-schema", title: "Organization Schema", isLocked: false },
];

const LocalSeoLayout = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const [menus, setMenus] = useState(initialMenus);
  const [currentPath, setCurrentPath] = useState("");
  const [showMobileSidebar, setShowMobileSidebar] = useState(false);

  useEffect(() => {
    setCurrentPath(menus?.find((m) => m.url === location?.pathname));
  }, [location?.pathname, menus]);

  const isActive = (path) => {
    if (location.pathname.startsWith("/settings/checkout") && path === "/subscription") {
      return true;
    }

    return path === location.pathname;
  };

  const goToPath = (path) => {
    if (showMobileSidebar) setShowMobileSidebar(!showMobileSidebar);
    if (!isActive(path)) return navigate(path);

    searchParams.append("refresh", "1");
    return navigate(`${path}?${searchParams.toString()}`);
  };

  const handleMobileSidebar = () => {
    setShowMobileSidebar(!showMobileSidebar);
  };

  return (
    <div style={rootFlexStyle}>
      {/*<Header />*/}
      <div style={{ flex: 1 }}>
        <Page
          // fullWidth
          title={t("SEO Schema")}
          backAction={{
            onAction() {
              navigate(-1);
            },
          }}
        >
          <div className="settings-layout">
            <div className={`settings-sidebar ${showMobileSidebar ? "show" : ""}`}>
              <div className="mobile-nav">
                <InlineStack
                  gap="400"
                  blockAlign={"center"}
                >
                  <Button
                    icon={!showMobileSidebar ? MenuIcon : XIcon}
                    onClick={handleMobileSidebar}
                  />
                  <Text
                    as={"h4"}
                    variant={"headingMd"}
                  >
                    {t(currentPath?.title)}
                  </Text>
                </InlineStack>
              </div>
              <Card padding={0}>
                <Navigation location="/local-seo">
                  {menus.map((im) => (
                    <Navigation.Item
                      key={im.url}
                      label={
                        <InlineStack gap="100">
                          <Text
                            tone={im.isLocked && "subdued"}
                            fontWeight="semibold"
                          >
                            {t(im.title)}
                          </Text>
                        </InlineStack>
                      }
                      selected={isActive(im.url)}
                      onClick={() => goToPath(im.url)}
                    />
                  ))}
                </Navigation>
              </Card>
            </div>
            <div className="settings-content">
              <Suspense fallback={<DummySettingsSkeleton />}>
                <BlockStack gap="400">
                  <ProFeatureBanner
                    title="SEO Schema is a PRO feature"
                    content="Upgrade your subscription to enable SEO Schema"
                  />
                  <Outlet />
                </BlockStack>
              </Suspense>
            </div>
          </div>
        </Page>
      </div>
      <Footer />
    </div>
  );
};

export default LocalSeoLayout;

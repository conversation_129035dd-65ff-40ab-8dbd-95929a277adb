import Footer from "@/components/common/Footer";
import ConnectToGoogleSection from "@/components/sitemap/ConnectToGoogle";
import { useAppNavigation } from "@/hooks/useAppNavigation";
import useSitemapsIndexFilters from "@/lib/hooks/useSitemapsIndexFilters";
import { IndexTableFilters } from "@/modules/components";
import SitemapTableSkeleton from "@/modules/sitemap/components/SitemapTableSkeleton";
import SitemapsLayoutProvider, { useSitemapLayoutContext } from "@/providers/SitemapsLayoutProvider";
import { rootFlexStyle } from "@/styles/common";
import { BlockStack, Card, Page } from "@shopify/polaris";
import { memo, Suspense } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { Outlet, useNavigate } from "react-router-dom";

export default function SitemapsLayout() {
  const { t } = useTranslation();
  const navigation = useAppNavigation();
  const navigate = useNavigate();

  const user = useSelector((state) => state.user);
  const hasGoogleConsolePermission = user?.permission?.google_console;
  const { BANNER_GOOGLE_SITEMAP: hideGoogleSitemapBanner } = useSelector((state) => state.hiddenBanner);

  return (
    <SitemapsLayoutProvider>
      <div style={rootFlexStyle}>
        <div style={{ flex: 1 }}>
          <Page
            title={t("Sitemaps")}
            backAction={navigation.backAction}
            primaryAction={{
              content: t("HTML Sitemap"),
              onAction: () => navigate("/sitemaps/html-sitemap"),
            }}
          >
            <BlockStack gap="400">
              <ConnectToGoogleSection
                show={!hasGoogleConsolePermission || hideGoogleSitemapBanner}
                dismissable
              />
              <Card padding="0">
                {/* Sitemap page tab for index table */}
                <SitemapIndexTableFilter />
                {/* Index table data */}
                <Suspense fallback={<SitemapTableSkeleton />}>
                  <Outlet />
                </Suspense>
              </Card>
            </BlockStack>
          </Page>
        </div>
        <Footer />
      </div>
    </SitemapsLayoutProvider>
  );
}
// Constants for tabs
const tabsContents = [
  { content: "Products", path: "/sitemaps" },
  { content: "Collections", path: "/sitemaps/collections" },
  { content: "Pages", path: "/sitemaps/pages" },
  { content: "Blog Posts", path: "/sitemaps/articles" },
];
const SitemapIndexTableFilter = memo(() => {
  const { isLoading } = useSitemapLayoutContext();
  const { filters, appliedFilters, setSelectedFilters, filterQuery } = useSitemapsIndexFilters();
  return (
    <IndexTableFilters
      filters={filters}
      appliedFilters={appliedFilters}
      setSelectedFilters={setSelectedFilters}
      filterQuery={filterQuery}
      loading={isLoading}
      tabs={tabsContents}
    />
  );
});

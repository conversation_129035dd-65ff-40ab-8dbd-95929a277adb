//@ts-check
import CollectionSyncButton from "@/components/collections/CollectionSyncButton";
import EmptyPage from "@/components/common/EmptyPage";
import Footer from "@/components/common/Footer";
import UsageLimitCard from "@/components/common/UsageLimitCard";
import ImageCompareModal from "@/components/image-optimizer/ImageCompareModal";
import ImageOptimizerNotEnabled from "@/components/image-optimizer/ImageOptimizerNotEnabled";
import ProductSyncButton from "@/components/products/ProductSyncButton";
import { EMPTYSTATE_IMAGES, HELP_URLS, MODAL_IDS } from "@/config";

import { useAppNavigation } from "@/hooks/useAppNavigation";
import useUserAddon from "@/hooks/useUserAddon";
import { useImagesList } from "@/lib/hooks/image-optimizer";
import { useImageOptimizeSettings } from "@/lib/hooks/image-optimizer/useImageOptimizeSettings";
import BackupRestoreRunningBanner from "@/modules/components/BackupRestoreRunningBanner";
import ImageOptimizerIndexTableFilter from "@/modules/image-optimizer/ImageOptimizerIndexTableFilter";
import ImageOptimizerSettingsModal from "@/modules/image-optimizer/ImageOptimizerSettingsModal";
import ImageOptimizerTableSkeleton from "@/modules/image-optimizer/ImageOptimizerTableSkeleton";
import BlogPostSyncButton from "@/modules/optimize-seo/articles/BlogPostSyncButton";
import ImageOptimizerLayoutProvider, { useImageOptimizerLayoutContext } from "@/providers/ImageOptimizerLayoutProvider";
import { rootFlexStyle } from "@/styles/common";
import { getCheckoutPath, getQueryFromUrlSearchParam } from "@/utility/helpers";
import queryKeys from "@/utility/queryKeys";
import { Badge, BlockStack, Card, Page } from "@shopify/polaris";
import { capitalize } from "lodash";
import { Suspense } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { Outlet, useLocation, useSearchParams } from "react-router-dom";
import ResourceType from "storeseo-enums/resourceType";
import SubscriptionAddonGroup from "storeseo-enums/subscriptionAddonGroup";
import { TableReferencesProvider } from "../components/TableReferencesContext";

export default function ImageOptimizerLayout() {
  return (
    <ImageOptimizerLayoutProvider>
      <TableReferencesProvider>
        <div style={rootFlexStyle}>
          <div style={{ flex: 1 }}>
            <ImageOptimizerPageContent />
          </div>
          <Footer />
        </div>
      </TableReferencesProvider>
    </ImageOptimizerLayoutProvider>
  );
}

const ImageOptimizerPageContent = () => {
  const { t } = useTranslation();
  // @ts-ignore
  const user = useSelector((s) => s.user);
  const checkoutPath = getCheckoutPath(user);
  const [searchParams] = useSearchParams();
  const query = getQueryFromUrlSearchParam(searchParams);
  const { pathname } = useLocation();
  const { backAction } = useAppNavigation();
  const { hasImageOptimizer } = useUserAddon();

  const { settings } = useImageOptimizeSettings();
  const { imageToCompare, setImageToCompare, handleImagesUpdate } = useImageOptimizerLayoutContext();

  // Determine resource type and sync button from pathname
  const resourcePath = pathname.split("/")[2];
  const resourceType = resourcePath?.startsWith("blog")
    ? ResourceType.ARTICLE
    : resourcePath?.toUpperCase().slice(0, -1) || ResourceType.PRODUCT;
  const queryKey = [queryKeys.IMAGES_LIST, resourceType, query];

  // Get empty state directly from the current route's data - automatically updates on route change!
  const { hasEmptyContent } = useImagesList(/** @type {any} */ (resourceType));

  const PrimaryAction = pathname.match(/image-optimizer\/collections/gim)
    ? CollectionSyncButton
    : pathname.match(/image-optimizer\/blog-posts/gim)
    ? BlogPostSyncButton
    : pathname.match(/image-optimizer/gim)
    ? ProductSyncButton
    : null;

  // Check if user has image optimizer access
  if (!hasImageOptimizer) {
    return <ImageOptimizerNotEnabled />;
  }

  return (
    <Page
      fullWidth={true}
      title={t("Image Optimizer")}
      titleMetadata={
        <Badge tone={settings?.autoOptimization ? "success" : "warning"}>
          {t(`Auto Optimization: ${settings?.autoOptimization ? "Enabled" : "Disabled"}`)}
        </Badge>
      }
      backAction={backAction}
      secondaryActions={[
        { content: t("Manage Settings"), onAction: () => shopify.modal.show(MODAL_IDS.IMAGE_OPTIMIZER_SETTINGS) },
      ]}
    >
      <BlockStack gap="400">
        {!hasEmptyContent && (
          <>
            <BackupRestoreRunningBanner />
            <UsageLimitCard
              // @ts-ignore
              imageOptimizeSettings={settings}
              title="Image Optimizer"
              group={SubscriptionAddonGroup.IMAGE_OPTIMIZER}
              learnMoreButton={{
                title: "How to increase my monthly usage limits?",
                url: HELP_URLS.IMAGE_OPTIMIZER,
              }}
              action={{
                content: "Increase limit",
                url: checkoutPath,
              }}
            />
          </>
        )}

        {hasEmptyContent ? (
          <Card padding="0">
            <ImageOptimizerIndexTableFilter
              hideFilters
              hideQueryField
            />
            <EmptyPage
              heading={`Add or sync your ${capitalize(resourcePath)} before optimizing images with StoreSEO`}
              content={`Before optimizing ${capitalize(resourceType)} images, you need to add ${capitalize(
                resourcePath
              )} or sync them first`}
              primaryAction={<PrimaryAction />}
              image={EMPTYSTATE_IMAGES.imageOptimizer}
              withWrapper={false}
              insideCard={false}
            />
          </Card>
        ) : (
          <Card padding="0">
            <ImageOptimizerIndexTableFilter />
            <Suspense fallback={<ImageOptimizerTableSkeleton />}>
              <Outlet />
            </Suspense>
          </Card>
        )}
      </BlockStack>

      {/* Image Compare Modal */}
      {imageToCompare && (
        <ImageCompareModal
          image={imageToCompare}
          onOptimize={(images) => {
            const img = images[0];
            setImageToCompare(img);
            handleImagesUpdate(images, queryKey);
          }}
          onRestore={(images) => handleImagesUpdate(images, queryKey)}
          onClose={() => setImageToCompare(null)}
          resourceType={/** @type {any} */ (resourceType)}
        />
      )}

      {/* Image Optimizer Settings Modal */}
      <ImageOptimizerSettingsModal />
    </Page>
  );
};

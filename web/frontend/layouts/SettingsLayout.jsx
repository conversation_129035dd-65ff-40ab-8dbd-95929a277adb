import { Badge, BlockStack, Box, <PERSON><PERSON>, Card, InlineStack, Navigation, Page, Text, Thumbnail } from "@shopify/polaris";
import { Suspense, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { Outlet, useLocation, useNavigate, useSearchParams } from "react-router-dom";
import { rootFlexStyle } from "../styles/common";

import { SETTINGS_MENU } from "@/config";
import { useAppNavigation } from "@/hooks/useAppNavigation";
import { ImageIcon, MenuIcon, XIcon } from "@shopify/polaris-icons";
import Footer from "../components/common/Footer.jsx";
import DummySettingsSkeleton from "../components/loader/DummySettingsSkeleton.jsx";
import useUserAddon from "../hooks/useUserAddon";

export default function SettingsLayout() {
  // const settingMenuRef = useRef();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const user = useSelector((state) => state.user);
  const [searchParams] = useSearchParams();
  const { backAction } = useAppNavigation();

  const [showMobileSidebar, setShowMobileSidebar] = useState(false);
  const [menus, setMenus] = useState(SETTINGS_MENU);
  const [currentPath, setCurrentPath] = useState("");

  const location = useLocation();

  const { hasImageOptimizer } = useUserAddon();

  const isActive = (path) => {
    if (location.pathname.startsWith("/settings/checkout") && path === "/subscription") {
      return true;
    }

    return path === location.pathname;
  };

  const goToPath = (path) => {
    if (showMobileSidebar) setShowMobileSidebar(!showMobileSidebar);
    if (!isActive(path)) return navigate(path);

    searchParams.append("refresh", "1");
    return navigate(`${path}?${searchParams.toString()}`);
  };

  const handleMobileSidebar = () => {
    setShowMobileSidebar(!showMobileSidebar);
  };

  // const currentPath = menus.find((m) => m.url === location?.pathname);

  useEffect(() => {
    setCurrentPath(menus?.find((m) => m.url === location?.pathname));
  }, [location?.pathname, menus]);

  useEffect(() => {
    let newMenus = SETTINGS_MENU.map((nm) => {
      if (nm.isLocked) {
        switch (nm.key) {
          case "image-optimizer":
            return { ...nm, isLocked: !hasImageOptimizer };
          case "multi-language":
            return { ...nm, isLocked: !user?.permission?.multi_language_seo };
          case "llms-txt-generator":
            return { ...nm, isLocked: !user?.permission?.llms_txt_generation };
          default:
            return { ...nm, isLocked: !user?.isPremium };
        }
      }
      return { ...nm };
    });
    setMenus(newMenus);
  }, [user]);

  return (
    <div style={rootFlexStyle}>
      {/*<Header />*/}
      <div style={{ flex: 1 }}>
        <Page
          // fullWidth
          title={t("Settings")}
          backAction={backAction}
        >
          <div className="settings-layout">
            <div className={`settings-sidebar ${showMobileSidebar ? "show" : ""}`}>
              <div className="mobile-nav">
                <InlineStack
                  gap="400"
                  blockAlign={"center"}
                >
                  <Button
                    icon={!showMobileSidebar ? MenuIcon : XIcon}
                    onClick={handleMobileSidebar}
                  />
                  <Text
                    as={"h4"}
                    variant={"headingMd"}
                  >
                    {t(currentPath?.title)}
                  </Text>
                </InlineStack>
              </div>
              <Card padding={0}>
                <Box
                  padding="400"
                  background="bg-surface-brand"
                >
                  <InlineStack
                    wrap={false}
                    gap="200"
                  >
                    <span>
                      <Thumbnail
                        source={user.shopLogo || ImageIcon}
                        alt={user.shopName}
                        size="small"
                      />
                    </span>
                    <BlockStack>
                      <Text
                        as="h4"
                        variant="headingSm"
                      >
                        {user.shopName}
                      </Text>
                      <Text
                        as="p"
                        tone="subdued"
                      >
                        {user.url?.replace("https://", "")}
                      </Text>
                    </BlockStack>
                  </InlineStack>
                </Box>
                <Navigation location="/settings">
                  {menus.map((im) => (
                    <Navigation.Item
                      key={im.url}
                      label={
                        <InlineStack gap="200">
                          <Text
                            tone={im.isLocked && "subdued"}
                            fontWeight="semibold"
                          >
                            {t(im.title)}
                          </Text>
                          {im?.isNew && <Badge tone="info">{t("New")}</Badge>}
                        </InlineStack>
                      }
                      icon={im.icon}
                      selected={isActive(im.url)}
                      onClick={() => goToPath(im.url)}
                    />
                  ))}
                </Navigation>
              </Card>
            </div>
            <div className="settings-content">
              <Suspense fallback={<DummySettingsSkeleton />}>
                <Outlet />
              </Suspense>
            </div>
          </div>
        </Page>
      </div>
      <Footer />
    </div>
  );
}

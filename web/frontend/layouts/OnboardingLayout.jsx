// @ts-check
import Footer from "@/components/common/Footer";
import OnboardingSkeleton from "@/components/loader/OnboardingSkeleton";
import { STEPS } from "@/config/onboarding";
import { useShopApi } from "@/hooks";
import { rootFlexStyle } from "@/styles/common";
import { getNextObject, getPrevObject } from "@/utility/helpers";
import { useAppBridge } from "@shopify/app-bridge-react";
import { BlockStack, InlineStack, Page, ProgressBar, Text } from "@shopify/polaris";
import { Suspense } from "react";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { useDispatch, useSelector } from "react-redux";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import interpolatedTranslationKeys from "storeseo-enums/interpolatedTranslationKeys";
// @ts-ignore
import { useAppBridgeModal } from "@/hooks/useAppBridgeModal";
import OnboardingSteps from "storeseo-enums/onboardingSteps";

export default function OnboardingLayout() {
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const shopApi = useShopApi();
  // @ts-ignore
  const user = useSelector((state) => state.user);
  const appBridge = useAppBridge();
  const dispatch = useDispatch();
  const { showCheckoutModal } = useAppBridgeModal();

  const currentStep = STEPS.find((step) => step.pathname === pathname);

  const totalSteps = STEPS.length;
  const nextStep = getNextObject(STEPS, currentStep?.name, "name");
  const prevStep = getPrevObject(STEPS, currentStep?.name, "name");

  const stepsCompleted = STEPS.findIndex((step) => step.pathname === pathname) + 1 || totalSteps;
  const onboardProgressPercentage = Math.round((stepsCompleted * 100) / totalSteps);

  const updateOnboardingStatus = useMutation({
    mutationFn: () => shopApi.updateOnboardingStatus(currentStep.name),
    onSuccess: () => {
      if (nextStep) {
        navigate(nextStep.pathname);
      } else {
        navigate("/");
      }
    },
  });

  const handleContinueFree = () => {
    // dispatch(setCheckout({ slug: "free" }));
    // showCheckoutModal();
    updateOnboardingStatus.mutate();
  };

  return (
    <div
      // @ts-ignore
      style={{ ...rootFlexStyle }}
    >
      <div style={{ flex: 1 }}>
        <Suspense fallback={<OnboardingSkeleton />}>
          <Page
            title={t(currentStep.title)}
            subtitle={t(currentStep.subtitle)}
            secondaryActions={
              currentStep.name === OnboardingSteps.SUBSCRIPTION &&
              user.isSubscribed && [
                {
                  content: t("Continue"),
                  onAction: updateOnboardingStatus.mutate,
                  loading: updateOnboardingStatus.isLoading,
                },
              ]
            }
            backAction={
              [OnboardingSteps.SUBSCRIPTION, OnboardingSteps.ANALYSIS].includes(currentStep.name) && {
                content: t("Back"),
                onAction: () => navigate(prevStep.pathname),
              }
            }
          >
            <BlockStack gap={"400"}>
              <BlockStack gap={"200"}>
                {/* <InlineStack
                  gap={"200"}
                  blockAlign="center"
                  align="start"
                >
                  {currentStep.name === OnboardingSteps.SUBSCRIPTION && (
                    <Button
                      onClick={() => navigate(prevStep.pathname)}
                      icon={ArrowLeftIcon}
                      variant="tertiary"
                    />
                  )}
                  <Text
                    as="h2"
                    variant="headingXl"
                  >
                    {t(currentStep.title)}
                  </Text>
                  {currentStep.name === OnboardingSteps.SUBSCRIPTION && (
                    <ButtonGroup>
                      <Button onClick={() => navigate(prevStep.pathname)}>{t("Back")}</Button>
                      <Button
                        variant="primary"
                        onClick={handleContinueFree}
                        loading={updateOnboardingStatus.isLoading}
                      >
                        {t("Continue with {{PLAN_NAME}}", { PLAN_NAME: user.planName })}
                      </Button>
                    </ButtonGroup>
                  )}
                </InlineStack>
                <Text
                  as="p"
                  variant="bodyMd"
                  tone="subdued"
                >
                  {t(currentStep.subtitle)}
                </Text> */}
                <InlineStack
                  gap={"400"}
                  wrap={false}
                  blockAlign="center"
                >
                  <Text as="p">
                    {t(interpolatedTranslationKeys.ONBOARD_PROGRESS_MSG, {
                      "?": stepsCompleted,
                      "??": totalSteps,
                    })}
                  </Text>

                  <div style={{ flex: 1 }}>
                    <ProgressBar
                      progress={onboardProgressPercentage}
                      tone={onboardProgressPercentage === 100 ? "success" : "highlight"}
                      size="small"
                    />
                  </div>
                </InlineStack>
              </BlockStack>
              <Outlet context={{ updateOnboardingStatus, currentStep, nextStep, prevStep }} />
            </BlockStack>
          </Page>
        </Suspense>
      </div>

      <Footer />
    </div>
  );
}

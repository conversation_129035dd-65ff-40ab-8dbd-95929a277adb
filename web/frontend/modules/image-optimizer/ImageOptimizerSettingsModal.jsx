//@ts-check
import AutoOptimizationToggler from "@/components/image-optimizer/AutoOptimizationToggler";
import ImageOptimizerForm from "@/components/image-optimizer/ImageOptimizerForm";
import ImageOptimizerNotEnabledBanner from "@/components/image-optimizer/NotEnabledBanner";
import { MODAL_IDS } from "@/config";
import { useUnsavedChanges } from "@/hooks/useUnsavedChanges";
import useUserAddon from "@/hooks/useUserAddon";
import useImageOptimizerForm from "@/lib/hooks/image-optimizer/useImageOptimizerForm";
import { useImageOptimizeSettings } from "@/lib/hooks/image-optimizer/useImageOptimizeSettings";
import Modal from "@/modules/components/Modal";
import { BlockStack, Box } from "@shopify/polaris";
import { AlertTriangleIcon } from "@shopify/polaris-icons";
import { isEmpty, omit } from "lodash";
import React, { memo, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useBanner } from "../../hooks/useBanner";

/**
 * @returns {React.ReactElement}
 */
const ImageOptimizerSettingsModal = () => {
  const { t } = useTranslation();
  const { hasImageOptimizer, isFreeImageOptimizer } = useUserAddon();

  // Get settings from the hook (includes autoOptimization and mutations)
  const { settings: apiSettings, updateSettings, toggleAutoOptimization } = useImageOptimizeSettings();
  const [noneSelected, setNoneSelected] = useState(true);

  // Get autoOptimization from the settings hook
  const autoOptimization = apiSettings?.autoOptimization || false;

  // Use the image optimizer form hook
  const { control, formState, watch, handleSubmit, isLoading: isFormLoading } = useImageOptimizerForm();

  const { isValid } = formState;

  // Form submission handler
  const onSubmit = async (formData) => {
    // Merge form data with current auto-optimization state
    const completeData = {
      ...formData,
      autoOptimization: autoOptimization, // Use current toggler value
    };
    await updateSettings.mutateAsync(completeData);
  };

  // Handle primary action (Save button)
  const handleSettingsUpdate = async () => {
    await handleSubmit(onSubmit)();
    if (isValid && !updateSettings.isLoading) {
      shopify.modal.hide(MODAL_IDS.IMAGE_OPTIMIZER_SETTINGS);
    }
  };

  // Watch form values to track changes
  const watchedValues = watch();

  // Track unsaved changes
  const { hasUnsavedChanges } = useUnsavedChanges({
    originalData: omit(apiSettings, "autoOptimization") || {},
    currentData: watchedValues || {},
  });

  const { Banner: ImageOptimizerSettingsWarningBanner, showBanner: showImageOptimizerSettingsWarningBanner } =
    useBanner({
      title: t("Warning"),
      message: t(
        "To use the Image Optimizer feature, you must select the compression methods from here. Currently, all are selected as 'None'."
      ),
      icon: AlertTriangleIcon,
      tone: "warning",
      noMargin: true,
    });

  // Check if none of the optimization settings are selected
  const checkNoneSelected = (values) => {
    if (!isEmpty(values)) {
      return !Object.values(values).filter((val) => val !== "none").length > 0;
    }
    return true;
  };

  const [errors, setErrors] = useState({
    autoOptimization: "",
  });

  // Auto optimization toggle handler - no confirmation modal needed
  const handleToggleAutoOptimization = async () => {
    if (noneSelected) {
      setErrors({
        ...errors,
        autoOptimization: "You need to setup optimization settings before enabling auto optimization.",
      });
      return;
    } else {
      setErrors({
        ...errors,
        autoOptimization: "",
      });
    }

    try {
      // Use the hook's mutation with mutateAsync - pass the new state
      await toggleAutoOptimization.mutateAsync(!autoOptimization);
    } catch (error) {
      console.error("Error toggling auto optimization:", error);
    }
  };

  // Update noneSelected when form values change
  useEffect(() => {
    setNoneSelected(checkNoneSelected(watchedValues));
  }, [watchedValues]);

  // Show warning banner when settings are loaded
  useEffect(() => {
    if (!isFormLoading && apiSettings) {
      showImageOptimizerSettingsWarningBanner(hasImageOptimizer && checkNoneSelected(apiSettings));
    }
  }, [isFormLoading, apiSettings, hasImageOptimizer]);

  return (
    <Modal
      id={MODAL_IDS.IMAGE_OPTIMIZER_SETTINGS}
      type="app-bridge"
    >
      <Modal.Section>
        <Modal.TitleBar title={t("Image Optimizer Settings")}>
          <button
            variant="primary"
            onClick={handleSettingsUpdate}
            loading={isFormLoading || updateSettings.isLoading ? "" : undefined}
            disabled={!hasImageOptimizer || !hasUnsavedChanges}
          >
            {t("Save")}
          </button>
        </Modal.TitleBar>
        <Box padding="400">
          <BlockStack gap="400">
            {!hasImageOptimizer && <ImageOptimizerNotEnabledBanner />}

            <ImageOptimizerSettingsWarningBanner />

            <AutoOptimizationToggler
              isEnabled={autoOptimization}
              isLoading={toggleAutoOptimization.isLoading}
              disabled={!hasImageOptimizer || isFreeImageOptimizer}
              error={errors?.autoOptimization}
              showWarningBanner={true}
              onToggle={handleToggleAutoOptimization}
            />

            <ImageOptimizerForm
              control={control}
              formState={formState}
              disabled={!hasImageOptimizer}
              showFormat={false}
              showHints={true}
              customTitles={{
                compression: t("Image Compression Settings"),
                resize: t("Advance Image Resizer"),
              }}
              customDescriptions={{
                compression: t("Choose your default image compression settings"),
                resize: t("Choose your default image size"),
              }}
            />
          </BlockStack>
        </Box>
      </Modal.Section>
    </Modal>
  );
};

export default memo(ImageOptimizerSettingsModal);

//@ts-check
import useImageOptimizerIndexFilters from "@/lib/hooks/useImageOptimizerIndexFilters";
import { IndexTableFilters } from "@/modules/components";
import { useImageOptimizerLayoutContext } from "@/providers/ImageOptimizerLayoutProvider";
import { memo } from "react";

const tabsContents = [
  { content: "Products", path: "/image-optimizer" },
  { content: "Collections", path: "/image-optimizer/collections" },
  { content: "Blog posts", path: "/image-optimizer/blog-posts" },
];

/**
 *
 * @param {{hideQueryField?: boolean, hideFilters?: boolean}} props
 * @returns
 */
const ImageOptimizerIndexTableFilter = (props) => {
  const { hideQueryField = false, hideFilters = false } = props;
  const { isLoading } = useImageOptimizerLayoutContext();
  const { filters, appliedFilters, setSelectedFilters, filterQuery, sortSelected, setSortSelected } =
    useImageOptimizerIndexFilters();

  return (
    <IndexTableFilters
      filters={filters}
      appliedFilters={appliedFilters}
      setSelectedFilters={setSelectedFilters}
      filterQuery={filterQuery}
      loading={isLoading}
      sortSelected={sortSelected}
      onSort={setSortSelected}
      tabs={tabsContents}
      hideQueryField={hideQueryField}
      hideFilters={hideFilters}
    />
  );
};

export default memo(ImageOptimizerIndexTableFilter);

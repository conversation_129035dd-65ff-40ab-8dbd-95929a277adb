//@ts-check
import ImageRows from "@/components/image-optimizer/ImageRows";
import { IndexTable, useBreakpoints } from "@shopify/polaris";
import { useTranslation } from "react-i18next";

/**
 * @template T
 * @typedef {import("@shopify/polaris/build/ts/src/types.js").NonEmptyArray<T>} NonEmptyArray
 */

/**
 * @typedef {import("@shopify/polaris/build/ts/src/components/IndexTable/IndexTable.js").IndexTableHeading} IndexTableHeading
 */

const resourceName = {
  singular: "image",
  plural: "images",
};

/**
 * @type {NonEmptyArray<{key: string, title: string} & IndexTableHeading>}
 */
const tableHeadings = [
  { title: "", key: "sl" },
  { title: "File name", key: "file_name" },
  { title: "Status", key: "optimization_status" },
  { title: "File size", key: "file_size" },
  { title: "", key: "product.title" },
  { title: "Action", key: "action", alignment: "center" },
];

const defaultRowItem = { id: null, title: null };

export default function ImageOptimizerTableSkeleton() {
  const { t } = useTranslation();
  const { smDown: isSmallDevice } = useBreakpoints();

  const sitemapRows = new Array(10).fill(defaultRowItem);
  return (
    <IndexTable
      condensed={isSmallDevice}
      resourceName={resourceName}
      itemCount={sitemapRows.length}
      // @ts-ignore
      headings={tableHeadings.map((heading) => ({
        ...heading,
        title: t(heading.title),
        alignment: heading.alignment || "start",
      }))}
      selectable={false}
    >
      {sitemapRows.map((row, index) => {
        return (
          <ImageRows
            key={index}
            image={row}
            isFetching={true}
          />
        );
      })}
    </IndexTable>
  );
}

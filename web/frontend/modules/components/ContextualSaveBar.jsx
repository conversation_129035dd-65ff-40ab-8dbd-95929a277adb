//@ts-check
import React from "react";
import { SaveBar } from "@shopify/app-bridge-react";
import { useEffect } from "react";
import { useBlocker } from "react-router-dom";

/**
 * @typedef {import("@shopify/app-bridge-react").SaveBarProps} SaveBarProps
 * @typedef {{isLoading?: boolean, onSave?: () => void, onDiscard?: () => void} & Omit<SaveBarProps, "children">} ContextualSaveBarProps
 * @type {React.ForwardRefExoticComponent<ContextualSaveBarProps & React.RefAttributes<UISaveBarElement>>} ContextualSaveBar
 * @returns JSX.Element
 */
const ContextualSaveBar = React.forwardRef(({ isLoading, onSave, onDiscard, open, ...props }, ref) => {
  const blocker = useBlocker(
    ({ currentLocation, nextLocation }) => open && currentLocation.pathname !== nextLocation.pathname
  );

  // Leave confirmation
  useEffect(() => {
    (async () => {
      if (blocker.state === "blocked") {
        await shopify.saveBar.leaveConfirmation();
      }
    })();
  }, [blocker]);

  return (
    <SaveBar
      ref={ref}
      open={open}
      {...props}
    >
      {/* Discard action */}
      <button
        onClick={onDiscard && onDiscard}
        disabled={isLoading ? true : false}
      ></button>
      {/* Save action */}
      <button
        variant="primary"
        loading={isLoading ? "" : null}
        onClick={onSave && onSave}
      ></button>
    </SaveBar>
  );
});

export default ContextualSaveBar;

import { useAppNavigation } from "@/hooks/useAppNavigation";
import { useBanner } from "@/hooks/useBanner";
import { Button } from "@shopify/polaris";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

export default function BackupRestoreInfoBanner() {
  const { t } = useTranslation();
  const [show, setShow] = useState(true);
  const { goToPath } = useAppNavigation();

  const { Banner, showBanner } = useBanner({
    message: (
      <>
        {t("Backup your data before making any changes to your store.")}{" "}
        <Button
          variant="plain"
          onClick={() => goToPath("/settings/backup-restore")}
        >
          {t("Backup & Restore Settings")}
        </Button>
      </>
    ),
    tone: "info",
    onDismiss: () => setShow(false),
    noMargin: true,
  });

  useEffect(() => {
    showBanner(show);
  }, [show]);

  return <Banner />;
}

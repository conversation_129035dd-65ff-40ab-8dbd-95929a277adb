//@ts-check
import { ChoiceList } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import CustomDateSelector from "./CustomDateSelector";

/**
 *
 * @param {{label : string, choices: Array<any>, selected: string[], onChange: import("@shopify/polaris").ChoiceListProps["onChange"],onCustomDatePicked : ({from, to} : {from:string, to:string}) => void, defaultCustomDateValue:{from:string, to:string}}} props
 * @returns {JSX.Element}
 */
const DatePickerFilterSelector = ({
  label,
  choices,
  selected,
  onChange,
  onCustomDatePicked,
  defaultCustomDateValue,
}) => {
  const isCustomSelected = selected.includes("customDate");
  const { t } = useTranslation();

  return (
    <>
      <ChoiceList
        title={t(label)}
        titleHidden
        choices={choices.map((choice) => ({
          ...choice,
          label: t(choice.label),
        }))}
        selected={selected}
        onChange={onChange}
        allowMultiple={false}
      />
      {isCustomSelected && (
        <CustomDateSelector
          defaultValue={defaultCustomDateValue}
          onChange={onCustomDatePicked}
        />
      )}
    </>
  );
};

export default DatePickerFilterSelector;

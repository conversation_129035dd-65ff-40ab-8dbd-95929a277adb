//@ts-check
import { IndexFilters, IndexFiltersMode, useSetIndexFiltersMode } from "@shopify/polaris";
import { useDebounce } from "@uidotdev/usehooks";
import { isEmpty, omit } from "lodash";
import { memo, useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { useLocation, useSearchParams } from "react-router-dom";
import { omitSearchQueryKeys, omitSortOrderQueryKeys } from "./constants";

/**
 *
 * @param {{setSelectedFilters: import("react").Dispatch<import("react").SetStateAction<{}>>, filterQuery: object, tabs?:{content:string, path:string}[]} & Partial<Omit<import("@shopify/polaris").IndexFiltersProps, "tabs">>} param
 * @returns
 */
export const IndexTableFiltersWithoutMemoize = ({
  filters = [],
  appliedFilters = [],
  setSelectedFilters = () => {},
  filterQuery = null,
  tabs = [],
  ...rest
}) => {
  const { t } = useTranslation();
  const { mode, setMode } = useSetIndexFiltersMode();
  const [searchParams, setSearchParams] = useSearchParams();
  const { pathname } = useLocation();
  const TabIndex = tabs.findIndex((tab) => pathname == tab.path);

  const searchParamsQuery = useMemo(() => Object.fromEntries(searchParams.entries()), [searchParams]);
  const [selectedTab, setSelectedTab] = useState(TabIndex > 0 ? TabIndex : 0);
  const [searchQueryValue, setSearchQueryValue] = useState(searchParamsQuery.search || "");
  const debouncedSearchTerm = useDebounce(searchQueryValue, 300);

  // @ts-ignore
  const user = useSelector((state) => state.user);
  const isBetterDocsInstalled = user?.betterDocsInstallationStatus;

  const tabContent = useMemo(
    () =>
      tabs
        .filter((t) => (!isBetterDocsInstalled ? !t.path.includes("/docs") : true))
        .map((item, index) => ({
          content: t(item.content),
          index,
          id: `${item.content}-${index}`,
          url: item.path,
          isLocked: index === 0,
        })),
    [tabs, isBetterDocsInstalled]
  );

  const onHandleCancel = useCallback(() => {
    setSearchQueryValue("");
    setSelectedFilters({});
    setSearchParams(undefined, { replace: true });
  }, []);

  const onHandleFilterClearAll = useCallback(() => {
    setSelectedFilters({});
    setSearchParams(undefined, { replace: true });
  }, []);

  useEffect(() => {
    setSelectedTab(TabIndex > 0 ? TabIndex : 0);
  }, [TabIndex]);

  useEffect(() => {
    let searchQuery = { ...omit(searchParamsQuery, omitSearchQueryKeys) };
    searchQuery = { ...searchQuery, ...filterQuery };

    if (Object.keys(searchQuery).length > 1) {
      // @ts-ignore
      delete searchQuery.page;
    }
    if (debouncedSearchTerm) {
      // @ts-ignore
      searchQuery.search = debouncedSearchTerm.trim();
    }
    setSearchParams(searchQuery, { replace: true });
  }, [debouncedSearchTerm, filterQuery]);

  useEffect(() => {
    const newFilterQuery = omit(filterQuery, omitSortOrderQueryKeys);
    if (debouncedSearchTerm?.length || !isEmpty(newFilterQuery)) {
      setMode(IndexFiltersMode.Filtering);
    }
  }, [debouncedSearchTerm, filterQuery]);

  useEffect(() => {
    shopify.loading(rest.loading);
    return () => shopify.loading(false);
  }, [rest.loading]);

  return (
    <IndexFilters
      queryValue={searchQueryValue}
      queryPlaceholder={t("Searching in all")}
      onQueryChange={(value) => setSearchQueryValue(value)}
      onQueryClear={() => setSearchQueryValue("")}
      cancelAction={{
        onAction: onHandleCancel,
      }}
      tabs={tabContent}
      selected={selectedTab}
      onSelect={setSelectedTab}
      canCreateNewView={false}
      filters={filters}
      appliedFilters={appliedFilters}
      onClearAll={onHandleFilterClearAll}
      mode={mode}
      setMode={setMode}
      filteringAccessibilityTooltip={t("Search and filter (F)")}
      {...rest}
    />
  );
};

export const IndexTableFilters = memo(IndexTableFiltersWithoutMemoize);

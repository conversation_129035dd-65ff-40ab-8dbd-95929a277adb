//@ts-check
import { BlockStack } from "@shopify/polaris";
import { useEffect, useState } from "react";
import DatePickerInput from "../DatePickerInput";
import { useTranslation } from "react-i18next";
/**
 *
 * @param {{defaultValue?: {from: string, to:string},onChange: ({from, to} : {from: string, to: string}) => void}} param0
 * @returns
 */
const CustomDateSelector = ({ defaultValue, onChange }) => {
  const { t } = useTranslation();
  const { from: qFrom, to: qTo } = defaultValue || {};

  const [from, setFrom] = useState(qFrom || "");
  const [to, setTo] = useState(qTo || "");

  useEffect(() => {
    onChange({ from, to });
  }, [from, to]);

  return (
    <BlockStack gap="200">
      <DatePickerInput
        label={t("Starting")}
        defaultValue={from}
        onChange={setFrom}
      />
      <DatePickerInput
        label={t("Ending")}
        defaultValue={to}
        onChange={setTo}
      />
    </BlockStack>
  );
};

export default CustomDateSelector;

//@ts-check
import { BlockStack, TextField } from "@shopify/polaris";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

/**
 * @typedef {Array<string>} FileSizeFilter
 * @param {{selected:FileSizeFilter, onChange: (value : FileSizeFilter) => void}} props
 * @returns {JSX.Element}
 */
export default function FileSizeFilterSelector(props) {
  const { selected, onChange } = props;
  const { t } = useTranslation();
  const [formValue, setFormValue] = useState({
    min: selected[0] || "",
    max: selected[1] || "",
  });

  useEffect(() => {
    const value = [formValue.min || "", formValue.max || ""];
    const changeValue = value.every((v) => v === "") ? null : value;
    onChange(changeValue);
  }, [formValue]);

  /**
   * @param {string} value
   * @param {"min"|"max"} path
   */
  const handleChanges = (value, path) => {
    setFormValue((prev) => ({ ...prev, [path]: value }));
  };

  return (
    <BlockStack gap="200">
      <TextField
        label={`${t("Min size")} (MB)`}
        type="number"
        value={formValue.min}
        onChange={(value) => handleChanges(value, "min")}
        autoComplete="off"
      />
      <TextField
        label={`${t("Max size")} (MB)`}
        type="number"
        value={formValue.max}
        onChange={(value) => handleChanges(value, "max")}
        autoComplete="off"
      />
    </BlockStack>
  );
}

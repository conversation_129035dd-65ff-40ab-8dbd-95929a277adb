# Enhanced Modal Component

A unified Modal component that supports both Polaris Modal and App Bridge Modal implementations using a compound component design pattern.

## Features

- **Backward Compatible**: All existing Polaris Modal usage continues to work without changes
- **Dual Modal Support**: Choose between Polaris Modal (default) or App Bridge Modal
- **Compound Components**: Clean API with Modal.Section, Modal.ToggleButton, Modal.AppBridgeSection, and Modal.TitleBar
- **Unified API**: Same props interface regardless of modal type
- **TypeScript Support**: Full type definitions for both modal types

## Basic Usage

### Polaris Modal (Default/Legacy)

```jsx
import Modal from './Modal';

function MyComponent() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Modal
      open={isOpen}
      setOpen={setIsOpen}
      title="My Modal"
      primaryAction={{
        content: "Save",
        onAction: () => setIsOpen(false),
      }}
    >
      <Modal.ToggleButton variant="primary">
        Open Modal
      </Modal.ToggleButton>

      <Modal.Section>
        <p>Modal content goes here</p>
      </Modal.Section>
    </Modal>
  );
}
```

### App Bridge Modal

```jsx
import Modal from './Modal';

function MyComponent() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Modal
      type="app-bridge"
      open={isOpen}
      setOpen={setIsOpen}
    >
      <Modal.ToggleButton variant="primary">
        Open App Bridge Modal
      </Modal.ToggleButton>

      <Modal.Section>
        <p>App Bridge modal content</p>

        <Modal.TitleBar title="My App Bridge Modal">
          <button variant="primary" onClick={() => setIsOpen(false)}>
            Save
          </button>
          <button onClick={() => setIsOpen(false)}>
            Cancel
          </button>
        </Modal.TitleBar>
      </Modal.Section>
    </Modal>
  );
}
```

## Props

### Common Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `open` | `boolean` | - | Control open state (controlled mode) |
| `setOpen` | `(open: boolean) => void` | - | Setter for open state (controlled mode) |
| `onClose` | `() => void` | - | Called when modal is closed |
| `disable` | `boolean` | `false` | If true, disables closing/toggling |
| `type` | `"polaris" \| "app-bridge"` | `"polaris"` | Type of modal to use |

### App Bridge Specific Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `id` | `string` | - | Optional unique identifier for App Bridge modals |
| `variant` | `"small" \| "base" \| "large" \| "max"` | - | App Bridge modal size |
| `src` | `string` | - | URL for iframe content |

### Polaris Specific Props

All existing Polaris Modal props are supported.

## Components

### Modal.Section (Polaris)

Used with Polaris modals. Renders a Polaris Modal.Section.

```jsx
<Modal.Section>
  <p>Content for Polaris modal</p>
</Modal.Section>
```

### Modal.Section (App Bridge)

Used with App Bridge modals. Renders the actual App Bridge modal wrapper.

```jsx
<Modal.Section>
  <p>Content for App Bridge modal</p>
  <Modal.TitleBar title="Modal Title">
    <button variant="primary">Primary Action</button>
    <button>Secondary Action</button>
  </Modal.TitleBar>
</Modal.Section>
```

### Modal.TitleBar (App Bridge)

Used with App Bridge modals to define the title and action buttons.

```jsx
<Modal.TitleBar title="Modal Title">
  <button variant="primary">Primary Action</button>
  <button>Secondary Action</button>
</Modal.TitleBar>
```

### Modal.ToggleButton

Works with both modal types. Renders a button that toggles the modal.

```jsx
<Modal.ToggleButton variant="primary">
  Open Modal
</Modal.ToggleButton>

// Or with render prop
<Modal.ToggleButton>
  {({ onClick }) => (
    <CustomButton onClick={onClick}>
      Custom Toggle Button
    </CustomButton>
  )}
</Modal.ToggleButton>
```

## Migration Guide

### From Polaris Modal to App Bridge Modal

1. Add `type="app-bridge"` prop
4. Replace `title` and action props with `Modal.TitleBar`

**Before (Polaris):**
```jsx
<Modal
  open={isOpen}
  setOpen={setIsOpen}
  title="My Modal"
  primaryAction={{ content: "Save", onAction: handleSave }}
  secondaryActions={[{ content: "Cancel", onAction: handleCancel }]}
>
  <Modal.Section>
    <p>Content</p>
  </Modal.Section>
</Modal>
```

**After (App Bridge):**
```jsx
<Modal
  type="app-bridge"
  open={isOpen}
  setOpen={setIsOpen}
>
  <Modal.Section>
    <p>Content</p>

    <Modal.TitleBar title="My Modal">
      <button variant="primary" onClick={handleSave}>Save</button>
      <button onClick={handleCancel}>Cancel</button>
    </Modal.TitleBar>
  </Modal.Section>
</Modal>
```

## Key Differences

### Polaris Modal
- Renders within the app frame
- Uses traditional Polaris styling
- Limited to app viewport
- Supports all existing Polaris Modal props

### App Bridge Modal
- Renders outside app frame in Shopify admin
- Covers entire admin interface
- Better integration with Shopify admin
- Automatic styling updates from Shopify
- Uses `Modal.TitleBar` for title and actions

### IntelliSense Behavior

**When using Polaris Modal (default):**
```jsx
<Modal
  title="My Modal"           // ✅ Available & REQUIRED
  primaryAction={{...}}      // ✅ Available
  size="large"              // ✅ Available
  id="modal-id"             // ❌ Not suggested (App Bridge only)
  variant="medium"          // ❌ Not suggested (App Bridge only)
>
```

**When using App Bridge Modal:**
```jsx
<Modal
  type="app-bridge"
  id="my-modal"             // ✅ Available
  variant="large"           // ✅ Available
  src="/modal-content"      // ✅ Available
  title="My Modal"          // ❌ Not available (use Modal.TitleBar instead)
  primaryAction={{...}}     // ❌ Not available (use Modal.TitleBar instead)
>
```

## TypeScript Support

The component includes full TypeScript definitions with proper discriminated unions. Import types as needed:

```typescript
import Modal, { useModalContext } from './Modal';
import type { ModalProps, ModalContextType } from './Modal';
```

## Backward Compatibility

All existing code using the Modal component will continue to work without any changes. The component defaults to `type="polaris"` to maintain backward compatibility.

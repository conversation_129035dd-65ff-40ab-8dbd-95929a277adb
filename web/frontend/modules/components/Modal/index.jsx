//@ts-check
import { Modal as AppBridgeModal, TitleBar as AppBridgeTitleBar } from "@shopify/app-bridge-react";
import { Button, Modal as PolarisModal } from "@shopify/polaris";
import { createContext, useCallback, useContext, useMemo, useState } from "react";

/**
 * @typedef {object} BaseModalProps
 * @property {boolean} [open] - Control open state (controlled mode)
 * @property {(open: boolean) => void} [setOpen] - Setter for open state (controlled mode)
 * @property {() => void} [onClose] - Called when modal is closed
 * @property {boolean} [disable] - If true, disables closing/toggling
 */

/**
 * @typedef {object} PolarisModalSpecificProps
 * @property {"polaris"} [type] - Use Polaris Modal (default)
 * @property {string | React.ReactNode} title - Modal title (required for Polaris)
 * @property {object} [primaryAction] - Primary action button config
 * @property {object[]} [secondaryActions] - Secondary action buttons
 * @property {import("@shopify/polaris/build/ts/src/components/Modal").ModalSize} [size] - Modal size (Polaris supports small/large)
 * @property {boolean} [titleHidden] - Whether to hide the title
 * @property {React.ReactNode} [footer] - Custom footer content
 * @property {boolean} [loading] - Whether modal is in loading state
 */

/**
 * @typedef {object} AppBridgeModalSpecificProps
 * @property {"app-bridge"} type - Use App Bridge Modal
 * @property {string} [id] - Optional unique identifier for App Bridge modal
 * @property {'small' | 'base' | 'large' | 'max'} [variant] - App Bridge modal variant
 * @property {string} [src] - App Bridge modal src URL for iframe content
 * @property {string} [iFrameName] - Name for the iframe
 */

/**
 * @typedef {BaseModalProps & PolarisModalSpecificProps} PolarisModalProps
 */

/**
 * @typedef {BaseModalProps & AppBridgeModalSpecificProps} AppBridgeModalProps
 */

/**
 * @typedef {PolarisModalProps | AppBridgeModalProps} CustomModalProps
 */

/**
 * @typedef {object} ModalContextType
 * @property {boolean} isOpen
 * @property {() => void} handleClose
 * @property {() => void} toggleModal
 * @property {"polaris" | "app-bridge"} type
 * @property {string} [id]
 * @property {'small' | 'base' | 'large' | 'max'} [variant]
 * @property {string} [src]
 * @property {string | React.ReactNode} title - Only used for Polaris modals
 * @property {object} [primaryAction]
 * @property {object[]} [secondaryActions]
 * @property {import("@shopify/polaris/build/ts/src/components/Modal").ModalSize} [size]
 * @property {boolean} [titleHidden]
 * @property {React.ReactNode} [footer]
 * @property {boolean} [loading]
 * @property {string} [iFrameName]
 * @property {() => void} [onClose] - Called when modal is closed
 */

/** @type {import("react").Context<ModalContextType | null>} */
const ModalContext = createContext(null);
ModalContext.displayName = "ModalContext";

/**
 * Modal component that supports both Polaris and App Bridge modals
 * @typedef {CustomModalProps & Record<string, any>} ModalProps
 * @param {ModalProps} props
 */
function Modal({
  open,
  setOpen,
  onClose,
  disable = false,
  type = "polaris",
  variant,
  src,
  children,
  title = undefined,
  ...modalProps
}) {
  const [internalOpen, setInternalOpen] = useState(false);
  const isControlled = open !== undefined && setOpen !== undefined;
  const isOpen = isControlled ? open : internalOpen;

  const handleClose = useCallback(() => {
    if (onClose) onClose();
    if (disable) return;
    if (isControlled) {
      setOpen(false);
    } else {
      setInternalOpen(false);
    }
  }, [disable, onClose, isControlled, setOpen]);

  const toggleModal = useCallback(() => {
    if (disable) return;
    if (isControlled) {
      setOpen(!open);
    } else {
      setInternalOpen((prev) => !prev);
    }
  }, [disable, isControlled, setOpen, open]);

  // Memoize context value to avoid unnecessary rerenders
  const contextValue = useMemo(
    () => ({
      isOpen,
      handleClose,
      toggleModal,
      type,
      variant,
      src,
      title,
      ...modalProps,
    }),
    [isOpen, handleClose, toggleModal, type, variant, src, modalProps, title]
  );

  return <ModalContext.Provider value={contextValue}>{children}</ModalContext.Provider>;
}

/**
 * @typedef {import("@shopify/polaris/build/ts/src/components/Modal/components/Section").SectionProps} ModalSectionProps
 * @param {ModalSectionProps} props
 */
function Section(props) {
  const { isOpen, handleClose, type, primaryAction, secondaryActions, variant, src, id, ...modalProps } =
    useContext(ModalContext);

  if (type === "app-bridge") {
    return (
      <AppBridgeModal
        id={id}
        open={isOpen}
        onHide={handleClose}
        variant={variant}
        src={src}
      >
        {props.children}
      </AppBridgeModal>
    );
  }

  // Remove non-Polaris props before passing to PolarisModal
  const { toggleModal: _toggleModal, ...polarisModalProps } = modalProps;

  return (
    <PolarisModal
      open={isOpen}
      onClose={handleClose}
      primaryAction={primaryAction}
      secondaryActions={secondaryActions}
      {...polarisModalProps}
    >
      <PolarisModal.Section {...props} />
    </PolarisModal>
  );
}

/**
 *
 * @param {{children: React.ReactNode | ((args: {onClick: () => void}) => React.ReactNode)} & Omit<import("@shopify/polaris").ButtonProps, "children">} props
 */
function ToggleButton(props) {
  const { toggleModal } = useContext(ModalContext);

  if (typeof props.children === "object" && props.children !== null && !Array.isArray(props.children)) {
    // If children is a custom React element, render it directly
    return props.children;
  }

  if (typeof props.children === "function") {
    return props.children({ onClick: toggleModal });
  }

  // Only pass children if it is a string or array of strings
  const buttonChildren =
    typeof props.children === "string" ||
    (Array.isArray(props.children) && props.children.every((c) => typeof c === "string"))
      ? props.children
      : undefined;

  return (
    <Button
      onClick={toggleModal}
      {...props}
    >
      {buttonChildren}
    </Button>
  );
}

Modal.Section = Section;
Modal.ToggleButton = ToggleButton;

// App Bridge Modal components
Modal.TitleBar = AppBridgeTitleBar;

export const useModalContext = () => {
  const context = useContext(ModalContext);
  if (!context) {
    throw new Error("useModalContext must be used within a Modal");
  }
  return context;
};

export default Modal;

//@ts-check
import { Icon, InlineStack, Text } from "@shopify/polaris";
import { AlertBubbleIcon } from "@shopify/polaris-icons";
import React from "react";
import { useTranslation } from "react-i18next";

/**
 * @param {{className: string}} props
 * @returns {React.ReactElement}
 */
export default function UnsavedChangesAlert({ className }) {
  const { t } = useTranslation();
  return (
    <div className={className}>
      <InlineStack
        blockAlign="center"
        gap="050"
      >
        <Icon source={AlertBubbleIcon} />
        <Text
          as="p"
          variant="bodySm"
        >
          {t("Unsaved changes")}
        </Text>
      </InlineStack>
    </div>
  );
}

import { useBanner } from "@/hooks/useBanner";
import useIsRunningBackupRestore from "@/lib/hooks/useIsRunningBackupRestore";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";

export default function BackupRestoreRunningBanner() {
  const { t } = useTranslation();
  const { isRunning: isRunningBackupOrRestore } = useIsRunningBackupRestore();
  const { Banner, showBanner } = useBanner({
    title: t("Backup or Restore process is running"),
    message: t("You cannot perform bulk action while backup or restore process is ongoing."),
    tone: "warning",
    noMargin: true,
  });

  useEffect(() => {
    showBanner(isRunningBackupOrRestore);
  }, [isRunningBackupOrRestore]);

  return <Banner />;
}

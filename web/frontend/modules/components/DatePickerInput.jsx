//@ts-check
import { isValidDate } from "@/utility/helpers";
import { BlockStack, Box, Button, DatePicker, Icon, Popover, TextField, Tooltip } from "@shopify/polaris";
import { CalendarIcon, XSmallIcon } from "@shopify/polaris-icons";
import moment from "moment";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

/**
 *
 * @param {{defaultValue?:string, onChange : (value:string) => void} & Omit<import("@shopify/polaris").TextFieldProps, "autoComplete">} props
 * @returns {JSX.Element}
 */
export default function DatePickerInput({ defaultValue, onChange, ...rest }) {
  const currentMonth = moment().month();
  const currentYear = moment().year();
  const [customDate, setCustomDate] = useState(defaultValue || "");

  const [currentMonthAndYear, setCurrentMonthAndYear] = useState({
    month: currentMonth,
    year: currentYear,
  });
  const [selectedDate, setSelectedDate] = useState({
    start: isValidDate(customDate) ? new Date(customDate) : new Date(),
    end: isValidDate(customDate) ? new Date(customDate) : new Date(),
  });
  useEffect(() => {
    setSelectedDate({
      start: isValidDate(customDate) ? new Date(customDate) : new Date(),
      end: isValidDate(customDate) ? new Date(customDate) : new Date(),
    });
  }, [customDate]);

  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);

  /**
   *
   * @param {string} value
   * @returns
   */
  const onChangeCustomDateInput = (value) => {
    if (!/^[0-9-]*$/.test(value)) return;

    setCustomDate(value);
    onChange(value);
    if (isValidDate(value) && moment(value).isValid() && value.length > 5) {
      setCurrentMonthAndYear({
        month: moment(value).month(),
        year: moment(value).year(),
      });
    }
  };

  /**
   *
   * @param {{start : Date, end: Date}} date - Range of date
   */
  const onChangeDatePicker = ({ start, end }) => {
    setSelectedDate({
      start,
      end,
    });
    setCustomDate(moment(start).format("YYYY-MM-DD"));
    onChange(moment(start).format("YYYY-MM-DD"));
    setCurrentMonthAndYear({
      month: moment(start).month(),
      year: moment(start).year(),
    });
    setIsDatePickerOpen(false);
  };

  /**
   *
   * @param {number} month
   * @param {number} year
   */
  const onChangeDatePickerMonth = (month, year) => {
    setCurrentMonthAndYear({ month, year });
  };
  return (
    <Popover
      active={isDatePickerOpen}
      autofocusTarget="none"
      activator={
        <TextField
          type="text"
          autoComplete="off"
          placeholder="YYYY-MM-DD"
          prefix={<Icon source={CalendarIcon} />}
          suffix={
            <BlockStack align="center">
              <ClearInputAction
                onClick={() => setCustomDate("")}
                disabled={!customDate}
              />
            </BlockStack>
          }
          onChange={(value) => onChangeCustomDateInput(value)}
          value={customDate}
          error={customDate && !isValidDate(customDate) && "Must match YYYY-MM-DD format"}
          onFocus={() => setIsDatePickerOpen(true)}
          {...rest}
        />
      }
      onClose={() => setIsDatePickerOpen(false)}
    >
      <Popover.Pane sectioned>
        <Box width="250px">
          <DatePicker
            month={currentMonthAndYear.month}
            year={currentMonthAndYear.year}
            selected={selectedDate}
            onChange={(date) => onChangeDatePicker(date)}
            onMonthChange={(month, year) => onChangeDatePickerMonth(month, year)}
          />
        </Box>
      </Popover.Pane>
    </Popover>
  );
}

/**
 *
 * @param {import("@shopify/polaris").ButtonProps} props
 * @returns
 */
const ClearInputAction = (props) => {
  const { t } = useTranslation();
  return (
    <Tooltip
      dismissOnMouseOut
      hoverDelay={500}
      preferredPosition="above"
      content={t("Clear")}
      activatorWrapper="div"
      active={!props.disabled}
    >
      <Button
        variant="tertiary"
        icon={XSmallIcon}
        {...props}
      />
    </Tooltip>
  );
};

import EmptyPage from "@/components/common/EmptyPage";
import SyncButton from "@/components/common/SyncButton";
import { useSyncPages } from "@/hooks/pages";
import { useAppBridgeRedirect } from "@/hooks/useAppBridgeRedirect";
import { Button } from "@shopify/polaris";
import React from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";

export default function PageSitemapsEmptyPage() {
  const { t } = useTranslation();
  const { handleAddPages } = useAppBridgeRedirect();
  const pageSync = useSelector((state) => state.pageSync);
  const { mutate: startPageSync, isLoading: isStartingSync } = useSyncPages({});

  const actionMarkup = (
    <SyncButton
      title="pages"
      loading={isStartingSync}
      callback={startPageSync}
      syncState={pageSync}
    />
  );
  return (
    <EmptyPage
      heading="Configure sitemaps for your pages"
      content="Add or sync your pages first to configure sitemaps"
      secondaryAction={actionMarkup}
      primaryAction={
        <Button
          variant="primary"
          onClick={handleAddPages}
        >
          {t("Add pages")}
        </Button>
      }
      insideCard={false}
    />
  );
}

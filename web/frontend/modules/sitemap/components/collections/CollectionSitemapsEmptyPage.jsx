import CollectionSyncButton from "@/components/collections/CollectionSyncButton";
import EmptyPage from "@/components/common/EmptyPage";
import { useAppBridgeRedirect } from "@/hooks/useAppBridgeRedirect";
import { Button } from "@shopify/polaris";
import React from "react";
import { useTranslation } from "react-i18next";

export default function CollectionSitemapsEmptyPage() {
  const { t } = useTranslation();
  const { handleAddCollections } = useAppBridgeRedirect();
  return (
    <EmptyPage
      heading="Configure sitemaps for your collections"
      content="Add or sync your collections first to configure sitemaps"
      secondaryAction={<CollectionSyncButton />}
      primaryAction={
        <Button
          variant="primary"
          onClick={handleAddCollections}
        >
          {t("Add collections")}
        </Button>
      }
      insideCard={false}
    />
  );
}

//@ts-check
import SitemapRow from "@/components/sitemap/SitemapRow";
import { IndexTable, useBreakpoints } from "@shopify/polaris";
import { memo } from "react";
import { useTranslation } from "react-i18next";

const resourceName = {
  singular: "sitemap",
  plural: "sitemaps",
};

const defaultRowItem = { id: null, title: null };

const SitemapTableSkeleton = memo(() => {
  const { t } = useTranslation();
  const { smDown: isSmallDevice } = useBreakpoints();

  const sitemapRows = new Array(10).fill(defaultRowItem);

  return (
    <IndexTable
      condensed={isSmallDevice}
      resourceName={resourceName}
      itemCount={sitemapRows.length}
      headings={[
        { title: "#" },
        { title: "" },
        { title: "" },
        { title: t("Noindex") },
        { title: t("Nofollow") },
        { title: t("Sitemap") },
      ]}
      selectable={false}
    >
      {sitemapRows.map((row, index) => {
        return (
          <SitemapRow
            key={index}
            sitemap={row}
            isFetching={true}
            sl={index + 1}
          />
        );
      })}
    </IndexTable>
  );
});

export default SitemapTableSkeleton;

import EmptyPage from "@/components/common/EmptyPage";
import ProductSyncButton from "@/components/products/ProductSyncButton";
import { useAppBridgeRedirect } from "@/hooks/useAppBridgeRedirect";
import { Button } from "@shopify/polaris";
import React from "react";
import { useTranslation } from "react-i18next";

export default function ProductSitemapsEmptyPage() {
  const { t } = useTranslation();
  const { handleAddProducts } = useAppBridgeRedirect();
  return (
    <EmptyPage
      heading="Configure sitemaps for your products"
      content="Add or sync your products first to configure sitemaps"
      secondaryAction={<ProductSyncButton />}
      primaryAction={
        <Button
          variant="primary"
          onClick={handleAddProducts}
        >
          {t("Add products")}
        </Button>
      }
      insideCard={false}
    />
  );
}

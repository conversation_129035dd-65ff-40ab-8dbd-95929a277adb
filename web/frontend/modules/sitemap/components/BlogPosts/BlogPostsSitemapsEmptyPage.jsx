import EmptyPage from "@/components/common/EmptyPage";
import SyncButton from "@/components/common/SyncButton";
import { useSyncBlogPosts } from "@/hooks/blogposts";
import { useAppBridgeRedirect } from "@/hooks/useAppBridgeRedirect";
import { But<PERSON> } from "@shopify/polaris";
import React from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";

export default function BlogPostsSitemapsEmptyPage() {
  const { t } = useTranslation();
  const { handleAddArticles } = useAppBridgeRedirect();
  const blogSync = useSelector((state) => state.blogSync);
  const { mutate: syncBlogs, isLoading: isStartingSync } = useSyncBlogPosts({});

  const actionMarkup = (
    <SyncButton
      title="blog posts"
      loading={isStartingSync}
      callback={syncBlogs}
      syncState={blogSync}
    />
  );
  return (
    <EmptyPage
      heading="Configure sitemaps for your blog posts"
      content="Add or sync your blog posts first to configure sitemaps"
      secondaryAction={actionMarkup}
      primaryAction={
        <Button
          variant="primary"
          onClick={handleAddArticles}
        >
          {t("Add blog posts")}
        </Button>
      }
      insideCard={false}
    />
  );
}

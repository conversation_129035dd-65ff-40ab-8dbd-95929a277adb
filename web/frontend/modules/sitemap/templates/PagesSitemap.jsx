import { BlockStack, IndexTable, useBreakpoints } from "@shopify/polaris";
import { isEmpty } from "lodash";
import React, { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSearchParams } from "react-router-dom";
import SitemapRow from "@/components/sitemap/SitemapRow.jsx";
import useConfirmation from "@/hooks/useConfirmation.jsx";
import { capitalizedString, generateTableSL, getQueryFromUrlSearchParam } from "@/utility/helpers";
import PageSitemapsEmptyPage from "../components/pages/PageSitemapsEmptyPage";
import { useSitemapLayoutContext } from "@/providers/SitemapsLayoutProvider";
import {
  useGetPagesSitemaps,
  useUpdatePageFollowStatus,
  useUpdatePageIndexStatus,
  useUpdatePageSitemapStatus,
} from "@/hooks/pages";
import useIndexTablePagination from "@/lib/hooks/useIndexTablePagination";
import useIndexTableSort from "@/lib/hooks/useIndexTableSort";

const resourceName = {
  singular: "sitemap",
  plural: "sitemaps",
};

const tableHeadings = [
  { title: "#", key: "sl" },
  { title: "Page", key: "title" },
  { title: "Noindex", key: "noIndex" },
  { title: "Nofollow", key: "noFollow" },
  { title: "Sitemap", key: "sitemap" },
];

export function PagesSitemap() {
  const { smDown: isSmallDevice } = useBreakpoints();
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const query = getQueryFromUrlSearchParam(searchParams);
  const { setIsLoading } = useSitemapLayoutContext();

  const defaultRowItem = { id: null, title: null };
  const [sitemapRows, setSitemapRows] = useState(new Array(10).fill(defaultRowItem));

  const { renderConfirmation, showConfirmation, hideConfirmation } = useConfirmation();

  const { data, isLoading, isFetching, isFetched } = useGetPagesSitemaps({ query });
  const { pagination, totalCount } = data || {};

  useEffect(() => {
    setIsLoading(isFetching);
  }, [isFetching]);

  useEffect(() => {
    if (!isEmpty(data)) {
      setSitemapRows(data.sitemaps);
    }
  }, [data]);

  const { handleSort, sortDir, sortIndex, defaultSortDir } = useIndexTableSort({ tableHeadings });

  const hasEmptyContent = isFetched && totalCount === 0;

  const [confirmMsg, setConfirmMsg] = useState("");
  const [confirmBtnText, setConfirmBtnText] = useState("");
  const [confirmBtnIsDestructive, setConfirmBtnIsDestructive] = useState(false);
  const togglerInitState = { id: null, op: null, isChecked: null };
  const [togglerState, setTogglerState] = useState(togglerInitState);

  const confirmChanges = (isChecked, id) => {
    const params = id.split("-");
    const operation = params[0];
    const productId = params[1];

    setConfirmMsg(
      `Are you sure about ${isChecked ? "enabling" : "disabling"} the '${capitalizedString(operation)}' attribute?`
    );
    setConfirmBtnText(isChecked ? "Enable" : "Disable");
    setConfirmBtnIsDestructive(operation === "sitemap" ? !isChecked : isChecked);

    setTogglerState({
      id: productId,
      op: operation,
      isChecked,
    });

    showConfirmation();
  };

  const handleMutationSuccess = (status) => {
    const updatedData = sitemapRows.map((sm) => {
      if (Number(sm.shopifyId) === Number(togglerState.id)) {
        return { ...sm, ...status };
      }
      return sm;
    });
    setSitemapRows(updatedData);
    hideConfirmation();
  };

  const { mutate: updateNoindex, isLoading: isNoindexLoading } = useUpdatePageIndexStatus({
    onSuccess: ({ noIndexStatus }) => {
      handleMutationSuccess({ noIndex: !!noIndexStatus });
    },
    onError: (err) => {
      console.error(err);
    },
  });

  const { mutate: updateNofollow, isLoading: isNofollowLoading } = useUpdatePageFollowStatus({
    onSuccess: ({ noFollowStatus }) => {
      handleMutationSuccess({ noFollow: !!noFollowStatus });
    },
    onError: (err) => {
      console.error(err);
    },
  });

  const { mutate: updateSitemap, isLoading: isSitemapLoading } = useUpdatePageSitemapStatus({
    onSuccess: ({ sitemapStatus }) => {
      handleMutationSuccess({ status: sitemapStatus });
    },
    onError: (err) => {
      console.error(err);
    },
  });

  const handleConfirmedChanges = useCallback(() => {
    switch (togglerState.op) {
      case "noindex":
        updateNoindex(togglerState.id);
        break;
      case "nofollow":
        updateNofollow(togglerState.id);
        break;
      case "sitemap":
        updateSitemap({
          id: togglerState.id,
          status: togglerState.isChecked,
        });
        break;
    }
  }, [togglerState]);

  const handleModalClose = () => {
    setTogglerState(togglerInitState);
  };
  const emptyCollections = new Array(20).fill(defaultRowItem);

  const { paginationConfigs } = useIndexTablePagination(pagination);

  return (
    <>
      {!hasEmptyContent ? (
        <BlockStack gap="400">
          <IndexTable
            condensed={isSmallDevice}
            resourceName={resourceName}
            itemCount={sitemapRows.length}
            headings={tableHeadings.map((heading) => ({
              title: t(heading.title),
            }))}
            selectable={false}
            sortable={[false, true, false, false, false]}
            onSort={handleSort}
            sortColumnIndex={sortIndex}
            sortDirection={sortDir}
            defaultSortDirection={defaultSortDir}
            pagination={paginationConfigs}
          >
            {isLoading &&
              emptyCollections.map((row, index) => {
                return (
                  <SitemapRow
                    key={index}
                    sitemap={row}
                    isFetching
                    showFeaturedImage={false}
                  />
                );
              })}
            {!isLoading &&
              sitemapRows?.map((row, index) => {
                const sl = generateTableSL(pagination, index);
                return (
                  <SitemapRow
                    key={index}
                    sitemap={row}
                    sl={sl}
                    confirm={confirmChanges}
                    showFeaturedImage={false}
                  />
                );
              })}
          </IndexTable>
        </BlockStack>
      ) : (
        <PageSitemapsEmptyPage />
      )}

      {renderConfirmation({
        content: confirmMsg,
        primaryActionText: confirmBtnText,
        primaryActionIsDestructive: confirmBtnIsDestructive,
        primaryAction: handleConfirmedChanges,
        onClose: handleModalClose,
        loading: isSitemapLoading || isNoindexLoading || isNofollowLoading,
      })}
    </>
  );
}

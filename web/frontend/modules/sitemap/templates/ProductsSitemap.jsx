import { IndexTable, useBreakpoints } from "@shopify/polaris";
import { isEmpty } from "lodash";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { useSearchParams } from "react-router-dom";
import SitemapRow from "@/components/sitemap/SitemapRow.jsx";
import { useAppQuery, useProductApi } from "@/hooks";
import useConfirmation from "@/hooks/useConfirmation.jsx";
import { capitalizedString, generateTableSL, getQueryFromUrlSearchParam } from "@/utility/helpers";
import queryKeys from "@/utility/queryKeys";
import { useSitemapLayoutContext } from "@/providers/SitemapsLayoutProvider";
import ProductSitemapsEmptyPage from "../components/products/ProductSitemapsEmptyPage";
import useIndexTablePagination from "@/lib/hooks/useIndexTablePagination";
import useIndexTableSort from "@/lib/hooks/useIndexTableSort";

const resourceName = {
  singular: "sitemap",
  plural: "sitemaps",
};
const tableHeadings = [
  { title: "#", key: "sl" },
  { title: "", key: "featureImage" },
  { title: "Product", key: "title" },
  { title: "Noindex", key: "noIndex" },
  { title: "Nofollow", key: "noFollow" },
  { title: "Sitemap", key: "sitemap" },
];

export function ProductsSitemap() {
  const { smDown: isSmallDevice } = useBreakpoints();
  const productApi = useProductApi();
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const query = getQueryFromUrlSearchParam(searchParams);
  const { setIsLoading } = useSitemapLayoutContext();

  const defaultRowItem = { id: null, title: null };
  const [sitemapRows, setSitemapRows] = useState(new Array(10).fill(defaultRowItem));

  const { renderConfirmation, showConfirmation, hideConfirmation } = useConfirmation();

  const { data, isLoading, isFetching, isFetched } = useAppQuery({
    queryKey: [queryKeys.SITEMAP_LIST, query],
    queryFn: () => productApi.getPaginatedSitemapList(query),
    reactQueryOptions: {
      staleTime: 0,
    },
  });
  const { pagination, totalCount } = data || {};

  useEffect(() => {
    setIsLoading(isFetching);
  }, [isFetching]);

  useEffect(() => {
    if (!isEmpty(data)) {
      setSitemapRows(data.sitemaps);
    }
  }, [data, isFetched]);

  const { handleSort, sortDir, sortIndex, defaultSortDir } = useIndexTableSort({ tableHeadings });

  const hasEmptyContent = isFetched && totalCount === 0;

  // const [showConfirmation, setShowConfirmation] = useState(false);
  const [confirmMsg, setConfirmMsg] = useState("");
  const [confirmBtnText, setConfirmBtnText] = useState("");
  const [confirmBtnIsDestructive, setConfirmBtnIsDestructive] = useState(false);
  const togglerInitState = { id: null, op: null, isChecked: null };
  const [togglerState, setTogglerState] = useState(togglerInitState);

  const confirmChanges = (isChecked, id) => {
    const params = id.split("-");
    const operation = params[0];
    const productId = params[1];

    setConfirmMsg(
      `Are you sure about ${isChecked ? "enabling" : "disabling"} the '${capitalizedString(operation)}' attribute?`
    );
    setConfirmBtnText(isChecked ? "Enable" : "Disable");
    setConfirmBtnIsDestructive(operation === "sitemap" ? !isChecked : isChecked);

    setTogglerState({
      id: productId,
      op: operation,
      isChecked,
    });

    showConfirmation();
  };

  const handleMutationSuccess = (status) => {
    const updatedData = sitemapRows.map((sm) => {
      if (Number(sm.shopifyId) === Number(togglerState.id)) {
        return { ...sm, ...status };
      }
      return sm;
    });
    setSitemapRows(updatedData);
    hideConfirmation();
  };

  let { mutate: updateNoindex, isLoading: isNoindexLoading } = useMutation({
    mutationFn: () => productApi.toggleNoIndexStatus(togglerState.id),
    onSuccess: ({ noIndexStatus }) => {
      handleMutationSuccess({ noIndex: noIndexStatus });
    },
    onError: (err) => {
      console.error(err);
    },
  });

  let { mutate: updateNofollow, isLoading: isNofollowLoading } = useMutation({
    mutationFn: () => productApi.toggleNoFollowStatus(togglerState.id),
    onSuccess: ({ noFollowStatus }) => {
      handleMutationSuccess({ noFollow: noFollowStatus });
    },
    onError: (err) => {
      console.error(err);
    },
  });

  let { mutate: updateSitemap, isLoading: isSitemapLoading } = useMutation({
    mutationFn: () => productApi.toggleSitemapStatus(togglerState.id, togglerState.isChecked),
    onSuccess: ({ sitemapStatus }) => {
      handleMutationSuccess({ status: sitemapStatus });
    },
    onError: (err) => {
      console.error(err);
    },
  });

  const handleConfirmedChanges = useCallback(() => {
    // setIsloading(true);
    switch (togglerState.op) {
      case "noindex":
        updateNoindex();
        break;
      case "nofollow":
        updateNofollow();
        break;
      case "sitemap":
        updateSitemap();
        break;
    }
  }, [togglerState]);

  const handleModalClose = () => {
    // setShowConfirmation(false);
    setTogglerState(togglerInitState);
  };

  // if (isFetching) return <DummyIndexPageSkeleton />;

  const emptyProducts = new Array(20).fill(defaultRowItem);

  const { paginationConfigs } = useIndexTablePagination(pagination);

  return (
    <>
      {!hasEmptyContent ? (
        <IndexTable
          condensed={isSmallDevice}
          resourceName={resourceName}
          itemCount={sitemapRows.length}
          headings={tableHeadings.map((heading) => ({
            title: t(heading.title),
          }))}
          selectable={false}
          sortable={[false, false, true, false, false, false]}
          onSort={handleSort}
          sortColumnIndex={sortIndex}
          sortDirection={sortDir}
          defaultSortDirection={defaultSortDir}
          pagination={paginationConfigs}
        >
          {isLoading &&
            emptyProducts.map((row, index) => {
              return (
                <SitemapRow
                  key={index}
                  sitemap={row}
                  isFetching
                />
              );
            })}
          {!isLoading &&
            sitemapRows?.map((row, index) => {
              const sl = generateTableSL(pagination, index);
              return (
                <SitemapRow
                  key={index}
                  sitemap={row}
                  sl={sl}
                  confirm={confirmChanges}
                />
              );
            })}
        </IndexTable>
      ) : (
        <ProductSitemapsEmptyPage />
      )}

      {renderConfirmation({
        content: confirmMsg,
        primaryActionText: confirmBtnText,
        primaryActionIsDestructive: confirmBtnIsDestructive,
        primaryAction: handleConfirmedChanges,
        onClose: handleModalClose,
        loading: isSitemapLoading || isNoindexLoading || isNofollowLoading,
      })}
    </>
  );
}

//@ts-check
import { BlockStack, Checkbox, InlineStack, Text } from "@shopify/polaris";
import { Controller, useFormState } from "react-hook-form";
import { useTranslation } from "react-i18next";
import ResourceType from "storeseo-enums/resourceType";
import { autoAiOptimizationSchema } from "storeseo-schema/settings/autoAiOptimization";
import FormSectionLayout from "./FormSectionLayout";

const aiTagGenerationAvailable = ["PRODUCT", "ARTICLE"];

/**
 *
 * @param {{control: import('react-hook-form').Control<import("yup").InferType<typeof autoAiOptimizationSchema>>, isDisable: boolean, resourceType: Partial<keyof typeof ResourceType>}} props
 * @returns
 */
export default function ContentOptimizationSettings(props) {
  const { control, isDisable, resourceType = ResourceType.PRODUCT } = props;
  const { errors, isValid } = useFormState({ control });
  const { t } = useTranslation();

  // @ts-ignore
  const metaOrTagError = errors?.metaOrTag?.message;

  return (
    <FormSectionLayout
      title="Content Optimization"
      subtitle="Select the content you want to optimize automatically with AI"
      hintText="Auto AI optimization will be done based on your provided focus keywords"
      isDisable={isDisable}
    >
      <BlockStack gap="100">
        <Text
          as="p"
          tone={isDisable ? "text-inverse-secondary" : "base"}
        >
          {t("Optimization options")}
        </Text>

        <InlineStack gap="400">
          <Controller
            control={control}
            name="meta"
            disabled={isDisable}
            render={({ field: { value, onChange, disabled }, fieldState: { error } }) => (
              <Checkbox
                label={t("Meta titles & descriptions")}
                checked={value}
                onChange={onChange}
                error={error?.message}
                disabled={disabled || resourceType === ResourceType.COLLECTION}
              />
            )}
          />

          <Controller
            control={control}
            name="tags"
            disabled={isDisable}
            render={({ field: { value, onChange, disabled }, fieldState: { error } }) =>
              aiTagGenerationAvailable.includes(resourceType) && (
                <Checkbox
                  label={t("Tags")}
                  checked={value}
                  onChange={onChange}
                  error={error?.message}
                  disabled={disabled}
                />
              )
            }
          />
        </InlineStack>
      </BlockStack>
      {!isValid && metaOrTagError && (
        <Text
          tone="critical"
          as="p"
        >
          {metaOrTagError}
        </Text>
      )}
    </FormSectionLayout>
  );
}

//@ts-check
import { useUnsavedChanges } from "@/hooks/useUnsavedChanges";
import { defaultValues } from "@/lib/form-default-value/autoAiOptimizationSettings";
import {
  useGetAutoAiOptimizationSettings,
  useUpdateAutoAiOptimizationSettings,
} from "@/lib/hooks/settings/auto-ai-optimization";
import useAutoAiOptimizationForm from "@/lib/hooks/settings/auto-ai-optimization/useAutoAiOptimizationForm";
import ContextualSaveBar from "@/modules/components/ContextualSaveBar";
import { BlockStack, Button, Card, InlineStack } from "@shopify/polaris";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import ResourceType from "storeseo-enums/resourceType";
import { autoAiOptimizationSchema } from "storeseo-schema/settings/autoAiOptimization";
import SettingsForm from "./SettingsForm";

/**
 * @param {{resourceType: keyof typeof ResourceType}} props
 */
export default function AutoAiOptimizationSettingsForm({ resourceType = "PRODUCT" }) {
  const { t } = useTranslation();
  const { data: autoAiOptimizationSettings, isLoading } = useGetAutoAiOptimizationSettings();
  const isEnabled = autoAiOptimizationSettings?.[resourceType]?.status ?? false;

  const { mutate, isLoading: isUpdating } = useUpdateAutoAiOptimizationSettings();

  const { control, watch, handleSubmit, reset } = useAutoAiOptimizationForm({ resourceType });
  /**
   *
   * @param {import("yup").InferType<typeof autoAiOptimizationSchema>} data
   */
  const onSubmit = (data) => {
    mutate({ ...autoAiOptimizationSettings, [resourceType]: { settings: data, status: isEnabled } });
  };

  const { hasUnsavedChanges, setHasUnsavedChanges, DiscardChangesModal, setShowDiscardChangeModal } = useUnsavedChanges(
    {
      originalData: !isEnabled ? defaultValues : autoAiOptimizationSettings?.[resourceType]?.settings || defaultValues,
      currentData: !isEnabled ? defaultValues : watch(),
      onDiscardAction: reset,
    }
  );

  useEffect(() => {
    setHasUnsavedChanges(false);
  }, [resourceType, setHasUnsavedChanges]);

  return (
    <>
      <DiscardChangesModal />

      <ContextualSaveBar
        id="auto-ai-optimization-settings"
        open={hasUnsavedChanges && isEnabled}
        isLoading={isLoading || isUpdating}
        onSave={handleSubmit(onSubmit)}
        onDiscard={() => setShowDiscardChangeModal(true)}
      />

      <BlockStack gap="400">
        <Card padding="0">
          <SettingsForm
            control={control}
            onSubmit={onSubmit}
            isEnabled={!!isEnabled}
            resourceType={resourceType}
          />
        </Card>

        <InlineStack align="end">
          <Button
            variant="primary"
            onClick={handleSubmit(onSubmit)}
            loading={isLoading || isUpdating}
            disabled={!isEnabled || !hasUnsavedChanges}
          >
            {t("Save")}
          </Button>
        </InlineStack>
      </BlockStack>
    </>
  );
}

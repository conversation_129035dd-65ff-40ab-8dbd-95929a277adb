//@ts-check

import { BlockStack, InlineStack, RadioButton, Text } from "@shopify/polaris";
import { Controller } from "react-hook-form";
import { useTranslation } from "react-i18next";
import ResourceType from "storeseo-enums/resourceType";
import { autoAiOptimizationSchema } from "storeseo-schema/settings/autoAiOptimization";
import FormSectionLayout from "./FormSectionLayout";

const imageTypeSelectionAvailable = ["PRODUCT"];

/**
 *
 * @param {{control: import('react-hook-form').Control<import("yup").InferType<typeof autoAiOptimizationSchema>>, isDisable: boolean, resourceType: Partial<keyof typeof ResourceType>}} props
 * @returns
 */
export default function ImageAltTextSettings(props) {
  const { control, isDisable, resourceType } = props;
  const { t } = useTranslation();
  const inputDisabled = isDisable || !imageTypeSelectionAvailable.includes(resourceType);

  return (
    <FormSectionLayout
      title="AI-Generated Image Alt Text"
      subtitle="Select the image type for which you want AI-generated image alt text"
      hintText="Image alt text will be generated automatically with AI based on your provided focus keywords"
      isDisable={isDisable}
    >
      <BlockStack gap="100">
        <Text
          as="p"
          tone={isDisable ? "text-inverse-secondary" : "base"}
        >
          {t("Image types")}
        </Text>

        <InlineStack gap="400">
          <Controller
            control={control}
            name="imageAltText"
            disabled={isDisable}
            render={({ field: { value, onChange, disabled, name } }) => (
              <RadioButton
                label={t("Featured image")}
                checked={value === "featuredImage"}
                name={name}
                id="featuredImage"
                onChange={(_, id) => onChange(id)}
                disabled={disabled || inputDisabled}
              />
            )}
          />
          {imageTypeSelectionAvailable.includes(resourceType) && (
            <Controller
              control={control}
              name="imageAltText"
              disabled={isDisable}
              render={({ field: { value, onChange, disabled, name } }) => (
                <RadioButton
                  label={t("Product images")}
                  checked={value === "allImages"}
                  name={name}
                  id="allImages"
                  onChange={(_, id) => onChange(id)}
                  disabled={disabled || inputDisabled}
                />
              )}
            />
          )}
        </InlineStack>
      </BlockStack>
    </FormSectionLayout>
  );
}

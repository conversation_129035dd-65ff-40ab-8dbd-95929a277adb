//@ts-check
import { Form } from "react-hook-form";
import ResourceType from "storeseo-enums/resourceType";
import { autoAiOptimizationSchema } from "storeseo-schema/settings/autoAiOptimization";
import ContentOptimizationSettings from "./ContentOptimizationSettings";
import ImageAltTextSettings from "./ImageAltTextSettings";

const aiAltTextGenerationAvailable = ["PRODUCT", "ARTICLE"];

/**
 * @typedef {import("yup").InferType<typeof autoAiOptimizationSchema>} settings
 * @typedef {settings} actionData
 * @param {{onSubmit : (data: actionData) => void, isEnabled?: boolean, control: import('react-hook-form').Control<import("yup").InferType<typeof autoAiOptimizationSchema>>, resourceType?: Partial<keyof typeof ResourceType>}} props
 * @returns
 */

export default function SettingsForm(props) {
  const { onSubmit, isEnabled = true, control, resourceType = ResourceType.PRODUCT } = props;

  return (
    <Form
      control={control}
      onSubmit={({ data }) => {
        onSubmit(data);
      }}
    >
      <ContentOptimizationSettings
        control={control}
        isDisable={!isEnabled}
        resourceType={resourceType}
      />
      {aiAltTextGenerationAvailable.includes(resourceType) && (
        <ImageAltTextSettings
          control={control}
          isDisable={!isEnabled}
          resourceType={resourceType}
        />
      )}
    </Form>
  );
}

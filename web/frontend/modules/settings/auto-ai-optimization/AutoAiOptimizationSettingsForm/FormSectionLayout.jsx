//@ts-check
import { BlockStack, Box, InlineStack, Icon, Text } from "@shopify/polaris";
import { InfoIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";

/**
 *
 * @param {{title: string, subtitle: string, hintText: string, isDisable?: boolean} & import("react").PropsWithChildren} props
 * @returns
 */
export default function FormSectionLayout(props) {
  const { title, subtitle, hintText, isDisable } = props;
  const { t } = useTranslation();

  return (
    <Box
      padding="400"
      borderBlockEndWidth="0165"
      borderColor="border"
    >
      <BlockStack gap="200">
        {/* title subtitle */}
        <BlockStack gap="100">
          <Text
            as="p"
            variant="headingMd"
            tone={isDisable ? "text-inverse-secondary" : "base"}
          >
            {t(title)}
          </Text>

          <Text
            as="p"
            tone={isDisable ? "text-inverse-secondary" : "subdued"}
          >
            {t(subtitle)}
          </Text>
        </BlockStack>

        {props.children}

        {/* Tips text */}
        <InlineStack
          gap="100"
          wrap={false}
        >
          <Box>
            <Icon
              source={InfoIcon}
              tone={isDisable ? "subdued" : "base"}
            />
          </Box>
          <Text
            as="span"
            tone={isDisable ? "text-inverse-secondary" : "subdued"}
          >
            {t(hintText)}
          </Text>
        </InlineStack>
      </BlockStack>
    </Box>
  );
}

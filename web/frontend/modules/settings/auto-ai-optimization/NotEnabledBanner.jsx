import { Banner, Text } from "@shopify/polaris";
import { LockIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";

const AutoAiOptimizationNotEnabledBanner = () => {
  const { t } = useTranslation();

  return (
    <Banner
      tone="warning"
      icon={LockIcon}
      title={t("Auto AI Optimization is not enabled")}
      action={{
        content: t("Choose your plan"),
        url: "/credit-bundles",
      }}
    >
      <Text
        as="p"
        tone="subdued"
      >
        {t("Please add AI Content Optimizer to your subscription plan to unlock this feature")}
      </Text>
    </Banner>
  );
};

export default AutoAiOptimizationNotEnabledBanner;

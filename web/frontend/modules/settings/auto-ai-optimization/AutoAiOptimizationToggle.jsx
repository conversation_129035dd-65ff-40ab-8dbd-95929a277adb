//@ts-check
import useUserAddon from "@/hooks/useUserAddon";
import { defaultValues } from "@/lib/form-default-value/autoAiOptimizationSettings";
import {
  useGetAutoAiOptimizationSettings,
  useUpdateAutoAiOptimizationSettings,
} from "@/lib/hooks/settings/auto-ai-optimization";
import { Badge, Banner, BlockStack, Box, Button, Card, InlineStack, Text, useBreakpoints } from "@shopify/polaris";
import { AlertCircleIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";
import ResourceType from "storeseo-enums/resourceType";

/**
 * @param {{resourceType: keyof typeof ResourceType}} props
 */
export default function AutoAiOptimizationToggle({ resourceType = "PRODUCT" }) {
  const { t } = useTranslation();
  const { data, isLoading } = useGetAutoAiOptimizationSettings();
  const { hasAiOptimizer } = useUserAddon();
  const { smDown: isSmallDevice } = useBreakpoints();
  const isEnabled = data?.[resourceType]?.status ?? false;

  const { mutate, isLoading: isUpdating } = useUpdateAutoAiOptimizationSettings();

  const toggleAutoAiOptimization = () => {
    const settings = data?.[resourceType];
    const newSettings = { ...(settings ? settings : { settings: defaultValues }), status: !isEnabled };
    mutate({
      ...data,
      [resourceType]: newSettings,
    });
  };

  return (
    <Card>
      <BlockStack gap="200">
        <BlockStack gap="100">
          <InlineStack align="space-between">
            <InlineStack
              align="start"
              blockAlign="center"
              gap="200"
            >
              <Text
                as="p"
                variant="headingMd"
              >
                {t("Auto AI Optimization")}
              </Text>
              <Badge tone={isEnabled ? "success" : "enabled"}>{isEnabled ? t("on") : t("off")}</Badge>
            </InlineStack>

            <Button
              onClick={toggleAutoAiOptimization}
              loading={isLoading || isUpdating}
              disabled={!hasAiOptimizer}
            >
              {isEnabled ? t(`Turn ${"off"}`) : t(`Turn ${"on"}`)}
            </Button>
          </InlineStack>

          <Box maxWidth={isSmallDevice ? "100%" : "80%"}>
            <Text as="p">
              {t("StoreSEO will automatically start optimizing with AI based on your selected settings")}
            </Text>
          </Box>
        </BlockStack>

        {isEnabled && (
          <Banner
            icon={AlertCircleIcon}
            tone="warning"
          >
            <Text as="p">
              {t("Enabling this feature will automatically use the available limit on your AI Content Optimizer plan")}
            </Text>
          </Banner>
        )}
      </BlockStack>
    </Card>
  );
}

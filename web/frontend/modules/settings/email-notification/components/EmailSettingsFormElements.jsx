//@ts-check
import { BlockStack } from "@shopify/polaris";
import { OPTIONS } from "storeseo-enums/settings/email-notification";
import EmailSettingsFormElementsItem from "./EmailSettingsFormElementsItem";

const initialSettingOptions = Object.keys(OPTIONS)
  .filter((key) => OPTIONS[key]?.configurable)
  .map((key) => ({
    key,
    label: OPTIONS[key].title,
    helpText: OPTIONS[key].description,
    configurable: OPTIONS[key]?.configurable,
  }));

/**
 *
 * @param {{control : any}} param0
 * @returns
 */
export default function EmailSettingsFormElements({ control }) {
  return (
    <BlockStack gap="400">
      {initialSettingOptions.map((option) => (
        <EmailSettingsFormElementsItem
          key={option.key}
          control={control}
          helpText={option.helpText}
          label={option.label}
          name={option.key}
        />
      ))}
    </BlockStack>
  );
}

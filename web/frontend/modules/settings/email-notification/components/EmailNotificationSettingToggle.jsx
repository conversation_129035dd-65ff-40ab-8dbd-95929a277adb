//@ts-check
import { Badge, BlockStack, Box, Button, Card, InlineStack, Text } from "@shopify/polaris";
import { useController } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { EMAIL_NOTIFICATION_REPORT } from "storeseo-enums/settings/email-notification";

/**
 *
 * @param {{control : any}} props
 * @returns
 */
export default function EmailNotificationSettingToggle(props) {
  const { control } = props;
  const { t } = useTranslation();
  const { field } = useController({
    name: EMAIL_NOTIFICATION_REPORT,
    control,
  });

  const isEnabled = field.value;

  return (
    <Card>
      <InlineStack
        align="space-between"
        blockAlign="center"
      >
        <BlockStack gap="100">
          <InlineStack
            align="start"
            blockAlign="center"
            gap="200"
          >
            <Text
              as="p"
              variant="headingMd"
            >
              {t("Email Notification")}
            </Text>
            <Badge tone={isEnabled ? "success" : "enabled"}>{isEnabled ? t("on") : t("off")}</Badge>
          </InlineStack>

          <Text as="p">{t("Get performance reports of your store delivered to your inbox")}</Text>
        </BlockStack>

        <Box>
          <Button onClick={() => field.onChange(!isEnabled)}>
            {isEnabled ? t(`Turn ${"off"}`) : t(`Turn ${"on"}`)}
          </Button>
        </Box>
      </InlineStack>
    </Card>
  );
}

//@ts-check
import { Badge, BlockStack, Box, Button, Card, Grid, Icon, InlineStack, Select, Text } from "@shopify/polaris";
import { ClockIcon } from "@shopify/polaris-icons";
import moment from "moment";
import { useCallback } from "react";
import { useController, useWatch } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { EMAIL_NOTIFICATION_REPORT } from "storeseo-enums/settings/email-notification";

const dayOptions = [
  { label: "Monday", value: "monday" },
  { label: "Tuesday", value: "tuesday" },
  { label: "Wednesday", value: "wednesday" },
  { label: "Thursday", value: "thursday" },
  { label: "Friday", value: "friday" },
  { label: "Saturday", value: "saturday" },
  { label: "Sunday", value: "sunday" },
];

const timeOptions = Array(24)
  .fill()
  .map((_, index) => ({
    label: moment({ hour: index, minute: 0 }).format("h:mm A"),
    value: moment({ hour: index, minute: 0 }).format("H"),
    prefix: <Icon source={ClockIcon} />,
  }));

/**
 *
 * @param {{name: string; label: string, helpText: string; control : any}} props
 * @returns
 */
export default function EmailSettingsFormElementsItem(props) {
  const { name, label, helpText, control } = props;
  const { t } = useTranslation();
  const { field: enabledField } = useController({
    name: `items.${name}.enabled`,
    control,
  });
  const { field: dayField } = useController({
    name: `items.${name}.day`,
    control,
  });
  const { field: timeField } = useController({
    name: `items.${name}.time`,
    control,
  });

  const isEmailNotificationEnable = useWatch({
    control,
    name: EMAIL_NOTIFICATION_REPORT,
  });

  const isEnabled = enabledField.value;

  const handleDayChange = useCallback((value) => {
    // setSelectedDay(value);
    dayField.onChange(value);
  }, []);
  const handleTimeChange = useCallback((value) => {
    // setSelectedTime(value);
    timeField.onChange(value);
  }, []);

  return (
    <Card>
      <BlockStack gap="400">
        <InlineStack
          align="space-between"
          blockAlign="center"
        >
          {/* Label and description */}
          <BlockStack gap="100">
            <InlineStack
              align="start"
              blockAlign="center"
              gap="200"
            >
              <Text
                as="p"
                variant="headingMd"
              >
                {t(label)}
              </Text>
              <Badge tone={isEnabled && isEmailNotificationEnable ? "success" : "enabled"}>
                {isEnabled && isEmailNotificationEnable ? t("Enabled") : t("Disabled")}
              </Badge>
            </InlineStack>

            <Text as="p">{t(helpText)}</Text>
          </BlockStack>
          {/* Toggle action */}
          <Box>
            <Button
              onClick={() => enabledField.onChange(!isEnabled)}
              disabled={!isEmailNotificationEnable}
            >
              {isEnabled && isEmailNotificationEnable ? t(`Disable`) : t(`Enable`)}
            </Button>
          </Box>
        </InlineStack>
        {/* Date and time picker */}
        <Box
          background="bg-surface-secondary"
          padding="300"
          borderRadius="200"
        >
          <Grid columns={{ xs: 6, sm: 6, md: 6, lg: 6, xl: 6 }}>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3, xl: 3 }}>
              <Box width="100%">
                <Select
                  label="Select Day"
                  options={dayOptions}
                  onChange={handleDayChange}
                  value={dayField.value}
                  disabled={!isEnabled || !isEmailNotificationEnable}
                  name={dayField.name}
                  onBlur={dayField.onBlur}
                />
              </Box>
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3, xl: 3 }}>
              <Box width="100%">
                <Select
                  label="Select Time"
                  options={timeOptions}
                  onChange={handleTimeChange}
                  value={timeField.value}
                  disabled={!isEnabled || !isEmailNotificationEnable}
                  name={timeField.name}
                  onBlur={timeField.onBlur}
                />
              </Box>
            </Grid.Cell>
          </Grid>
        </Box>
      </BlockStack>
    </Card>
  );
}

//@ts-check
import { defaultValues } from "@/lib/form-default-value/backupAndRestoreSettings";
import { useCreateBackup } from "@/lib/hooks/settings/backup-restore";
import useIsRunningBackupRestore from "@/lib/hooks/useIsRunningBackupRestore";
import Modal from "@/modules/components/Modal";
import { callAll } from "@/utility/helpers";
import queryKeys from "@/utility/queryKeys";
import { yupResolver } from "@hookform/resolvers/yup";
import { useAppBridge } from "@shopify/app-bridge-react";
import { Banner, BlockStack, Box, Checkbox, Form, InlineStack, Text } from "@shopify/polaris";
import { Controller, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";
import { backupAndRestoreSchema } from "storeseo-schema/settings/backupAndRestore";

const { BACKUP_LIST, BACKUP_RESTORE_STATUS } = queryKeys;

const MODAL_ID = "create-backup-modal";

export default function CreateBackupModal() {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { isRunning: isRunningBackupOrRestore } = useIsRunningBackupRestore();
  const shopify = useAppBridge();

  const { mutate, isLoading, isError, error, reset: resetMutation } = useCreateBackup();

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    reset,
  } = useForm({
    resolver: yupResolver(backupAndRestoreSchema),
    defaultValues,
  });

  /**
   *
   * @param {import("yup").InferType<typeof backupAndRestoreSchema>} data
   */
  const onSubmit = (data) => {
    mutate(data, {
      onSuccess: () => {
        shopify.modal.hide(MODAL_ID);
        queryClient.invalidateQueries(BACKUP_LIST);
        const backupRestoreStatus = {
          hasBackupInProgress: true,
          hasRestoreInProgress: false,
        };
        queryClient.setQueryData([BACKUP_RESTORE_STATUS], backupRestoreStatus);
      },
    });
  };

  // @ts-ignore
  const resourcesError = errors?.resourcesError?.message;
  // @ts-ignore
  const formErrorMessage = typeof error === "object" ? error?.message : "";

  return (
    <>
      <Modal
        type="app-bridge"
        id={MODAL_ID}
        onClose={callAll(() => reset(defaultValues), resetMutation)}
      >
        <Box>
          <Modal.ToggleButton
            variant="primary"
            disabled={isRunningBackupOrRestore}
          >
            {t("Create backup")}
          </Modal.ToggleButton>
        </Box>
        <Modal.Section>
          <Modal.TitleBar title={t("Create backup")}>
            <button
              variant="primary"
              onClick={handleSubmit(onSubmit)}
              loading={isLoading ? "" : undefined}
            >
              {t("Start backup")}
            </button>
          </Modal.TitleBar>
          <Box padding="400">
            {isError && (
              <Box paddingBlockEnd="200">
                <Banner
                  tone="warning"
                  title={t(formErrorMessage)}
                />
              </Box>
            )}
            <BlockStack gap="100">
              <Box paddingBlockEnd="300">
                <Banner tone="info">
                  <Text as="p">
                    {t(
                      "This backup covers the following: Meta Title, Meta Description, Focus Keyword, URL Handle, Tags and Image ALT Text"
                    )}
                  </Text>
                </Banner>
              </Box>
              <Text
                as="p"
                variant="bodyMd"
              >
                {t("Select where you want to do backup")}:{" "}
              </Text>
              <Form onSubmit={handleSubmit(onSubmit)}>
                <InlineStack gap="400">
                  <Controller
                    control={control}
                    name="COLLECTION"
                    render={({ field: { value, onChange, disabled }, fieldState: { error } }) => (
                      <Checkbox
                        label={t("Collections")}
                        checked={value}
                        onChange={onChange}
                        error={error?.message}
                        disabled={disabled}
                      />
                    )}
                  />

                  <Controller
                    control={control}
                    name="PRODUCT"
                    render={({ field: { value, onChange, disabled }, fieldState: { error } }) => (
                      <Checkbox
                        label={t("Products")}
                        checked={value}
                        onChange={onChange}
                        error={error?.message}
                        disabled={disabled}
                      />
                    )}
                  />

                  <Controller
                    control={control}
                    name="ARTICLE"
                    render={({ field: { value, onChange, disabled }, fieldState: { error } }) => (
                      <Checkbox
                        label={t("Blog posts")}
                        checked={value}
                        onChange={onChange}
                        error={error?.message}
                        disabled={disabled}
                      />
                    )}
                  />

                  {/* <Controller
                  control={control}
                  name="PAGE"
                  render={({ field: { value, onChange, disabled }, fieldState: { error } }) => (
                    <Checkbox
                      label={t("Pages")}
                      checked={value}
                      onChange={onChange}
                      error={error?.message}
                      disabled={disabled}
                    />
                  )}
                /> */}
                </InlineStack>
              </Form>
              {!isValid && resourcesError && (
                <Text
                  tone="critical"
                  as="p"
                  variant="bodyMd"
                >
                  {resourcesError}
                </Text>
              )}
            </BlockStack>
          </Box>
        </Modal.Section>
      </Modal>
    </>
  );
}

//@ts-check

import { useBackupList } from "@/lib/hooks/settings/backup-restore";
import { BlockStack, Card, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import BackupDataListItems from "./BackupDataListItems";

export default function BackupDataList() {
  const { t } = useTranslation();
  const { data } = useBackupList();
  const title = data && data.length > 0 ? "Data backups" : "Create a backup of your store’s SEO data";
  const description =
    data && data.length > 0
      ? "Here you can find all the backups of your store’s SEO data that you have created so far. Restore them later anytime you need."
      : "Start backing up your store’s important SEO data here, with all backups conveniently listed for easy access. Restore them later anytime you need.";

  return (
    <Card>
      <BlockStack gap="400">
        <BlockStack gap="100">
          <Text
            as="p"
            variant="headingSm"
            fontWeight="semibold"
          >
            {t(title)}
          </Text>

          <Text
            as="p"
            variant="bodyMd"
          >
            {t(description)}
          </Text>
        </BlockStack>

        <BackupDataListItems />
      </BlockStack>
    </Card>
  );
}

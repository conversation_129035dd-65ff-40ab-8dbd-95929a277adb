//@ts-check
import {
  Badge,
  Box,
  ButtonGroup,
  Grid,
  ResourceItem,
  Text,
  useBreakpoints,
  useFocusIn,
  useHover,
  useMediaQuery,
} from "@shopify/polaris";
import moment from "moment";
import { useRef } from "react";
import { useTranslation } from "react-i18next";
import { backupAndRestoreDatabaseSchema } from "storeseo-schema/settings/backupAndRestore";
import DeleteBackupAction from "./DeleteBackupAction";
import RestoreBackupAction from "./RestoreBackupAction";

/**
 *
 * @param {{item: import("yup").InferType<typeof backupAndRestoreDatabaseSchema>, isLast: boolean}} param0
 * @returns
 */
export default function BackupDataListItem({ item, isLast }) {
  const { id, title, resources, backup_status } = item;
  const { smDown: isSmallDevice } = useBreakpoints();
  const { t } = useTranslation();

  const ref = useRef(null);
  const isFocusedIn = useFocusIn(ref);
  const isHovered = useHover(ref);
  const isMouseDevice = useMediaQuery("mouse");
  const isMouseHovered = isMouseDevice ? isHovered : true;

  const description = resources.map((item) => item.toLowerCase()).join(", ");
  const lastUpdatedDate = moment(item.created_at).format("DD MMM YYYY");
  const status = backup_status === "COMPLETE" ? null : t("In progress");

  return (
    <div
      ref={ref}
      style={{
        borderBlockEnd: isLast ? "none" : "var(--p-border-width-025) solid var(--p-color-border)",
      }}
    >
      <ResourceItem
        id={String(id)}
        onClick={() => {}}
      >
        <Grid gap={{ xs: "10px" }}>
          {/* Details */}
          <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 5, xl: 5 }}>
            <Box>
              <Text
                variant="headingSm"
                fontWeight="semibold"
                as="h3"
              >
                {title}
              </Text>

              <Text
                as="p"
                variant="bodyMd"
              >
                {description}
              </Text>
              {status && (
                <Box paddingBlockStart="200">
                  <Badge tone="attention">{status}</Badge>
                </Box>
              )}
            </Box>
          </Grid.Cell>
          <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 7, xl: 7 }}>
            <div
              style={{
                display: "flex",
                height: "100%",
                flexDirection: isSmallDevice ? "column" : "row",
                alignItems: isSmallDevice ? "start" : "center",
                justifyContent: "space-between",
              }}
            >
              <Text
                as="p"
                variant="bodyMd"
              >
                {t("Last Updated")}: {lastUpdatedDate}
              </Text>
              {/* Action items */}
              <div
                style={{
                  opacity: isMouseHovered || isFocusedIn ? 1 : 0,
                  transition:
                    isMouseHovered || isFocusedIn
                      ? "var(--p-motion-duration-100) var(--p-motion-ease) opacity"
                      : "none",
                  alignSelf: isSmallDevice && "flex-end",
                }}
              >
                <ButtonGroup
                  noWrap
                  fullWidth
                >
                  <RestoreBackupAction id={id} />
                  <DeleteBackupAction id={id} />
                </ButtonGroup>
              </div>
            </div>
          </Grid.Cell>
        </Grid>
      </ResourceItem>
    </div>
  );
}

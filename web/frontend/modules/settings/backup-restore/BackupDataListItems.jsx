//@ts-check
import EmptyPage from "@/components/common/EmptyPage";
import { useBackupList } from "@/lib/hooks/settings/backup-restore";
import { Card, ResourceList } from "@shopify/polaris";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import BackupDataListItem from "./BackupDataListItem";
import CreateBackupModal from "./CreateBackupModal";

export default function BackupDataListItems() {
  const { t } = useTranslation();
  const [sortValue, setSortValue] = useState("DATE_MODIFIED_DESC");
  const queryParams = {
    sortBy: "created_at",
    sortOrder: sortValue === "DATE_MODIFIED_DESC" ? "DESC" : "ASC",
  };
  const { data, isFetching } = useBackupList(queryParams);

  const resourceName = {
    singular: t("backup"),
    plural: t("backups"),
  };

  return (
    <Card padding="0">
      <ResourceList
        resourceName={resourceName}
        items={data || []}
        renderItem={(item, _, index) => (
          <BackupDataListItem
            key={item.id}
            item={item}
            isLast={data.length - 1 === index}
          />
        )}
        sortValue={sortValue}
        sortOptions={[
          { label: t("Newest"), value: "DATE_MODIFIED_DESC" },
          { label: t("Oldest"), value: "DATE_MODIFIED_ASC" },
        ]}
        onSortChange={(selected) => {
          setSortValue(selected);
        }}
        loading={isFetching}
        emptyState={
          <EmptyPage
            heading="Create your first backup"
            content="Restore the SEO data anytime after creating the backup"
            withWrapper={false}
            primaryAction={<CreateBackupModal />}
          />
        }
      />
    </Card>
  );
}

//@ts-check
import { useRestoreBackup } from "@/lib/hooks/settings/backup-restore";
import useIsRunningBackupRestore from "@/lib/hooks/useIsRunningBackupRestore";
import Modal from "@/modules/components/Modal";
import queryKeys from "@/utility/queryKeys";
import { useAppBridge } from "@shopify/app-bridge-react";
import { Banner, BlockStack, Box, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";

const { BACKUP_LIST, BACKUP_RESTORE_STATUS } = queryKeys;

const MODAL_ID = "restore-backup-modal";

/**
 *
 * @param {{id: number}} props
 * @returns {import('react').ReactElement}
 */
export default function RestoreBackupAction(props) {
  const { id } = props;
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const { isRunning: isRunningBackupOrRestore } = useIsRunningBackupRestore();
  const shopify = useAppBridge();

  const { mutate, isLoading } = useRestoreBackup();

  const handleAction = () => {
    mutate(
      {
        id,
      },
      {
        onSuccess: () => {
          shopify.modal.hide(MODAL_ID);
          queryClient.invalidateQueries([BACKUP_LIST]);
          const backupRestoreStatus = {
            hasBackupInProgress: false,
            hasRestoreInProgress: true,
          };
          queryClient.setQueryData([BACKUP_RESTORE_STATUS], backupRestoreStatus);
        },
      }
    );
  };

  return (
    <Modal
      type="app-bridge"
      id={MODAL_ID}
    >
      <Modal.ToggleButton
        variant="tertiary"
        disabled={isRunningBackupOrRestore}
      >
        {t("Restore")}
      </Modal.ToggleButton>
      <Modal.Section>
        <Modal.TitleBar title={t("Restore Confirmation")}>
          <button
            variant="primary"
            onClick={handleAction}
            loading={isLoading ? "" : undefined}
          >
            {t("Restore")}
          </button>
        </Modal.TitleBar>
        <Box padding="400">
          <BlockStack gap="400">
            <Banner tone="info">
              <Text as="p">
                {t(
                  "Restoring will replace your current data with the backup data, including focus_keyword, handles, tags, metadata, image alt text, and image source URLs"
                )}
              </Text>
            </Banner>
            <Text
              as="p"
              variant="bodyMd"
            >
              {t("Are you sure you want to restore this backup?")}
            </Text>
          </BlockStack>
        </Box>
      </Modal.Section>
    </Modal>
  );
}

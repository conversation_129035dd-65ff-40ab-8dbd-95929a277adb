//@ts-check
import useIsRunningBackupRestore from "@/lib/hooks/useIsRunningBackupRestore";
import BackupRestoreRunningBanner from "@/modules/components/BackupRestoreRunningBanner";
import { BlockStack, Card, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import CreateBackupModal from "./CreateBackupModal";

export default function CreateBackupActionCard() {
  const { t } = useTranslation();
  const { isRunning: isRunningBackupOrRestore, backupRestoreStatus } = useIsRunningBackupRestore();

  const description =
    isRunningBackupOrRestore && backupRestoreStatus.hasRestoreInProgress
      ? "Protect your store’s important SEO data with regular backups. Prevent the loss of meta titles, meta descriptions and other crucial data from accidental changes or system errors."
      : "Start backing up your store’s important SEO data here, with all backups conveniently listed for easy access. Restore them later anytime you need.";

  return (
    <>
      {isRunningBackupOrRestore && backupRestoreStatus.hasRestoreInProgress && <BackupRestoreRunningBanner />}
      <Card>
        <BlockStack gap="400">
          <BlockStack gap="100">
            <Text
              as="p"
              variant="headingSm"
              fontWeight="semibold"
            >
              {t("Create a backup of your store’s SEO data")}
            </Text>

            <Text
              as="p"
              variant="bodyMd"
            >
              {t(description)}
            </Text>
          </BlockStack>
          <CreateBackupModal />
        </BlockStack>
      </Card>
    </>
  );
}

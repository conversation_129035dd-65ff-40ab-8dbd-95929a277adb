//@ts-check
import { useDeleteBackup } from "@/lib/hooks/settings/backup-restore";
import useIsRunningBackupRestore from "@/lib/hooks/useIsRunningBackupRestore";
import Modal from "@/modules/components/Modal";
import queryKeys from "@/utility/queryKeys";
import { useAppBridge } from "@shopify/app-bridge-react";
import { Box, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";

const { BACKUP_LIST } = queryKeys;

const MODAL_ID = "delete-backup-modal";

/**
 *
 * @param {{id: number}} props
 * @returns {React.ReactElement}
 */
export default function DeleteBackupAction(props) {
  const { id } = props;
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const { isRunning: isRunningBackupOrRestore } = useIsRunningBackupRestore();
  const shopify = useAppBridge();

  const { mutate, isLoading } = useDeleteBackup();

  const handleAction = () => {
    mutate(
      {
        id,
      },
      {
        onSuccess: () => {
          shopify.modal.hide(MODAL_ID);
          queryClient.invalidateQueries(BACKUP_LIST);
        },
      }
    );
  };

  return (
    <Modal
      type="app-bridge"
      id={MODAL_ID}
    >
      <Modal.ToggleButton
        variant="tertiary"
        tone="critical"
        disabled={isRunningBackupOrRestore}
      >
        {t("Delete")}
      </Modal.ToggleButton>
      <Modal.Section>
        <Modal.TitleBar title={t("Delete Confirmation")}>
          <button
            variant="primary"
            tone="critical"
            onClick={handleAction}
            loading={isLoading ? "" : undefined}
          >
            {t("Delete")}
          </button>
        </Modal.TitleBar>
        <Box padding="400">
          <Text
            as="p"
            variant="bodyMd"
          >
            {t("Are you sure you want to delete this backup?")}
          </Text>
        </Box>
      </Modal.Section>
    </Modal>
  );
}

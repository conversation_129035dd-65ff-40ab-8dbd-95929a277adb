//@ts-check
import { defaultValues } from "@/lib/form-default-value/llmsSettings";
import { useGenerateLlmsTxt, useLlmsSettings } from "@/lib/hooks/settings/llms-txt-generator";
import Modal from "@/modules/components/Modal";
import { yupResolver } from "@hookform/resolvers/yup";
import { useAppBridge } from "@shopify/app-bridge-react";
import { Banner, BlockStack, Box, Checkbox, InlineStack, RadioButton } from "@shopify/polaris";
import { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { llmsSettingsSchema } from "storeseo-schema/settings/llmsSettings";

const MODAL_ID = "generate-llms-txt-modal";

const scheduleOptions = [
  { label: "Daily", value: "daily" },
  { label: "Weekly", value: "weekly" },
  { label: "Monthly", value: "monthly" },
];

const resourceOptions = [
  { key: "products", label: "Products" },
  { key: "collections", label: "Collections" },
  { key: "pages", label: "Pages" },
  { key: "articles", label: "Articles" },
  // { key: "variants", label: "Variants" },
];

export default function GenerateLlmsTxtModal() {
  const { t } = useTranslation();
  const user = useSelector((state) => state.user);
  const { data: settingsData } = useLlmsSettings();
  const { mutateAsync: generateLlmsTxt, isLoading, isError, error, reset: resetMutation } = useGenerateLlmsTxt();
  const appBridge = useAppBridge();

  // Check if user has permission for LLMs.txt generation
  const hasLlmsTxtPermission = user.permission?.llms_txt_generation;

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    reset,
    watch,
  } = useForm({
    resolver: yupResolver(llmsSettingsSchema),
    defaultValues,
  });

  // Reset form with database values when settings data is loaded
  useEffect(() => {
    if (settingsData?.settings) {
      reset(settingsData.settings);
    }
  }, [settingsData?.settings, reset]);

  // Watch resources_enabled to check if at least one is selected
  const resourcesEnabled = watch("resources_enabled");
  const hasEnabledResource = Object.values(resourcesEnabled || {}).some((enabled) => enabled === true);

  const onSubmit = async (data) => {
    await generateLlmsTxt(data);
    appBridge.modal.hide(MODAL_ID);
  };

  const handleClose = () => {
    appBridge.modal.hide(MODAL_ID);
    reset(settingsData?.settings || defaultValues);
    resetMutation();
  };

  const formErrorMessage = error?.message || "Something went wrong. Please try again.";

  return (
    <Modal
      type="app-bridge"
      id={MODAL_ID}
      onClose={handleClose}
    >
      <Box>
        <Modal.ToggleButton
          variant="primary"
          disabled={isLoading || !hasLlmsTxtPermission}
        >
          {t("Generate")}
        </Modal.ToggleButton>
      </Box>
      <Modal.Section>
        <Modal.TitleBar title={t("Generate LLMs.txt File")}>
          <button onClick={handleClose}>{t("Cancel")}</button>
          <button
            variant="primary"
            onClick={handleSubmit(onSubmit)}
            loading={isLoading ? "" : undefined}
            disabled={!isValid || !hasEnabledResource || !hasLlmsTxtPermission}
          >
            {t("Start generating")}
          </button>
        </Modal.TitleBar>
        <Box padding="400">
          {isError && (
            <Box paddingBlockEnd="200">
              <Banner
                tone="warning"
                title={t(formErrorMessage)}
              />
            </Box>
          )}

          <BlockStack gap="400">
            <BlockStack gap="200">
              <Box>
                <strong>{t("Resource Types")}</strong>
              </Box>
              <BlockStack gap="100">
                {resourceOptions.map((resource) => (
                  <Controller
                    key={resource.key}
                    name={`resources_enabled.${resource.key}`}
                    control={control}
                    render={({ field }) => (
                      <Checkbox
                        label={t(resource.label)}
                        checked={field.value || false}
                        onChange={field.onChange}
                      />
                    )}
                  />
                ))}
              </BlockStack>
              {!hasEnabledResource && (
                <Box paddingBlockStart="100">
                  <Banner
                    tone="warning"
                    title={t("Please select at least one resource type")}
                  />
                </Box>
              )}
            </BlockStack>

            <Controller
              name="schedule"
              control={control}
              render={({ field }) => (
                <BlockStack gap="200">
                  <Box>
                    <strong>{t("Generation Schedule")}</strong>
                  </Box>
                  <InlineStack gap="400">
                    {scheduleOptions.map((option) => (
                      <RadioButton
                        key={option.value}
                        label={t(option.label)}
                        checked={field.value === option.value}
                        id={`schedule-${option.value}`}
                        name="schedule"
                        onChange={() => field.onChange(option.value)}
                      />
                    ))}
                  </InlineStack>
                </BlockStack>
              )}
            />
          </BlockStack>
        </Box>
      </Modal.Section>
    </Modal>
  );
}

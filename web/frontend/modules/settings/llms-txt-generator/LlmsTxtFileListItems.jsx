//@ts-check

import EmptyPage from "@/components/common/EmptyPage";
import { BlockStack, Card, ResourceList, SkeletonBodyText } from "@shopify/polaris";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useSearchParams } from "react-router-dom";
import GenerateLlmsTxtModal from "./GenerateLlmsTxtModal";
import LlmsTxtFileListItem from "./LlmsTxtFileListItem";

export default function LlmsTxtFileListItems({ data = [], isLoading, pagination, isFetching }) {
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();
  const [currentPage, setCurrentPage] = useState(1);
  const [sortValue, setSortValue] = useState("generated_at:desc");

  if (isLoading) {
    return (
      <BlockStack gap="200">
        <SkeletonBodyText lines={3} />
      </BlockStack>
    );
  }

  const hasNextPage = pagination && currentPage < pagination.totalPages;
  const hasPreviousPage = pagination && currentPage > 1;
  const hasPagination = pagination && pagination.totalPages > 1;

  const onNext = () => {
    setCurrentPage(currentPage + 1);
    // Update query params with new page
    setSearchParams({
      ...Object.fromEntries(searchParams.entries()),
      page: String(currentPage + 1),
      limit: String(pagination.limit || 5),
    });
  };

  const onPrevious = () => {
    setCurrentPage(currentPage - 1);
    // Update query params with new page
    setSearchParams({
      ...Object.fromEntries(searchParams.entries()),
      page: String(currentPage - 1),
      limit: String(pagination.limit || 5),
    });
  };

  const resourceName = {
    singular: t("file"),
    plural: t("files"),
  };

  /**
   * Handles sorting of the file list
   * @param {string} selected - The selected sort option in format "field:direction"
   * @returns {void}
   */
  const onSort = (selected) => {
    setSortValue(selected);
    const [sortBy, sortOrder] = selected.split(":");
    setSearchParams({
      ...Object.fromEntries(searchParams.entries()),
      sortBy,
      sortOrder,
      page: "1", // Reset to first page when sorting
    });
  };

  return (
    <Card padding="0">
      <ResourceList
        resourceName={resourceName}
        items={data || []}
        totalItemsCount={pagination?.totalItems}
        renderItem={(item, _, index) => (
          <LlmsTxtFileListItem
            key={item.id}
            item={item}
            isLast={data.length - 1 === index}
          />
        )}
        sortValue={sortValue}
        sortOptions={[
          { label: t("Newest"), value: "generated_at:desc" },
          { label: t("Oldest"), value: "generated_at:asc" },
        ]}
        onSortChange={onSort}
        loading={isFetching}
        emptyState={
          <EmptyPage
            heading="Generate your LLMs.txt"
            content="Make your AI indexing easier with the LLMs.txt file in one click."
            withWrapper={false}
            primaryAction={<GenerateLlmsTxtModal />}
          />
        }
        pagination={
          hasPagination && {
            hasPrevious: hasPreviousPage,
            onPrevious,
            hasNext: hasNextPage,
            onNext,
          }
        }
      />
    </Card>
  );
}

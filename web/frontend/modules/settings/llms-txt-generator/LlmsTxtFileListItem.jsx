//@ts-check
import useConfirmation from "@/hooks/useConfirmation";
import { useDeleteLlmsFile } from "@/lib/hooks/settings/llms-txt-generator";
import { formatDate, formatFileSize } from "@/utility/helpers";
import {
  Badge,
  Box,
  Button,
  ButtonGroup,
  Grid,
  ResourceItem,
  Text,
  useBreakpoints,
  useFocusIn,
  useHover,
  useMediaQuery,
} from "@shopify/polaris";
import { DeleteIcon, ViewIcon } from "@shopify/polaris-icons";
import moment from "moment";
import { useRef } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";

/**
 * Renders a list item for an LLMs.txt file
 * @param {Object} props - Component props
 * @param {Object} props.item - The LLMs.txt file data
 * @param {string} props.item.id - File ID
 * @param {string} props.item.generated_at - Generation timestamp (converted to numeric version for proxy URL)
 * @param {number} props.item.file_size - File size in bytes
 * @param {string} props.item.generated_by - Generation method ('manual' or 'scheduled')
 * @param {string} [props.item.notes] - Optional notes about the file
 * @param {string} props.item.created_at - Creation timestamp
 * @param {boolean} props.isLast - Whether this is the last item in the list
 * @returns {React.ReactNode} The rendered list item
 */
export default function LlmsTxtFileListItem({ item, isLast }) {
  const { id, generated_at, file_size, generated_by, notes } = item;
  const { smDown: isSmallDevice } = useBreakpoints();
  const { t } = useTranslation();
  // @ts-ignore
  const user = useSelector((state) => state.user);

  // Delete functionality
  const { mutate: deleteFile, isLoading: isDeleting } = useDeleteLlmsFile();
  const { renderConfirmation, showConfirmation, hideConfirmation } = useConfirmation();

  const ref = useRef(null);
  const isFocusedIn = useFocusIn(ref);
  const isHovered = useHover(ref);
  const isMouseDevice = useMediaQuery("mouse");
  const isMouseHovered = isMouseDevice ? isHovered : true;

  const lastUpdatedDate = moment(item.created_at).format("DD MMM YYYY");

  const openFile = () => {
    // Construct proxy URL with numeric version parameter
    if (!user?.url) {
      console.error("Shop URL not available");
      return;
    }

    // Convert generated_at timestamp to numeric value (milliseconds since epoch) for version parameter
    const versionNumber = new Date(generated_at).getTime();
    const proxyUrl = `${user.url}/llms.txt?v=${versionNumber}`;
    window.open(proxyUrl, "_blank");
  };

  const handleDeleteClick = () => {
    showConfirmation();
  };

  const handleDeleteConfirm = () => {
    deleteFile(parseInt(id), {
      onSuccess: () => {
        hideConfirmation();
      },
    });
  };

  return (
    <>
      <div
        ref={ref}
        style={{
          borderBlockEnd: isLast ? "none" : "var(--p-border-width-025) solid var(--p-color-border)",
        }}
      >
        <ResourceItem
          id={String(id)}
          onClick={() => {}}
        >
          <Grid gap={{ xs: "10px" }}>
            {/* Details */}
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 7, xl: 7 }}>
              <Box>
                <Text
                  as="p"
                  variant="bodyMd"
                  fontWeight="semibold"
                >
                  {t("Generated on")} {formatDate(generated_at)}
                </Text>
                <Text
                  as="span"
                  variant="bodySm"
                  tone="subdued"
                >
                  {t("File size: {{FILE_SIZE}}", { FILE_SIZE: formatFileSize(file_size) })}
                </Text>

                {notes && (
                  <Text
                    as="p"
                    variant="bodySm"
                    tone="subdued"
                  >
                    {notes}
                  </Text>
                )}
              </Box>
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 1, sm: 1, md: 1, lg: 4, xl: 4 }}>
              <div
                style={{
                  display: "flex",
                  height: "100%",
                  flexDirection: !isSmallDevice ? "column" : "row",
                  alignItems: "start",
                  justifyContent: "center",
                  gap: "8px",
                }}
              >
                <Badge tone={generated_by === "manual" ? "info" : "success"}>
                  {generated_by === "manual" ? t("Manual") : t("Scheduled")}
                </Badge>
                <Text
                  as="p"
                  variant="bodySm"
                >
                  {t("Last Updated")}: {lastUpdatedDate}
                </Text>
              </div>
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 1, sm: 1, md: 1 }}>
              <div
                style={{
                  display: "flex",
                  height: "100%",
                  flexDirection: isSmallDevice ? "column" : "row",
                  alignItems: isSmallDevice ? "start" : "center",
                  justifyContent: "end",
                }}
              >
                {/* Action items */}
                <div
                  style={{
                    opacity: isMouseHovered || isFocusedIn ? 1 : 0,
                    transition:
                      isMouseHovered || isFocusedIn
                        ? "var(--p-motion-duration-100) var(--p-motion-ease) opacity"
                        : "none",
                    alignSelf: isSmallDevice && "flex-end",
                  }}
                >
                  <ButtonGroup
                    noWrap
                    fullWidth
                  >
                    <Button
                      icon={ViewIcon}
                      onClick={openFile}
                      variant="tertiary"
                      accessibilityLabel={t("View LLMs.txt file")}
                    />
                    <Button
                      icon={DeleteIcon}
                      onClick={handleDeleteClick}
                      variant="tertiary"
                      tone="critical"
                      loading={isDeleting}
                      accessibilityLabel={t("Delete LLMs.txt file")}
                    />
                  </ButtonGroup>
                </div>
              </div>
            </Grid.Cell>
          </Grid>
        </ResourceItem>
      </div>

      {renderConfirmation({
        title: "Delete LLMs.txt File",
        content: "Are you sure you want to delete this LLMs.txt file? This action cannot be undone.",
        primaryActionText: "Delete",
        primaryAction: handleDeleteConfirm,
        primaryActionIsDestructive: true,
        loading: isDeleting,
        onClose: hideConfirmation,
      })}
    </>
  );
}

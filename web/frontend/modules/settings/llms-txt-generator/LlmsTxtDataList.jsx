//@ts-check

import CustomNote from "@/components/common/CustomNote";
import { BlockStack, Card, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import LlmsTxtFileListItems from "./LlmsTxtFileListItems";

export default function LlmsTxtDataList({ data = [], isLoading, pagination, isFetching }) {
  const { t } = useTranslation();
  const title = data && data.length > 0 ? "LLMs.txt List" : "LLMs.txt Generator";
  const description =
    data && data.length > 0
      ? "Here's all your LLMs.txt files in one place. Restore any version with a single click, and keep your AI indexing always up to date."
      : "Generate LLMs.txt for AI indexing, making it easier to discover and use LLM resources effectively. It serves as a structured guide for AI systems to categorize LLMs quickly and accurately.";

  return (
    <Card>
      <BlockStack gap="400">
        <BlockStack gap="100">
          <Text
            as="p"
            variant="headingSm"
            fontWeight="semibold"
          >
            {t(title)}
          </Text>

          <Text
            as="p"
            variant="bodyMd"
          >
            {t(description)}
          </Text>
        </BlockStack>

        <LlmsTxtFileListItems
          data={data}
          isLoading={isLoading}
          pagination={pagination}
          isFetching={isFetching}
        />

        <CustomNote
          content={t("We will keep latest {{KEEP_COUNT}} files and automatically delete the older ones.", {
            KEEP_COUNT: 10,
          })}
        />
      </BlockStack>
    </Card>
  );
}

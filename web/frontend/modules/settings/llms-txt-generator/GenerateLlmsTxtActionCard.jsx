//@ts-check
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, InlineStack, Text } from "@shopify/polaris";
import { ViewIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import GenerateLlmsTxtModal from "./GenerateLlmsTxtModal";

export default function GenerateLlmsTxtActionCard({ hasLlmsTxtFile = false }) {
  const { t } = useTranslation();
  // @ts-ignore
  const user = useSelector((state) => state.user);

  const llmsTxtFileUrl = `${user.url}/llms${
    user.proxyPath !== "/a/sitemap" ? `-${user.proxyPath.split("-").pop()}` : ""
  }.txt`;

  return (
    <Card>
      <BlockStack gap="400">
        <BlockStack gap="100">
          <Text
            as="p"
            variant="headingSm"
            fontWeight="semibold"
          >
            {t("LLMs.txt Generator")}
          </Text>
          <Text
            as="p"
            variant="bodyMd"
          >
            {t(
              "Generate LLMs.txt for AI indexing, making it easier to discover and use LLM resources effectively. It serves as a structured guide for AI systems to categorize LLMs quickly and accurately."
            )}
          </Text>
        </BlockStack>

        <InlineStack gap="200">
          <GenerateLlmsTxtModal />
          {hasLlmsTxtFile && (
            <Button
              icon={ViewIcon}
              url={llmsTxtFileUrl}
              target="_blank"
            >
              {t("View LLMs.txt")}
            </Button>
          )}
        </InlineStack>
      </BlockStack>
    </Card>
  );
}

// @ts-nocheck
import TooltipWrapper from "@/components/common/TooltipWrapper";
import { Badge, Icon } from "@shopify/polaris";
import { MagicIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";
import ProductsAiOptimizationStatus from "storeseo-enums/aiOptimization";

/**
 * @param {{status: string, noBadge?:boolean}} props
 * @returns {React.ReactElement}
 */
export default function AIOptimizationStatusBadge(props) {
  const { status, noBadge = false } = props;
  const { t } = useTranslation();

  const isPending = [
    ProductsAiOptimizationStatus.PENDING,
    ProductsAiOptimizationStatus.DISPATCHED,
    ProductsAiOptimizationStatus.PROCESSING,
    ProductsAiOptimizationStatus.SAVING,
  ].includes(status);

  const isOptimized = status === ProductsAiOptimizationStatus.OPTIMIZED;
  const isNotOptimized = status === ProductsAiOptimizationStatus.NOT_OPTIMIZED;

  return !isNotOptimized ? (
    noBadge ? (
      <TooltipWrapper
        content={t(isPending ? "Pending AI optimization" : isOptimized ? "AI Optimized" : null)}
        show={isPending || isOptimized}
      >
        <Icon
          source={MagicIcon}
          tone={isOptimized ? "magic" : "subdued"}
        />
      </TooltipWrapper>
    ) : (
      <Badge tone={isPending ? "enabled" : "magic"}>{t(isPending ? "Pending AI optimization" : "AI Optimized")}</Badge>
    )
  ) : null;
}

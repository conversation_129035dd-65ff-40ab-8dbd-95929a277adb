//@ts-check
import { useBanner } from "@/hooks/useBanner";
import useAutoAiOptimizationForm from "@/lib/hooks/settings/auto-ai-optimization/useAutoAiOptimizationForm";
import Modal from "@/modules/components/Modal";
import SettingsForm from "@/modules/settings/auto-ai-optimization/AutoAiOptimizationSettingsForm/SettingsForm";
import queryKeys from "@/utility/queryKeys";
import { Box } from "@shopify/polaris";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useIsMutating } from "react-query";
import analysisEntityTypes from "storeseo-enums/analysisEntityTypes";
import ResourceType from "storeseo-enums/resourceType";
import toastMessages from "storeseo-enums/toastMessages";
import { autoAiOptimizationSchema } from "storeseo-schema/settings/autoAiOptimization";
import BackupRestoreInfoBanner from "../components/BackupRestoreInfoBanner";

const backupRestoreFeatureAvailable = ["PRODUCT", "COLLECTION", "ARTICLE"];

/**
 * @typedef {keyof Pick<typeof ResourceType, "PRODUCT" | "COLLECTION" | "PAGE" | "ARTICLE">} AiResourceType
 * @typedef {import("yup").InferType<typeof autoAiOptimizationSchema>} settings
 * @typedef {settings} actionData
 * @param {{isOpen: boolean, setIsOpen: (value: boolean) => void, resourceType?: AiResourceType, onAction: (value: actionData) => void, isError?: boolean }} props
 * @returns
 */
export default function BulkResourceAiOptimizationModal(props) {
  const { isOpen, setIsOpen, resourceType = analysisEntityTypes.PRODUCT, onAction, isError = false } = props;

  const { t } = useTranslation();
  const { control, handleSubmit, reset } = useAutoAiOptimizationForm({
    defaultSelected: true,
    resourceType,
  });

  const { Banner, showBanner } = useBanner({
    message: t(toastMessages.SOMETHING_WENT_WRONG),
    tone: "warning",
    hideIcon: true,
    noMargin: true,
  });

  const bulkAIContentGenerationCount = useIsMutating({
    mutationKey: [queryKeys.BULK_AI_CONTENT_GENERATING, resourceType],
  });
  const isLoading = bulkAIContentGenerationCount > 0;

  /**
   * @param {settings} settings
   */
  const onSubmit = (settings) => {
    onAction(settings);
  };

  // Reset form when modal is closed
  useEffect(() => {
    const resetForm = () => {
      if (!isOpen) {
        reset();
      }
    };
    const timeout = setTimeout(resetForm, 500);

    return () => clearTimeout(timeout);
  }, [isOpen]);

  // Show error banner
  useEffect(() => {
    showBanner(isError);
  }, [isError]);

  return (
    <Modal
      type="app-bridge"
      open={isOpen}
      setOpen={setIsOpen}
    >
      <Modal.Section>
        <Modal.TitleBar
          title={t(`Optimize {{resourceType}} content's using AI`, { resourceType: resourceType.toLowerCase() })}
        >
          <button
            variant="primary"
            onClick={handleSubmit(onSubmit)}
            loading={isLoading ? "" : undefined}
          >
            {t("Optimize")}
          </button>
        </Modal.TitleBar>

        {backupRestoreFeatureAvailable.includes(resourceType) && (
          <Box
            paddingInline="400"
            paddingBlockStart="400"
          >
            <BackupRestoreInfoBanner />
          </Box>
        )}
        {/* Error banner */}
        {isError && (
          <Box
            paddingInline="400"
            paddingBlockStart="400"
          >
            <Banner />
          </Box>
        )}

        <SettingsForm
          control={control}
          onSubmit={onSubmit}
          resourceType={resourceType}
        />
      </Modal.Section>
    </Modal>
  );
}

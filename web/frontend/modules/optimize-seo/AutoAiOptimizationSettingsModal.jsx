//@ts-check
import { useUnsavedChanges } from "@/hooks/useUnsavedChanges";
import useUserAddon from "@/hooks/useUserAddon";
import { defaultValues } from "@/lib/form-default-value/autoAiOptimizationSettings";
import {
  useGetAutoAiOptimizationSettings,
  useUpdateAutoAiOptimizationSettings,
} from "@/lib/hooks/settings/auto-ai-optimization";
import useAutoAiOptimizationForm from "@/lib/hooks/settings/auto-ai-optimization/useAutoAiOptimizationForm";
import Modal from "@/modules/components/Modal";
import SettingsForm from "@/modules/settings/auto-ai-optimization/AutoAiOptimizationSettingsForm/SettingsForm";
import { BlockStack, Box, Card } from "@shopify/polaris";
import { capitalize } from "lodash";
import React, { memo, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import ResourceType from "storeseo-enums/resourceType";
import { autoAiOptimizationSchema } from "storeseo-schema/settings/autoAiOptimization";
import AutoAiOptimizationToggle from "../settings/auto-ai-optimization/AutoAiOptimizationToggle";
import AutoAiOptimizationNotEnabledBanner from "../settings/auto-ai-optimization/NotEnabledBanner";

/**
 * @param {{resourceType: keyof typeof ResourceType, isOpen: boolean, setIsOpen: import("react").Dispatch<import("react").SetStateAction<boolean>>}} props
 * @returns {React.ReactElement}
 */
const AutoAiOptimizationSettingsModal = (props) => {
  const { resourceType, isOpen, setIsOpen } = props;
  const { t } = useTranslation();
  const { hasAiOptimizer } = useUserAddon();
  const { data: autoAiOptimizationSettings, isLoading } = useGetAutoAiOptimizationSettings();
  const isEnabled = autoAiOptimizationSettings?.[resourceType]?.status ?? false;

  const { mutateAsync, isLoading: isUpdating } = useUpdateAutoAiOptimizationSettings();

  const {
    control,
    watch,
    handleSubmit,
    reset,
    formState: { isValid },
  } = useAutoAiOptimizationForm({ resourceType });
  /**
   *
   * @param {import("yup").InferType<typeof autoAiOptimizationSchema>} data
   */
  const onSubmit = async (data) => {
    await mutateAsync({ ...autoAiOptimizationSettings, [resourceType]: { settings: data, status: isEnabled } });
  };

  const handlePrimaryAction = async () => {
    await handleSubmit(onSubmit)();
    if (isValid && !isUpdating) {
      setIsOpen(false);
    }
  };

  const { hasUnsavedChanges, setHasUnsavedChanges } = useUnsavedChanges({
    originalData: !isEnabled ? defaultValues : autoAiOptimizationSettings?.[resourceType]?.settings || defaultValues,
    currentData: !isEnabled ? defaultValues : watch(),
  });

  useEffect(() => {
    setHasUnsavedChanges(false);
  }, [resourceType]);

  useEffect(() => {
    if (!isOpen) {
      reset();
    }
  }, [isOpen]);

  const modalTitle = useMemo(() => {
    const resourceName = resourceType === "ARTICLE" ? "Blog Post" : resourceType;
    return t(`${capitalize(resourceName)} AI Optimization Setting`);
  }, [resourceType]);

  return (
    <Modal
      type="app-bridge"
      open={isOpen}
      setOpen={setIsOpen}
    >
      <Modal.Section>
        <Modal.TitleBar title={modalTitle}>
          <button
            variant="primary"
            onClick={handlePrimaryAction}
            loading={isLoading || isUpdating ? "" : undefined}
            disabled={!isEnabled || !hasUnsavedChanges}
          >
            {t("Save")}
          </button>
        </Modal.TitleBar>
        <Box padding="400">
          <BlockStack gap="400">
            {!hasAiOptimizer && <AutoAiOptimizationNotEnabledBanner />}

            <AutoAiOptimizationToggle resourceType={resourceType} />

            <Card padding="0">
              <SettingsForm
                control={control}
                onSubmit={onSubmit}
                isEnabled={!!isEnabled}
                resourceType={resourceType}
              />
            </Card>
          </BlockStack>
        </Box>
      </Modal.Section>
    </Modal>
  );
};

export default memo(AutoAiOptimizationSettingsModal);

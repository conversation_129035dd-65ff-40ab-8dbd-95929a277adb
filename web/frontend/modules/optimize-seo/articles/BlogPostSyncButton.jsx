//@ts-check
import SyncButton from "@/components/common/SyncButton";
import { useSyncBlogPosts } from "@/hooks/blogposts";
import { memo } from "react";
import { useSelector } from "react-redux";

const BlogPostSyncButton = memo(() => {
  // @ts-ignore
  const blogSync = useSelector((state) => state.blogSync);

  const { mutate: syncBlogs, isLoading: isStartingSync } = useSyncBlogPosts();
  return (
    <SyncButton
      title="blog posts"
      loading={isStartingSync}
      callback={syncBlogs}
      syncState={blogSync}
    />
  );
});

export default BlogPostSyncButton;

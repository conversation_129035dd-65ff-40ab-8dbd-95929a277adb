import useDocsIndexFilters from "@/lib/hooks/optimize-seo/useDocsIndexFilters";
import { indexTableTabs } from "@/lib/util/optimize-seo";
import { IndexTableFilters } from "@/modules/components";
import { memo } from "react";

/**
 *
 * @param {{isLoading: boolean}} props
 * @returns {JSX.Element}
 */
const DocsIndexTableFilters = memo(({ isLoading = false }) => {
  const { filters, appliedFilters, setSelectedFilters, filterQuery } = useDocsIndexFilters();

  return (
    <IndexTableFilters
      filters={filters}
      appliedFilters={appliedFilters}
      setSelectedFilters={setSelectedFilters}
      filterQuery={filterQuery}
      loading={isLoading}
      tabs={indexTableTabs}
    />
  );
});

export default DocsIndexTableFilters;

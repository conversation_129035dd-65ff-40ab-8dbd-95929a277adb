import useProductsIndexFilters from "@/lib/hooks/optimize-seo/useProductsIndexFilters";
import { indexTableTabs } from "@/lib/util/optimize-seo";
import { IndexTableFilters } from "@/modules/components";
import { memo } from "react";

/**
 *
 * @param {{isLoading : boolean}} props
 * @returns {JSX.Element}
 */
const ProductsIndexTableFilters = memo(({ isLoading = false }) => {
  const { filters, appliedFilters, setSelectedFilters, filterQuery } = useProductsIndexFilters();

  return (
    <IndexTableFilters
      filters={filters}
      appliedFilters={appliedFilters}
      setSelectedFilters={setSelectedFilters}
      filterQuery={filterQuery}
      loading={isLoading}
      tabs={indexTableTabs}
    />
  );
});

export default ProductsIndexTableFilters;

//@ts-check
import { Badge, BadgeProgressValue, BadgeStatusValue } from "@shopify/polaris";
import { useMemo } from "react";
import { useTranslation } from "react-i18next";
/**
 * @description Returns badge tone based on score
 * @param {number} score
 */
const badgeTone = (score) => {
  return score >= 75 ? BadgeStatusValue.Success : score >= 50 ? BadgeStatusValue.Attention : BadgeStatusValue.New;
};

/**
 * @description Optimize SEO status badge
 * @param {{score?: number, message?: string, withProgress?: boolean} & Omit<import("@shopify/polaris").BadgeProps, "icon">} props
 * @returns {React.ReactElement}
 */
export default function OptimizeSeoStatusBadge({ score, withProgress = true, message, children, tone, ...rest }) {
  const { t } = useTranslation();
  const calculatedTone = useMemo(() => (tone ? tone : score && badgeTone(score)), [score, tone]);

  const badgeMessage = message
    ? message
    : calculatedTone === BadgeStatusValue.Success
    ? "Optimized"
    : calculatedTone === BadgeStatusValue.Attention
    ? "Need Improvement"
    : "Not Optimized";

  const progress =
    calculatedTone === BadgeStatusValue.Success
      ? BadgeProgressValue.Complete
      : calculatedTone === BadgeStatusValue.Attention
      ? BadgeProgressValue.PartiallyComplete
      : BadgeProgressValue.Incomplete;

  return (
    <Badge
      tone={calculatedTone}
      progress={withProgress && progress}
      toneAndProgressLabelOverride={withProgress && badgeMessage}
      {...rest}
    >
      {children ? children : t(badgeMessage)}
    </Badge>
  );
}

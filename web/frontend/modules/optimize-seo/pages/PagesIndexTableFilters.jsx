import usePagesIndexFilters from "@/lib/hooks/optimize-seo/usePagesIndexFilters";
import { indexTableTabs } from "@/lib/util/optimize-seo";
import { IndexTableFilters } from "@/modules/components";
import { memo } from "react";

/**
 *
 * @param {{isLoading : boolean}} props
 * @returns {JSX.Element}
 */
const PagesIndexTableFilters = memo(({ isLoading = false }) => {
  const { filters, appliedFilters, setSelectedFilters, filterQuery } = usePagesIndexFilters();

  return (
    <IndexTableFilters
      filters={filters}
      appliedFilters={appliedFilters}
      setSelectedFilters={setSelectedFilters}
      filterQuery={filterQuery}
      loading={isLoading}
      tabs={indexTableTabs}
    />
  );
});

export default PagesIndexTableFilters;

//@ts-check
import TableReferences from "@/components/TableReferences";
import { useProductApi } from "@/hooks";
import { getQueryFromUrlSearchParam, minifyTitle } from "@/utility/helpers";
import queryKeys from "@/utility/queryKeys";
import {
  BlockStack,
  Box,
  Button,
  ButtonGroup,
  IndexTable,
  InlineStack,
  Text,
  Thumbnail,
  Tooltip,
  useBreakpoints,
} from "@shopify/polaris";
import { UndoIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";
import { useMutation, useQueryClient } from "react-query";
import { useSearchParams } from "react-router-dom";
import altTextOptimizationStatus from "storeseo-enums/altTextOptimization";
import OptimizeSeoStatusBadge from "../optimize-seo/OptimizeSeoStatusBadge";
import { AltTextGenerateModal } from "./AltTextGenerateModal";
import { useAltTextImageRowsContext } from "./AltTextTableRows";

export default function AltTextTableRowItem() {
  const { smDown: isSmallDevice } = useBreakpoints();
  const queryClient = useQueryClient();
  const aiContentApi = useProductApi();

  const { resources, id } = useAltTextImageRowsContext();

  const [searchParams] = useSearchParams();
  const query = getQueryFromUrlSearchParam(searchParams);

  const queryKey = [queryKeys.IMAGES_LIST, query];

  const { mutate, isLoading } = useMutation({
    mutationFn: aiContentApi.undoOptimizedImgAltTexts,
    onSuccess: ({ images }) => {
      const currentImages = queryClient.getQueryData(queryKey);

      const updatedImages = currentImages?.images?.map((img) => {
        const findImage = images.find((item) => String(item.id) === String(id));
        if (img.id === id && findImage) {
          return {
            ...img,
            alt_text: findImage.alt_text,
            alt_text_optimization_status: altTextOptimizationStatus.RESTORED,
          };
        }
        return img;
      });

      queryClient.setQueryData(queryKey, {
        ...currentImages,
        images: updatedImages,
      });
    },
  });

  const handleUndoAltText = () => {
    const { id: productId } = resources[0];
    mutate({
      images: [{ productId, imageId: id }],
    });
  };

  return (
    <>
      {isSmallDevice ? (
        <RowsCellForSmallDevices
          undoAction={handleUndoAltText}
          isUndoLoading={isLoading}
        />
      ) : (
        <RowsCellForLargeDevices
          undoAction={handleUndoAltText}
          isUndoLoading={isLoading}
        />
      )}
    </>
  );
}

/**
 * @param {{isUndoLoading: boolean, undoAction:  () => void}} props
 * @returns {JSX.Element}
 */
const RowsCellForSmallDevices = (props) => {
  const { isUndoLoading, undoAction } = props;
  const { t } = useTranslation();
  const { statusTone, statusProgress, status, thumbnail, alt_text, title, resources } = useAltTextImageRowsContext();

  return (
    <Box
      paddingBlock="300"
      paddingInline="400"
      width="100%"
    >
      <InlineStack
        align="space-between"
        wrap={false}
        gap="200"
      >
        {/* Thumbnail */}
        <Box>
          <Thumbnail
            source={thumbnail}
            alt={alt_text}
            size="small"
          />
        </Box>
        <Box width="100%">
          <BlockStack gap="300">
            {/* Basic info */}
            <BlockStack gap="150">
              <Text
                as={"h4"}
                fontWeight="semibold"
              >
                {minifyTitle(title, 24)}
              </Text>

              <Text
                as="p"
                variant="bodySm"
                fontWeight="semibold"
              >
                {t("Alt Text")}:{" "}
                <Text
                  as="span"
                  fontWeight="regular"
                >
                  {alt_text}
                </Text>
              </Text>

              <Text
                as="p"
                variant="bodySm"
                fontWeight="semibold"
              >
                {t("Product")}:{" "}
                <Text
                  as="span"
                  fontWeight="regular"
                >
                  {resources.map((p) => p?.title).join(", ")}
                </Text>
              </Text>
            </BlockStack>
            {/* Status and actions */}
            <BlockStack gap="400">
              {/* Status */}
              <Box width="max-content">
                <OptimizeSeoStatusBadge
                  tone={statusTone}
                  message={altTextOptimizationStatus.labels[status]}
                  progress={statusProgress}
                />
              </Box>
              {/* Actions */}
              <div onClick={(e) => (e.stopPropagation(), e.preventDefault())}>
                <ButtonGroup noWrap>
                  <AltTextGenerateModal />

                  <Tooltip content={t("Restore")}>
                    <Button
                      icon={UndoIcon}
                      disabled={status !== altTextOptimizationStatus.OPTIMIZED}
                      loading={isUndoLoading}
                      onClick={undoAction}
                    />
                  </Tooltip>
                </ButtonGroup>
              </div>
            </BlockStack>
          </BlockStack>
        </Box>
      </InlineStack>
    </Box>
  );
};

/**
 * @param {{isUndoLoading: boolean, undoAction:  () => void}} props
 * @returns {JSX.Element}
 */
const RowsCellForLargeDevices = (props) => {
  const { isUndoLoading, undoAction } = props;
  const { t } = useTranslation();
  const {
    id,
    statusTone,
    statusProgress,
    status,
    thumbnail,
    alt_text,
    title,
    resources,
    selectedResources,
    resourceType,
  } = useAltTextImageRowsContext();

  const isStatusPending =
    status === altTextOptimizationStatus.PENDING || status === altTextOptimizationStatus.DISPATCHED;

  return (
    <>
      <IndexTable.Cell className="width-50">
        <Thumbnail
          source={thumbnail}
          alt={alt_text}
          size="small"
        />
      </IndexTable.Cell>
      <IndexTable.Cell className="break_coll_content width-resource_table_title">
        <Text
          as="span"
          variant="bodyMd"
          fontWeight="medium"
        >
          {minifyTitle(title, 24)}
        </Text>
      </IndexTable.Cell>
      <IndexTable.Cell className="break_coll_content width-resource_table_title">{alt_text}</IndexTable.Cell>
      <IndexTable.Cell className="width-150">
        <OptimizeSeoStatusBadge
          tone={statusTone}
          message={altTextOptimizationStatus.labels[status]}
          progress={statusProgress}
        />
      </IndexTable.Cell>
      <IndexTable.Cell className="break_coll_content width-resource_table_title">
        <TableReferences
          resources={resources}
          resourceType={resourceType}
        />
      </IndexTable.Cell>
      <IndexTable.Cell className="width-100">
        <div onClick={(e) => (e.stopPropagation(), e.preventDefault())}>
          <BlockStack
            align="center"
            inlineAlign="center"
          >
            <ButtonGroup noWrap>
              <AltTextGenerateModal disabled={selectedResources.includes(id) || isStatusPending} />

              <Tooltip content={t("Restore")}>
                <Button
                  icon={UndoIcon}
                  disabled={status !== altTextOptimizationStatus.OPTIMIZED || selectedResources.includes(id)}
                  loading={isUndoLoading}
                  onClick={undoAction}
                />
              </Tooltip>
            </ButtonGroup>
          </BlockStack>
        </div>
      </IndexTable.Cell>
    </>
  );
};

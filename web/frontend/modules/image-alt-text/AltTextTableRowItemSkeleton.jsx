//@ts-check
import {
  IndexTable,
  useBreakpoints,
  SkeletonBodyText,
  SkeletonThumbnail,
  SkeletonDisplayText,
  Box,
  BlockStack,
  InlineStack,
  Grid,
} from "@shopify/polaris";

export default function AltTextTableRowItemSkeleton() {
  const { smDown: isSmallDevice } = useBreakpoints();

  return <>{isSmallDevice ? <RowsCellForSmallDevices /> : <RowsCellForLargeDevices />}</>;
}

const RowsCellForSmallDevices = () => {
  return (
    <Box
      paddingBlock="300"
      paddingInline="400"
      width="100%"
    >
      <BlockStack gap="150">
        <InlineStack
          align="space-between"
          wrap={false}
          gap="200"
        >
          {/* Thumbnail */}
          <Box>
            <SkeletonThumbnail size="small" />
          </Box>
          <Box width="100%">
            <BlockStack gap="400">
              {/* Basic info */}
              <BlockStack gap="300">
                <SkeletonBodyText lines={1} />
                <SkeletonBodyText lines={1} />
                <SkeletonBodyText lines={1} />
              </BlockStack>
              {/* Scoring */}
              <Grid gap={{ xs: "200" }}>
                <Grid.Cell columnSpan={{ xs: 3 }}>
                  <SkeletonBodyText lines={1} />
                </Grid.Cell>
              </Grid>
              <SkeletonDisplayText size={"small"} />
            </BlockStack>
          </Box>
        </InlineStack>
      </BlockStack>
    </Box>
  );
};

const RowsCellForLargeDevices = () => {
  return (
    <>
      <IndexTable.Cell className="width-50">
        <SkeletonThumbnail size="small" />
      </IndexTable.Cell>
      <IndexTable.Cell className="break_coll_content width-resource_table_title">
        <SkeletonBodyText lines={1} />
      </IndexTable.Cell>
      <IndexTable.Cell className="break_coll_content width-resource_table_title">
        <SkeletonBodyText lines={1} />
      </IndexTable.Cell>

      <IndexTable.Cell className="width-150">
        <SkeletonBodyText lines={1} />
      </IndexTable.Cell>
      <IndexTable.Cell className="break_coll_content width-resource_table_title">
        <SkeletonBodyText lines={1} />
      </IndexTable.Cell>
      <IndexTable.Cell className="width-100">
        <SkeletonDisplayText size={"small"} />
      </IndexTable.Cell>
    </>
  );
};

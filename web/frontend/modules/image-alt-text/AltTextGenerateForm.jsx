//@ts-check
import {
  InlineStack,
  Thumbnail,
  TextField,
  Box,
  Icon,
  Text,
  useBreakpoints,
  BlockStack,
  Button,
} from "@shopify/polaris";
import { InfoIcon, MagicIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";
import { useAltTextImageRowsContext } from "./AltTextTableRows";
import { useController } from "react-hook-form";

/**
 *
 * @param {{generatedAltText: string, isLoading: boolean, formControl: any}} props
 * @returns
 */
export default function AltTextGenerateForm(props) {
  const { generatedAltText, isLoading, formControl } = props;
  const { t } = useTranslation();
  const { smDown: isSmallDevice } = useBreakpoints();
  const { thumbnail, alt_text } = useAltTextImageRowsContext();

  const { field } = useController({
    name: "alt_text",
    control: formControl,
  });
  const { field: keepingField } = useController({
    name: "isKeeping",
    control: formControl,
  });

  const handleKeep = () => {
    field.onChange(generatedAltText);
    keepingField.onChange(true);
  };
  const handleRevert = () => {
    field.onChange(alt_text || "");
    keepingField.onChange(false);
  };
  const isKeeping = keepingField.value;

  return (
    <>
      <AltTextGenerationFromLayout>
        <Thumbnail
          size="large"
          alt={alt_text}
          source={thumbnail}
        />
        <div style={{ flex: 1 }}>
          <BlockStack gap="100">
            <TextField
              label={t("Alt Text")}
              type="text"
              multiline={2}
              autoComplete="off"
              value={field.value}
              onChange={(value) => field.onChange(value)}
              tone={isKeeping ? "magic" : null}
            />
          </BlockStack>
        </div>
      </AltTextGenerationFromLayout>
      {/* AI alt text streaming content */}
      <InlineStack
        gap="400"
        blockAlign="center"
      >
        {!isSmallDevice && <Box width="80px"></Box>}
        <div style={{ flex: 1 }}>
          {isLoading ? (
            <InlineStack
              gap="100"
              wrap={false}
              align="start"
              blockAlign="start"
            >
              <Box>
                <Icon
                  source={MagicIcon}
                  tone="magic"
                />
              </Box>

              <Text
                as="p"
                tone="magic"
              >
                {t("Generating")}...
              </Text>
            </InlineStack>
          ) : generatedAltText.length > 0 ? (
            <InlineStack
              gap="200"
              wrap={false}
              align="space-between"
              blockAlign="start"
            >
              <InlineStack
                gap="100"
                wrap={false}
                align="start"
                blockAlign="start"
              >
                {isKeeping ? (
                  <>
                    <Box>
                      <Icon
                        source={InfoIcon}
                        tone="subdued"
                      />
                    </Box>

                    <Text
                      as="p"
                      tone="subdued"
                    >
                      {t("You can modify and keep it or revert to the original alt text")}
                    </Text>
                  </>
                ) : (
                  <>
                    <Box>
                      <Icon
                        source={MagicIcon}
                        tone="magic"
                      />
                    </Box>

                    <Text
                      as="p"
                      tone="magic"
                    >
                      {generatedAltText}
                    </Text>
                  </>
                )}
              </InlineStack>
              <Box minWidth="70px">
                {isKeeping ? (
                  <Button
                    onClick={handleRevert}
                    fullWidth
                  >
                    {t("Revert")}
                  </Button>
                ) : (
                  <Button
                    onClick={handleKeep}
                    fullWidth
                  >
                    {t("Keep it")}
                  </Button>
                )}
              </Box>
            </InlineStack>
          ) : null}
        </div>
      </InlineStack>
    </>
  );
}

function AltTextGenerationFromLayout({ children }) {
  const { smDown: isSmallDevice } = useBreakpoints();
  if (isSmallDevice) {
    return <BlockStack gap="400">{children}</BlockStack>;
  }

  return (
    <InlineStack
      gap="400"
      blockAlign="center"
    >
      {children}
    </InlineStack>
  );
}

import useAltTextIndexFilters from "@/lib/hooks/image-alt-text/useAltTextIndexFilters";
import { IndexTableFilters } from "@/modules/components";
import { memo } from "react";

/**
 *
 * @param {{isLoading : boolean}} param0
 * @returns
 */
const AltTextIndexTableFilter = memo(({ isLoading = false }) => {
  const { filters, appliedFilters, setSelectedFilters, filterQuery } = useAltTextIndexFilters();

  return (
    <IndexTableFilters
      filters={filters}
      appliedFilters={appliedFilters}
      setSelectedFilters={setSelectedFilters}
      filterQuery={filterQuery}
      loading={isLoading}
    />
  );
});

export default AltTextIndexTableFilter;

//@ts-check
import { MODAL_IDS } from "@/config";
import { useUnsavedChanges } from "@/hooks/useUnsavedChanges";
import useUserAddon from "@/hooks/useUserAddon";
import { useGenerateImageAltText, useOptimizeImageAltText } from "@/lib/hooks/image-alt-text/mutations";
import ContextualSaveBar from "@/modules/components/ContextualSaveBar";
import Modal from "@/modules/components/Modal";
import AltTextGenerateForm from "@/modules/image-alt-text/AltTextGenerateForm";
import { useAltTextGenerateModal } from "@/modules/image-alt-text/useAltTextGenerateModal";
import { getQueryFromUrlSearchParam } from "@/utility/helpers";
import queryKeys from "@/utility/queryKeys";
import { Banner, BlockStack, Box, Button, Icon, Tooltip } from "@shopify/polaris";
import { MagicIcon } from "@shopify/polaris-icons";
import { useCallback, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useSearchParams } from "react-router-dom";
import { useAltTextImageRowsContext } from "./AltTextTableRows";

/**
 * @param {{disabled?: boolean}} props
 * @returns {React.ReactElement}
 */
export const AltTextGenerateModal = (props) => {
  const { disabled } = props;
  const { t } = useTranslation();
  const { image, alt_text, resources } = useAltTextImageRowsContext();

  const { hideAltTextGenerateModal, showAltTextGenerateModal } = useAltTextGenerateModal(image.id);
  const [generatedAltText, setGeneratedAltText] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [isRegenerating, setIsRegenerating] = useState(false);

  const formValues = useMemo(
    () => ({
      isKeeping: false,
      alt_text: alt_text || "",
    }),
    [alt_text]
  );

  // console.table({ [resources[0].title]: formValues });

  const { handleSubmit, control, reset, getFieldState, formState, resetField, watch } = useForm({
    values: formValues,
  });

  const { mutate, isLoading, error, isError } = useGenerateImageAltText({
    setGeneratedAltText,
    setIsGenerating,
    setIsRegenerating,
  });

  const generateAltText = () => {
    if (resources.length < 1) return;

    const newImage = { id: String(image.id), src: `${image.src}?width=512&height=512`, product: resources[0] };
    setIsGenerating(true);
    mutate({ image: newImage });
  };

  const regenerateAltText = () => {
    setIsRegenerating(true);
    generateAltText();
    reset();
  };

  const [searchParams] = useSearchParams();
  const query = getQueryFromUrlSearchParam(searchParams);
  const queryKey = [queryKeys.IMAGES_LIST, query];

  const { mutate: saveGeneratedAltText, isLoading: isSaveLoading } = useOptimizeImageAltText({
    queryKey,
    image,
  });

  /**
   *
   * @param {{alt_text: string}} data
   * @returns
   */
  const onSave = (data) => {
    if (resources.length < 1) return;
    const { id } = resources[0];

    saveGeneratedAltText(
      {
        id,
        image: {
          ...image,
          prev_alt_text: alt_text,
          alt_text: data.alt_text,
        },
      },
      {
        onSuccess: () => {
          resetField("isKeeping");
          hideAltTextGenerateModal();
          setGeneratedAltText("");
        },
      }
    );
  };

  const isAltTextGenerated = !isLoading && generatedAltText?.length > 0;
  const isAltTextChange = getFieldState("alt_text", formState).isDirty;
  const isDisabled = isGenerating || isAltTextChange;

  const onCloseModal = useCallback(() => {
    if (isGenerating || isAltTextChange) {
      showAltTextGenerateModal();
      return;
    }

    setGeneratedAltText("");
    reset();
    hideAltTextGenerateModal();
  }, [reset, hideAltTextGenerateModal]);

  const { hasUnsavedChanges, setShowDiscardChangeModal } = useUnsavedChanges({
    originalData: formValues,
    currentData: watch(),
  });

  return (
    <>
      <ContextualSaveBar
        id="alt-text-generate-modal"
        open={hasUnsavedChanges}
        isLoading={isLoading || isSaveLoading}
        onSave={handleSubmit(onSave)}
        onDiscard={() => setShowDiscardChangeModal(true)}
      />

      <Modal
        type="app-bridge"
        id={MODAL_IDS.ALT_TEXT_GENERATE + `-${image.id}`}
        onClose={onCloseModal}
        disable={isDisabled}
      >
        <Modal.ToggleButton>
          <ModalToggleButton disabled={disabled} />
        </Modal.ToggleButton>

        <Modal.Section>
          <Box padding="400">
            <BlockStack gap="200">
              {/* @ts-ignore */}
              {isError && <Banner tone="warning">{t(error?.message)}</Banner>}

              <AltTextGenerateForm
                generatedAltText={generatedAltText}
                isLoading={!isError && isGenerating}
                formControl={control}
              />
            </BlockStack>
          </Box>

          <Modal.TitleBar title={t("AI generated image alt text")}>
            <button
              variant="primary"
              onClick={isAltTextGenerated || isRegenerating ? handleSubmit(onSave) : generateAltText}
              loading={(!isRegenerating && isLoading) || isSaveLoading ? "" : undefined}
              disabled={!!isRegenerating || (isAltTextGenerated && !isAltTextChange)}
            >
              {isAltTextGenerated || isRegenerating ? t("Save") : t("Generate with AI")}
            </button>
            {(isAltTextGenerated || isRegenerating) && (
              <button
                onClick={regenerateAltText}
                loading={isLoading ? "" : undefined}
              >
                {t("Re-Generate")}
              </button>
            )}
          </Modal.TitleBar>
        </Modal.Section>
      </Modal>
    </>
  );
};

/**
 *
 * @param {{disabled: boolean}} props
 * @returns
 */
const ModalToggleButton = (props) => {
  const { disabled } = props;
  const { t } = useTranslation();
  const { image } = useAltTextImageRowsContext();
  const { showAltTextGenerateModal } = useAltTextGenerateModal(image.id);
  const { aiOptimizerUsageLimit, aiOptimizerUsageCount } = useUserAddon();
  const limitExceeded = aiOptimizerUsageCount + 6 > aiOptimizerUsageLimit;

  return (
    <Tooltip content={limitExceeded ? t("Usage limit reached!") : t("Generate")}>
      <Button
        disabled={disabled || limitExceeded}
        onClick={showAltTextGenerateModal}
        icon={
          <Icon
            source={MagicIcon}
            tone="magic"
          />
        }
        tone="success"
      />
    </Tooltip>
  );
};

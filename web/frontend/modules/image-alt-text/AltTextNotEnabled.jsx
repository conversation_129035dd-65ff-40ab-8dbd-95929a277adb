//@ts-check
import { <PERSON>, <PERSON>Stack, <PERSON><PERSON>, <PERSON>, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useAppNavigation } from "../../hooks/useAppNavigation";
import { useNavigate } from "react-router-dom";
import { EMPTYSTATE_IMAGES } from "../../config";
import { LockIcon } from "@shopify/polaris-icons";
import EmptyPage from "@/components/common/EmptyPage";

const AltTextNotEnabled = () => {
  const { t } = useTranslation();
  const { backAction } = useAppNavigation();
  const navigate = useNavigate();

  const handleClick = () => {
    navigate("/credit-bundles");
  };

  return (
    <Page
      title={t("Image Alt Text Generator")}
      backAction={backAction}
    >
      <BlockStack gap="400">
        <Banner
          tone="warning"
          icon={LockIcon}
          title={t("Image Alt Text Generator is not enabled")}
          action={{
            content: t("Choose your plan"),
            url: "/credit-bundles",
          }}
        >
          <Text
            as="p"
            tone="subdued"
          >
            {t("Please add AI Content Optimizer to your subscription plan to unlock this feature.")}
          </Text>
        </Banner>
        <EmptyPage
          heading="Add AI Content Optimizer to your subscription"
          content="Please add AI Content Optimizer to your subscription plan to unlock this feature."
          primaryAction={
            <Button
              variant="primary"
              onClick={handleClick}
            >
              {t("Choose your plan")}
            </Button>
          }
          image={EMPTYSTATE_IMAGES.imageOptimizer}
          withWrapper={false}
        />
      </BlockStack>
    </Page>
  );
};

export default AltTextNotEnabled;

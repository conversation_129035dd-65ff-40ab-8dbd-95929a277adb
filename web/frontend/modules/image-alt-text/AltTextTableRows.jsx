//@ts-check
import useIsRunningBackupRestore from "@/lib/hooks/useIsRunningBackupRestore";
import { prepareThumbnailURL } from "@/utility/helpers";
import { BadgeProgressValue, BadgeStatusValue, IndexTable } from "@shopify/polaris";
import React, { useMemo } from "react";
import altTextOptimizationStatus from "storeseo-enums/altTextOptimization";
import ResourceType from "storeseo-enums/resourceType";
import AltTextTableRowItem from "./AltTextTableRowItem";

/**
 *
 * @typedef {keyof Pick<typeof ResourceType, "PRODUCT" | "COLLECTION" | "PAGE" | "ARTICLE">} ImageResourceType
 * @type {React.Context<{image: Object, statusTone: import("@shopify/polaris/build/ts/src/components/Badge").Tone, statusProgress: import("@shopify/polaris/build/ts/src/components/Badge").Progress, thumbnail: string, alt_text: string, title: string, resources: Object[], status: string, id: string, selectedResources: string[], resourceType?: ImageResourceType}>}
 */
const AltTextImageRowsContext = React.createContext(null);
AltTextImageRowsContext.displayName = "AltTextImageRowsContext";

/**
 *
 * @param {{image: Object, rowPosition:number, selectedResources:string[], resourceType?:ImageResourceType}} props
 * @returns
 */
const AltTextTableRows = (props) => {
  const { image, rowPosition, selectedResources, resourceType } = props;
  const { isRunning: isRunningBackupOrRestore } = useIsRunningBackupRestore();

  const { src, id, alt_text_optimization_status: status, alt_text, title, resources } = image;

  const optimizationsStatusColor = {
    [altTextOptimizationStatus.OPTIMIZED]: BadgeStatusValue.Info,
    [altTextOptimizationStatus.PENDING]: BadgeStatusValue.Info,
    [altTextOptimizationStatus.DISPATCHED]: BadgeStatusValue.Info,
    [altTextOptimizationStatus.PROCESSING]: BadgeStatusValue.Info,
    [altTextOptimizationStatus.SAVING]: BadgeStatusValue.Info,
    [altTextOptimizationStatus.NOT_OPTIMIZED]: BadgeStatusValue.New,
  };

  const { progress, tone } = useMemo(() => {
    const tone = optimizationsStatusColor[status];
    const progress =
      status === altTextOptimizationStatus.OPTIMIZED
        ? BadgeProgressValue.Complete
        : status === altTextOptimizationStatus.NOT_OPTIMIZED || status === altTextOptimizationStatus.RESTORED
        ? BadgeProgressValue.Incomplete
        : BadgeProgressValue.PartiallyComplete;
    return { progress, tone };
  }, [optimizationsStatusColor, status]);

  const thumbnail = prepareThumbnailURL(src);

  const contextValues = {
    image,
    statusTone: tone,
    statusProgress: progress,
    thumbnail,
    alt_text,
    title,
    resources,
    status,
    id,
    selectedResources,
    resourceType,
  };

  const isDisableRow = status === altTextOptimizationStatus.PENDING || status === altTextOptimizationStatus.DISPATCHED;

  return (
    <IndexTable.Row
      id={id}
      position={rowPosition}
      selected={selectedResources?.includes(id)}
      disabled={isDisableRow || isRunningBackupOrRestore}
    >
      <AltTextImageRowsContext.Provider value={contextValues}>
        <AltTextTableRowItem />
      </AltTextImageRowsContext.Provider>
    </IndexTable.Row>
  );
};

export const useAltTextImageRowsContext = () => {
  const context = React.useContext(AltTextImageRowsContext);
  if (context === undefined) {
    throw new Error("useAltTextImageRowsContext must be used within a AltTextTableRows");
  }
  return context;
};

export default AltTextTableRows;

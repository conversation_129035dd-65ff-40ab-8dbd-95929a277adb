import {
  FINITE_RETURN_POLICY,
  NO_RETUR_NPOLICY,
  UNLIMITED_RETURN_POLICY,
  RETURN_AT_KIOSK,
  RETURN_BY_MAIL,
  RETURN_IN_STORE,
  FREE_RETURN,
  CUSTOMER_RESPONSIBILITY,
  SHIPPING_FEES_APPLIED,
} from "storeseo-schema/local-seo/productMerchantSchema";

//@ts-check
export const policyOptions = [
  { label: "Finite return policy", value: FINITE_RETURN_POLICY },
  { label: "No return policy", value: NO_RETUR_NPOLICY },
  { label: "Unlimited return policy", value: UNLIMITED_RETURN_POLICY },
];

export const returnMethodOptions = [
  { label: "Return at kiosk", value: RETURN_AT_KIOSK },
  { label: "Return by mail", value: RETURN_BY_MAIL },
  { label: "Return in store", value: RETURN_IN_STORE },
];

export const returnFeesOptions = [
  { label: "Free return", value: FREE_RETURN },
  { label: "Customer responsibility", value: CUSTOMER_RESPONSIBILITY },
  { label: "Shipping fees applied", value: SHIPPING_FEES_APPLIED },
];

//@ts-check
import { isEmpty } from "lodash";

/**
 * Generates payload for bulk image optimization
 * @param {{
 *   selectedResources: string[],
 *   images: any[],
 *   compressionType: string,
 *   compressionFormat: string,
 *   resizeType: string
 * }} params
 * @returns {{images: {id: number, media_id: string}[], setting: object} | null}
 */
export const generateBulkOptimizePayload = ({
  selectedResources,
  images,
  compressionType,
  compressionFormat,
  resizeType,
}) => {
  if (isEmpty(selectedResources)) {
    return null;
  }

  // Build optimization settings
  const setting = {};
  if (compressionType !== "none") setting["compression_type"] = compressionType;
  if (compressionFormat !== "none") setting["target_format"] = compressionFormat;
  if (resizeType !== "none") setting["target_width"] = resizeType;

  // Build images list
  const list = [];
  const imagesHash = images.reduce((hash, img) => ({ ...hash, [img.id]: img }), {});
  
  for (let id of selectedResources) {
    if (imagesHash[id]) {
      const image = imagesHash[id];
      list.push({
        id,
        media_id: image.media_id,
      });
    }
  }

  return {
    images: list,
    setting,
  };
};

/**
 * Generates payload for bulk image restoration
 * @param {{
 *   selectedResources: string[],
 *   formattedImages: any[]
 * }} params
 * @returns {{id: string, media_id: string, resource_id?: string, originalSource: string, file_size: number}[] | null}
 */
export const generateBulkRestorePayload = ({ selectedResources, formattedImages }) => {
  if (isEmpty(selectedResources)) {
    return null;
  }

  const list = [];
  const imagesHash = formattedImages.reduce((hash, img) => ({ ...hash, [img.id]: img }), {});

  for (let id of selectedResources) {
    const image = imagesHash[id];
    if (image?.optimization_meta?.original_image_url) {
      const payload = {
        id,
        media_id: image.media_id,
        originalSource: image.optimization_meta.original_image_url,
        file_size: image.optimization_meta.original_size,
      };

      // Add resource_id for products (if available)
      if (image.resources?.[0]?.resource_id) {
        payload.resource_id = image.resources[0].resource_id;
      }

      list.push(payload);
    }
  }

  return list.length > 0 ? list : null;
};

/**
 * Updates query cache with optimized/restored images
 * @param {{
 *   queryClient: any,
 *   queryKey: any[],
 *   updatedImages: any[]
 * }} params
 */
export const updateImagesInCache = ({ queryClient, queryKey, updatedImages }) => {
  const updatedImageHash = updatedImages.reduce((hash, img) => ({ ...hash, [img.id]: img }), {});
  
  const currentData = queryClient.getQueryData(queryKey);
  if (!currentData) return;

  const { images, pagination } = currentData;
  const updatedImageList = images.map((img) => {
    if (!updatedImageHash[img.id]) return img;
    return {
      ...updatedImageHash[img.id],
    };
  });

  queryClient.setQueryData(queryKey, { images: updatedImageList, pagination });
};

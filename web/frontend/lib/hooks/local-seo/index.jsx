import { useLoc<PERSON><PERSON><PERSON><PERSON> } from "@/hooks/apiHooks/useLocal<PERSON><PERSON><PERSON>";
import { useMutation, useQuery, useQueryClient } from "react-query";
import {
  LOCAL_SEO_ARTICLE_SCHEMA,
  LOCAL_SEO_BLOG_SCHEMA,
  LOCAL_SEO_BREADCRUMB_SCHEMA,
  LOCAL_SEO_COLLECTION_SCHEMA,
  LOCAL_SEO_LOCAL_BUSINESS_SCHEMA,
  LOCAL_SEO_ORGANIZATION_SCHEMA,
  LOCAL_SEO_PRODUCT_MERCHANT_SCHEMA,
  LOCAL_SEO_PRODUCT_SCHEMA,
} from "storeseo-enums/localSEOSchemaTypes";

export const useLocalSEOBreadCrumbSchema = () => {
  const localSEOApi = useLocalSEOApi();

  return useQuery({
    queryKey: [LOCAL_SEO_BREADCRUMB_SCHEMA],
    queryFn: localSEOApi.getBreadCrumbSchemaSettings,
    refetchOnWindowFocus: false,
  });
};

export const useUpdateLocalSEOBreadCrumbSchema = () => {
  const localSEOApi = useLocalSEOApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: localSEOApi.updateBreadCrumbSchemaSettings,
    onSuccess: (data) => {
      const status = data?.status;
      queryClient.setQueryData([LOCAL_SEO_BREADCRUMB_SCHEMA], { status });
    },
  });
};

export const useLocalSEOCollectionSchema = () => {
  const localSEOApi = useLocalSEOApi();

  return useQuery({
    queryKey: [LOCAL_SEO_COLLECTION_SCHEMA],
    queryFn: localSEOApi.getCollectionSchemaSettings,
    refetchOnWindowFocus: false,
  });
};

export const useUpdateLocalSEOCollectionSchema = () => {
  const localSEOApi = useLocalSEOApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: localSEOApi.updateCollectionSchemaSettings,
    onSuccess: (data) => {
      const status = data?.status;
      queryClient.setQueryData([LOCAL_SEO_COLLECTION_SCHEMA], { status });
    },
  });
};

export const useLocalSEOBlogSchema = () => {
  const localSEOApi = useLocalSEOApi();

  return useQuery({
    queryKey: [LOCAL_SEO_BLOG_SCHEMA],
    queryFn: localSEOApi.getBlogSchemaSettings,
    refetchOnWindowFocus: false,
  });
};

export const useUpdateLocalSEOBlogSchema = () => {
  const localSEOApi = useLocalSEOApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: localSEOApi.updateBlogSchemaSettings,
    onSuccess: (data) => {
      const status = data?.status;
      queryClient.setQueryData([LOCAL_SEO_BLOG_SCHEMA], { status });
    },
  });
};

export const useLocalSEOProductSchema = () => {
  const localSEOApi = useLocalSEOApi();

  return useQuery({
    queryKey: [LOCAL_SEO_PRODUCT_SCHEMA],
    queryFn: localSEOApi.getProductSchemaSettings,
    refetchOnWindowFocus: false,
  });
};

export const useUpdateLocalSEOProductSchema = () => {
  const localSEOApi = useLocalSEOApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: localSEOApi.updateProductSchemaSettings,
    onSuccess: () => {
      queryClient.invalidateQueries([LOCAL_SEO_PRODUCT_SCHEMA]);
    },
  });
};

export const useLocalSEOProductMerchantSchema = () => {
  const localSEOApi = useLocalSEOApi();

  return useQuery({
    queryKey: [LOCAL_SEO_PRODUCT_MERCHANT_SCHEMA],
    queryFn: localSEOApi.getProductMerchantSchemaSettings,
    refetchOnWindowFocus: false,
  });
};

export const useUpdateLocalSEOProductMerchantSchema = () => {
  const localSEOApi = useLocalSEOApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: localSEOApi.updateProductMerchantSchemaSettings,
    onSuccess: (data) => {
      queryClient.setQueryData([LOCAL_SEO_PRODUCT_MERCHANT_SCHEMA], data);
      queryClient.invalidateQueries([LOCAL_SEO_PRODUCT_MERCHANT_SCHEMA]);
    },
  });
};

export const useLocalSEOArticleSchema = () => {
  const localSEOApi = useLocalSEOApi();

  return useQuery({
    queryKey: [LOCAL_SEO_ARTICLE_SCHEMA],
    queryFn: localSEOApi.getArticleSchemaSettings,
    refetchOnWindowFocus: false,
  });
};

export const useUpdateLocalSEOArticleSchema = () => {
  const localSEOApi = useLocalSEOApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: localSEOApi.updateArticleSchemaSettings,
    onSuccess: (data) => {
      queryClient.setQueryData([LOCAL_SEO_ARTICLE_SCHEMA], data);
      queryClient.invalidateQueries([LOCAL_SEO_ARTICLE_SCHEMA]);
    },
  });
};

export const useLocalSEOOrganizationSchema = () => {
  const localSEOApi = useLocalSEOApi();

  return useQuery({
    queryKey: [LOCAL_SEO_ORGANIZATION_SCHEMA],
    queryFn: localSEOApi.getOrganizationSchemaSettings,
    refetchOnWindowFocus: false,
  });
};

export const useUpdateLocalSEOOrganizationSchema = () => {
  const localSEOApi = useLocalSEOApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: localSEOApi.updateOrganizationSchemaSettings,
    onSuccess: (data) => {
      queryClient.setQueryData([LOCAL_SEO_ORGANIZATION_SCHEMA], data);
      queryClient.invalidateQueries([LOCAL_SEO_ORGANIZATION_SCHEMA]);
    },
  });
};

export const useLocalSEOLocalBusinessSchema = () => {
  const localSEOApi = useLocalSEOApi();

  return useQuery({
    queryKey: [LOCAL_SEO_LOCAL_BUSINESS_SCHEMA],
    queryFn: localSEOApi.getLocalBusinessSchemaSettings,
    refetchOnWindowFocus: false,
  });
};

export const useUpdateLocalSEOLocalBusinessSchema = () => {
  const localSEOApi = useLocalSEOApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: localSEOApi.updateLocalBusinessSchemaSettings,
    onSuccess: (data) => {
      queryClient.setQueryData([LOCAL_SEO_LOCAL_BUSINESS_SCHEMA], data);
      queryClient.invalidateQueries([LOCAL_SEO_LOCAL_BUSINESS_SCHEMA]);
    },
  });
};

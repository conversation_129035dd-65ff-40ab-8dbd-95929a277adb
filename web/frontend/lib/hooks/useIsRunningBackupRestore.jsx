import { useSelector } from "react-redux";

export default function useIsRunningBackupRestore() {
  const backupRestoreStatus = useSelector((state) => state.user?.backupRestoreStatus);
  const runningProcessMap = backupRestoreStatus ? Object.values(backupRestoreStatus) : [];
  const isRunningBackupOrRestore = runningProcessMap.some((process) => process === true);
  return { isRunning: isRunningBackupOrRestore, backupRestoreStatus };
}

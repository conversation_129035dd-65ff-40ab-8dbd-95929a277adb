import { useRef } from "react";

// inspired by https://alibaba.github.io/hooks/use-previous
/**
 * Function type for determining whether the previous value should be updated
 * @template T
 * @callback ShouldUpdateFunc
 * @param {T | undefined} prev - The previous value
 * @param {T} next - The next value
 * @returns {boolean} Whether the value should be updated
 */

/**
 * Default function to determine if values should be updated
 * Uses Object.is for comparison
 * @template T
 * @param {T} [a] - First value to compare
 * @param {T} [b] - Second value to compare
 * @returns {boolean} True if values are different
 */
const defaultShouldUpdate = (a, b) => !Object.is(a, b);

/**
 * Hook that returns the previous value of a state variable
 * @template T
 * @param {T} state - The current state value
 * @param {ShouldUpdateFunc<T>} [shouldUpdate=defaultShouldUpdate] - Function to determine when to update the previous value
 * @returns {T | undefined} The previous value, or undefined if no previous value exists
 */
function usePrevious(state, shouldUpdate = defaultShouldUpdate) {
  const prevRef = useRef();
  const curRef = useRef();

  if (shouldUpdate(curRef.current, state)) {
    prevRef.current = curRef.current;
    curRef.current = state;
  }

  return prevRef.current;
}

export default usePrevious;

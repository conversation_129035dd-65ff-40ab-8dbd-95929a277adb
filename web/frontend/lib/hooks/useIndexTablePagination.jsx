import { useTranslation } from "react-i18next";
import { useSearchParams } from "react-router-dom";

/**
 *
 * @param {{pageCount : number, page : number, pageSize:number, rowCount:number}} pagination
 * @param {import("@shopify/polaris/build/ts/src/components/IndexTable").IndexTablePaginationProps} options
 * @returns {{ goToPage: (page: number) => void, nextPage: number, prevPage: number, paginationConfigs: { previousKeys: number[], previousTooltip: string, onPrevious: () => void, hasNext: boolean, nextKeys: number[], nextTooltip: string, onNext: () => void, hasPrevious: boolean, label: string } } }
 */
export default function useIndexTablePagination(pagination, options = {}) {
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();

  const goToPage = (page) => {
    if (page !== "undefined" && page) {
      setSearchParams({
        ...Object.fromEntries(searchParams.entries()),
        page,
      });
    }
  };

  const hasMultiplePage = pagination?.pageCount > 1;
  const nextPage = hasMultiplePage && pagination.page < pagination.pageCount ? pagination.page + 1 : null;
  const prevPage = hasMultiplePage && pagination.page > 1 ? pagination.page - 1 : null;
  const paginationConfigs = hasMultiplePage && {
    previousKeys: [74],
    previousTooltip: t("Prev (J)"),
    onPrevious: () => goToPage(prevPage),
    hasNext: !!nextPage,
    nextKeys: [75],
    nextTooltip: t("Next (K)"),
    onNext: () => goToPage(nextPage),
    hasPrevious: !!prevPage,
    label: t("Page {{pageNo}} of {{totalPage}}", {
      pageNo: pagination?.page,
      totalPage: pagination?.pageCount,
    }),
    ...options,
  };
  return {
    goToPage,
    nextPage,
    prevPage,
    paginationConfigs,
  };
}

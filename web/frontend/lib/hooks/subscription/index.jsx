// @ts-check
import { useSubscriptionApi } from "@/hooks/apiHooks/useSubscriptionApi";
import { useAppBridgeRedirect } from "@/hooks/useAppBridgeRedirect";
import { useMutation } from "react-query";

export const useSubscribeNowAction = () => {
  const subscriptionApi = useSubscriptionApi();
  const redirect = useAppBridgeRedirect();

  return useMutation({
    mutationFn: async () => {
      return await subscriptionApi.handleSwitchToPaidPlan();
    },
    onSuccess: async ({ confirmationUrl }) => {
      return redirect.REMOTE(confirmationUrl);
    },
  });
};

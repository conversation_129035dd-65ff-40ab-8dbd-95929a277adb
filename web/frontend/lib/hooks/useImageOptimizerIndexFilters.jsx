import FileSizeFilterSelector from "@/modules/components/IndexTable/FileSizeFilterSelector";
import { ChoiceList } from "@shopify/polaris";
import { isEmpty } from "lodash";
import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSearchParams } from "react-router-dom";
import imageOptimization from "storeseo-enums/imageOptimization";
import { indexTableLimitFilterOptions } from "../util/optimize-seo";

const optimizeStatus = [
  {
    label: imageOptimization.labels[imageOptimization.OPTIMIZED],
    value: imageOptimization.OPTIMIZED,
  },
  {
    label: imageOptimization.labels[imageOptimization.NOT_OPTIMIZED],
    value: imageOptimization.NOT_OPTIMIZED,
  },
  {
    label: imageOptimization.labels[imageOptimization.ALREADY_OPTIMIZED],
    value: imageOptimization.ALREADY_OPTIMIZED,
  },
  { label: imageOptimization.labels[imageOptimization.PENDING], value: imageOptimization.PENDING },
];

const filtersOptions = [
  {
    label: "File size",
    key: "fileSize",
    choices: [],
    pinned: true,
  },
  { label: "Status", key: "status", choices: optimizeStatus, pinned: true, allowMultiple: true },
  {
    label: "Show images",
    key: "limit",
    choices: indexTableLimitFilterOptions,
    pinned: true,
    allowMultiple: false,
  },
];

export default function useImageOptimizerIndexFilters() {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const { status } = useMemo(() => Object.fromEntries(searchParams.entries()), [searchParams]);
  const [sortSelected, setSortSelected] = useState(["none"]);

  const [filterQuery, setFilterQuery] = useState({});
  const [selectedFilters, setSelectedFilters] = useState({ status: status ? status.split(",") : null });

  const handleFilterChange = (key, value) => {
    setSelectedFilters((prev) => ({ ...prev, [key]: value }));
  };

  const filters = useMemo(
    () =>
      filtersOptions.map(({ label, key, choices, pinned, allowMultiple }) => {
        const FilterComponent =
          key === "fileSize" ? (
            <FileSizeFilterSelector
              selected={selectedFilters[key] || []}
              onChange={(value) => handleFilterChange(key, value)}
            />
          ) : (
            <ChoiceList
              title={t(label)}
              titleHidden
              choices={choices.map((choice) => ({ ...choice, label: t(choice.label) }))}
              selected={selectedFilters[key] || []}
              onChange={(value) => handleFilterChange(key, value)}
              allowMultiple={allowMultiple}
            />
          );
        return { key, label: t(label), filter: FilterComponent, pinned };
      }),
    [filtersOptions, selectedFilters]
  );

  const appliedFilters = useMemo(
    () =>
      filtersOptions.reduce((acc, { key, label, choices }) => {
        const selected = selectedFilters[key];
        if (selected && !isEmpty(selected)) {
          const selectedLabels =
            key === "fileSize"
              ? selected[0].length > 0 && selected[1].length > 0
                ? selected.map((value) => `${value} MB`).join(" - ")
                : selected[0].length > 0
                ? `Greater than ${selected[0]} MB`
                : selected[1].length > 0
                ? `Less than ${selected[1]} MB`
                : ""
              : selected.map((value) => t(choices.find((choice) => choice.value === value)?.label)).join(", ");

          acc.push({
            key,
            label: `${t(label)}: ${selectedLabels || ""}`,
            onRemove: () => handleFilterChange(key, null),
          });
        }
        return acc;
      }, []),
    [filtersOptions, selectedFilters]
  );

  useEffect(() => {
    const query = {};
    filtersOptions.forEach(({ key }) => {
      if (selectedFilters[key] && !isEmpty(selectedFilters[key])) {
        query[key] = selectedFilters[key].join(",");
      }
    });

    if (sortSelected[0] !== "none") {
      const [sortBy, sortOrder] = sortSelected[0].split(" ");
      query.sortBy = sortBy;
      query.sortOrder = sortOrder;
    }

    setFilterQuery(query);
  }, [selectedFilters, sortSelected]);

  return {
    filters,
    selectedFilters,
    setSelectedFilters,
    appliedFilters,
    filterQuery,
    sortSelected,
    setSortSelected,
  };
}

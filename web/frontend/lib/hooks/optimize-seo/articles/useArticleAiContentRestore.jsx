import { useBlog<PERSON><PERSON> } from "@/hooks/apiHooks/useBlogApi";
import { useMutation, useQueryClient } from "react-query";
import AiOptimizationStatus from "storeseo-enums/aiOptimization";

/**
 *
 * @param {{clearSelection: () => void, queryKey: string, selectedResources: Array<string> }} props
 * @returns
 */
export default function useArticleAiContentRestore(props) {
  const { clearSelection, queryKey, selectedResources } = props;
  const queryClient = useQueryClient();
  const blogApi = useBlogApi();

  return useMutation({
    mutationFn: blogApi.restoreBulkAiContent,
    onMutate: async () => {
      await queryClient.cancelQueries({ queryKey });
      /**
       * @type {{articles: Array<any>}}
       */
      const previousData = queryClient.getQueryData(queryKey);

      // @ts-ignore
      const updateArticles = previousData?.articles?.map((article) => {
        if (selectedResources.includes(article.id)) {
          return {
            ...article,
            ai_optimization_status: AiOptimizationStatus.NOT_OPTIMIZED,
          };
        }
        return article;
      });

      queryClient.setQueryData(queryKey, (oldCacheData) => ({ ...oldCacheData, articles: updateArticles }));

      return { previousData };
    },
    onError: (_error, _variables, { previousData }) => {
      queryClient.setQueryData(queryKey, previousData);
    },
    onSuccess: () => {
      clearSelection();
      queryClient.invalidateQueries({ queryKey });
    },
  });
}

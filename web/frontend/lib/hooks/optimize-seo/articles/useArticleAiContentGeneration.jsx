import { useBlog<PERSON><PERSON> } from "@/hooks/apiHooks/useBlogApi";
import useUserAddon from "@/hooks/useUserAddon";
import queryKeys from "@/utility/queryKeys";
import { useMutation, useQueryClient } from "react-query";
import AiOptimizationStatus from "storeseo-enums/aiOptimization";
import analysisEntityTypes from "storeseo-enums/analysisEntityTypes";

/**
 *
 * @param {{clearSelection: () => void, setModalOpen: import('react').Dispatch<import('react').SetStateAction<boolean>>, queryKey: string, selectedResources: Array<string> }} props
 * @returns
 */
export default function useArticleAiContentGeneration(props) {
  const { clearSelection, setModalOpen, queryKey, selectedResources } = props;
  const queryClient = useQueryClient();
  const blogApi = useBlogApi();
  const { updateAiOptimizerUsage } = useUserAddon();

  return useMutation({
    mutationKey: [queryKeys.BULK_AI_CONTENT_GENERATING, analysisEntityTypes.ARTICLE],
    mutationFn: blogApi.generateBulkAiContent,
    onMutate: async () => {
      await queryClient.cancelQueries({ queryKey });
      const previousData = queryClient.getQueryData(queryKey);

      // @ts-ignore
      const updateArticles = previousData?.articles?.map((article) => {
        if (selectedResources.includes(article.id)) {
          return {
            ...article,
            ai_optimization_status: AiOptimizationStatus.PENDING,
          };
        }
        return article;
      });

      queryClient.setQueryData(queryKey, (oldCacheData) => ({ ...oldCacheData, articles: updateArticles }));

      return { previousData };
    },
    onError: (_error, _variables, { previousData }) => {
      queryClient.setQueryData(queryKey, previousData);
    },
    onSuccess: ({ creditUsage }) => {
      updateAiOptimizerUsage(creditUsage);
      clearSelection();
      setModalOpen(false);
    },
  });
}

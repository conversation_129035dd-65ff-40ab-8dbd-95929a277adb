import { useCollec<PERSON><PERSON><PERSON> } from "@/hooks/apiHooks/useCollectionApi";
import useUserAddon from "@/hooks/useUserAddon";
import queryKeys from "@/utility/queryKeys";
import { useMutation, useQueryClient } from "react-query";
import AiOptimizationStatus from "storeseo-enums/aiOptimization";
import analysisEntityTypes from "storeseo-enums/analysisEntityTypes";

/**
 *
 * @param {{clearSelection: () => void, setModalOpen: import('react').Dispatch<import('react').SetStateAction<boolean>>, queryKey: string, selectedResources: Array<string> }} props
 * @returns
 */
export default function useCollectionAiContentGeneration(props) {
  const { clearSelection, setModalOpen, queryKey, selectedResources } = props;
  const queryClient = useQueryClient();
  const collectionApi = useCollectionApi();
  const { updateAiOptimizerUsage } = useUserAddon();

  return useMutation({
    mutationKey: [queryKeys.BULK_AI_CONTENT_GENERATING, analysisEntityTypes.COLLECTION],
    mutationFn: collectionApi.generateBulkAiContent,
    onMutate: async () => {
      await queryClient.cancelQueries({ queryKey });
      const previousData = queryClient.getQueryData(queryKey);

      // @ts-ignore
      const updateCollections = previousData?.collections?.map((collection) => {
        if (selectedResources.includes(collection.id)) {
          return {
            ...collection,
            ai_optimization_status: AiOptimizationStatus.PENDING,
          };
        }
        return collection;
      });

      queryClient.setQueryData(queryKey, (oldCacheData) => ({ ...oldCacheData, collections: updateCollections }));

      return { previousData };
    },
    onError: (_error, _variables, { previousData }) => {
      queryClient.setQueryData(queryKey, previousData);
    },
    onSuccess: ({ creditUsage }) => {
      updateAiOptimizerUsage(creditUsage);
      clearSelection();
      setModalOpen(false);
    },
  });
}

import { useColl<PERSON><PERSON><PERSON><PERSON> } from "@/hooks/apiHooks/useCollection<PERSON>pi";
import { useMutation, useQueryClient } from "react-query";
import AiOptimizationStatus from "storeseo-enums/aiOptimization";

/**
 *
 * @param {{clearSelection: () => void, queryKey: string, selectedResources: Array<string> }} props
 * @returns
 */
export default function useCollectionAiContentRestore(props) {
  const { clearSelection, queryKey, selectedResources } = props;
  const queryClient = useQueryClient();
  const collectionApi = useCollectionApi();

  return useMutation({
    mutationFn: collectionApi.restoreBulkAiContent,
    onMutate: async () => {
      await queryClient.cancelQueries({ queryKey });
      /**
       * @type {{collections: Array<any>}}
       */
      const previousData = queryClient.getQueryData(queryKey);

      // @ts-ignore
      const updateCollections = previousData?.collections?.map((collection) => {
        if (selectedResources.includes(collection.id)) {
          return {
            ...collection,
            ai_optimization_status: AiOptimizationStatus.NOT_OPTIMIZED,
          };
        }
        return collection;
      });

      queryClient.setQueryData(queryKey, (oldCacheData) => ({ ...oldCacheData, collections: updateCollections }));

      return { previousData };
    },
    onError: (_error, _variables, { previousData }) => {
      queryClient.setQueryData(queryKey, previousData);
    },
    onSuccess: () => {
      clearSelection();
      queryClient.invalidateQueries({ queryKey });
    },
  });
}

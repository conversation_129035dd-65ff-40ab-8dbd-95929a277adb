//@ts-check
import {
  aiOptimizedFilterOptions,
  dateFilterOptions,
  indexTableLimitFilterOptions,
  optimizeStatusFilterOptions,
} from "@/lib/util/optimize-seo";
import DatePickerFilterSelector from "@/modules/components/IndexTable/DatePickerFilterSelector";
import { isValidDate } from "@/utility/helpers";
import { ChoiceList } from "@shopify/polaris";
import { isArray, isEmpty, isEqual } from "lodash";
import moment from "moment";
import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";

const filtersOptions = [
  {
    label: "Date",
    key: "created_at",
    choices: dateFilterOptions,
    pinned: false,
    allowMultiple: true,
  },
  {
    label: "Optimize status",
    key: "optimize_status",
    choices: optimizeStatusFilterOptions,
    pinned: true,
    allowMultiple: true,
  },
  {
    label: "AI optimize status",
    key: "ai_optimize_status",
    choices: aiOptimizedFilterOptions,
    pinned: true,
    allowMultiple: true,
  },
  { label: "Show products", key: "limit", choices: indexTableLimitFilterOptions, pinned: true, allowMultiple: false },
];

export default function useProductsIndexFilters() {
  const { t } = useTranslation();
  const [filterQuery, setFilterQuery] = useState({});
  const [selectedFilters, setSelectedFilters] = useState({});
  const [customDate, setCustomDate] = useState({ from: "", to: "" });

  useEffect(() => {
    if (selectedFilters["created_at"] !== "customDate") setCustomDate({ from: "", to: "" });
  }, [selectedFilters]);

  /**
   * @type {import("@shopify/polaris").FilterInterface[]}
   */
  const filters = useMemo(
    () =>
      filtersOptions.map(({ label, key, choices, pinned, allowMultiple }) => {
        const FilterComponent =
          key === "created_at" ? (
            <DatePickerFilterSelector
              label={t(label)}
              choices={choices}
              selected={selectedFilters?.[key] || []}
              onChange={(value) => {
                setSelectedFilters({
                  ...selectedFilters,
                  [key]: isEqual(value[0], choices[choices.length - 1].value) ? "customDate" : value,
                });
              }}
              onCustomDatePicked={setCustomDate}
              defaultCustomDateValue={customDate}
            />
          ) : (
            <ChoiceList
              title={t(label)}
              titleHidden
              choices={choices.map((choice) => ({
                ...choice,
                label: t(choice.label),
              }))}
              selected={selectedFilters?.[key] || []}
              onChange={(value) => {
                setSelectedFilters({
                  ...selectedFilters,
                  [key]: value,
                });
              }}
              allowMultiple={allowMultiple}
            />
          );

        return {
          key,
          label: t(label),
          filter: FilterComponent,
          pinned,
        };
      }),
    [filtersOptions, selectedFilters, customDate]
  );

  /**
   * @type {import("@shopify/polaris").AppliedFilterInterface[]}
   */
  const appliedFilters = useMemo(
    () =>
      filtersOptions.reduce((acc, { key, label, choices }) => {
        if (selectedFilters?.[key] && !isEmpty(selectedFilters?.[key])) {
          // Generate the label for the applied filter
          const selectedLabels = isArray(selectedFilters?.[key])
            ? selectedFilters?.[key]
                .map((value) => t(choices.find((choice) => choice.value === value)?.label))
                .join(", ")
            : selectedFilters?.[key] === "customDate" // Rest of the label from here for our custom date filter
            ? customDate.from && customDate.to
              ? `${moment(customDate.from).format("ll")} - ${moment(customDate.to).format("ll")}`
              : customDate.from
              ? `Starting ${moment(customDate.from).format("ll")}`
              : customDate.to
              ? `Ending ${moment(customDate.to).format("ll")}`
              : ""
            : selectedFilters?.[key];

          acc.push({
            key,
            label: `${t(label)}: ${selectedLabels || ""}`,
            onRemove: () => {
              setSelectedFilters({
                ...selectedFilters,
                [key]: null,
              });
            },
          });
        }
        return acc;
      }, []),
    [filtersOptions, selectedFilters, customDate]
  );

  useEffect(() => {
    const query = {};
    filtersOptions.forEach(({ key }) => {
      if (selectedFilters[key] && !isEmpty(selectedFilters[key])) {
        // Date filter query
        if (key === "created_at") {
          selectedFilters[key] !== "customDate"
            ? selectedFilters[key].forEach((value) => {
                Object.keys(value).map((k) => (query[k] = value[k]));
              })
            : customDate.from || customDate.to
            ? (isValidDate(customDate.from) && (query["from"] = customDate.from),
              isValidDate(customDate.to) && (query["to"] = customDate.to))
            : null;
        } else query[key] = selectedFilters[key].join(",");
      }
    });

    setFilterQuery(query);
  }, [selectedFilters, customDate]);

  return {
    filters,
    selectedFilters,
    setSelectedFilters,
    appliedFilters,
    filterQuery,
  };
}

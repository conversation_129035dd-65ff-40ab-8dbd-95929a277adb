import { indexTableLimitFilterOptions, optimizeStatusFilterOptions } from "@/lib/util/optimize-seo";
import { ChoiceList } from "@shopify/polaris";
import { isEmpty } from "lodash";
import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSearchParams } from "react-router-dom";

const filtersOptions = [
  {
    label: "Optimize status",
    key: "optimize_status",
    choices: optimizeStatusFilterOptions,
    pinned: true,
    allowMultiple: true,
  },
  {
    label: "Show pages",
    key: "limit",
    choices: indexTableLimitFilterOptions,
    pinned: true,
    allowMultiple: false,
  },
];

export default function usePagesIndexFilters() {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const { status } = useMemo(() => Object.fromEntries(searchParams.entries()), [searchParams]);

  const [filterQuery, setFilterQuery] = useState({});
  const [selectedFilters, setSelectedFilters] = useState({ status: status ? status?.split(",") : null });

  const filters = useMemo(
    () =>
      filtersOptions.map(({ label, key, choices, pinned, allowMultiple }) => ({
        key,
        label: t(label),
        filter: (
          <ChoiceList
            title={t(label)}
            titleHidden
            choices={choices.map((choice) => ({
              ...choice,
              label: t(choice.label),
            }))}
            selected={selectedFilters?.[key] || []}
            onChange={(value) => {
              setSelectedFilters({
                ...selectedFilters,
                [key]: value,
              });
            }}
            allowMultiple={allowMultiple}
          />
        ),
        pinned,
      })),
    [filtersOptions, selectedFilters]
  );

  const appliedFilters = useMemo(
    () =>
      filtersOptions.reduce((acc, { key, label, choices }) => {
        if (selectedFilters?.[key] && !isEmpty(selectedFilters?.[key])) {
          // Generate the label for the applied filter
          const selectedLabels = selectedFilters?.[key]
            .map((value) => t(choices.find((choice) => choice.value === value)?.label))
            .join(", ");

          acc.push({
            key,
            label: `${t(label)}: ${selectedLabels || ""}`,
            onRemove: () => {
              setSelectedFilters({
                ...selectedFilters,
                [key]: null,
              });
            },
          });
        }
        return acc;
      }, []),
    [filtersOptions, selectedFilters]
  );

  useEffect(() => {
    const query = {};
    filtersOptions.forEach(({ key }) => {
      if (selectedFilters[key] && !isEmpty(selectedFilters[key])) {
        query[key] = selectedFilters[key].join(",");
      }
    });
    setFilterQuery(query);
  }, [selectedFilters]);

  return {
    filters,
    selectedFilters,
    setSelectedFilters,
    appliedFilters,
    filterQuery,
  };
}

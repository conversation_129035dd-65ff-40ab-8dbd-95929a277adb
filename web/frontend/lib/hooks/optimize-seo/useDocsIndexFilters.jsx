import { optimizeStatusFilterOptions } from "@/lib/util/optimize-seo";
import { ChoiceList } from "@shopify/polaris";
import { isEmpty } from "lodash";
import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSearchParams } from "react-router-dom";

const filterOptions = [
  {
    label: "Optimize status",
    key: "optimize_status",
    choices: optimizeStatusFilterOptions,
    pinned: true,
    allowMultiple: true,
  },
];

export default function useDocsIndexFilters() {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const { status } = useMemo(() => Object.fromEntries(searchParams.entries()), [searchParams]);

  const [filterQuery, setFilterQuery] = useState({});
  const [selectedFilters, setSelectedFilters] = useState({ status: status ? status?.split(",") : null });

  const filters = useMemo(
    () =>
      filterOptions.map(({ label, key, choices, pinned, allowMultiple }) => ({
        key,
        label: t(label),
        filter: (
          <ChoiceList
            title={t(label)}
            titleHidden
            choices={choices.map((choice) => ({
              ...choice,
              label: t(choice.label),
            }))}
            selected={selectedFilters?.[key] || []}
            onChange={(value) => {
              setSelectedFilters({
                ...selectedFilters,
                [key]: value,
              });
            }}
            allowMultiple={allowMultiple}
          />
        ),
        pinned,
      })),
    [filterOptions, selectedFilters]
  );

  const appliedFilters = useMemo(
    () =>
      filterOptions.reduce((acc, { key, label, choices }) => {
        if (selectedFilters?.[key] && !isEmpty(selectedFilters?.[key])) {
          const selectedLabels = selectedFilters?.[key]
            .map((value) => t(choices.find((choice) => choice.value === value)?.label))
            .join(", ");
          acc.push({
            key,
            label: `${t(label)}: ${selectedLabels || ""}`,
            onRemove: () => {
              setSelectedFilters({
                ...selectedFilters,
                [key]: null,
              });
            },
          });
        }
        return acc;
      }, []),
    [filterOptions, selectedFilters]
  );

  useEffect(() => {
    const query = {};
    filterOptions.forEach(({ key }) => {
      if (selectedFilters[key] && !isEmpty(selectedFilters[key])) {
        query[key] = selectedFilters[key].join(",");
      }
    });
    setFilterQuery(query);
  }, [selectedFilters]);

  return {
    filters,
    selectedFilters,
    setSelectedFilters,
    appliedFilters,
    filterQuery,
  };
}

import { setIsKept } from "@/store/features/AiContent";
import { useCallback, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

/**
 *
 * @param {{collection:object, formData: object, setFormData:React.Dispatch<React.SetStateAction<any>>,setErrors : React.Dispatch<React.SetStateAction<any>>, originalFormData: object }} args
 *
 */
export default function useAiContentOptimizerHelper({ formData, setFormData, originalFormData }) {
  const [isSaved, setIsSaved] = useState(false);
  const aiContent = useSelector((state) => state.aiContent);
  const dispatch = useDispatch();

  const handleAiKeepAll = useCallback(() => {
    setFormData({
      ...formData,
      metaTitle: aiContent.metaTitle,
      metaDescription: aiContent.metaDescription,
      tags: aiContent.tags ?? [],
    });

    dispatch(setIsKept({ metaTitle: true, metaDescription: true, tags: true }));
  }, [formData, aiContent]);

  const handleAiRevertAll = useCallback(() => {
    setFormData({
      ...formData,
      metaTitle: originalFormData.metaTitle,
      metaDescription: originalFormData.metaDescription,
      tags: originalFormData.tags,
    });

    dispatch(setIsKept({ metaTitle: false, metaDescription: false, tags: false }));
  }, [formData, originalFormData]);
  return {
    isSaved,
    setIsSaved,
    handleAiKeepAll,
    handleAiRevertAll,
  };
}

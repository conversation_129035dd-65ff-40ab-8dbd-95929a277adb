//@ts-check
import { useProductApi } from "@/hooks";
import { useMutation, useQueryClient } from "react-query";
import AiOptimizationStatus from "storeseo-enums/aiOptimization";

/**
 *
 * @param {{clearSelection: () => void, queryKey: string, selectedResources: Array<string> }} props
 * @returns
 */
export default function useProductAiContentRestore(props) {
  const { clearSelection, queryKey, selectedResources } = props;
  const queryClient = useQueryClient();
  const productApi = useProductApi();

  return useMutation({
    mutationFn: productApi.restoreBulkAiContent,
    onMutate: async () => {
      await queryClient.cancelQueries({ queryKey });
      /**
       * @type {{products: Array<any>}}
       */
      const previousData = queryClient.getQueryData(queryKey);

      // @ts-ignore
      const updateProducts = previousData?.products?.map((product) => {
        if (selectedResources.includes(product.id)) {
          return {
            ...product,
            ai_optimization_status: AiOptimizationStatus.NOT_OPTIMIZED,
          };
        }
        return product;
      });

      queryClient.setQueryData(queryKey, (oldCacheData) => ({ ...oldCacheData, products: updateProducts }));

      return { previousData };
    },
    onError: (_error, _variables, { previousData }) => {
      queryClient.setQueryData(queryKey, previousData);
    },
    onSuccess: () => {
      clearSelection();
      queryClient.invalidateQueries({ queryKey });
    },
  });
}

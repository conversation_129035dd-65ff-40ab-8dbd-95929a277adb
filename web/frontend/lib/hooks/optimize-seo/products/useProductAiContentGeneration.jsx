//@ts-check
import { useProductApi } from "@/hooks";
import useUserAddon from "@/hooks/useUserAddon";
import queryKeys from "@/utility/queryKeys";
import { useMutation, useQueryClient } from "react-query";
import AiOptimizationStatus from "storeseo-enums/aiOptimization";
import analysisEntityTypes from "storeseo-enums/analysisEntityTypes";

/**
 *
 * @param {{clearSelection: () => void, setModalOpen: import('react').Dispatch<import('react').SetStateAction<boolean>>, queryKey: string, selectedResources: Array<string> }} props
 * @returns
 */
export default function useProductAiContentGeneration(props) {
  const { clearSelection, setModalOpen, queryKey, selectedResources } = props;
  const queryClient = useQueryClient();
  const productApi = useProductApi();
  const { updateAiOptimizerUsage } = useUserAddon();

  return useMutation({
    mutationKey: [queryKeys.BULK_AI_CONTENT_GENERATING, analysisEntityTypes.PRODUCT],
    mutationFn: productApi.generateBulkAiContent,
    onMutate: async () => {
      await queryClient.cancelQueries({ queryKey });
      const previousData = queryClient.getQueryData(queryKey);

      // @ts-ignore
      const updateProducts = previousData?.products?.map((product) => {
        if (selectedResources.includes(product.id)) {
          return {
            ...product,
            ai_optimization_status: AiOptimizationStatus.PENDING,
          };
        }
        return product;
      });

      queryClient.setQueryData(queryKey, (oldCacheData) => ({ ...oldCacheData, products: updateProducts }));

      return { previousData };
    },
    onError: (_error, _variables, { previousData }) => {
      queryClient.setQueryData(queryKey, previousData);
    },
    onSuccess: ({ creditUsage }) => {
      updateAiOptimizerUsage(creditUsage);
      clearSelection();
      setModalOpen(false);
    },
  });
}

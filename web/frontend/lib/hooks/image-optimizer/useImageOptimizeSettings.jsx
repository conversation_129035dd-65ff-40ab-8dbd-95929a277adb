//@ts-check
import { useAppQuery, useShop<PERSON><PERSON> } from "@/hooks";
import queryKeys from "@/utility/queryKeys";
import { useMutation, useQueryClient } from "react-query";

// Default settings for image optimizer
const DEFAULT_OPTIMIZATION_SETTINGS = {
  compressionType: "none",
  format: "none",
  resize: "none",
};

export const useImageOptimizeSettings = () => {
  const shopApi = useShopApi();
  const queryClient = useQueryClient();
  const queryKey = [queryKeys.IMAGE_OPTIMIZE_SETTINGS];

  const { data: apiData, isLoading } = useAppQuery({
    queryKey,
    queryFn: shopApi.getImageOptimizerSetting,
    reactQueryOptions: {
      cacheTime: Infinity,
      staleTime: Infinity,
      refetchInterval: false,
    },
  });

  // Mutation for updating settings
  const updateSettings = useMutation({
    mutationFn: (settingsData) => shopApi.updateImageOptimizerSetting(settingsData),
    onSuccess: () => {
      // Invalidate and refetch the settings query
      queryClient.invalidateQueries(queryKey);
    },
  });

  // Mutation for toggling auto optimization
  const toggleAutoOptimization = useMutation({
    mutationFn: (isEnabled) => shopApi.toggleAutoImageOptimization(isEnabled),
    onSuccess: () => {
      // Invalidate and refetch the settings query
      queryClient.invalidateQueries(queryKey);
    },
  });

  // Return settings with default fallback, isLoading status, defaultSettings, and mutations
  return {
    settings: apiData || DEFAULT_OPTIMIZATION_SETTINGS,
    isLoading,
    defaultSettings: DEFAULT_OPTIMIZATION_SETTINGS,
    updateSettings,
    toggleAutoOptimization,
  };
};

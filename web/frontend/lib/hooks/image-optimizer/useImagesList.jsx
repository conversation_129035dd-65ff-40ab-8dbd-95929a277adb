//@ts-check
import { useAppQuery, useImage<PERSON><PERSON> } from "@/hooks";
import useIsRunningBackupRestore from "@/lib/hooks/useIsRunningBackupRestore.jsx";
import { useImageOptimizerLayoutContext } from "@/providers/ImageOptimizerLayoutProvider";
import { usePusher } from "@/providers/PusherProvider";
import { getQueryFromUrlSearchParam, isSvgImage } from "@/utility/helpers";
import queryKeys from "@/utility/queryKeys";
import { useIndexResourceState } from "@shopify/polaris";
import { useEffect, useMemo } from "react";
import { useSearchParams } from "react-router-dom";
import imageOptimization from "storeseo-enums/imageOptimization";
import ResourceType from "storeseo-enums/resourceType";
import SocketEvents from "storeseo-enums/socketEvents";

/**
 * @typedef {keyof Pick<typeof ResourceType, "PRODUCT" | "COLLECTION" | "PAGE" | "ARTICLE">} ImageResourceType
 */

/**
 * Reusable hook for fetching and managing images list across different resource types
 * @param {ImageResourceType} resourceType - The type of resource to fetch images for
 * @returns {{
 *   data: any,
 *   images: any[],
 *   formattedImages: any[],
 *   pagination: any,
 *   totalCount: number,
 *   hasEmptyContent: boolean,
 *   isFetching: boolean,
 *   isLoading: boolean,
 *   isFetched: boolean,
 *   refetchImages: () => void,
 *   queryKey: any[],
 *   selectedResources: string[],
 *   allResourcesSelected: boolean,
 *   handleSelectionChange: (type: any, toggleType: any, selection: any) => void,
 *   clearSelection: () => void,
 *   removeSelectedResources: (resources: string[]) => void,
 *   selectableImagesCount: number
 * }}
 */
export const useImagesList = (resourceType) => {
  const imageApi = useImageApi();
  const [searchParams] = useSearchParams();
  const query = getQueryFromUrlSearchParam(searchParams);
  const { setIsLoading, countSelectableImages, DUMMY_IMAGES } = useImageOptimizerLayoutContext();
  const { pusherChannel } = usePusher();
  const { isRunning: isRunningBackupOrRestore } = useIsRunningBackupRestore();

  // Build query key
  const queryKey = [queryKeys.IMAGES_LIST, resourceType, query];

  // Fetch images data
  const {
    data,
    isFetching,
    isLoading,
    isFetched,
    refetch: refetchImages,
  } = useAppQuery({
    queryKey,
    queryFn: () => {
      return imageApi.getImages(resourceType, query);
    },
    reactQueryOptions: {
      refetchInterval: false,
    },
  });

  // Update loading state in layout context
  useEffect(() => {
    setIsLoading(isFetching);
  }, [isFetching, setIsLoading]);

  // Extract data with defaults
  const { images = DUMMY_IMAGES, pagination = {}, totalCount } = data || {};

  // Calculate empty content state (local to this hook only)
  const hasEmptyContent = isFetched && totalCount === 0;

  // No need to update global state - layout gets hasEmptyContent directly from this hook!

  // Handle index resource state for selection
  const { selectedResources, allResourcesSelected, handleSelectionChange, clearSelection, removeSelectedResources } =
    useIndexResourceState(images);

  // Clear selection when search params change
  useEffect(() => {
    if (selectedResources.length) clearSelection();
  }, [searchParams]);

  // Set up pusher event listeners for real-time updates
  useEffect(() => {
    if (!pusherChannel) return;

    // Map resource types to their corresponding socket events
    const socketEventMap = {
      [ResourceType.PRODUCT]: SocketEvents.PRODUCT_SYNC_COMPLETE,
      [ResourceType.COLLECTION]: SocketEvents.COLLECTION_SYNC_COMPLETE,
      [ResourceType.ARTICLE]: SocketEvents.BLOG_SYNC_COMPLETE,
    };

    const socketEvent = socketEventMap[resourceType];
    if (socketEvent) {
      pusherChannel.bind(socketEvent, refetchImages);

      return () => {
        pusherChannel.unbind(socketEvent, refetchImages);
      };
    }
  }, [pusherChannel, resourceType, refetchImages]);

  // Remove unoptimizable images from selection
  useEffect(
    function removeUnoptimizableImagesFromSelection() {
      const imagesHash = images?.reduce((hash, img) => ({ ...hash, [img.id]: img }), {});

      const filteredResources = isRunningBackupOrRestore
        ? selectedResources.map((id) => id)
        : selectedResources.filter(
            (id) => imagesHash[id].optimization_status === imageOptimization.PENDING || isSvgImage(imagesHash[id].src)
          );

      if (filteredResources.length) removeSelectedResources(filteredResources);
    },
    [selectedResources, images, isRunningBackupOrRestore, removeSelectedResources]
  );

  // Format images for display
  const formattedImages = useMemo(() => {
    return (
      images?.map((img) => {
        const baseFormatting = {
          ...img,
          title: img?.fileName?.split("?")[0],
        };

        // Add resource-specific formatting for products
        if (resourceType === ResourceType.PRODUCT) {
          return {
            ...baseFormatting,
            resources: img?.products?.map((item) => ({ ...item, resource_id: item.product_id })),
          };
        }

        return baseFormatting;
      }) || []
    );
  }, [images, resourceType]);

  // Calculate selectable images count
  const selectableImagesCount = countSelectableImages(images);

  return {
    data,
    images,
    formattedImages,
    pagination,
    totalCount,
    hasEmptyContent,
    isFetching,
    isLoading,
    isFetched,
    refetchImages,
    queryKey,
    selectedResources,
    allResourcesSelected,
    handleSelectionChange,
    clearSelection,
    removeSelectedResources,
    selectableImagesCount,
  };
};

//@ts-check
import { defaultValues } from "@/lib/form-default-value/imageOptimizerSettings";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import { imageOptimizerSchema } from "storeseo-schema/settings/imageOptimizer";
import { useImageOptimizeSettings } from "./useImageOptimizeSettings";

/**
 * Custom hook for managing image optimizer form state using react-hook-form and yup validation
 *
 * @param {{
 *   onSubmit?: (data: import("yup").InferType<typeof imageOptimizerSchema>) => void | Promise<void>,
 *   enableReinitialize?: boolean
 * }} props
 * @returns {import('react-hook-form').UseFormReturn<import("yup").InferType<typeof imageOptimizerSchema>> & {
 *   isLoading: boolean,
 *   defaultSettings: import("yup").InferType<typeof imageOptimizerSchema>,
 *   handleSubmit: (onValid: (data: import("yup").InferType<typeof imageOptimizerSchema>) => void | Promise<void>) => (e?: React.BaseSyntheticEvent) => Promise<void>
 * }}
 */
export default function useImageOptimizerForm(props = {}) {
  const { onSubmit, enableReinitialize = true } = props;
  const { settings, isLoading, defaultSettings } = useImageOptimizeSettings();

  const form = useForm({
    resolver: yupResolver(imageOptimizerSchema),
    defaultValues,
    values: enableReinitialize ? {
      compressionType: settings?.compressionType || defaultValues.compressionType,
      format: settings?.format || defaultValues.format,
      resize: settings?.resize || defaultValues.resize,
    } : undefined,
    mode: "onChange", // Validate on change for better UX
  });

  // Enhanced handleSubmit that can accept onSubmit callback
  const handleSubmit = (onValid) => {
    return form.handleSubmit(onValid || onSubmit || (() => {}));
  };

  return {
    ...form,
    isLoading,
    defaultSettings,
    handleSubmit,
  };
}

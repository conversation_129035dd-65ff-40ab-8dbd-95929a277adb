//@ts-check
import { useState } from "react";

/**
 * Custom hook for managing image optimizer modals
 * @returns {Object} Modal state and handlers
 */
export const useImageOptimizerModals = () => {
  // Modal states
  const [showBulkImageOptimizeModal, setShowBulkImageOptimizeModal] = useState(false);

  // Modal handlers
  const openBulkOptimizeModal = () => setShowBulkImageOptimizeModal(true);
  const closeBulkOptimizeModal = () => setShowBulkImageOptimizeModal(false);

  return {
    // Bulk optimize modal
    showBulkImageOptimizeModal,
    setShowBulkImageOptimizeModal,
    openBulkOptimizeModal,
    closeBulkOptimizeModal,
  };
};

//@ts-check
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import ResourceType from "storeseo-enums/resourceType";

/**
 * @typedef {keyof Pick<typeof ResourceType, "PRODUCT" | "COLLECTION" | "PAGE" | "ARTICLE">} ImageResourceType
 */

/**
 *
 * @param {{resourceType: ImageResourceType}} props
 * @returns
 */
export const useResourceTitle = (props) => {
  const { resourceType } = props;
  const { t } = useTranslation();
  const [titles, setTitles] = useState({
    singular: "",
    plural: "",
  });

  useEffect(() => {
    switch (resourceType) {
      case "PRODUCT":
        setTitles({
          singular: t("Product"),
          plural: t("Products"),
        });
        break;
      case "COLLECTION":
        setTitles({
          singular: t("Collection"),
          plural: t("Collections"),
        });
        break;
      case "PAGE":
        setTitles({
          singular: t("Page"),
          plural: t("Pages"),
        });
        break;
      case "ARTICLE":
        setTitles({
          singular: t("Article"),
          plural: t("Articles"),
        });
        break;
      default:
        break;
    }
  }, [resourceType]);
  return titles;
};

export { useBulkImageOptimize, useBulkImageRestore, useImageOptimizeActions } from "./useImageOptimizeActions.jsx";
export { useImagesList } from "./useImagesList.jsx";


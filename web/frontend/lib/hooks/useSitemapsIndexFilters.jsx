//@ts-check
import { ChoiceList } from "@shopify/polaris";
import { isEmpty } from "lodash";
import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";

const noIndexAndFollowChoiceOptions = [
  { label: "Active", value: "1" },
  { label: "Inactive", value: "0" },
];
const sitemapChoiceOptions = [
  { label: "Active", value: "0" },
  { label: "Inactive", value: "1" },
];
const filtersOptions = [
  { label: "Noindex", key: "noindex", choices: noIndexAndFollowChoiceOptions, pinned: true },
  { label: "Nofollow", key: "nofollow", choices: noIndexAndFollowChoiceOptions, pinned: true },
  { label: "Sitemap", key: "sitemap", choices: sitemapChoiceOptions, pinned: true },
];

export default function useSitemapsIndexFilters() {
  const { t } = useTranslation();
  const [filterQuery, setFilterQuery] = useState({});
  const [selectedFilters, setSelectedFilters] = useState({});

  const filters = useMemo(
    () =>
      filtersOptions.map(({ label, key, choices, pinned }) => ({
        key,
        label: t(label),
        filter: (
          <ChoiceList
            title={t(label)}
            titleHidden
            choices={choices.map((choice) => ({ ...choice, label: t(choice.label) }))}
            selected={selectedFilters?.[key] || []}
            onChange={(value) => {
              setSelectedFilters({
                [key]: value[0],
              });
            }}
          />
        ),
        pinned,
      })),
    [filtersOptions, selectedFilters]
  );

  const appliedFilters = useMemo(
    () =>
      filtersOptions.reduce((acc, { key, label, choices }) => {
        if (selectedFilters?.[key] && !isEmpty(selectedFilters?.[key])) {
          const selectedLabel = choices.find((choice) => choice.value === selectedFilters?.[key])?.label;
          acc.push({
            key,
            label: `${t(label)}: ${t(selectedLabel)}`,
            onRemove: () => {
              setSelectedFilters({
                ...selectedFilters,
                [key]: undefined,
              });
            },
          });
        }
        return acc;
      }, []),
    [filtersOptions, selectedFilters]
  );

  useEffect(() => {
    const query = {};
    const key = Object.keys(selectedFilters)[0];
    if (selectedFilters?.[key]) {
      query.filterOn = key;
      query.filterValue = selectedFilters?.[key];
    }
    setFilterQuery(query);
  }, [selectedFilters]);

  return {
    filters,
    selectedFilters,
    setSelectedFilters,
    appliedFilters,
    filterQuery,
  };
}

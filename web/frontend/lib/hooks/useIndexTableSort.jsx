import { SORT_DIR } from "@/config";
import { toggleSortDir } from "@/utility/helpers";
import { useCallback, useState } from "react";
import { useSearchParams } from "react-router-dom";

/**
 * @typedef {import("@shopify/polaris/build/ts/src/components/IndexTable/IndexTable.js").IndexTableSortDirection} IndexTableSortDirection
 * @param {{tableHeadings : {title: string, key: string}[]}} param0
 * @returns
 */
export default function useIndexTableSort({ tableHeadings }) {
  const [searchParams, setSearchParams] = useSearchParams();

  const [sortDir, setSortDir] = useState(SORT_DIR.DESC);
  const [sortIndex, setSortIndex] = useState(() => 3);

  /**
   * @type {IndexTableSortDirection}
   */
  const defaultSortDir = SORT_DIR.DESC;

  const handleSort = useCallback(
    (index, direction) => {
      setSortIndex(index);
      setSortDir(direction);
      let key = tableHeadings[index].key;
      let newDir = toggleSortDir(direction);

      setSearchParams({
        ...Object.fromEntries(searchParams.entries()),
        sortBy: key,
        sortOrder: newDir === SORT_DIR.ASC ? "ASC" : "DESC",
        page: 1,
      });
    },
    [searchParams]
  );

  return {
    sortDir,
    sortIndex,
    handleSort,
    defaultSortDir,
  };
}

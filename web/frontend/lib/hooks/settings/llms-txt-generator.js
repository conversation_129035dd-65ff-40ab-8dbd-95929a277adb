//@ts-check
import { useAppQuery } from "@/hooks";
import { useLlmsTxtApi } from "@/hooks/apiHooks/useLlmsTxtApi";
import { getQueryFromUrlSearchParam } from "@/utility/helpers";
import queryKeys from "@/utility/queryKeys";
import { useMutation, useQueryClient } from "react-query";
import { useSearchParams } from "react-router-dom";

const { LLMS_SETTINGS, LLMS_FILE_VERSIONS } = queryKeys;

/**
 * Hook to get LLMs.txt settings
 * @param {object} reactQueryOptions - React Query options
 * @returns {object} Query result with settings and summary
 */
export const useLlmsSettings = (reactQueryOptions = {}) => {
  const llmsTxtApi = useLlmsTxtApi();

  return useAppQuery({
    queryKey: [LLMS_SETTINGS],
    queryFn: async () => await llmsTxtApi.getLlmsSettings(),
    reactQueryOptions: {
      refetchInterval: false,
      ...reactQueryOptions,
    },
  });
};

/**
 * Hook to update LLMs.txt settings
 * @returns {object} Mutation object
 */
export const useUpdateLlmsSettings = () => {
  const queryClient = useQueryClient();
  const llmsTxtApi = useLlmsTxtApi();

  return useMutation({
    mutationFn: (settings) => llmsTxtApi.updateLlmsSettings(settings),
    onSuccess: (data) => {
      // Update the settings cache
      queryClient.setQueryData([LLMS_SETTINGS], data);
    },
  });
};

/**
 * Hook to reset LLMs.txt settings
 * @returns {object} Mutation object
 */
export const useResetLlmsSettings = () => {
  const queryClient = useQueryClient();
  const llmsTxtApi = useLlmsTxtApi();

  return useMutation({
    mutationFn: () => llmsTxtApi.resetLlmsSettings(),
    onSuccess: (data) => {
      // Update the settings cache
      queryClient.setQueryData([LLMS_SETTINGS], data);
    },
  });
};

/**
 * Hook to update specific resource enablement
 * @returns {object} Mutation object
 */
export const useUpdateResourceEnabled = () => {
  const queryClient = useQueryClient();
  const llmsTxtApi = useLlmsTxtApi();

  return useMutation({
    mutationFn: ({ resource, enabled }) => llmsTxtApi.updateResourceEnabled(resource, enabled),
    onSuccess: (data) => {
      // Update the settings cache
      queryClient.setQueryData([LLMS_SETTINGS], data);
    },
  });
};

/**
 * Hook to update schedule setting
 * @returns {object} Mutation object
 */
export const useUpdateSchedule = () => {
  const queryClient = useQueryClient();
  const llmsTxtApi = useLlmsTxtApi();

  return useMutation({
    mutationFn: (schedule) => llmsTxtApi.updateSchedule(schedule),
    onSuccess: (data) => {
      // Update the settings cache
      queryClient.setQueryData([LLMS_SETTINGS], data);
    },
  });
};

/**
 * Hook to generate LLMs.txt file
 * @returns {object} Mutation object
 */
export const useGenerateLlmsTxt = () => {
  const queryClient = useQueryClient();
  const llmsTxtApi = useLlmsTxtApi();

  return useMutation({
    mutationFn: (settings) => llmsTxtApi.generateLlmsTxt(settings),
    onSuccess: (data) => {
      // Update the settings cache with new settings
      queryClient.setQueryData([LLMS_SETTINGS], (oldData) => ({
        ...oldData,
        settings: data.settings,
      }));
    },
  });
};

/**
 * Hook to get LLMs.txt file versions (paginated)
 * @param {import('react-query').UseQueryOptions} reactQueryOptions - React Query options
 * @returns {import('react-query').UseQueryResult<{versions: Array<LlmsFileVersion>, pagination: PaginationData}>} Query result with file versions and pagination
 */
export const useLlmsFileVersions = (reactQueryOptions = {}) => {
  const [searchParams] = useSearchParams();
  const query = getQueryFromUrlSearchParam(searchParams);
  const llmsTxtApi = useLlmsTxtApi();

  return useAppQuery({
    queryKey: [LLMS_FILE_VERSIONS, query],
    queryFn: async () => await llmsTxtApi.getLlmsFileVersions(query),
    reactQueryOptions: {
      refetchInterval: false,
      ...reactQueryOptions,
    },
  });
};

/**
 * Hook to get specific file version details
 * @param {string} id - File version ID
 * @param {object} reactQueryOptions - React Query options
 * @returns {object} Query result with file version details
 */
export const useLlmsFileVersion = (id, reactQueryOptions = {}) => {
  const llmsTxtApi = useLlmsTxtApi();

  return useAppQuery({
    queryKey: [LLMS_FILE_VERSIONS, id],
    queryFn: async () => await llmsTxtApi.getLlmsFileVersion(id),
    reactQueryOptions: {
      enabled: !!id,
      refetchInterval: false,
      ...reactQueryOptions,
    },
  });
};

/**
 * Hook to download LLMs.txt file
 * @returns {object} Mutation object
 */
export const useDownloadLlmsFile = () => {
  const llmsTxtApi = useLlmsTxtApi();

  return useMutation({
    mutationFn: (id) => llmsTxtApi.downloadLlmsFile(id),
  });
};

/**
 * Hook to delete LLMs.txt file
 * @returns {object} Mutation object
 */
export const useDeleteLlmsFile = () => {
  const queryClient = useQueryClient();
  const llmsTxtApi = useLlmsTxtApi();

  return useMutation({
    mutationFn: (id) => llmsTxtApi.deleteLlmsFile(id),
    onSuccess: () => {
      // Invalidate the file versions cache to refresh the list
      queryClient.invalidateQueries([LLMS_FILE_VERSIONS]);
    },
  });
};

/**
 * Hook to copy file URL to clipboard
 * @returns {object} Mutation object
 */
export const useCopyFileUrl = () => {
  const llmsTxtApi = useLlmsTxtApi();

  return useMutation({
    mutationFn: (fileUrl) => llmsTxtApi.copyFileUrl(fileUrl),
  });
};

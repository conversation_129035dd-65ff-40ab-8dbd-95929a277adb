//@ts-check

import { useAiContentA<PERSON> } from "@/hooks";
import { useMutation, useQuery, useQueryClient } from "react-query";
import settingKeys from "storeseo-enums/settingKeys";

export const useGetAutoAiOptimizationSettings = () => {
  const { getAiAutoOptimizationSettings } = useAiContentApi();

  return useQuery({
    queryKey: [settingKeys.AI_CONTENT_SETTINGS],
    queryFn: getAiAutoOptimizationSettings,
  });
};

export const useUpdateAutoAiOptimizationSettings = () => {
  const { updateAiAutoOptimizationSettings } = useAiContentApi();
  const queryClient = useQueryClient();
  const queryKey = [settingKeys.AI_CONTENT_SETTINGS];

  return useMutation({
    mutationFn: updateAiAutoOptimizationSettings,
    onSuccess: (data) => {
      queryClient.setQueryData(queryKey, data);
      queryClient.invalidateQueries(queryKey);
    },
  });
};

//@ts-check
import { defaultValues } from "@/lib/form-default-value/autoAiOptimizationSettings";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import ResourceType from "storeseo-enums/resourceType";
import { autoAiOptimizationSchema } from "storeseo-schema/settings/autoAiOptimization";
import { useGetAutoAiOptimizationSettings } from "./index";

/**
 *
 * @param {{defaultSelected?: boolean, resourceType: keyof typeof ResourceType}} props
 * @returns {import('react-hook-form').UseFormReturn<import("yup").InferType<typeof autoAiOptimizationSchema>>}
 */
export default function useAutoAiOptimizationForm(props) {
  const { defaultSelected = false, resourceType } = props;
  const { data: autoAiOptimizationSettings } = useGetAutoAiOptimizationSettings();

  return useForm({
    resolver: yupResolver(autoAiOptimizationSchema),
    defaultValues,
    values: {
      meta: autoAiOptimizationSettings?.[resourceType]?.settings?.meta ?? defaultSelected,
      tags: autoAiOptimizationSettings?.[resourceType]?.settings?.tags ?? defaultSelected,
      imageAltText: autoAiOptimizationSettings?.[resourceType]?.settings?.imageAltText || defaultValues.imageAltText,
    },
  });
}

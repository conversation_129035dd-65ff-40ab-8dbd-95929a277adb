//@ts-check
import { useAppQuery } from "@/hooks";
import useEmailNotification<PERSON>pi from "@/hooks/apiHooks/useEmailNotificationApi";
import { showNotification } from "@/utility/helpers";
import { useTranslation } from "react-i18next";
import { useMutation, useQueryClient } from "react-query";

const EMAIL_NOTIFICATION_SETTINGS_QUERY_KEY = "email-notification-settings";

export const useEmailNotificationSettings = () => {
  const { getEmailNotificationSettings } = useEmailNotificationApi();

  return useAppQuery({
    queryKey: EMAIL_NOTIFICATION_SETTINGS_QUERY_KEY,
    queryFn: () => getEmailNotificationSettings(),
  });
};

export const useUpdateEmailNotificationSettings = () => {
  const { t } = useTranslation();
  const { postEmailNotificationSettings } = useEmailNotificationApi();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: postEmailNotificationSettings,
    onSuccess: () => {
      queryClient.invalidateQueries(EMAIL_NOTIFICATION_SETTINGS_QUERY_KEY);
      showNotification({
        message: t("Settings updated"),
      });
    },
  });
};

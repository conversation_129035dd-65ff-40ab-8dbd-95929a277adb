import { indexTableLimitFilterOptions } from "@/lib/util/optimize-seo";
import { ChoiceList } from "@shopify/polaris";
import { isEmpty } from "lodash";
import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSearchParams } from "react-router-dom";
import altTextOptimizationStatus from "storeseo-enums/altTextOptimization";

const optimizeStatus = [
  {
    label: altTextOptimizationStatus.labels[altTextOptimizationStatus.OPTIMIZED],
    value: altTextOptimizationStatus.OPTIMIZED,
  },
  {
    label: altTextOptimizationStatus.labels[altTextOptimizationStatus.NOT_OPTIMIZED],
    value: altTextOptimizationStatus.NOT_OPTIMIZED,
  },
  {
    label: altTextOptimizationStatus.labels[altTextOptimizationStatus.PENDING],
    value: altTextOptimizationStatus.PENDING,
  },
];

const hasAltTextChoiceOptions = [
  { label: "True", value: true },
  { label: "False", value: false },
];

const filtersOptions = [
  { label: "Status", key: "alt_status", choices: optimizeStatus, pinned: true, allowMultiple: true },
  {
    label: "Show images",
    key: "limit",
    choices: indexTableLimitFilterOptions,
    pinned: true,
    allowMultiple: false,
  },
  {
    label: "Has alt text",
    key: "has_alt_text",
    choices: hasAltTextChoiceOptions,
    pinned: true,
    allowMultiple: false,
  },
];

export default function useAltTextIndexFilters() {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const { status } = useMemo(() => Object.fromEntries(searchParams.entries()), [searchParams]);

  const [filterQuery, setFilterQuery] = useState("");
  const [selectedFilters, setSelectedFilters] = useState({ status: status ? status?.split(",") : null });

  const filters = useMemo(
    () =>
      filtersOptions.map(({ label, key, choices, pinned, allowMultiple }) => ({
        key,
        label: t(label),
        filter: (
          <ChoiceList
            title={t(label)}
            titleHidden
            choices={choices.map((choice) => ({
              ...choice,
              label: t(choice.label),
            }))}
            selected={selectedFilters?.[key] || []}
            onChange={(value) => {
              setSelectedFilters({
                ...selectedFilters,
                [key]: value,
              });
            }}
            allowMultiple={allowMultiple}
          />
        ),
        pinned,
      })),
    [filtersOptions, selectedFilters]
  );

  const appliedFilters = useMemo(
    () =>
      filtersOptions.reduce((acc, { key, label, choices }) => {
        if (selectedFilters?.[key] && !isEmpty(selectedFilters?.[key])) {
          const selectedLabels = selectedFilters?.[key]
            .map((value) => t(choices.find((choice) => choice.value === value)?.label))
            .join(", ");

          acc.push({
            key,
            label: `${t(label)}: ${selectedLabels || ""}`,
            onRemove: () => {
              setSelectedFilters({
                ...selectedFilters,
                [key]: null,
              });
            },
          });
        }

        return acc;
      }, []),
    [filtersOptions, selectedFilters]
  );

  useEffect(() => {
    const query = {};
    filtersOptions.forEach(({ key }) => {
      if (selectedFilters[key] && !isEmpty(selectedFilters[key])) {
        query[key] = selectedFilters[key].join(",");
      }
    });

    setFilterQuery(query);
  }, [selectedFilters]);

  return {
    filters,
    selectedFilters,
    setSelectedFilters,
    appliedFilters,
    filterQuery,
  };
}

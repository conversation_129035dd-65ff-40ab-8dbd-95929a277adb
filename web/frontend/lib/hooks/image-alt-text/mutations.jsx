//@ts-check
import { useAiContentApi, useProductApi } from "@/hooks";
import useUserAddon from "@/hooks/useUserAddon";
import { useMutation, useQueryClient } from "react-query";
import altTextOptimizationStatus from "storeseo-enums/altTextOptimization";

export function useGenerateImageAltText({ setGeneratedAltText, setIsGenerating, setIsRegenerating }) {
  const aiContentApi = useAiContentApi();
  const { updateAiOptimizerUsage } = useUserAddon();
  return useMutation({
    /**
     *
     * @param {{image: {id: string, src: string, product: object}}} data
     */
    mutationFn: (data) => aiContentApi.generateImageAltText({ ...data }, setGeneratedAltText, setIsGenerating),
    onMutate: () => {
      setGeneratedAltText("");
    },
    onSuccess: ({ creditUsage }) => {
      updateAiOptimizerUsage(creditUsage);
      setIsRegenerating(false);
    },
    onError: () => {
      setIsGenerating(false);
    },
  });
}

export function useOptimizeImageAltText({ queryKey, image }) {
  const queryClient = useQueryClient();
  const productApi = useProductApi();

  return useMutation({
    mutationFn: productApi.optimizeImgAltTexts,
    onSuccess: (data) => {
      const { images } = data;
      const findImage = images.find((img) => parseInt(img.id) === image.id);
      if (!findImage) return;

      /**
       * @type {{images: Array<any>}}
       */
      const currentImages = queryClient.getQueryData(queryKey);
      const updatedImages = currentImages?.images?.map((img) => {
        if (img.id === image.id) {
          return {
            ...img,
            alt_text: findImage.altText,
            alt_text_optimization_status: altTextOptimizationStatus.OPTIMIZED,
          };
        }
        return img;
      });

      queryClient.setQueryData(queryKey, {
        ...currentImages,
        images: updatedImages,
      });

      queryClient.invalidateQueries(queryKey);
    },
  });
}

//@ts-check

import EventTopics from "storeseo-enums/eventTopics";
import { EMAIL_NOTIFICATION_REPORT } from "storeseo-enums/settings/email-notification";

export const defaultValues = {
  [EMAIL_NOTIFICATION_REPORT]: false,
  items: {
    [EventTopics.WEEKLY_STORE_REPORT]: {
      enabled: false,
      day: "monday",
      time: "12",
      configurable: true,
      instantNotification: false,
    },
    [EventTopics.AUTO_AI_CONTENT_OPTIMIZATION_REPORT]: {
      enabled: false,
      day: "tuesday",
      time: "12",
      configurable: true,
      instantNotification: false,
    },
  },
};

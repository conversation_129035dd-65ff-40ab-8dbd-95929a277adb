//@ts-check
import {
  FINITE_RETURN_POLICY,
  FREE_RETURN,
  productMerchantSchema,
  RETURN_AT_KIOSK,
} from "storeseo-schema/local-seo/productMerchantSchema";

/**
 * @type {import("yup").InferType<typeof productMerchantSchema>}
 */
export const defaultValues = {
  shippingDestination: "",
  shippingCurrency: "",
  shippingAmount: null,
  shippingHandingMinDays: null,
  shippingHandingMaxDays: null,
  shippingTransitMinDays: null,
  shippingTransitMaxDays: null,
  returnApplicableCountries: "",
  returnPolicyCategory: FINITE_RETURN_POLICY,
  returnMethod: RETURN_AT_KIOSK,
  returnDays: null,
  returnFees: FREE_RETURN,
};

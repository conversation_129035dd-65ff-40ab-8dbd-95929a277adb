//@ts-check

/**
 *
 * @param {string} percentageValue
 * @param {"before" | "after"} state
 * @returns {import("@shopify/polaris/build/ts/src/components/Text").TextProps["tone"]}
 */
export const textTone = (percentageValue, state) => {
  const percentage = Number(percentageValue?.split("%")[0]);

  if (state === "before") {
    return percentage > 0 ? "critical" : "base";
  } else {
    return percentage > 0 ? "success" : "critical";
  }
};

import moment from "moment";
import AiOptimizationStatus from "storeseo-enums/aiOptimization";

export const indexTableTabs = [
  { content: "Products", path: "/optimize-seo" },
  { content: "Collections", path: "/optimize-seo/collections" },
  { content: "Pages", path: "/optimize-seo/pages" },
  { content: "Blog Posts", path: "/optimize-seo/articles" },
  { content: "Docs", path: "/optimize-seo/docs" },
];

export const optimizeStatusFilterOptions = [
  {
    label: "Optimized",
    value: "OPTIMIZED",
  },
  {
    label: "Need Improvement",
    value: "NEED_IMPROVEMENT",
  },
  {
    label: "Not Optimized",
    value: "NOT_OPTIMIZED",
  },
];

export const dateFilterOptions = [
  {
    label: "Today",
    value: {
      from: moment().startOf("day").format("YYYY-MM-DD"),
      to: moment().endOf("day").format("YYYY-MM-DD"),
    },
  },
  {
    label: "Last 7 days",
    value: {
      from: moment().subtract(7, "days").startOf("day").format("YYYY-MM-DD"),
      to: moment().endOf("day").format("YYYY-MM-DD"),
    },
  },
  {
    label: "Last 30 days",
    value: {
      from: moment().subtract(30, "days").startOf("day").format("YYYY-MM-DD"),
      to: moment().endOf("day").format("YYYY-MM-DD"),
    },
  },
  {
    label: "Last 90 days",
    value: {
      from: moment().subtract(90, "days").startOf("day").format("YYYY-MM-DD"),
      to: moment().endOf("day").format("YYYY-MM-DD"),
    },
  },
  {
    label: "Last 12 months",
    value: {
      from: moment().subtract(12, "months").startOf("day").format("YYYY-MM-DD"),
      to: moment().endOf("day").format("YYYY-MM-DD"),
    },
  },
  {
    label: "Custom",
    value: "customDate",
  },
];

export const indexTableLimitFilterOptions = [
  {
    label: "50",
    value: 50,
  },
  {
    label: "100",
    value: 100,
  },
  {
    label: "200",
    value: 200,
  },
];

export const aiOptimizedFilterOptions = [
  {
    label: "Optimized",
    value: AiOptimizationStatus.OPTIMIZED,
  },
  {
    label: "Not Optimized",
    value: AiOptimizationStatus.NOT_OPTIMIZED,
  },
  {
    label: "Pending",
    value: AiOptimizationStatus.PENDING,
  },
];

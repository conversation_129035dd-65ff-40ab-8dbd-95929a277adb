import moment from "moment";

export const dayRangeOptions = moment.weekdays().map((day) => ({ label: day, value: day.toLowerCase() }));

const startOfDay = moment().startOf("day");

export const openingAndEndingTimes = Array(48)
  .fill(null)
  .map((_, index) => {
    const time = startOfDay.add(index ? 30 : 0, "minutes").format("HH:mm");
    return {
      label: time,
      value: time,
    };
  });

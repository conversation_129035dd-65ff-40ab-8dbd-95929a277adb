// import "./assets/css/icon/style.css";
// import "./assets/css/style.min.css";
// import "./assets/dashboard/css/dashboard.min.css";
// import "./assets/css/custom.css";

import { onCLS, onLCP } from "web-vitals";
onLCP((metric) => console.log("LCP: ", { ...metric }), { reportAllChanges: true });
onCLS((metric) => console.log("CLS: ", { ...metric }), { reportAllChanges: true });

async function loadApp() {
  setTimeout(async () => {
    const { default: renderApp } = await import("./index");
    console.log("\n---");
    console.log("rendering app...");
    renderApp();
    console.log("Done!\n---");
  }, 1500);
}

loadApp();

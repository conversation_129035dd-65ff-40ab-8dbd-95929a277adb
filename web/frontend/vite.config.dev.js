import react from "@vitejs/plugin-react";
import { dirname } from "path";
import { fileURLToPath } from "url";
import { defineConfig, loadEnv } from "vite";

if (process.env.npm_lifecycle_event === "build" && !process.env.CI && !process.env.SHOPIFY_API_KEY) {
  console.warn(
    "\nBuilding the frontend app without an API key. The frontend build will not run without an API key. Set the SHOPIFY_API_KEY environment variable when running the build command.\n"
  );
}

const proxyOptions = {
  target: `http://127.0.0.1:${process.env.BACKEND_PORT}`,
  changeOrigin: false,
  secure: true,
  ws: false,
};

const host = process.env.HOST ? process.env.HOST.replace(/https?:\/\//, "") : "localhost";

let hmrConfig;
if (host === "localhost") {
  hmrConfig = {
    protocol: "ws",
    host: "localhost",
    port: 64999,
    clientPort: 64999,
  };
} else {
  hmrConfig = {
    protocol: "wss",
    host: host,
    port: process.env.FRONTEND_PORT,
    clientPort: 443,
  };
}

/**
 *
 * **Homepage**
 *
 * * index page,
 * * default layout,
 * * product components rendered in homepage,
 * * promotional components,
 * * statistics components
 *
 * @param {string} id
 * @returns {boolean}
 */
const isHomepageComponent = (id) => {
  const indexPage = /\/frontend\/pages\/index/gim;
  const defaultLayout = /\/frontend\/layouts\/Default/gim;
  const productComponent =
    /\/frontend\/components\/products\/.*(?:(?:filter)|(?:row)|(?:list)|(?:table)|(?:button))/gim;
  const promotionalComponent = /\/frontend\/components\/promotions/gim;
  const statisticsComponent = /\/frontend\/components\/statistics/gim;

  return (
    id.match(indexPage) ||
    id.match(defaultLayout) ||
    id.match(productComponent) ||
    id.match(promotionalComponent) ||
    id.match(statisticsComponent)
  );
};

/**
 *
 * **Subscripiton page after installation**
 *
 * * subscription page
 * * components rendered in subscription page
 * * subscription layout
 *
 * @param {string} id
 * @returns {boolean}
 */
const isSubscripitonPageComponent = (id) => {
  const subscriptionPage = /\/frontend\/pages\/subscription/gim;
  const subscriptionLayout = /\/frontend\/layouts\/Subscription/gim;
  const subscriptionComponent = /\/frontend\/components\/subscription/gim;

  return id.match(subscriptionPage) || id.match(subscriptionLayout) || id.match(subscriptionComponent);
};

export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd() + "/../", "FRONTEND_");
  return {
    root: dirname(fileURLToPath(import.meta.url)),
    plugins: [react()],
    define: {
      "process.env": { ...env },
    },
    resolve: {
      preserveSymlinks: true,
    },
    server: {
      host: "localhost",
      port: process.env.FRONTEND_PORT,
      hmr: hmrConfig,
      proxy: {
        "^/(?:(?:pages)|(?:products)|(?:blogs))?(\\?.*)?$": proxyOptions,
        "^/api(/|(\\?.*)?$)": proxyOptions,
        "^/api/v1(/|(\\?.*)?$)": proxyOptions,
        "^/webhooks.*": proxyOptions, // webhooks path
        "^/(?:(?:img)|(?:fonts)|(?:locales)).*": proxyOptions, // static assets in public folder
        "^/(?:(?:install)|(?:oauth2callback)).*": proxyOptions, // root app routes for install/third-party redirects
      },
    },
    build: {
      emptyOutDir: true,
      assetsInlineLimit: "7168",
      rollupOptions: {
        output: {
          entryFileNames: "[name]-[hash].js",
          chunkFileNames: "chunks/[name]-[hash].js",
          assetFileNames: "assets/[name]-[hash][extname]",
          sanitizeFileName: true,
          manualChunks: (id, { getModuleIds, getModuleInfo }) => {
            if (id.match(/node_modules\/.*(?:lodash-custom)/gim)) {
              return "lodash-custom";
              // return "common";
            }

            if (id.match(/\/frontend\/layouts\/Default/gim) || id.match(/\/frontend\/layouts\/Subscription/gim)) {
              return "layout";
            }

            if (id.match(/\/frontend\/components\/common\/submit/gim)) {
              return "common";
            }

            // if (id.match(/components\/common\/Footer/gim)) {
            //   console.log("dy importers: ", getModuleInfo(id).dynamicImporters);
            //   console.log("importers: ", getModuleInfo(id).importers);
            //   console.log("imported: ", getModuleInfo(id).importedIds);

            //   return "footer";
            // }

            if (id.match(/frontend\/config/gim) || id.match(/storeseo-enums/gim)) {
              // return "config";
              return "common";
            }

            // if (id.match(/node_modules\/.*(?:lodash-custom)/gim)) {
            //   return "lodash-custom";
            // }

            if (id.match(/node_modules\/.*(?:react-dom)/gim)) {
              return "react-dom";
              // return "react-app";
            }

            if (
              id.match(/node_modules\/.*(?:(?:react)|(?:redux))/gim) &&
              !id.match(
                /node_modules\/.*(?:(?:react-router)|(?:react-query)|(?:redux)|(?:shopify)|(?:i18n)|(?:pdf)|(?:confirm)|(?:cropper)|(?:date)|(?:select)|(?:tags)|(?:toast))/gim
              ) &&
              id.match(/node_modules\/react\//gim)
            ) {
              return "react-app";
            }

            if (id.match(/react\/jsx-runtime/gim)) {
              // return "jsx-runtime";
              return "react-app";
            }

            if (id.match(/node_modules.*(?:(?:@babel)|(?:@emotion)|(?:float))/gim)) {
              // if (id.includes("@emotion")) return "emotion";
              // if (id.includes("float")) return "float";
              // return "babel";
              return "react-app";
            }

            if (id.match(/node_modules\/react-select/gim)) {
              // console.log("\n---");
              // console.log("ID: ", id);
              // console.log("dy importers: ", getModuleInfo(id).dynamicImporters);
              // console.log("importers: ", getModuleInfo(id).importers);
              // console.log("imported: ", getModuleInfo(id).importedIds);
              // console.log("---\n");
              return "react-select";
            }

            if (id.match(/node_modules\/.*(?:react-router)/gim)) {
              return "react-router";
              // return "react-app";
            }

            // if (id.match(/node_modules\/.*(?:react-query)/gim)) {
            // return "react-query";
            //   // return "redux-react-query";
            //   return id.replace(/.*node_modules\//gim, "").split("/")[0];
            // }

            if (id.match(/node_modules\/.*(?:redux)/gim)) {
              // return "redux-app";
              // return "redux-react-query";
              // return id.replace(/.*node_modules\//gim, "").split("/")[0];
            }

            if (id.match(/node_modules\/.*(?:react-query)/gim)) {
              return "react-query";
              // return "redux-react-query";
            }

            if (id.match(/node_modules\/.*(?:redux)/gim)) {
              return "redux-app";
              // return "redux-react-query";
            }

            if (id.match(/frontend\/hooks/gim)) {
              // return "redux-react-query";
              // return "react-app";
              // return "common";
              // return "layout";
              // return "hooks";
            }

            if (id.match(/node_modules\/@shopify\/app-bridge-react/gim)) {
              return "shopify-app-bridge";
              // return "shopify-vendor";
            }

            if (id.match(/node_modules\/@shopify/gim)) {
              // return "shopify-vendor";
              // console.log("id: ", id.replace(/.*node_modules\/@shopify\//gim, ""));
              return id.replace(/.*node_modules\/@shopify\//gim, "").split("/")[0];
            }

            if (id.match(/node_modules\/.*(?:i18n)/gim)) {
              // return "shopify-vendor";
              // console.log("id: ", id.replace(/.*node_modules\/@shopify\//gim, ""));
              return id.replace(/.*node_modules\//gim, "").split("/")[0];
            }

            if (id.match(/node_modules\/.*(?:i18n)/gim) || id.match(/.*18n/gim)) {
              // return "i18n";
              // return "common";
            }

            if (id.match(/node_modules\/.*(?:pusher)/gim)) {
              return "pusher";
            }

            if (id.match(/\/frontend\/components\/loader\/(?:(?:loader)|(?:table)|(?:skeleton))/gim)) {
              // console.log("id: ", id);
              return "common";
            }

            if (id.match(/\/frontend\/components\/statistics/gim)) {
              // return "statistics";
              // return "products";
              // return "layout";
              // return "common";
            }

            if (id.match(/\/frontend\/components\/products\/.*(?:(?:row)|(?:list)|(?:table))/gim)) {
              // return "products";
              // return "layout";
              // return "common";
            }

            if (id.match(/\/frontend\/pages\/(?:(?:index)|(?:subscription))/gim)) {
              return "layout";
            }

            // if (isHomepageComponent(id) || isSubscripitonPageComponent(id)) {
            //   return "homepage";
            // } else if (isOnboardPagesChunk(id)) {
            //   return "onboarding-pages";
            //   // } else if (isCommonChunk(id)) {
            //   //   return "common-chunk";
            // } else if (isSettingsPagesComponent(id)) {
            //   return "settings-pages";
            // } else if (id.includes("/pages/")) {
            //   return "route-pages";
            // }
          },
        },
      },
    },
  };
});

import { Block<PERSON>ta<PERSON>, <PERSON>, <PERSON>, <PERSON>, InlineStack, Text, useBreakpoints } from "@shopify/polaris";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

const AnalysisScanning = ({ onSkip }) => {
  const { t } = useTranslation();
  const [contentWidth, setContentWidth] = useState("60%");
  const breakpoints = useBreakpoints();

  useEffect(() => {
    if (breakpoints.mdDown) {
      setContentWidth("100%");
    } else {
      setContentWidth("60%");
    }
  }, [breakpoints]);

  return (
    <Card>
      <InlineStack align="center">
        <Box
          padding="800"
          width={contentWidth}
        >
          <BlockStack
            gap={"400"}
            align="center"
            inlineAlign="center"
          >
            <img
              src="https://cdn.storeseo.com/onboarding/scanner.gif"
              alt="StoreSEO Scanner"
              width={172}
              height={168}
              loading="eager"
            />

            <Text
              as="h4"
              variant="headingLg"
              alignment="center"
            >
              {t("Product scanning has started - hang tight!")}
            </Text>

            <Text
              as="p"
              tone="subdued"
              alignment="center"
            >
              {t(
                "If you have a large number of products, the process will keep running in the background which may take a moment. So do not worry if you do not see all products right away, we will notify you when it is done."
              )}
            </Text>

            <InlineStack
              align="center"
              gap={"200"}
            >
              {onSkip && <Button onClick={onSkip}>{t("Skip Scanning")}</Button>}
            </InlineStack>
          </BlockStack>
        </Box>
      </InlineStack>
    </Card>
  );
};

export default AnalysisScanning;

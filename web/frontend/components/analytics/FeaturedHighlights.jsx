import metrics from "storeseo-enums/analytics/metrics";
import { useAppQuery } from "../../hooks";
import { useAnalyticsApi } from "../../hooks/apiHooks/useAnalyticsApi";
import queryKeys from "../../utility/queryKeys";
import { useTranslation } from "react-i18next";
import { useState } from "react";
import classNames from "classnames";
import {
  convertSecondsToReadableTimeValue,
  formatAnalyticsMetricDataForChart,
  formatDate,
  getReadableStringFormatFromNumber,
} from "../../utility/helpers";
import moment from "moment";
import FeaturedHighlightLoader from "../loader/FeaturedHighlightLoader";
import DataComparison from "./DataComparison";
import { Badge, BlockStack, Card, Divider, Grid, InlineGrid, InlineStack, Text } from "@shopify/polaris";
import { tabItemsStyle } from "../../styles/common";
import LineChart from "../charts/LineChart";

const Tab = ({ active = false, onClick, ...item }) => {
  const { t } = useTranslation();

  const { label, data, dataKey } = item;

  return (
    <div
      className={classNames("tab-nav-item", { active })}
      style={tabItemsStyle}
      onClick={onClick}
    >
      <BlockStack gap="200">
        <Text
          as="p"
          tone="subdued"
        >
          {t(label)}
        </Text>
        <InlineStack
          blockAlign="center"
          gap="200"
        >
          <Text
            variant="headingXl"
            as="h3"
          >
            {data.total}
          </Text>
          <DataComparison
            currentValue={data.total}
            prevValue={data.prevData?.total}
            formatter={getReadableStringFormatFromNumber}
            boldVersion
          />
        </InlineStack>
      </BlockStack>
    </div>
  );
};

export default function FeaturedHighlights({ startDate, endDate }) {
  const analyticsApi = useAnalyticsApi();
  const { t } = useTranslation();

  const [selectedTab, setSelectedTab] = useState({});

  const dateRange = {
    startDate: moment(startDate).format("YYYY-MM-DD"),
    endDate: moment(endDate).format("YYYY-MM-DD"),
  };

  const { data: usersMetrics, isFetching } = useAppQuery({
    queryKey: `${queryKeys.ANALYTICS_OVERVIEW_USERS_METRICS}-${dateRange.startDate}-${dateRange.endDate}`,
    queryFn: () =>
      analyticsApi.getReportByDate({
        dateRange,
        metrics: [metrics.TOTAL_USERS, metrics.NEW_USERS, metrics.USER_ENGAGEMENT_DURATION],
      }),
  });

  const userEngagementData = usersMetrics?.find((m) => m.metricName === metrics.USER_ENGAGEMENT_DURATION);

  const users = {
    label: "Users",
    dataKey: metrics.TOTAL_USERS,
    data: usersMetrics?.find((m) => m.metricName === metrics.TOTAL_USERS),
    color: "rgba(0, 148, 213, 1)",
  };

  const newUsers = {
    label: "New Users",
    dataKey: metrics.NEW_USERS,
    data: usersMetrics?.find((m) => m.metricName === metrics.NEW_USERS),
    color: "rgba(128, 81, 255, 1)",
  };

  const {
    series: { 0: usersChartSeries },
    categories: userChartCategories,
    xaxisType: userChartXAxisType,
  } = formatAnalyticsMetricDataForChart(users.data, users.label);
  const {
    series: { 0: newUsersChartSeries },
  } = formatAnalyticsMetricDataForChart(newUsers.data, newUsers.label);

  const averageEngagementTime = {
    label: "Avr. Engagement Time",
    dataKey: metrics.USER_ENGAGEMENT_DURATION,
    data: userEngagementData
      ? {
          ...userEngagementData,
          total: Math.floor(userEngagementData.total / 7),
          prevData: {
            ...userEngagementData.prevData,
            total: Math.floor(userEngagementData.prevData.total / 7),
          },
        }
      : null,
    color: "rgba(0, 66, 153, 1)",
  };

  return (
    <InlineGrid
      columns={3}
      gap="400"
    >
      {isFetching && <FeaturedHighlightLoader />}
      {!isFetching && (
        <>
          <Grid.Cell columnSpan={{ lg: 2, md: 3, sm: 3 }}>
            <Card>
              <BlockStack gap="300">
                <Text
                  as="p"
                  fontWeight="medium"
                  variant="bodySm"
                >
                  {t("Users")}
                </Text>

                <InlineStack
                  blockAlign="end"
                  gap="025"
                >
                  <Text
                    variant="headingLg"
                    as="h3"
                  >
                    {getReadableStringFormatFromNumber(users.data?.total + newUsers.data?.total || 0)}
                  </Text>
                  <DataComparison
                    currentValue={users.data?.total + newUsers.data?.total}
                    prevValue={users.data?.prevData?.total + newUsers.data?.prevData?.total}
                    formatter={getReadableStringFormatFromNumber}
                  />
                </InlineStack>
                <Divider />
                <div style={{ width: "100%", height: "fit-content" }}>
                  <LineChart
                    width="100%"
                    height="200px"
                    showAxisLabel
                    series={[usersChartSeries, newUsersChartSeries]}
                    categories={userChartCategories}
                    xaxisType={userChartXAxisType}
                    colors={[users.color, newUsers.color]}
                  />
                </div>

                <InlineStack
                  align="space-between"
                  gap="400"
                >
                  <Badge>
                    {formatDate(startDate, "DD MMM")} - {formatDate(endDate, "DD MMM")}
                  </Badge>
                  <InlineStack
                    gap={"400"}
                    align="end"
                  >
                    <InlineStack
                      align="end"
                      blockAlign="center"
                      gap={"150"}
                    >
                      <div
                        style={{
                          width: "10px",
                          height: "10px",
                          backgroundColor: users.color,
                        }}
                      ></div>
                      <Text
                        as="p"
                        tone="subdued"
                        variant="bodySm"
                      >
                        Users
                      </Text>
                    </InlineStack>

                    <InlineStack
                      align="end"
                      blockAlign="center"
                      gap={"150"}
                    >
                      <div
                        style={{
                          width: "10px",
                          height: "10px",
                          backgroundColor: newUsers.color,
                        }}
                      ></div>
                      <Text
                        as="p"
                        tone="subdued"
                        variant="bodySm"
                      >
                        New Users
                      </Text>
                    </InlineStack>
                  </InlineStack>
                </InlineStack>
              </BlockStack>
            </Card>
          </Grid.Cell>
          <Grid.Cell columnSpan={{ lg: 1, md: 3, sm: 3 }}>
            <Card>
              <BlockStack gap="200">
                <Text
                  as="p"
                  fontWeight="medium"
                  variant="bodySm"
                >
                  {t(averageEngagementTime.label)}
                </Text>

                <InlineStack
                  blockAlign="end"
                  gap="025"
                >
                  <Text
                    variant="headingLg"
                    as="h3"
                  >
                    {convertSecondsToReadableTimeValue(averageEngagementTime.data?.total || 0)}
                  </Text>
                  <DataComparison
                    currentValue={averageEngagementTime.data?.total}
                    prevValue={averageEngagementTime.data?.prevData?.total}
                    formatter={convertSecondsToReadableTimeValue}
                  />
                </InlineStack>
                <Divider />
                <div style={{ width: "100%", height: "fit-content" }}>
                  <LineChart
                    width="100%"
                    height="218px"
                    showAxisLabel
                    color={averageEngagementTime.color}
                    {...formatAnalyticsMetricDataForChart(averageEngagementTime.data, averageEngagementTime.label)}
                  />
                </div>

                <InlineStack
                  align="space-between"
                  gap="400"
                >
                  <Badge>
                    {formatDate(startDate, "DD MMM")} - {formatDate(endDate, "DD MMM")}
                  </Badge>
                  <InlineStack
                    align="end"
                    blockAlign="center"
                    gap={"150"}
                  >
                    <div
                      style={{
                        width: "10px",
                        height: "10px",
                        backgroundColor: averageEngagementTime.color,
                      }}
                    ></div>
                    <Text
                      as="p"
                      tone="subdued"
                      variant="bodySm"
                    >
                      Avr. Engagement Time
                    </Text>
                  </InlineStack>
                </InlineStack>
              </BlockStack>
            </Card>
          </Grid.Cell>
        </>
      )}
    </InlineGrid>
  );
}

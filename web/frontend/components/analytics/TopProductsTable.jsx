import { useTranslation } from "react-i18next";
import { useAppQuery } from "../../hooks";
import { useAnalyticsApi } from "../../hooks/apiHooks/useAnalyticsApi";
import queryKeys from "../../utility/queryKeys";
import { convertSecondsToReadableTimeValue, getReadableStringFormatFromNumber } from "../../utility/helpers";
import DataComparison from "./DataComparison";
import moment from "moment";
import { Box, Card, IndexTable, InlineStack, Text } from "@shopify/polaris";
import TableRowsSkeleton from "../loader/TableRowsSkeleton";

const TABLE_COLUMNS = [
  { label: "#", isActive: true, className: "w__50" },
  { label: "Product Name", isActive: true, className: "flex__1" },
  { label: "Impressions", isActive: true, className: "w__200" },
  { label: "Users", isActive: true, className: "w__200" },
  { label: "Average Session", isActive: true, className: "w__200" },
];

export default function TopProductsTable({ startDate, endDate }) {
  const analyticsApi = useAnalyticsApi();
  const { t } = useTranslation();

  const dateRange = {
    startDate: moment(startDate).format("YYYY-MM-DD"),
    endDate: moment(endDate).format("YYYY-MM-DD"),
  };

  const { data: products = [], isFetching } = useAppQuery({
    queryKey: `${queryKeys.TOP_PRODUCTS_ANALYTICS}-${dateRange.startDate}-${dateRange.endDate}`,
    queryFn: () => analyticsApi.getProductsReport({ dateRange }),
  });

  return (
    <div>
      <Box paddingBlockEnd="500">
        <Text
          as="h3"
          variant="headingLg"
        >
          {t("Top Products")}
        </Text>
      </Box>
      {!isFetching && products.length == 0 ? (
        <Card>
          <Text
            tone="subdued"
            alignment="center"
          >
            {t("No product analytics found.")}
          </Text>
        </Card>
      ) : null}
      {isFetching || products.length ? (
        <Card padding={0}>
          <IndexTable
            headings={TABLE_COLUMNS.map((c) => ({ title: t(c.label) }))}
            itemCount={isFetching ? 5 : products.length}
            resourceName={{
              singular: "product analytic",
              plural: "product analytics",
            }}
            selectable={false}
          >
            {isFetching && <TableRowsSkeleton rows={5} />}
            {products.map((p, idx) => (
              <IndexTable.Row
                id={idx}
                position={idx}
              >
                <IndexTable.Cell>
                  <Text>{idx + 1}</Text>
                </IndexTable.Cell>
                <IndexTable.Cell className="break_coll_content">
                  <Text
                    fontWeight="medium"
                    variant="bodySm"
                  >
                    {p.title}
                  </Text>
                </IndexTable.Cell>
                <IndexTable.Cell>
                  <InlineStack
                    gap="200"
                    blockAlign="center"
                  >
                    <Text
                      variant="bodySm"
                      fontWeight="medium"
                    >
                      {p.metrics.screenPageViews}{" "}
                    </Text>
                    <DataComparison
                      currentValue={p.metrics.screenPageViews}
                      prevValue={p.prevData?.metrics?.screenPageViews}
                      formatter={getReadableStringFormatFromNumber}
                    />
                  </InlineStack>
                </IndexTable.Cell>
                <IndexTable.Cell>
                  <InlineStack
                    gap="200"
                    blockAlign="center"
                  >
                    <Text
                      fontWeight="medium"
                      variant="bodySm"
                    >
                      {p.metrics.totalUsers}{" "}
                    </Text>
                    <DataComparison
                      currentValue={p.metrics.totalUsers}
                      prevValue={p.prevData?.metrics?.totalUsers}
                      formatter={getReadableStringFormatFromNumber}
                    />
                  </InlineStack>
                </IndexTable.Cell>

                <IndexTable.Cell>
                  <InlineStack
                    gap="200"
                    blockAlign="center"
                  >
                    <Text
                      fontWeight="medium"
                      variant="bodySm"
                    >
                      {convertSecondsToReadableTimeValue(p.metrics.averageSessionDuration)}{" "}
                    </Text>
                    <DataComparison
                      currentValue={p.metrics.averageSessionDuration}
                      prevValue={p.prevData?.metrics?.averageSessionDuration}
                      formatter={convertSecondsToReadableTimeValue}
                    />
                  </InlineStack>
                </IndexTable.Cell>
              </IndexTable.Row>
            ))}
          </IndexTable>
        </Card>
      ) : null}
    </div>
  );
}

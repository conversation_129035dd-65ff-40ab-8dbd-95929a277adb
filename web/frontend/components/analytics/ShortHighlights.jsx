import metrics from "storeseo-enums/analytics/metrics";
import { useAppQuery } from "../../hooks";
import { useAnalyticsApi } from "../../hooks/apiHooks/useAnalyticsApi";
import queryKeys from "../../utility/queryKeys";
import ShortHighlightCard from "./ShortHighlightCard";
import moment from "moment";
import ShortHighlightLoader from "../loader/ShortHighlightLoader";
import { InlineGrid } from "@shopify/polaris";

export default function ShortHighlights({ startDate, endDate }) {
  const analyticsApi = useAnalyticsApi();

  const dateRange = {
    startDate: moment(startDate).format("YYYY-MM-DD"),
    endDate: moment(endDate).format("YYYY-MM-DD"),
  };

  const { data: searchMetrics, isFetching } = useAppQuery({
    queryKey: `${queryKeys.ANALYTICS_OVERVIEW_SEARCH_METRICS}-${dateRange.startDate}-${dateRange.endDate}`,
    queryFn: () =>
      analyticsApi.getReportByDate({
        dateRange,
        metrics: [
          metrics.ORGANIC_GOOGLE_SEARCH_CLICKS,
          metrics.ORGANIC_GOOGLE_SEARCH_IMPRESSIONS,
          metrics.ORGANIC_GOOGLE_SEARCH_AVERAGE_POSITION,
        ],
      }),
  });

  // const { data: keywordReport, isFetching: isFetching2 } = useAppQuery({
  //   queryKey: `${queryKeys.KEYWORD_METRICS}-${dateRange.startDate}-${dateRange.endDate}`,
  //   queryFn: () =>
  //     analyticsApi.getKeywordsReport({
  //       dateRange,
  //     }),
  // });

  const highlights = [
    {
      label: "Search Traffic",
      showGraph: true,
      data: searchMetrics?.find((m) => m.metricName === metrics.ORGANIC_GOOGLE_SEARCH_CLICKS),
      graphColor: "rgba(161, 237, 208, 1)",
    },
    {
      label: "Search Impressions",
      showGraph: true,
      data: searchMetrics?.find((m) => m.metricName === metrics.ORGANIC_GOOGLE_SEARCH_IMPRESSIONS),
      graphColor: "rgba(250, 229, 178, 1)",
    },
    // {
    //   label: "Total Keywords",
    //   showGraph: false,
    //   totalKey: "totalUsers",
    //   data: keywordReport,
    // },
    {
      label: "Avg. Position",
      showGraph: true,
      data: searchMetrics?.find((m) => m.metricName === metrics.ORGANIC_GOOGLE_SEARCH_AVERAGE_POSITION),
      graphColor: "rgba(251, 197, 188, 1)",
    },
  ];

  return (
    <InlineGrid
      columns={{ xs: 1, sm: 1, md: 3, lg: 3, xl: 3 }}
      gap="400"
    >
      {isFetching &&
        highlights.map((h, idx) => (
          <ShortHighlightLoader
            key={idx}
            title={h.label}
          />
        ))}
      {!isFetching &&
        highlights.map((h, idx) => (
          <ShortHighlightCard
            key={idx}
            {...h}
          />
        ))}
    </InlineGrid>
  );
}

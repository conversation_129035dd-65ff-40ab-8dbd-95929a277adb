import { Banner, Text } from "@shopify/polaris";
import { GlobeIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

export default function ConnectToGoogleWarning() {
  const { t } = useTranslation();
  const navigate = useNavigate();

  return (
    <Banner
      tone="warning"
      icon={GlobeIcon}
      title={t("Connect with Google Services")}
      secondaryAction={{
        content: t("Connect to Google"),
        onAction: () => navigate("/settings/google-analytics"),
      }}
    >
      <Text>
        {t(
          "Integrate StoreSEO with Google Search Console and Google Analytics to track your store traffic, and understand how people find and interact with your store."
        )}
      </Text>
    </Banner>
  );
}

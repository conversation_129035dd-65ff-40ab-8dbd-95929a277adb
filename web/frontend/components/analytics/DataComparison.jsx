import { Icon, InlineStack, Text } from "@shopify/polaris";
import { CaretDownIcon, CaretUpIcon } from "@shopify/polaris-icons";

const DataComparison = ({ currentValue, prevValue = 0, formatter, boldVersion = false }) => {
  const valueDifferenceWithPrevCycle = currentValue - prevValue;
  const positiveChange = valueDifferenceWithPrevCycle >= 0;

  const comparisonIcon = positiveChange ? CaretUpIcon : CaretDownIcon;
  const textClassName = positiveChange ? "success" : "critical";

  return (
    <InlineStack
      blockAlign="center"
      align="start"
    >
      <div>
        <Icon
          source={comparisonIcon}
          tone={textClassName}
        />
      </div>
      <Text
        tone={textClassName}
        variant={boldVersion ? "bodyMd" : "bodySm"}
        fontWeight={boldVersion ? "medium" : "regular"}
      >
        {formatter ? formatter(Math.abs(valueDifferenceWithPrevCycle)) : valueDifferenceWithPrevCycle}
      </Text>
    </InlineStack>
  );
};

export default DataComparison;

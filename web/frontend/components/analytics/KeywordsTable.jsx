import { useTranslation } from "react-i18next";
import { useAppQuery } from "../../hooks";
import { useAnalyticsApi } from "../../hooks/apiHooks/useAnalyticsApi";
import queryKeys from "../../utility/queryKeys";
import { getReadableStringFormatFromNumber } from "../../utility/helpers";
import DataComparison from "./DataComparison";
import moment from "moment";
import { Box, Card, IndexTable, InlineStack, Text } from "@shopify/polaris";
import TableRowsSkeleton from "../loader/TableRowsSkeleton";
import EmptyPage from "../common/EmptyPage";

const TABLE_COLUMNS = [
  { label: "#", isActive: true, className: "w__50" },
  { label: "Keywords", isActive: true, className: "flex__1" },
  { label: "Impressions", isActive: true, className: "w__200" },
  { label: "Clicks", isActive: true, className: "w__200" },
  { label: "Position", isActive: true, className: "w__200" },
  { label: "CTR", isActive: true, className: "w__200" },
];

export default function KeywordsTable({ startDate, endDate }) {
  const analyticsApi = useAnalyticsApi();
  const { t } = useTranslation();

  const dateRange = {
    startDate: moment(startDate).format("YYYY-MM-DD"),
    endDate: moment(endDate).format("YYYY-MM-DD"),
  };

  const { data: queries = [], isFetching } = useAppQuery({
    queryKey: `${queryKeys.TOP_KEYWORDS_ANALYTICS}-${dateRange.startDate}-${dateRange.endDate}`,
    queryFn: () => analyticsApi.getSearchConsoleQueriesReport({ dateRange }),
  });

  return (
    <div>
      <Box
        // paddingBlockStart="600"
        paddingBlockEnd="500"
      >
        <Text
          as="h3"
          variant="headingLg"
        >
          {t("Keywords")}
        </Text>
      </Box>

      {!isFetching && queries.length == 0 ? (
        <Card>
          <Text
            tone="subdued"
            alignment="center"
          >
            {t("No keyword analytics found.")}
          </Text>
        </Card>
      ) : null}
      {isFetching || queries.length ? (
        <Card padding={"0"}>
          <IndexTable
            headings={TABLE_COLUMNS.map((c) => ({ title: t(c.label) }))}
            itemCount={isFetching ? 5 : queries.length}
            resourceName={{
              singular: "keyword analytic",
              plural: "keyword analytics",
            }}
            selectable={false}
          >
            {isFetching && <TableRowsSkeleton cols={TABLE_COLUMNS.length} />}
            {queries.map((q, idx) => (
              <IndexTable.Row
                id={idx}
                position={idx}
              >
                <IndexTable.Cell>
                  <Text>{idx + 1}</Text>
                </IndexTable.Cell>
                <IndexTable.Cell className="break_coll_content">
                  <Text
                    fontWeight="medium"
                    variant="bodySm"
                  >
                    {q.query}
                  </Text>
                </IndexTable.Cell>
                <IndexTable.Cell>
                  <InlineStack
                    gap="200"
                    blockAlign="center"
                  >
                    <Text
                      fontWeight="medium"
                      variant="bodySm"
                    >
                      {q.impressions}{" "}
                    </Text>
                    <DataComparison
                      currentValue={q.impressions}
                      prevValue={q.prevData?.impressions}
                      formatter={getReadableStringFormatFromNumber}
                    />
                  </InlineStack>
                </IndexTable.Cell>
                <IndexTable.Cell>
                  <InlineStack
                    gap="200"
                    blockAlign="center"
                  >
                    <Text
                      fontWeight="medium"
                      variant="bodySm"
                    >
                      {q.clicks}{" "}
                    </Text>
                    <DataComparison
                      currentValue={q.clicks}
                      prevValue={q.prevData?.clicks}
                      formatter={getReadableStringFormatFromNumber}
                    />
                  </InlineStack>
                </IndexTable.Cell>
                <IndexTable.Cell>
                  <InlineStack
                    gap="200"
                    blockAlign="center"
                  >
                    <Text
                      fontWeight="medium"
                      variant="bodySm"
                    >
                      {q.position}{" "}
                    </Text>
                    <DataComparison
                      currentValue={q.position}
                      prevValue={q.prevData?.position}
                      formatter={getReadableStringFormatFromNumber}
                    />
                  </InlineStack>
                </IndexTable.Cell>
                <IndexTable.Cell>
                  <InlineStack
                    gap="200"
                    blockAlign="center"
                  >
                    <Text
                      fontWeight="medium"
                      variant="bodySm"
                    >
                      {q.ctr}{" "}
                    </Text>
                    <DataComparison
                      currentValue={q.ctr}
                      prevValue={q.prevData?.ctr}
                      formatter={getReadableStringFormatFromNumber}
                    />
                  </InlineStack>
                </IndexTable.Cell>
                {/* <IndexTable.Cell>
                <div style={{ width: "10vw" }}>
                  <div style={{ width: "100%" }}>
                    <AreaChart
                      width="100%"
                      height="auto"
                      color={"#FF4560"}
                      showTooltip={false}
                      showGrid={false}
                      {...serializeDateValueMapForChart({ data: q.positionByDates, label: "History", dateRange })}
                    />
                  </div>
                </div>
              </IndexTable.Cell> */}
              </IndexTable.Row>
            ))}
          </IndexTable>
        </Card>
      ) : null}
    </div>
  );
}

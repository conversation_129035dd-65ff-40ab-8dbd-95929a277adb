import { useTranslation } from "react-i18next";
import { getReadableStringFormatFromNumber } from "../../utility/helpers";
import DataComparison from "./DataComparison";
import { BlockStack, Card, InlineStack, Text } from "@shopify/polaris";

export default function ShortHighlightCard({ label, showGraph, graphColor, totalKey = "total", data }) {
  const { t } = useTranslation();

  return (
    <Card>
      <BlockStack gap="200">
        <Text
          as="p"
          fontWeight="medium"
        >
          {t(label)}
        </Text>
        <InlineStack
          gap={"050"}
          align="start"
          blockAlign="end"
        >
          <Text
            variant="headingLg"
            as="h3"
          >
            {getReadableStringFormatFromNumber(data?.[totalKey])}
          </Text>
          <DataComparison
            currentValue={data?.[totalKey]}
            prevValue={data?.prevData?.[totalKey]}
            formatter={getReadableStringFormatFromNumber}
            className=""
          />
        </InlineStack>
      </BlockStack>
    </Card>
  );
}

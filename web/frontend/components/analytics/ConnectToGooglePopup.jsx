import { But<PERSON> } from "@shopify/polaris";
import classNames from "classnames";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

const ConnectToGooglePopup = () => {
  // const popUpRef = useRef();
  const { t } = useTranslation();
  const navigate = useNavigate();

  useEffect(() => {
    // if (fullVersion) popUpRef?.current?.scrollIntoView({ behavior: "smooth" });
  }, []);

  return (
    // <Modal
    //   open
    //   small
    //   title={t("Connect with Google Services")}
    //   primaryAction={{ content: t("Connect To Google"), onAction: () => navigate("/settings/google-integration") }}
    // >
    //   <Modal.Section>
    //     <Text as="p">
    //       {t(
    //         "Integrate StoreSEO with Google Search Console and Google Analytics to track your store traffic, and understand how people find and interact with your store."
    //       )}
    //     </Text>
    //   </Modal.Section>
    // </Modal>
    <div
      className="modal-wrap"
      // ref={popUpRef}
    >
      <div className={classNames("modal", { "modal-sm": true })}>
        <div className="modal-header">
          <h3 className="modal-title">{t("Connect with Google Services")}</h3>
        </div>
        <div className="modal-body">
          {t(
            "Integrate StoreSEO with Google Search Console and Google Analytics to track your store traffic, and understand how people find and interact with your store."
          )}
        </div>
        <div className="modal-footer">
          <Button
            onClick={() => navigate("/settings/google-analytics")}
            variant="primary"
          >
            {t("Connect To Google")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ConnectToGooglePopup;

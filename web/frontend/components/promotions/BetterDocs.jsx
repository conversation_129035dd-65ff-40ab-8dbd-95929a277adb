import { CalloutCard, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useShopApi } from "../../hooks";
import { updateUser } from "../../store/features/User";

function BetterDocs() {
  const { t } = useTranslation();
  const user = useSelector((state) => state.user);
  const dispatch = useDispatch();
  const shopApi = useShopApi();

  const handleClose = async () => {
    const { hideBetterDocsPromo } = await shopApi.hideBetterDocsPromoTemporarily();
    dispatch(updateUser({ hideBetterDocsPromo }));
  };

  // const link = `https://kb-app.betterdocs.co/login?shop=${user.shop}`;
  const link = `https://apps.shopify.com/betterdocs-knowledgebase`;

  if (user?.hideBetterDocsPromo) return null;

  return (
    <div className="betterdocs-faq">
      <CalloutCard
        title={t("Create FAQ & store documentation with BetterDocs")}
        illustration="/img/svg/better-docs.svg"
        primaryAction={{
          content: t("Check it out"),
          url: link,
          target: "_blank",
        }}
        onDismiss={handleClose}
      >
        <Text
          as="p"
          tone="subdued"
        >
          {t(
            "BetterDocs is the ultimate knowledge base solution to create FAQs and store documentation. It comes with Instant Answers with WhatsApp & Messenger integrations, Quick Order-Tracking, in-depth Analytics, and many more exclusive features."
          )}
        </Text>
      </CalloutCard>
    </div>
  );
}

export default BetterDocs;

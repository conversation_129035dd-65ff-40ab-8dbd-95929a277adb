import React from "react";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";

const CampaignBanner = ({ isDashboard = false }) => {
  const user = useSelector((state) => state.user);
  const campaign = useSelector((state) => state.campaign);

  if (isDashboard) {
    return (
      <div className="ss-row mb30">
        <Link to="/subscription">
          <img
            src={campaign?.dashboard_banner}
            alt={campaign?.name}
            loading="lazy"
            style={{ width: "100%" }}
          />
        </Link>
      </div>
    );
  }
  return (
    <div className="ss-row">
      <img
        src={campaign?.subs_banner}
        alt={campaign?.name}
        loading="eager"
        style={{ width: "100%" }}
      />
    </div>
  );
};

export default CampaignBanner;

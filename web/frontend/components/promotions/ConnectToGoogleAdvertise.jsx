import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { updateUser } from "../../store/features/User.js";
import { useShopApi } from "../../hooks/index.js";

const ConnectToGoogleAdvertise = () => {
  const { t } = useTranslation();
  const user = useSelector((state) => state.user);
  const dispatch = useDispatch();
  const { hideAdvertiseTemporarily } = useShopApi();

  if (user.isPremium || user.hideAdvertise) return null;

  const handleClose = async (e) => {
    e.preventDefault();
    const { hideAdvertise } = await hideAdvertiseTemporarily();
    dispatch(updateUser({ hideAdvertise }));
  };

  return (
    <div className="ss-container--fluid">
      <div className="background__white onboard__card onboard__card--flex align__center justify__between mb30 p30 radius-20 ss-relative">
        <a
          href="#"
          className="ss-close"
          onClick={handleClose}
        >
          <i className="ss-icon ss-times"></i>
        </a>
        <div className="card__content">
          <h4>
            {t("Connect with Google Services")} <span className="pro__badge">{t("PREMIUM")}</span>
          </h4>
          <p>
            {t(
              "Integrate StoreSEO with your Google Search Console & Google Analytics to automatically optimize your store."
            )}
          </p>
        </div>
        <div className="card__button">
          <Link
            to="/subscription"
            className="button button__primary radius-20"
          >
            {t("upgrade to premium")}
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ConnectToGoogleAdvertise;

import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, InlineStack, Text } from "@shopify/polaris";
import { halloween2023 } from "../../config/index.js";
import { useTranslation } from "react-i18next";
import { useAppNavigation } from "../../hooks/useAppNavigation.js";
import Halloween2023 from "../svg/Halloween2023.jsx";
import { XIcon } from "@shopify/polaris-icons";
import { DEAL_BANNER } from "storeseo-enums/cacheKeys";
import { hideDeal } from "../../store/features/HiddenBanner.js";
import { useDispatch, useSelector } from "react-redux";
import { useShopApi } from "../../hooks/index.js";

const HalloweenBanner2023 = () => {
  const { t } = useTranslation();
  const { goToPath } = useAppNavigation();
  const { title, content, actionText, Illustration } = halloween2023;

  const shopApi = useShopApi();
  const dispatch = useDispatch();
  const { deal } = useSelector((state) => state.hiddenBanner);

  const handleClose = async () => {
    const { value } = await shopApi.hideBanner(DEAL_BANNER);
    dispatch(hideDeal());
  };

  if (!deal) return null;

  return (
    <div className="promo-banner">
      <Card padding="0">
        <InlineStack
          gap="300"
          blockAlign="center"
          align="space-between"
          // wrap={false}
        >
          <InlineStack
            gap="300"
            wrap={false}
          >
            <div className="left-image">
              <Halloween2023 />
            </div>
            <BlockStack gap="050">
              <Text
                variant="headingMd"
                as="h6"
              >
                {t(title)}
              </Text>
              <Text
                as={"p"}
                tone={"subdued"}
              >
                {t(content)}
              </Text>

              <span>
                <Button onClick={() => goToPath("/subscription")}>{t(actionText)}</Button>
              </span>
            </BlockStack>
          </InlineStack>
        </InlineStack>
        <span className="ss-close-button">
          <Button
            icon={XIcon}
            variant="plain"
            onClick={handleClose}
          />
        </span>
      </Card>
    </div>
  );
};

export default HalloweenBanner2023;

import React from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";

const ProductLimitAlert = () => {
  const user = useSelector((state) => state.user);
  const { t } = useTranslation();

  const products = user?.permission?.products;

  return (
    <div className="ss-alert ss-alert--primary d__flex align__center mb30">
      <i className="ss-icon ss-warning-circle mr10 color__primary" />
      <p>
        {t("Note: With the")} "{user?.planName?.toUpperCase()}" {t("version of StoreSEO")},{" "}
        {t("you will be able to sync and optimize your first")} <strong>{products}</strong>{" "}
        {t("products in your store")}.{" "}
        <Link
          className="link"
          to="/subscription"
        >
          <strong>{t("Click here to upgrade your plan")}.</strong>
        </Link>
      </p>
    </div>
  );
};

export default ProductLimitAlert;

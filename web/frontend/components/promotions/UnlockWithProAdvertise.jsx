import { isEmpty } from "lodash";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import Logo from "../common/Logo";

const UnlockWithProAdvertise = () => {
  const user = useSelector((state) => state.user);
  const { t } = useTranslation();

  if (isEmpty(user) || user.isPremium) return null;

  return (
    <div className="featured__card p30 mt30">
      <div className="logo">
        <Logo />
      </div>
      <h3>{t("Get More Advanced Features To Rank Your Business With StoreSEO Premium")} 🚀</h3>
      <p>
        {t(
          "Rank your business to the top of search results pages for UNLIMITED products & unlock exclusive features like SEO Schema, JSON-LD structured data, connect with Google Search Console & more."
        )}
      </p>
      <div className="d__flex justify__end">
        <Link
          to="/subscription"
          className="button button--md button__primary radius-20"
        >
          {t("Unlock All Premium Features Now")}
        </Link>
      </div>
    </div>
  );
};

export default UnlockWithProAdvertise;

import { createContext, useContext, useState } from "react";

const TableReferencesContext = createContext();

/**
 * Provider component to track and manage active TableReferences popover
 * This ensures only one popover can be open at a time
 */
export function TableReferencesProvider({ children }) {
  const [activePopoverId, setActivePopoverId] = useState(null);

  const openPopover = (id) => {
    setActivePopoverId(id);
  };

  const closePopover = () => {
    setActivePopoverId(null);
  };

  return (
    <TableReferencesContext.Provider value={{ activePopoverId, openPopover, closePopover }}>
      {children}
    </TableReferencesContext.Provider>
  );
}

/**
 * Hook to access the TableReferencesContext
 * @returns {Object} The context containing activePopoverId and functions to manage it
 */
export function useTableReferences() {
  const context = useContext(TableReferencesContext);
  if (context === undefined) {
    throw new Error("useTableReferences must be used within a TableReferencesProvider");
  }
  return context;
}

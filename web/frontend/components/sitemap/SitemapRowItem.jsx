import {
  BlockStack,
  Box,
  Checkbox,
  IndexTable,
  InlineStack,
  SkeletonBodyText,
  SkeletonThumbnail,
  Text,
  Thumbnail,
  useBreakpoints,
} from "@shopify/polaris";
import { prepareThumbnailURL } from "../../utility/helpers.jsx";
import { useSitemapRowContext } from "./SitemapRow.jsx";

const SitemapRowItem = () => {
  const { smDown: isSmallDevice } = useBreakpoints();

  return <>{isSmallDevice ? <RowsCellForSmallDevices /> : <RowsCellForLargeDevices />}</>;
};

const RowsCellForSmallDevices = () => {
  const { sitemap, isFetching, confirm, showFeaturedImage } = useSitemapRowContext();
  const { noIndex, noFollow, status, featuredImage, shopifyId, title } = sitemap;
  return (
    <>
      <Box
        paddingBlock="300"
        paddingInline="400"
        width="100%"
      >
        <InlineStack
          align="space-between"
          wrap={false}
          gap="300"
        >
          {/* Thumbnail */}
          {showFeaturedImage && (
            <Box>
              {isFetching ? (
                <SkeletonThumbnail size="small" />
              ) : (
                <Thumbnail
                  source={prepareThumbnailURL(featuredImage?.src)}
                  alt={featuredImage?.altText}
                  size="small"
                />
              )}
            </Box>
          )}
          <Box width="100%">
            <BlockStack gap="400">
              {/* Basic info */}

              {isFetching ? (
                <SkeletonBodyText lines={1} />
              ) : (
                <Text
                  as={"h4"}
                  variant={"headingSm"}
                >
                  {title}
                </Text>
              )}

              {/* Actions */}
              <InlineStack
                align="start"
                gap="200"
              >
                {isFetching ? (
                  <SkeletonBodyText lines={1} />
                ) : (
                  <Checkbox
                    id={`noindex-${shopifyId}`}
                    label={`Noindex`}
                    value={noIndex}
                    onChange={confirm}
                    checked={!!noIndex}
                  />
                )}
                {isFetching ? (
                  <SkeletonBodyText lines={1} />
                ) : (
                  <Checkbox
                    id={`nofollow-${shopifyId}`}
                    label={`Nofollow`}
                    value={noFollow}
                    onChange={confirm}
                    checked={!!noFollow}
                  />
                )}
                {isFetching ? (
                  <SkeletonBodyText lines={1} />
                ) : (
                  <Checkbox
                    id={`sitemap-${shopifyId}`}
                    label={`Sitemap`}
                    value={status}
                    onChange={confirm}
                    checked={!!status}
                  />
                )}
              </InlineStack>
            </BlockStack>
          </Box>
        </InlineStack>
      </Box>
    </>
  );
};

const RowsCellForLargeDevices = () => {
  const { sitemap, isFetching, sl, confirm, showFeaturedImage } = useSitemapRowContext();
  const { noIndex, noFollow, status, featuredImage, shopifyId, title } = sitemap;
  return (
    <>
      <IndexTable.Cell className={"width-50 height-1200"}>
        {isFetching ? <SkeletonBodyText lines={1} /> : sl}
      </IndexTable.Cell>

      {showFeaturedImage && (
        <IndexTable.Cell className={"width-60"}>
          {isFetching ? (
            <SkeletonThumbnail size="small" />
          ) : (
            <Thumbnail
              source={prepareThumbnailURL(featuredImage?.src)}
              alt={featuredImage?.altText}
              size="small"
            />
          )}
        </IndexTable.Cell>
      )}

      <IndexTable.Cell className="break_coll_content">
        {isFetching ? (
          <SkeletonBodyText lines={1} />
        ) : (
          <Text
            as={"h4"}
            variant={"headingSm"}
          >
            {title}
          </Text>
        )}
      </IndexTable.Cell>
      <IndexTable.Cell className={"width-100"}>
        {isFetching ? (
          <SkeletonBodyText lines={1} />
        ) : (
          <Checkbox
            id={`noindex-${shopifyId}`}
            label={`Noindex`}
            labelHidden
            value={noIndex}
            onChange={confirm}
            checked={!!noIndex}
          />
        )}
      </IndexTable.Cell>

      <IndexTable.Cell className={"width-100"}>
        {isFetching ? (
          <SkeletonBodyText lines={1} />
        ) : (
          <Checkbox
            id={`nofollow-${shopifyId}`}
            label={`Nofollow`}
            labelHidden
            value={noFollow}
            onChange={confirm}
            checked={!!noFollow}
          />
        )}
      </IndexTable.Cell>

      <IndexTable.Cell className={"width-100"}>
        {isFetching ? (
          <SkeletonBodyText lines={1} />
        ) : (
          <Checkbox
            id={`sitemap-${shopifyId}`}
            label={`Sitemap`}
            labelHidden
            value={status}
            onChange={confirm}
            checked={!!status}
          />
        )}
      </IndexTable.Cell>
    </>
  );
};

export default SitemapRowItem;

import useSitemapApi from "@/hooks/apiHooks/useSitemapApi";
import { Badge, CalloutCard, InlineStack, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useMutation, useQueryClient } from "react-query";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { BANNER_GOOGLE_SITEMAP } from "storeseo-enums/cacheKeys";
import googleAuthScopes from "storeseo-enums/googleAuthScopes";
import { useAppQuery, useShopApi } from "../../hooks";
import { setHiddenBanner } from "../../store/features/HiddenBanner.js";
import queryKeys from "../../utility/queryKeys";

const ConnectToGoogleSection = ({ show = true, dismissable = false }) => {
  if (!show) return null;
  const { t } = useTranslation();
  const sitemapApi = useSitemapApi();
  const navigate = useNavigate();
  const user = useSelector((state) => state.user);
  const shopApi = useShopApi();
  const queryClient = useQueryClient();
  const dispatch = useDispatch();

  const { data: sitemap, isLoading } = useAppQuery({
    queryKey: queryKeys.SITEMAP_INFO,
    queryFn: sitemapApi.getShopSitemapInfo,
    reactQueryOptions: {
      staleTime: Infinity,
      refetchInterval: false,
    },
  });

  const { mutate: submitToGoogle, isLoading: isSubmittingToGoogle } = useMutation({
    mutationFn: shopApi.submitSitemapsToGoogle,
    onSuccess: (data) => queryClient.setQueryData(queryKeys.SITEMAP_INFO, data),
  });

  // const hasGoogleConsolePermission = user?.permission?.google_console;
  const hasAuthenticatedForSitemapSubmission = sitemap?.google_integration_info?.grantedScopes?.includes(
    googleAuthScopes.SEARCH_CONSOLE_READ_WRITE
  );

  const connectToGoogle = () => {
    navigate("/settings/google-integration");
  };

  const primaryAction = {
    variant: "primary",
    content: hasAuthenticatedForSitemapSubmission ? t("Submit to Google") : t("Connect to Google"),
    onAction: hasAuthenticatedForSitemapSubmission ? submitToGoogle : connectToGoogle,
    loading: isLoading || isSubmittingToGoogle,
    disabled: !user.isPremium || sitemap?.is_sitemap_submitted,
  };

  const handleGoogleConnectClose = async () => {
    await shopApi.hideBanner(BANNER_GOOGLE_SITEMAP);
    dispatch(setHiddenBanner({ bannerKey: BANNER_GOOGLE_SITEMAP, status: false }));
  };

  // if (!hasGoogleConsolePermission) return null;

  const titleMarkup = (
    <InlineStack gap="200">
      <Text
        as="h4"
        variant="headingMd"
      >
        {t("Connect with Google Services")}
      </Text>
      {sitemap?.is_sitemap_submitted && <Badge tone="success">{t("Sitemap submitted")}</Badge>}
    </InlineStack>
  );

  return (
    <>
      <CalloutCard
        title={titleMarkup}
        primaryAction={primaryAction}
        onDismiss={dismissable && handleGoogleConnectClose}
      >
        <Text
          as={"p"}
          tone={"subdued"}
        >
          {t("Integrate StoreSEO with your")} <strong>{t("Google Search Console")}</strong> &{" "}
          <strong>{t("Google Analytics")}</strong> {t("to automatically optimize your store")}
        </Text>
      </CalloutCard>
      {/* <Card>
        <Flex
          justifyContent="space-between"
          alignItems="center"
          gap="16px"
        >
          <BlockStack gap="1">
            <Text
              variant="headingMd"
              as="h6"
            >
              {t("Connect with Google Services")}
            </Text>
            <Text
              as={"p"}
              tone={"subdued"}
            >
              {t("Integrate StoreSEO with your")} <strong>{t("Google search console")}</strong> &{" "}
              <strong>{t("Google analytics")}</strong> {t("to automatically optimize your store")}
            </Text>
          </BlockStack>

          {sitemap?.is_sitemap_submitted ? (
            <Badge>{t("Sitemap submitted to google console.")}</Badge>
          ) : (
            <>
              {hasAuthenticatedForSitemapSubmission ? (
                <Button
                  onClick={submitToGoogle}
                  loading={isLoading}
                  variant="primary"
                >
                  {t("Submit to Google")}
                </Button>
              ) : (
                <Button
                  onClick={connectToGoogle}
                  variant="primary"
                >
                  {t("Connect to Google")}
                </Button>
              )}
            </>
          )}
        </Flex>
      </Card> */}
    </>
  );
};

export default ConnectToGoogleSection;

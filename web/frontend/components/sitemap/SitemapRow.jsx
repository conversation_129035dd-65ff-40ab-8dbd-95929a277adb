import { IndexTable } from "@shopify/polaris";
import React from "react";
import SitemapRowItem from "./SitemapRowItem";

const SitemapRowContext = React.createContext();

/**
 *
 * @param {{sitemap: object, isFetching: boolean, sl?:number, confirm?: () => void, showFeaturedImage?: boolean }} props
 * @returns
 */
const SitemapRow = ({ sitemap, isFetching = false, sl, confirm = () => {}, showFeaturedImage = true }) => {
  const contextValues = {
    sitemap,
    isFetching,
    sl,
    confirm,
    showFeaturedImage,
  };
  return (
    <IndexTable.Row
      id={sl}
      key={sl}
      position={sl}
    >
      <SitemapRowContext.Provider value={contextValues}>
        <SitemapRowItem />
      </SitemapRowContext.Provider>
    </IndexTable.Row>
  );
};

export const useSitemapRowContext = () => {
  const context = React.useContext(SitemapRowContext);
  if (context === undefined) {
    throw new Error("useSitemapRowContext must be used within a SitemapRowContext.Provider");
  }
  return context;
};

export default SitemapRow;

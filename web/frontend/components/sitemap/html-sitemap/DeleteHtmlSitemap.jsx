import { <PERSON>, <PERSON><PERSON>, Card, InlineStack, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useAppBridgeRedirect } from "../../../hooks/useAppBridgeRedirect";

export default function DeleteHtmlSitemap() {
  const { t } = useTranslation();
  const { ADMIN_PATH } = useAppBridgeRedirect();

  return (
    <Card>
      <Box paddingBlockEnd="200">
        <Text variant="headingSm">{t("Delete HTML Sitemap")}</Text>
      </Box>

      <Box paddingBlockEnd="400">
        <Text>
          {t(
            "Navigate to your Online Store → Navigation. Select the menu where you previously added the HTML sitemap page link. Then, click on “Delete” next to the Sitemap document link"
          )}
          .
        </Text>
      </Box>

      <InlineStack gap="300">
        <Button
          tone="critical"
          onClick={() => ADMIN_PATH("/content/menus", { external: true })}
        >
          {t("Edit Navigation")}
        </Button>
      </InlineStack>
    </Card>
  );
}

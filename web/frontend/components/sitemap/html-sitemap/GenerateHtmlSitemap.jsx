import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, InlineStack, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useMutation, useQueryClient } from "react-query";
import { useAppQuery, useShopApi } from "../../../hooks";
import { DEFAULT_HTML_SITEMAP_CONFIG } from "../../../config/htmlSitemap";
import queryKeys from "../../../utility/queryKeys";

export default function GenerateHtmlSitemap() {
  const { t } = useTranslation();
  const shopApi = useShopApi();
  const queryClient = useQueryClient();

  const { data } = useAppQuery({
    queryKey: queryKeys.HTML_SITEMAP_SETUP_INFO,
    queryFn: shopApi.getHtmlSitemapSetupInfo,
  });

  const { isLoading, mutate: generateSitemap } = useMutation({
    mutationFn: () =>
      shopApi.updateHtmlSitemapSetupInfo({ setting: DEFAULT_HTML_SITEMAP_CONFIG, setupStep: 1, branding: true }),
    onSuccess: (data) => {
      queryClient.setQueryData(queryKeys.HTML_SITEMAP_SETUP_INFO, data);
    },
  });

  return (
    <BlockStack gap="200">
      <Text>
        {t(
          "Once you click the button below, we will generate an HTML Sitemap for your store. It might take a few moments."
        )}
      </Text>
      <InlineStack>
        <Button
          variant="primary"
          onClick={generateSitemap}
          loading={isLoading}
          disabled={data?.setupStep >= 1}
        >
          {t("Generate now")}
        </Button>
      </InlineStack>
    </BlockStack>
  );
}

import { BlockStack, Button, InlineStack, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useMutation, useQueryClient } from "react-query";
import queryKeys from "../../../utility/queryKeys";
import { useShopApi } from "../../../hooks";

export default function CompleteHtmlSitemapSetup() {
  const { t } = useTranslation();
  const shopApi = useShopApi();
  const queryClient = useQueryClient();
  const data = queryClient.getQueryData(queryKeys.HTML_SITEMAP_SETUP_INFO);

  const { isLoading, mutate: completeSetup } = useMutation({
    mutationFn: () => shopApi.updateHtmlSitemapSetupInfo({ setupStep: 3 }),
    onSuccess: (data) => {
      queryClient.setQueryData(queryKeys.HTML_SITEMAP_SETUP_INFO, data);
    },
  });

  return (
    <BlockStack gap="200">
      <Text>
        {t(
          "Once you have followed all the previous steps successfully, click on the button below to complete the full process."
        )}
      </Text>
      <InlineStack>
        <Button
          variant="primary"
          onClick={completeSetup}
          disabled={data.setupStep >= 3}
          loading={isLoading}
        >
          {t("Confirm")}
        </Button>
      </InlineStack>
    </BlockStack>
  );
}

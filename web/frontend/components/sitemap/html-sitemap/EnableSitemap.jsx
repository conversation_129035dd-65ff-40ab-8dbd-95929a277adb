import {
  <PERSON>Stack,
  Button,
  Link,
  Text,
  TextField,
  Tooltip,
  useCopyToClipboard,
  useFocusIn,
  useHover,
  useMediaQuery,
} from "@shopify/polaris";
import { CheckIcon, ClipboardIcon } from "@shopify/polaris-icons";
import { useRef } from "react";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";
import { HTML_SITEMAP_URL } from "../../../config";
import { useAppBridgeRedirect } from "../../../hooks/useAppBridgeRedirect";
import queryKeys from "../../../utility/queryKeys";

export default function EnableSitemap() {
  const { t } = useTranslation();
  const { ADMIN_PATH } = useAppBridgeRedirect();
  const queryClient = useQueryClient();
  const [copy, status] = useCopyToClipboard({
    defaultValue: HTML_SITEMAP_URL,
  });
  const ref = useRef(null);
  const isFocusedIn = useFocusIn(ref);
  const isHovered = useHover(ref);
  const isMouseDevice = useMediaQuery("mouse");
  const isMouseHovered = isMouseDevice ? isHovered : true;

  return (
    <BlockStack gap="200">
      <Text>
        {t(
          "Copy the links from below and go to the ‘Content’ → ‘Menu’ tab from your Shopify dashboard. Then choose a menu where you want to add this sitemap and click on the ‘Add menu item’ button. Now, give it a name (e.g. Sitemap) and paste the copied HTML link there."
        )}
      </Text>
      <div ref={ref}>
        <TextField
          value={HTML_SITEMAP_URL}
          readOnly
          suffix={
            <div
              style={{
                opacity: isMouseHovered || isFocusedIn || status === "copied" ? 1 : 0,
                transition:
                  isMouseHovered || isFocusedIn ? "var(--p-motion-duration-100) var(--p-motion-ease) opacity" : "none",
              }}
            >
              <Tooltip
                dismissOnMouseOut
                hoverDelay={500}
                preferredPosition="above"
                content={t("Copy")}
                active={status === "copied" ? false : undefined}
                activatorWrapper="div"
              >
                <Button
                  variant="tertiary"
                  accessibilityLabel="Copy sitemap URL"
                  onClick={copy}
                  icon={status === "copied" ? CheckIcon : ClipboardIcon}
                />
              </Tooltip>
            </div>
          }
          connectedRight={
            <Button
              variant="primary"
              onClick={() => {
                ADMIN_PATH("/content/menus", { external: true });
                queryClient.setQueryData(queryKeys.HTML_SITEMAP_SETUP_INFO, {
                  ...queryClient.getQueryData(queryKeys.HTML_SITEMAP_SETUP_INFO),
                  setupStep: 2,
                });
              }}
            >
              {t("Edit Navigation")}
            </Button>
          }
          helpText={
            <>
              {t("You can customize the url by following")}{" "}
              <Link
                url="https://storeseo.com/docs/how-to-generate-html-sitemap/#1-toc-title"
                target="_blank"
              >
                {t("this doc")}
              </Link>
            </>
          }
        />
      </div>
    </BlockStack>
  );
}

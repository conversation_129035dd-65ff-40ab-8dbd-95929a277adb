import { <PERSON>, Button, Card, InlineStack, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

export default function CustomizeHtmlSitemap() {
  const { t } = useTranslation();
  const navigate = useNavigate();

  return (
    <Card>
      <Box paddingBlockEnd="200">
        <Text variant="headingSm">{t("Customize HTML Sitemap")}</Text>
      </Box>

      <Box paddingBlockEnd="400">
        <Text>
          {t(
            "If you want to customize the sitemap page and match your brand identity, go to our settings option and customize it as you want."
          )}
        </Text>
      </Box>

      <InlineStack gap="300">
        <Button onClick={() => navigate("/settings/html-sitemap?tab=basic")}>{t("Go to Settings")}</Button>
      </InlineStack>
    </Card>
  );
}

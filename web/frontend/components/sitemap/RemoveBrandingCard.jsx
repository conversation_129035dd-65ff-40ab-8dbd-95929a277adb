import { sendRequestToCrisp } from "@/utility/crisp";
import { BlockStack, Box, Button, Card, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";

const RemoveBrandingCard = () => {
  const { t } = useTranslation();
  const user = useSelector((state) => state.user);

  if (user.isPremium) return null;

  return (
    <Card>
      <BlockStack gap="200">
        <Text
          as="h4"
          variant="headingMd"
        >
          {t("Remove StoreSEO branding")}
        </Text>
        <Text as="p">
          {t(
            "Removing 'Powered by StoreSEO' from your HTML Sitemap page is a Pro feature, but we will be happy to remove this for you. Simply contact support, and let us help you enhance your SEO growth"
          )}{" "}
          .
        </Text>

        <Box as="span">
          <Button
            variant="primary"
            onClick={() =>
              sendRequestToCrisp(
                "Hi! I'd like to remove 'Powered by StoreSEO' branding from HTML Sitemap page, can you help?"
              )
            }
          >
            {t("Send request")}
          </Button>
        </Box>
      </BlockStack>
    </Card>
  );
};

export default RemoveBrandingCard;

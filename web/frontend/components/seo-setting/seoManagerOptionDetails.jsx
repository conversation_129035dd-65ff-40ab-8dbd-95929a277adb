import { useTranslation } from "react-i18next";

const Se<PERSON><PERSON>anagerOptionDetails = ({ checked }) => {
  const { t } = useTranslation();
  return (
    <div className="template__tags">
      <h4>{t("Migrate Data From SEO Manager")}</h4>
      <p>
        {t(
          "It seems you're using SEO Manager on your store. You can migrate all your SEO data from SEO Manager to StoreSEO!"
        )}
      </p>
      <div className="migrate__options">
        <label className="ss-checkbox">
          <input
            type="checkbox"
            className="form__control"
            checked={checked}
          />
          <span className="input__text">{t("Meta Title")}</span>
        </label>
        <label className="ss-checkbox">
          <input
            type="checkbox"
            className="form__control"
            checked={checked}
          />
          <span className="input__text">{t("JSON-LD")}</span>
        </label>
        <label className="ss-checkbox">
          <input
            type="checkbox"
            className="form__control"
            checked={checked}
          />
          <span className="input__text">{t("Meta Description")}</span>
        </label>
        <label className="ss-checkbox">
          <input
            type="checkbox"
            className="form__control"
            checked={checked}
          />
          <span className="input__text">{t("Image Alt-text")}</span>
        </label>
        {/* <label className="ss-checkbox">
          <input
            type="checkbox"
            className="form__control"
            checked={checked}
          />
          <span className="input__text">Focus Keyword</span>
        </label> */}
      </div>
    </div>
  );
};

export default SeoManagerOptionDetails;

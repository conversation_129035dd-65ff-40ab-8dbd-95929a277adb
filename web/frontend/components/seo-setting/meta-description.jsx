import useIsRunningBackupRestore from "@/lib/hooks/useIsRunningBackupRestore";
import { Box, Card, Text } from "@shopify/polaris";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import SeoTag from "./seoTag";
import SeoTagsBox from "./seoTagsBox";

const MetaDescription = ({
  seoTags,
  template,
  setTemplate,
  readOnly = false,
  additionalClasses = null,
  disabled = false,
}) => {
  const [tabThreeTags, setTabThreeTags] = useState([]);
  const inputRef = useRef("");
  const { isRunning: isRunningBackupOrRestore } = useIsRunningBackupRestore();

  const { t } = useTranslation();

  useEffect(() => {
    setTabThreeTags(
      seoTags.map((item) => {
        if (template && template.includes(`[${item.key}]`)) {
          return { ...item, selected: true };
        }
        return { ...item, selected: false };
      })
    );
  }, [template, seoTags]);

  const addTagToString = (tag) => {
    if (readOnly || disabled) return;

    const cursorPosition = inputRef.current.selectionStart;
    const textBeforeCursorPosition = inputRef.current.value.substring(0, cursorPosition);
    const textAfterCursorPosition = inputRef.current.value.substring(cursorPosition, inputRef.current.value.length);
    const string = `${textBeforeCursorPosition}[${tag.key}]${textAfterCursorPosition}`;
    setTemplate(string);
  };

  return (
    <Card>
      <Text
        as="h4"
        fontWeight="bold"
        variant="headingMd"
      >
        {t("Choose A Template For Meta Description:")}
      </Text>
      <Box
        color="text-subdued"
        paddingBlockStart="200"
        paddingBlockEnd="300"
      >
        <Text variant="bodyMd">
          {t(
            "Meta descriptions are displayed on the search results page. Use our default template or click on the tags below to set your own."
          )}
        </Text>
      </Box>

      <Box
        id="seo-setting-input"
        maxWidth="450px"
      >
        <div className="Polaris-Connected">
          <div className="Polaris-Connected__Item Polaris-Connected__Item--primary">
            <input
              type="text"
              name="metaDescription"
              value={template}
              onChange={(e) => setTemplate(e.target.value)}
              className="Polaris-TextField__Input"
              ref={inputRef}
              disabled={isRunningBackupOrRestore || disabled}
            />
          </div>
          <div className="Polaris-TextField__Backdrop"></div>
        </div>
        <Box
          color="text-subdued"
          paddingBlockStart="200"
        >
          <Text variant="bodySm">{t("Maximum 165 characters")}</Text>
        </Box>
      </Box>

      <Box
        paddingBlockStart="400"
        paddingBlockEnd="200"
        width="100%"
      >
        <SeoTagsBox>
          {tabThreeTags.map((tag) => (
            <SeoTag
              key={tag.key}
              title={tag.title}
              selected={tag.selected}
              onClick={() => addTagToString(tag)}
            />
          ))}
        </SeoTagsBox>
      </Box>
    </Card>
  );
};

export default MetaDescription;

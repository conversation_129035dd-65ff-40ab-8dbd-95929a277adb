import { useTranslation } from "react-i18next";
import { BlockStack, Card, Text } from "@shopify/polaris";
import React from "react";

export default function HintMessage() {
  const { t } = useTranslation();

  return (
    <Card>
      <BlockStack gap="200">
        <Text
          variant="headingMd"
          as="h5"
        >
          {t("Migrate Your SEO Data From Another App")}
        </Text>
        <Text
          as="p"
          tone="subdued"
        >
          {t(
            "Using multiple SEO apps together may cause unintended side effects! StoreSEO can detect any other SEO apps you have installed on your store & help you migrate important SEO data from those apps to StoreSEO. Please note that it's a one-way process & this will update all your product data; your existing SEO score may also change after this."
          )}
        </Text>
      </BlockStack>
    </Card>
  );
}

import { <PERSON><PERSON>ta<PERSON>, <PERSON><PERSON>, <PERSON> } from "@shopify/polaris";
import EmptyPage from "../common/EmptyPage";
import { useTranslation } from "react-i18next";
import { useAppNavigation } from "../../hooks/useAppNavigation";
import ImageOptimizerNotEnabledBanner from "./NotEnabledBanner";
import { useNavigate } from "react-router-dom";
import { EMPTYSTATE_IMAGES } from "../../config";

const ImageOptimizerNotEnabled = () => {
  const { t } = useTranslation();
  const { backAction } = useAppNavigation();
  const navigate = useNavigate();

  const handleClick = () => {
    navigate("/subscription");
  };

  return (
    <Page
      title={t("Image Optimizer")}
      backAction={backAction}
    >
      <BlockStack gap="400">
        <ImageOptimizerNotEnabledBanner />
        <EmptyPage
          heading="Add Image Optimizer to your subscription"
          content="Please add Image Optimizer to your subscription plan to unlock this feature"
          primaryAction={
            <Button
              variant="primary"
              onClick={handleClick}
            >
              {t("Choose your plan")}
            </Button>
          }
          image={EMPTYSTATE_IMAGES.imageOptimizer}
          withWrapper={false}
        />
      </BlockStack>
    </Page>
  );
};

export default ImageOptimizerNotEnabled;

import { Banner, Text } from "@shopify/polaris";
import { LockIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";

const ImageOptimizerNotEnabledBanner = () => {
  const { t } = useTranslation();

  return (
    <Banner
      tone="warning"
      icon={LockIcon}
      title={t("Image Optimizer is not enabled")}
      action={{
        content: t("Choose your plan"),
        url: "/subscription",
      }}
    >
      <Text
        as="p"
        tone="subdued"
      >
        {t("Please add Image Optimizer to your subscription plan to unlock this feature")}
      </Text>
    </Banner>
  );
};

export default ImageOptimizerNotEnabledBanner;

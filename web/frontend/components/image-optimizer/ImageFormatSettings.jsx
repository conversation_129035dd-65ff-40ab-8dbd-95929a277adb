//@ts-check
import { BlockStack, Select, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import HintText from "../common/HintText";

/**
 * Reusable Image Format Settings Component
 * 
 * @param {{
 *   formatOptions: Array<{label: string, value: string, hint?: string}>,
 *   selectedFormat: string,
 *   onFormatChange: (value: string) => void,
 *   disabled?: boolean,
 *   title?: string,
 *   description?: string,
 *   label?: string,
 *   showHint?: boolean
 * }} props
 */
export default function ImageFormatSettings({
  formatOptions,
  selectedFormat,
  onFormatChange,
  disabled = false,
  title,
  description,
  label,
  showHint = true,
}) {
  const { t } = useTranslation();

  const defaultTitle = title || t("Image Format Settings");
  const defaultDescription = description || t("Choose your default image format");
  const defaultLabel = label || t("Convert image to");

  const selectedOption = formatOptions?.find((option) => option.value === selectedFormat);

  return (
    <BlockStack gap="300">
      <BlockStack gap="050">
        <Text
          as="h3"
          variant="headingMd"
        >
          {defaultTitle}
        </Text>
        <Text
          as="p"
          tone="subdued"
        >
          {defaultDescription}
        </Text>
      </BlockStack>

      <BlockStack gap="050">
        <Select
          label={defaultLabel}
          options={formatOptions}
          onChange={onFormatChange}
          value={selectedFormat}
          disabled={disabled}
        />
        
        {showHint && selectedOption?.hint && (
          <HintText content={t(selectedOption.hint)} />
        )}
      </BlockStack>
    </BlockStack>
  );
}

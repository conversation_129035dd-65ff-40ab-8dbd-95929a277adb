import useIsRunningBackupRestore from "@/lib/hooks/useIsRunningBackupRestore.jsx";
import { IndexTable } from "@shopify/polaris";
import React from "react";
import imageOptimization from "storeseo-enums/imageOptimization";
import ResourceType from "storeseo-enums/resourceType";
import { useSingleImageOptimizer } from "../../hooks/imageOptimizer/useSingleImageOptimizer.jsx";
import { isSvgImage, prepareThumbnailURL } from "../../utility/helpers.jsx";
import ImageRowItem from "./ImageRowItem.jsx";

/**
 * @typedef {keyof Pick<typeof ResourceType, "PRODUCT" | "COLLECTION" | "PAGE" | "ARTICLE">} ImageResourceType
 */

const ImageRowsContext = React.createContext();

/**
 * @param {{
 * image: any,
 * rowPosition?: number,
 * isFetching: boolean,
 * selectedResources?: string[],
 * onOptimize?: (images: Array<object>) => void,
 * onRestore?: (images: Array<object>) => void,
 * resourceType?: ImageResourceType
 * }} param0
 */

const ImageRows = ({
  image,
  rowPosition,
  isFetching = false,
  selectedResources = [],
  onOptimize,
  onRestore,
  resourceType = ResourceType.PRODUCT,
}) => {
  const { isRunning: isRunningBackupOrRestore } = useIsRunningBackupRestore();
  const { src, id, optimization_status } = image;
  const optimizationsStatusColor = {
    [imageOptimization.OPTIMIZED]: "success",
    [imageOptimization.ALREADY_OPTIMIZED]: "success",
    [imageOptimization.PENDING]: "info",
    [imageOptimization.DISPATCHED]: "info",
    [imageOptimization.PROCESSING]: "info",
    [imageOptimization.SAVING]: "info",
    [imageOptimization.NOT_OPTIMIZED]: "enabled",
    [imageOptimization.RESTORED]: "enabled",
  };
  const thumbnail = prepareThumbnailURL(src);
  const { isOptimizingImage, isRestoringImage, optimizeImage, restoreImage } = useSingleImageOptimizer({
    onOptimize,
    onRestore,
    resourceType,
  });

  const contextValues = {
    image,
    optimizationsStatusColor,
    thumbnail,
    isOptimizingImage,
    isRestoringImage,
    optimizeImage,
    restoreImage,
    selectedResources,
    isFetching,
    resourceType,
  };

  return (
    <IndexTable.Row
      id={id}
      position={rowPosition}
      selected={selectedResources?.includes(id)}
      disabled={optimization_status === imageOptimization.PENDING || isSvgImage(src) || isRunningBackupOrRestore}
    >
      <ImageRowsContext.Provider value={contextValues}>
        <ImageRowItem />
      </ImageRowsContext.Provider>
    </IndexTable.Row>
  );
};

export const useImageRowsContext = () => {
  const context = React.useContext(ImageRowsContext);
  if (context === undefined) {
    throw new Error("useImageRowsContext must be used within a ImageRows");
  }
  return context;
};

export default ImageRows;

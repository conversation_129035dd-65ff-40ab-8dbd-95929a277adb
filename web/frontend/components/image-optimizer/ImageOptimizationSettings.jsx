//@ts-check
import { Bleed, BlockStack, Card, Divider } from "@shopify/polaris";
import ImageCompressionSettings from "./ImageCompressionSettings";
import ImageFormatSettings from "./ImageFormatSettings";
import ImageResizeSettings from "./ImageResizeSettings";

/**
 * Combined Image Optimization Settings Component
 * 
 * @param {{
 *   compressionOptions: Array<{label: string, value: string, hint: string}>,
 *   formatOptions: Array<{label: string, value: string, hint?: string}>,
 *   resizeOptions: Array<{label: string, value: string, hint?: string}>,
 *   selectedCompression: string,
 *   selectedFormat: string,
 *   selectedResize: string,
 *   onCompressionChange: (item: {value: string}) => void,
 *   onFormatChange: (value: string) => void,
 *   onResizeChange: (value: string) => void,
 *   disabled?: boolean,
 *   showFormat?: boolean,
 *   showHints?: boolean,
 *   customTitles?: {
 *     compression?: string,
 *     format?: string,
 *     resize?: string
 *   },
 *   customDescriptions?: {
 *     compression?: string,
 *     format?: string,
 *     resize?: string
 *   }
 * }} props
 */
export default function ImageOptimizationSettings({
  compressionOptions,
  formatOptions,
  resizeOptions,
  selectedCompression,
  selectedFormat,
  selectedResize,
  onCompressionChange,
  onFormatChange,
  onResizeChange,
  disabled = false,
  showFormat = false,
  showHints = true,
  customTitles = {},
  customDescriptions = {},
}) {
  return (
    <Card>
      <BlockStack gap="400">
        {/* Compression Settings */}
        <ImageCompressionSettings
          compressionOptions={compressionOptions}
          selectedCompression={selectedCompression}
          onCompressionChange={onCompressionChange}
          disabled={disabled}
          title={customTitles.compression}
          description={customDescriptions.compression}
          showHint={showHints}
        />

        {/* Format Settings (optional) */}
        {showFormat && formatOptions && (
          <>
            <Bleed marginInline="400">
              <Divider />
            </Bleed>
            <ImageFormatSettings
              formatOptions={formatOptions}
              selectedFormat={selectedFormat}
              onFormatChange={onFormatChange}
              disabled={disabled}
              title={customTitles.format}
              description={customDescriptions.format}
              showHint={showHints}
            />
          </>
        )}

        {/* Resize Settings */}
        <Bleed marginInline="400">
          <Divider />
        </Bleed>
        <ImageResizeSettings
          resizeOptions={resizeOptions}
          selectedResize={selectedResize}
          onResizeChange={onResizeChange}
          disabled={disabled}
          title={customTitles.resize}
          description={customDescriptions.resize}
          showHint={showHints}
        />
      </BlockStack>
    </Card>
  );
}

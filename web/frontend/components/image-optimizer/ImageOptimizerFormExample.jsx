//@ts-check
import { BlockSta<PERSON>, Button, InlineStack } from "@shopify/polaris";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import useImageOptimizerForm from "../../lib/hooks/image-optimizer/useImageOptimizerForm";
import ImageOptimizerForm from "./ImageOptimizerForm";

/**
 * Example usage of ImageOptimizerForm with useImageOptimizerForm hook
 */
export default function ImageOptimizerFormExample() {
  const { t } = useTranslation();
  const [isSaving, setIsSaving] = useState(false);
  // Use the form hook
  const {
    control,
    formState,
    handleSubmit,
    reset,
    isLoading,
    defaultSettings,
  } = useImageOptimizerForm();

  // Handle form submission
  const onSubmit = async (data) => {
    setIsSaving(true);
    try {
      console.log("Saving image optimizer settings:", data);
      // Here you would call your API to save the settings
      // await saveImageOptimizerSettings(data);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      console.log("Settings saved successfully!");
    } catch (error) {
      console.error("Error saving settings:", error);
    } finally {
      setIsSaving(false);
    }
  };

  // Handle reset form
  const handleReset = () => {
    reset();
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <BlockStack gap="400">
        {/* Form Component */}
        <ImageOptimizerForm
          control={control}
          formState={formState}
          disabled={isLoading || isSaving}
          showFormat={true}
          showHints={true}
          customTitles={{
            compression: t("Image Compression Settings"),
            format: t("Image Format Settings"),
            resize: t("Advance Image Resizer"),
          }}
          customDescriptions={{
            compression: t("Choose your default image compression settings"),
            format: t("Choose your default image format"),
            resize: t("Choose your default image size"),
          }}
        />

        {/* Action Buttons */}
        <InlineStack align="end" gap="200">
          <Button
            onClick={handleReset}
            disabled={isLoading || isSaving}
          >
            {t("Reset")}
          </Button>
          <Button
            variant="primary"
            submit
            loading={isSaving}
            disabled={isLoading}
          >
            {t("Save Settings")}
          </Button>
        </InlineStack>

        {/* Debug Info (remove in production) */}
        <details style={{ marginTop: "20px" }}>
          <summary>Debug Info (Form State)</summary>
          <pre style={{ fontSize: "12px", background: "#f5f5f5", padding: "10px" }}>
            {JSON.stringify({
              errors: formState.errors,
              isValid: formState.isValid,
              isDirty: formState.isDirty,
              isLoading,
              defaultSettings,
            }, null, 2)}
          </pre>
        </details>
      </BlockStack>
    </form>
  );
}

////@ts-check
import Modal from "@/modules/components/Modal";
import { BlockStack, Box, Divider, InlineStack, Select, Text } from "@shopify/polaris";
import { AlertCircleIcon, ImageAddIcon, UndoIcon } from "@shopify/polaris-icons";
import { isEmpty } from "lodash";
import { useEffect, useState } from "react";
import { ReactCompareSlider, ReactCompareSliderImage, styleFitContainer } from "react-compare-slider";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import ResourceType from "storeseo-enums/resourceType";
import { IMAGE_OPTIMIZER_OPTIONS } from "../../config";
import { useImageApi } from "../../hooks";
import { useImageOptimizationOptions } from "../../hooks/imageOptimizer/useImageOptimzationOptions";
import { useBanner } from "../../hooks/useBanner";
import useUserAddon from "../../hooks/useUserAddon";
import HintText from "../common/HintText";
import RadioGroup from "../common/RadioGroup";

/**
 * @typedef {keyof Pick<typeof ResourceType, "PRODUCT" | "COLLECTION" | "PAGE" | "ARTICLE">} ImageResourceType
 */

/**
 *
 * @param {{
 *  image: Object,
 *  onRestore: (images: Array<object>) => void,
 *  onOptimize: (images: Array<object>) => void,
 *  onClose: () => void,
 *  resourceType: ImageResourceType,
 * }} props
 * @returns
 */
export default function ImageCompareModal({
  image,
  onRestore,
  onOptimize,
  onClose = () => {},
  resourceType = ResourceType.PRODUCT,
}) {
  const { t } = useTranslation();
  const imageApi = useImageApi();

  const [open, setOpen] = useState(false);
  const [settingsChanged, setSettingsChanged] = useState(false);

  useEffect(() => {
    if (!isEmpty(image)) setOpen(true);
  }, [image]);

  const { compressionType, compressionFormat, resizeType, handleCompressionTypeChange, handleResizeTypeChange } =
    useImageOptimizationOptions(image.optimization_setting);

  const { imageOptimizerUsageCount, imageOptimizerUsageLimit, updateImageOptimizerUsage } = useUserAddon();

  const { isLoading: isOptimizingImage, mutate: optimizeImage } = useMutation({
    mutationFn: (data) => imageApi.optimizeImage(data, resourceType),
    onSuccess: (data) => {
      updateImageOptimizerUsage(data.images.length);
      const images = data.images.map((img) => ({
        ...img,
        resources: image.resources,
        title: image.title,
      }));
      onOptimize(images);
      return;
    },
  });

  const { isLoading: isRestoringImage, mutate: restoreImage } = useMutation({
    mutationFn: (data) => imageApi.restoreImage(data, resourceType),
    onSuccess: (data) => {
      onRestore(data.images);
      setOpen(false);
      return;
    },
  });

  const { Banner: OptimizationErrorBanner, showBanner: ShowOptimizationSettingError } = useBanner({
    title: t(
      "Please select at least one of 'image compression' or 'image format' or 'resize' options. Currently all selected settings are 'none'."
    ),
    noMargin: true,
    icon: AlertCircleIcon,
    tone: "critical",
  });

  const handleOptimize = () => {
    if (compressionType === "none" && compressionFormat === "none" && resizeType === "none")
      return ShowOptimizationSettingError(true);

    const data = {};
    if (compressionType !== "none") data["compression_type"] = compressionType;
    if (compressionFormat !== "none") data["target_format"] = compressionFormat;
    if (resizeType !== "none") data["target_width"] = resizeType;

    if (!isEmpty(image)) {
      data["image_details"] = [
        {
          image_url: image.src,
          image_id: String(image.id),
          resource_id: image.resources[0]?.resource_id,
          media_id: image.media_id,
          title: image.title,
        },
      ];

      optimizeImage(data);
    }
  };

  const handleRestore = () => {
    const data = {
      id: image.id,
      media_id: image.media_id,
      resource_id: image.resources[0]?.resource_id,
      originalSource: image.optimization_meta?.original_image_url,
      file_size: image.optimization_meta.original_size,
    };

    restoreImage([data]);
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      onClose={(e) => {
        e?.preventDefault();
        e?.stopPropagation();
        if (!(isOptimizingImage || isRestoringImage)) {
          setOpen(false);
          onClose();
        }
      }}
      title={
        <>
          <Text
            variant="headingMd"
            fontWeight="bold"
          >
            {t("Optimize Image")}
          </Text>
          <Text tone="subdued">{image.title}</Text>
        </>
      }
      primaryAction={[
        {
          content: t("Re-optimize"),
          icon: ImageAddIcon,
          onAction: (e) => {
            e.stopPropagation();
            e.preventDefault();

            handleOptimize();
          },
          loading: isOptimizingImage,
          disabled: !settingsChanged || isRestoringImage || imageOptimizerUsageCount >= imageOptimizerUsageLimit,
        },
      ]}
      secondaryActions={[
        {
          content: t("Restore original"),
          icon: UndoIcon,
          onAction: (e) => {
            e.stopPropagation();
            e.preventDefault();

            handleRestore();
          },
          disabled: !image?.optimization_meta?.original_image_url || isOptimizingImage,
          loading: isRestoringImage,
        },
      ]}
    >
      <Modal.Section>
        <BlockStack gap={"600"}>
          {image?.optimization_meta?.processed_image_url ? (
            <BlockStack gap={"100"}>
              <InlineStack align="space-between">
                <Text
                  fontWeight=""
                  variant=""
                >
                  {t("Original Size")}: {image.optimization_meta.original_size}
                </Text>
                <Text
                  fontWeight=""
                  variant=""
                >
                  {t("Compressed Size")}: {image.optimization_meta.processed_size} (
                  {image.optimization_meta.percentage_optimized})
                </Text>
              </InlineStack>
              <Box
                minHeight="300px"
                borderRadius="200"
                overflowX="hidden"
                overflowY="hidden"
                background="bg"
              >
                <ReactCompareSlider
                  itemOne={
                    <ReactCompareSliderImage
                      src={image.optimization_meta?.original_image_url}
                      alt="Image one"
                      style={{
                        ...styleFitContainer({
                          objectFit: "cover",
                          objectPosition: "center",
                          height: "auto",
                          maxHeight: "300px",
                          width: "100%",
                        }),
                      }}
                    />
                  }
                  itemTwo={
                    <ReactCompareSliderImage
                      src={image.optimization_meta.processed_image_url}
                      alt="Image two"
                      style={{
                        ...styleFitContainer({
                          objectFit: "cover",
                          objectPosition: "center",
                          height: "auto",
                          maxHeight: "300px",
                          width: "100%",
                        }),
                      }}
                    />
                  }
                />
              </Box>
            </BlockStack>
          ) : (
            <div
              style={{
                height: "100%",
                width: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Text
                color="subdued"
                as="h1"
                variant={"headingMd"}
              >
                {t("Click on the 'Optimize' button to see preview")}
              </Text>
            </div>
          )}

          <Divider />

          <BlockStack gap="600">
            <OptimizationErrorBanner />
            <BlockStack gap="300">
              <BlockStack gap="100">
                <Text
                  as="h3"
                  variant="headingMd"
                >
                  {t("Image Compression Settings")}
                </Text>
                <Text
                  as="p"
                  tone="subdued"
                >
                  {t("Choose your default image compression settings")}
                </Text>
              </BlockStack>
              <div>
                <Text as="p">{t("Compression Types")}:</Text>
                <RadioGroup
                  items={IMAGE_OPTIMIZER_OPTIONS.COMPRESSION_TYPES}
                  selected={compressionType}
                  onChange={(item) => {
                    setSettingsChanged(true);
                    handleCompressionTypeChange(item);
                  }}
                ></RadioGroup>
                <HintText
                  content={IMAGE_OPTIMIZER_OPTIONS.COMPRESSION_TYPES?.find((ct) => ct.value === compressionType)?.hint}
                />
              </div>
              <Text as="p">
                {IMAGE_OPTIMIZER_OPTIONS.COMPRESSION_TYPES.find((t) => t.value === compressionType)?.description}
              </Text>
            </BlockStack>

            <BlockStack gap="300">
              <BlockStack gap="100">
                <Text
                  as="h3"
                  variant="headingMd"
                >
                  {t("Advance Image Resizer")}
                </Text>
                <Text
                  as="p"
                  tone="subdued"
                >
                  {t("Choose your default image size")}
                </Text>
              </BlockStack>

              <Select
                label={t("Resize your image to")}
                options={IMAGE_OPTIMIZER_OPTIONS.RESIZES}
                onChange={(item) => {
                  setSettingsChanged(true);
                  handleResizeTypeChange(item);
                }}
                value={resizeType}
              />
            </BlockStack>
          </BlockStack>
        </BlockStack>
      </Modal.Section>
    </Modal>
  );
}

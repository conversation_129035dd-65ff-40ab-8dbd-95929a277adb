import useConfirmation from "@/hooks/useConfirmation";
import useImageOptimizerForm from "@/lib/hooks/image-optimizer/useImageOptimizerForm";
import { useImageOptimizeSettings } from "@/lib/hooks/image-optimizer/useImageOptimizeSettings";
import ContextualSaveBar from "@/modules/components/ContextualSaveBar";
import { BlockStack, Button, InlineStack } from "@shopify/polaris";
import { AlertTriangleIcon } from "@shopify/polaris-icons";
import { isEmpty } from "lodash";
import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useBanner } from "../../hooks/useBanner";
import { useUnsavedChanges } from "../../hooks/useUnsavedChanges";
import useUserAddon from "../../hooks/useUserAddon";
import AutoOptimizationToggler from "./AutoOptimizationToggler";
import ImageOptimizerForm from "./ImageOptimizerForm";

export const IMAGE_OPTIMIZER_INITIAL_SETTINGS = {
  compressionType: "none",
  format: "none",
  resize: "none",
  autoOptimization: false,
};

const ImageOptimizerSettingsCard = ({ isOnboarding = false, onComplete = () => {}, onBack = () => {} }) => {
  const { t } = useTranslation();
  const { renderConfirmation, showConfirmation, hideConfirmation } = useConfirmation();

  // Get settings from the hook (includes autoOptimization and mutations)
  const { settings: apiSettings, updateSettings, toggleAutoOptimization } = useImageOptimizeSettings();
  const [noneSelected, setNoneSelected] = useState(true);

  const { hasImageOptimizer, isFreeImageOptimizer } = useUserAddon();

  // Get autoOptimization from the settings hook
  const autoOptimization = apiSettings?.autoOptimization || false;

  // Form submission handler
  const handleFormSubmit = (formData) => {
    // Merge form data with current auto-optimization state
    const completeData = {
      ...formData,
      autoOptimization: autoOptimization, // Use current toggler value
    };
    return updateSettings.mutateAsync(completeData);
  };

  // Use the image optimizer form hook
  const { control, formState, handleSubmit, watch, reset, isLoading: isFormLoading } = useImageOptimizerForm();

  // Watch form values to check if none are selected
  const watchedValues = watch();

  // Data tracking for unsaved changes (form fields only)
  const originalData = useMemo(
    () => ({
      compressionType: apiSettings?.compressionType || "none",
      format: apiSettings?.format || "none",
      resize: apiSettings?.resize || "none",
    }),
    [apiSettings]
  );

  // Track unsaved changes for form fields only
  const { hasUnsavedChanges, setShowDiscardChangeModal, DiscardChangesModal } = useUnsavedChanges({
    originalData,
    currentData: watchedValues,
    onDiscardAction: () => {
      // Reset form to original values using react-hook-form's reset
      reset({
        compressionType: apiSettings?.compressionType || "none",
        format: apiSettings?.format || "none",
        resize: apiSettings?.resize || "none",
      });
    },
  });

  const { Banner: ImageOptimizerSettingsWarningBanner, showBanner: showImageOptimizerSettingsWarningBanner } =
    useBanner({
      title: t("Warning"),
      message: t(
        "To use the Image Optimizer feature, you must select the compression methods from here. Currently, all are selected as 'None'. {{ONBOARD_MESSAGE}}",
        {
          ONBOARD_MESSAGE: isOnboarding
            ? t("You can also configure this from the Image Optimizer settings afterwards")
            : "",
        }
      ),
      icon: AlertTriangleIcon,
      tone: "warning",
      noMargin: true,
    });

  // Check if none of the optimization settings are selected
  const checkNoneSelected = (values) => {
    if (!isEmpty(values)) {
      return !Object.values(values).filter((val) => val !== "none").length > 0;
    }
    return true;
  };

  const [errors, setErrors] = useState({
    autoOptimization: "",
  });

  const checkAndToggleAutoOptimization = async () => {
    if (noneSelected) {
      setErrors({
        ...errors,
        autoOptimization: "You need to setup optimization settings before enableing auto optimization.",
      });
      hideConfirmation();
      return;
    } else {
      setErrors({
        ...errors,
        autoOptimization: "",
      });
    }

    try {
      // Use the hook's mutation with mutateAsync
      await toggleAutoOptimization.mutateAsync(!autoOptimization);
    } catch (error) {
      console.error("Error toggling auto optimization:", error);
    } finally {
      hideConfirmation();
    }
  };

  // Update noneSelected when form values change
  useEffect(() => {
    setNoneSelected(checkNoneSelected(watchedValues));
  }, [watchedValues]);

  // Show warning banner when settings are loaded
  useEffect(() => {
    if (!isFormLoading && apiSettings) {
      showImageOptimizerSettingsWarningBanner(hasImageOptimizer && checkNoneSelected(apiSettings));
    }
  }, [isFormLoading, apiSettings, hasImageOptimizer]);

  const submitSetting = () => {
    // Use react-hook-form's handleSubmit to validate and submit the form
    handleSubmit(async (formData) => {
      try {
        const result = await handleFormSubmit(formData);
        onComplete(result);
      } catch (error) {
        console.error("Error updating settings:", error);
      }
    })();
  };

  return (
    <BlockStack gap="400">
      <DiscardChangesModal />

      <ContextualSaveBar
        id="image-optimizer-settings"
        open={hasUnsavedChanges}
        isLoading={updateSettings.isLoading}
        onSave={submitSetting}
        onDiscard={() => setShowDiscardChangeModal(true)}
      />

      {!isOnboarding && <ImageOptimizerSettingsWarningBanner />}

      <BlockStack gap="400">
        <AutoOptimizationToggler
          isEnabled={autoOptimization}
          isLoading={toggleAutoOptimization.isLoading}
          disabled={!hasImageOptimizer || isFreeImageOptimizer}
          error={errors?.autoOptimization}
          showWarningBanner={true}
          onToggle={showConfirmation}
        />
        {/* Auto image optimization settings toggle confirmation modal */}
        {renderConfirmation({
          content: t("Are you sure you want to save the changes?"),
          primaryActionText: autoOptimization ? t("Disable") : t("Enable"),
          primaryActionIsDestructive: autoOptimization,
          primaryAction: checkAndToggleAutoOptimization,
          onClose: hideConfirmation,
          loading: toggleAutoOptimization.isLoading,
        })}

        <ImageOptimizerForm
          control={control}
          formState={formState}
          disabled={!hasImageOptimizer}
          showFormat={false}
          showHints={true}
          customTitles={{
            compression: t("Image Compression Settings"),
            resize: t("Advance Image Resizer"),
          }}
          customDescriptions={{
            compression: t("Choose your default image compression settings"),
            resize: t("Choose your default image size"),
          }}
        />
      </BlockStack>

      {isOnboarding && <ImageOptimizerSettingsWarningBanner />}

      <InlineStack
        align="end"
        gap="200"
      >
        {isOnboarding && <Button onClick={onBack}>{t("Back")}</Button>}
        <Button
          variant="primary"
          onClick={submitSetting}
          loading={updateSettings.isLoading}
          disabled={isFormLoading || !hasImageOptimizer}
        >
          {t(isOnboarding ? "Next" : "Save")}
        </Button>
      </InlineStack>
    </BlockStack>
  );
};

export default ImageOptimizerSettingsCard;

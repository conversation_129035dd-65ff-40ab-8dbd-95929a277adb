//@ts-check
import { BlockStack, Select, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import HintText from "../common/HintText";

/**
 * Reusable Image Resize Settings Component
 * 
 * @param {{
 *   resizeOptions: Array<{label: string, value: string, hint?: string}>,
 *   selectedResize: string,
 *   onResizeChange: (value: string) => void,
 *   disabled?: boolean,
 *   title?: string,
 *   description?: string,
 *   label?: string,
 *   showHint?: boolean
 * }} props
 */
export default function ImageResizeSettings({
  resizeOptions,
  selectedResize,
  onResizeChange,
  disabled = false,
  title,
  description,
  label,
  showHint = true,
}) {
  const { t } = useTranslation();

  const defaultTitle = title || t("Advance Image Resizer");
  const defaultDescription = description || t("Choose your default image size");
  const defaultLabel = label || t("Resize your image to");

  const selectedOption = resizeOptions?.find((option) => option.value === selectedResize);

  return (
    <BlockStack gap="300">
      <BlockStack gap="050">
        <Text
          as="h3"
          variant="headingMd"
        >
          {defaultTitle}
        </Text>
        <Text
          as="p"
          tone="subdued"
        >
          {defaultDescription}
        </Text>
      </BlockStack>

      <BlockStack gap="050">
        <Select
          label={defaultLabel}
          options={resizeOptions}
          onChange={onResizeChange}
          value={selectedResize}
          disabled={disabled}
        />
        
        {showHint && selectedOption?.hint && (
          <HintText content={t(selectedOption.hint)} />
        )}
      </BlockStack>
    </BlockStack>
  );
}

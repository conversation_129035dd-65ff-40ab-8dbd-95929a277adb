//@ts-check
import { IndexTable, useBreakpoints } from "@shopify/polaris";
import { memo, useEffect } from "react";
import { useTranslation } from "react-i18next";

import TableEmptyState from "@/components/common/TableEmptyState";
import BulkImageOptimizationModal from "@/components/image-optimizer/BulkImageOptimizationModal";
import ImageRows from "@/components/image-optimizer/ImageRows";
import useUserAddon from "@/hooks/useUserAddon";
import { useImageOptimizeActions, useImagesList } from "@/lib/hooks/image-optimizer";
import { useImageOptimizerModals } from "@/lib/hooks/image-optimizer/useImageOptimizerModals";
import useIndexTablePagination from "@/lib/hooks/useIndexTablePagination";
import useIndexTableSort from "@/lib/hooks/useIndexTableSort";
import {
  generateBulkOptimizePayload,
  generateBulkRestorePayload,
} from "@/lib/utils/image-optimizer/imageOptimizerUtils";
import { useImageOptimizerLayoutContext } from "@/providers/ImageOptimizerLayoutProvider";
import { useStoreSeo } from "@/providers/StoreSeoProvider";

/**
 * @typedef {Object} ImageOptimizerTableProps
 * @property {keyof typeof import("storeseo-enums/resourceType")} resourceType - The resource type (PRODUCT, COLLECTION, ARTICLE)
 * @property {import("@/config/imageOptimizerConfig").ResourceConfig} config - Configuration for the resource type
 */

/**
 * Shared table component for image optimizer pages
 * @type {import("react").FC<ImageOptimizerTableProps>}
 */
const ImageOptimizerTable = memo(({ resourceType, config }) => {
  const { t } = useTranslation();
  const { smDown: isSmallDevice } = useBreakpoints();
  const { doManualRefresh } = useStoreSeo();

  const { handleImagesUpdate } = useImageOptimizerLayoutContext();
  const { imageOptimizerUsageCount, imageOptimizerUsageLimit } = useUserAddon();

  // Modal management
  const { showBulkImageOptimizeModal, openBulkOptimizeModal, closeBulkOptimizeModal, setShowBulkImageOptimizeModal } =
    useImageOptimizerModals();

  // Use the reusable hook for images list
  const {
    images,
    formattedImages,
    pagination,
    hasEmptyContent,
    isLoading,
    queryKey,
    refetchImages,
    selectedResources,
    allResourcesSelected,
    selectableImagesCount,
    handleSelectionChange,
    clearSelection,
  } = useImagesList(/** @type {any} */ (resourceType));

  // Use reusable image optimize actions hooks
  const { bulkOptimize, bulkRestore } = useImageOptimizeActions({
    queryKey,
    resourceType: /** @type {any} */ (resourceType),
    onOptimizeSuccess: () => {
      clearSelection();
      closeBulkOptimizeModal();
    },
    onRestoreSuccess: () => {
      clearSelection();
    },
  });

  // Handle manual refresh
  useEffect(() => {
    if (doManualRefresh) {
      refetchImages();
    }
  }, [doManualRefresh, refetchImages]);

  // Bulk operation handlers
  const handleBulkOptimize = ({ compressionType, compressionFormat, resizeType }) => {
    const payload = generateBulkOptimizePayload({
      selectedResources,
      images,
      compressionType,
      compressionFormat,
      resizeType,
    });

    if (payload) {
      bulkOptimize.mutate(payload);
    }
  };

  const handleListRestore = () => {
    const payload = generateBulkRestorePayload({
      selectedResources,
      formattedImages,
    });

    if (payload) {
      bulkRestore.mutate(payload);
    }
  };

  // Table configuration
  const { handleSort, sortDir, sortIndex, defaultSortDir } = useIndexTableSort({
    tableHeadings: config.tableHeadings,
  });
  const { paginationConfigs } = useIndexTablePagination(pagination);

  const emptyStateMarkup = (
    <TableEmptyState
      title={config.emptyStateTitle}
      description={config.emptyStateDescription}
      withIllustration
    />
  );

  // Don't render if there's empty content (handled by ImageOptimizerLayout)
  if (hasEmptyContent) {
    return null;
  }

  return (
    <>
      <IndexTable
        condensed={isSmallDevice}
        resourceName={config.resourceName}
        itemCount={selectableImagesCount || formattedImages?.length || 0}
        selectedItemsCount={allResourcesSelected ? "All" : selectedResources.length}
        onSelectionChange={handleSelectionChange}
        // @ts-ignore
        headings={config.tableHeadings.map((heading) => ({
          ...heading,
          title: t(heading.title),
          alignment: heading.alignment || "start",
        }))}
        selectable={true}
        onSort={handleSort}
        sortable={[false, false, true, true, false, false]}
        sortColumnIndex={sortIndex}
        // @ts-ignore
        sortDirection={sortDir}
        // @ts-ignore
        defaultSortDirection={defaultSortDir}
        emptyState={emptyStateMarkup}
        promotedBulkActions={[
          {
            content: `${t("Restore")} (${selectedResources.length})`,
            disabled: bulkRestore.isLoading,
            onAction: handleListRestore,
          },
          {
            content: `${t("Optimize")} (${selectedResources.length})`,
            disabled:
              bulkRestore.isLoading || imageOptimizerUsageCount + selectedResources.length > imageOptimizerUsageLimit,
            onAction: openBulkOptimizeModal,
          },
        ]}
        pagination={paginationConfigs}
      >
        {formattedImages.map((image, index) => (
          <ImageRows
            image={image}
            key={index}
            rowPosition={index}
            isFetching={isLoading}
            selectedResources={selectedResources}
            resourceType={/** @type {any} */ (resourceType)}
            onOptimize={(images) => handleImagesUpdate(images, queryKey)}
            onRestore={(images) => handleImagesUpdate(images, queryKey)}
          />
        ))}
      </IndexTable>

      <BulkImageOptimizationModal
        isOpen={showBulkImageOptimizeModal}
        setIsOpen={setShowBulkImageOptimizeModal}
        onOptimize={handleBulkOptimize}
        selectedCount={selectedResources.length}
        isLoading={bulkOptimize.isLoading}
      />
    </>
  );
});

ImageOptimizerTable.displayName = "ImageOptimizerTable";

export default ImageOptimizerTable;

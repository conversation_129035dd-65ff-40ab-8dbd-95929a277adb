//@ts-check
import { useImageOptimizeSettings } from "@/lib/hooks/image-optimizer/useImageOptimizeSettings";
import BackupRestoreInfoBanner from "@/modules/components/BackupRestoreInfoBanner.jsx";
import Modal from "@/modules/components/Modal";
import { BlockStack, Box, InlineGrid, Select, Text } from "@shopify/polaris";
import { AlertCircleIcon } from "@shopify/polaris-icons";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { IMAGE_OPTIMIZER_OPTIONS } from "../../config/index.js";
import { useBanner } from "../../hooks/useBanner.jsx";
import HintText from "../common/HintText.jsx";
import RadioGroup from "../common/RadioGroup.jsx";

/**
 * Reusable Bulk Image Optimization Modal Component
 *
 * @example
 * ```jsx
 * <BulkImageOptimizationModal
 *   isOpen={showModal}
 *   setIsOpen={setShowModal}
 *   onOptimize={({ compressionType, compressionFormat, resizeType }) => {
 *     // Handle optimization with the selected settings
 *     console.log('Optimize with:', { compressionType, compressionFormat, resizeType });
 *   }}
 *   selectedCount={selectedImages.length}
 *   isLoading={isOptimizing}
 * />
 * ```
 *
 * @param {{
 *   isOpen: boolean,
 *   setIsOpen: (open: boolean) => void,
 *   onOptimize: (settings: {compressionType: string, compressionFormat: string, resizeType: string}) => void,
 *   selectedCount: number,
 *   isLoading?: boolean
 * }} props
 */
export default function BulkImageOptimizationModal({
  isOpen,
  setIsOpen,
  onOptimize,
  selectedCount,
  isLoading = false,
}) {
  const { t } = useTranslation();
  const { settings, defaultSettings } = useImageOptimizeSettings();

  // Optimization settings state
  const [compressionType, setCompressionType] = useState(defaultSettings.compressionType);
  const [compressionFormat, setCompressionFormat] = useState(defaultSettings.format);
  const [resizeType, setResizeType] = useState(defaultSettings.resize);

  // Handlers for settings changes
  const handleCompressionTypeSelectionChange = useCallback((item) => setCompressionType(item.value), []);
  const handleResizeTypeSelectionChange = useCallback((item) => setResizeType(item), []);

  // Error banner for validation
  const { Banner: OptimizationSettingError, showBanner: ShowOptimizationSettingError } = useBanner({
    title: t(
      "Please select at least one of 'image compression' or 'image format' or 'resize' options. Currently all selected settings are 'none'."
    ),
    noMargin: true,
    icon: AlertCircleIcon,
    tone: "critical",
  });

  // Initialize settings from saved preferences
  useEffect(
    function setInitialOptimizationSetting() {
      setCompressionType(settings?.compressionType || defaultSettings.compressionType);
      setCompressionFormat(settings?.format || defaultSettings.format);
      setResizeType(settings?.resize || defaultSettings.resize);
      ShowOptimizationSettingError(false);
    },
    [settings, defaultSettings, isOpen]
  );

  // Handle optimization action
  const handleOptimize = () => {
    // Validate that at least one optimization option is selected
    if (compressionType === "none" && compressionFormat === "none" && resizeType === "none") {
      return ShowOptimizationSettingError(true);
    }

    // Call the parent's optimize handler with current settings
    onOptimize({
      compressionType,
      compressionFormat,
      resizeType,
    });
  };

  return (
    <Modal
      type="app-bridge"
      open={isOpen}
      setOpen={setIsOpen}
    >
      <Modal.Section>
        <Modal.TitleBar title={`${t("Optimize Image")} - ${selectedCount} ${t("Images")}`}>
          <button
            variant="primary"
            onClick={handleOptimize}
            loading={isLoading ? "" : undefined}
          >
            {t("Optimize")}
          </button>
        </Modal.TitleBar>

        <Box padding="400">
          <BlockStack gap="400">
            <BackupRestoreInfoBanner />

            <BlockStack gap="800">
              <OptimizationSettingError />
              <BlockStack gap="300">
                <BlockStack gap="100">
                  <Text
                    as="h3"
                    variant="headingMd"
                  >
                    {t("Image Compression Settings")}
                  </Text>
                  <Text
                    as="p"
                    tone="subdued"
                  >
                    {t("Choose your default image compression settings")}
                  </Text>
                </BlockStack>
                <div>
                  <Text as="p">{t("Compression Types")}:</Text>
                  <RadioGroup
                    items={IMAGE_OPTIMIZER_OPTIONS.COMPRESSION_TYPES}
                    selected={compressionType}
                    onChange={handleCompressionTypeSelectionChange}
                  ></RadioGroup>
                  <HintText
                    content={
                      IMAGE_OPTIMIZER_OPTIONS.COMPRESSION_TYPES?.find((ct) => ct.value === compressionType)?.hint
                    }
                  />
                </div>
                <Text as="p">
                  {IMAGE_OPTIMIZER_OPTIONS.COMPRESSION_TYPES.find((t) => t.value === compressionType)?.description}
                </Text>
              </BlockStack>
              <BlockStack gap="300">
                <BlockStack gap="100">
                  <Text
                    as="h3"
                    variant="headingMd"
                  >
                    {t("Advance Image Resizer")}
                  </Text>
                  <Text
                    as="p"
                    tone="subdued"
                  >
                    {t("Choose your default image size")}
                  </Text>
                </BlockStack>

                <Select
                  label={t("Resize your image to")}
                  options={[
                    { label: "Do not resize", value: "none" },
                    { label: "4000px", value: "4000" },
                    { label: "2048px (Recommended by Shopify)", value: "2048" },
                    { label: "1600px", value: "1600" },
                  ]}
                  onChange={handleResizeTypeSelectionChange}
                  value={resizeType}
                />
              </BlockStack>
            </BlockStack>
          </BlockStack>
        </Box>
      </Modal.Section>
    </Modal>
  );
}

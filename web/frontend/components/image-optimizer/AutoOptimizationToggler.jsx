//@ts-check
import { <PERSON><PERSON>, <PERSON>, BlockStack, Box, Button, Card, InlineError, InlineStack, Link, Text } from "@shopify/polaris";
import { AlertCircleIcon, InfoIcon } from "@shopify/polaris-icons";
import { useEffect } from "react";
import { Trans, useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { BANNER_UPGRADE_IMAGE_OPTIMIZER } from "storeseo-enums/cacheKeys";
import { useShopApi } from "../../hooks";
import { useBanner } from "../../hooks/useBanner";
import useUserAddon from "../../hooks/useUserAddon";
import { setHiddenBanner } from "../../store/features/HiddenBanner";
import { getCheckoutPath } from "../../utility/helpers";

/**
 * Reusable Auto Optimization Toggler Component
 *
 * @param {{
 *   isEnabled: boolean,
 *   isLoading: boolean,
 *   disabled: boolean,
 *   error?: string,
 *   showWarningBanner?: boolean,
 *   onToggle: () => void,
 *   customTitle?: string,
 *   customDescription?: string
 * }} props
 */
export default function AutoOptimizationToggler({
  isEnabled,
  isLoading,
  disabled,
  error,
  showWarningBanner = false,
  onToggle,
  customTitle,
  customDescription,
}) {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const user = useSelector((state) => state.user);
  const hiddenBanner = useSelector((state) => state.hiddenBanner);
  const shopApi = useShopApi();

  const { hasImageOptimizer, isFreeImageOptimizer } = useUserAddon();
  const checkoutPath = getCheckoutPath(user);

  const title = customTitle || t("Auto Image Optimization");
  const description =
    customDescription || t("Images will automatically start optimizing when you upload them to the Shopify Store");

  // Upgrade banner for free plan users
  const { Banner: ImageOptimizerUpgradeBanner, showBanner: showImageOptimizerUpgradeBanner } = useBanner({
    title: (
      <Text as="p">
        <Trans
          t={t}
          i18nKey="bannerForImageFreePlanUser"
        >
          It seems you are on a FREE plan for the Image Optimization Add-On.
          <Link url={checkoutPath}>Upgrade to a higher plan</Link> to access Auto Image Optimization.
        </Trans>
      </Text>
    ),
    noMargin: true,
    tone: "info",
    icon: InfoIcon,
    onDismiss: () => {
      dispatch(setHiddenBanner({ bannerKey: BANNER_UPGRADE_IMAGE_OPTIMIZER, status: false }));
      shopApi.hideBanner(BANNER_UPGRADE_IMAGE_OPTIMIZER, 7);
    },
  });

  // Show upgrade banner for free plan users
  useEffect(() => {
    if (isFreeImageOptimizer && hiddenBanner[BANNER_UPGRADE_IMAGE_OPTIMIZER]) {
      showImageOptimizerUpgradeBanner(true);
    }
  }, [isFreeImageOptimizer, hiddenBanner, showImageOptimizerUpgradeBanner]);

  return (
    <Card>
      <BlockStack gap="200">
        <InlineStack
          align="space-between"
          blockAlign="baseline"
        >
          <Box maxWidth="85%">
            <BlockStack gap="150">
              <InlineStack
                align="start"
                blockAlign="center"
                gap="150"
              >
                <Text
                  as="h3"
                  variant="headingMd"
                >
                  {title}
                </Text>
                <Badge tone={disabled ? undefined : isEnabled ? "success" : "warning"}>
                  {isEnabled && !disabled ? t("on") : t("off")}
                </Badge>
              </InlineStack>

              {error && (
                <InlineError
                  message={t(error)}
                  fieldID="autoOptimization"
                />
              )}

              <Box width="inherit">
                <Text as="p">{description}</Text>
              </Box>
            </BlockStack>
          </Box>

          <Button
            onClick={onToggle}
            loading={isLoading}
            disabled={disabled}
          >
            {t(`Turn ${isEnabled ? "off" : "on"}`)}
          </Button>
        </InlineStack>

        {/* Show upgrade banner for free plan users */}
        {isFreeImageOptimizer && <ImageOptimizerUpgradeBanner />}

        {showWarningBanner && isEnabled && (
          <Banner
            icon={AlertCircleIcon}
            tone="warning"
          >
            <Text as="p">
              {t("Enabling this feature will automatically use the available limit on your Image Optimizer plan")}
            </Text>
          </Banner>
        )}
      </BlockStack>
    </Card>
  );
}

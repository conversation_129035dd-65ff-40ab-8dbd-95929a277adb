//@ts-check

import { useResourceTitle } from "@/lib/hooks/image-optimizer";
import { textTone } from "@/lib/util/image-optimizer";
import { useImageOptimizerLayoutContext } from "@/providers/ImageOptimizerLayoutProvider";
import {
  Badge,
  BlockStack,
  Box,
  Button,
  ButtonGroup,
  Icon,
  IndexTable,
  InlineStack,
  SkeletonBodyText,
  SkeletonDisplayText,
  SkeletonThumbnail,
  Text,
  Thumbnail,
  useBreakpoints,
} from "@shopify/polaris";
import { ArrowRightIcon, ImageMagicIcon, ImagesIcon, MinusIcon, UndoIcon } from "@shopify/polaris-icons";
import { useMemo } from "react";
import { useTranslation } from "react-i18next";
import imageOptimization from "storeseo-enums/imageOptimization";
import useUserAddon from "../../hooks/useUserAddon";
import { bytesToSize, isSvgImage, minifyTitle, pluralizeText } from "../../utility/helpers";
import TooltipWrapper from "../common/TooltipWrapper";
import TableReferences from "../TableReferences";
import { useImageRowsContext } from "./ImageRows";

export default function ImageRowItem() {
  const { smDown: isSmallDevice } = useBreakpoints();

  return <>{isSmallDevice ? <RowsCellForSmallDevices /> : <RowsCellForLargeDevices />}</>;
}

const RowsCellForSmallDevices = () => {
  const { t } = useTranslation();
  const {
    image,
    thumbnail,
    isOptimizingImage,
    isRestoringImage,
    optimizeImage,
    restoreImage,
    selectedResources,
    optimizationsStatusColor,
    isFetching,
    resourceType,
  } = useImageRowsContext();

  // Get compare function directly from layout context
  const { setImageToCompare } = useImageOptimizerLayoutContext();
  const { src, title, alt_text, id, resources = [], optimization_status, optimization_meta } = image;

  const { imageOptimizerUsageCount, imageOptimizerUsageLimit } = useUserAddon();
  const { singular: singularResourceTile, plural: pluralResourceTitle } = useResourceTitle({ resourceType });

  const resourceTile = useMemo(
    () => pluralizeText(resources.length, singularResourceTile, pluralResourceTitle),
    [resources.length, singularResourceTile, pluralResourceTitle]
  );

  return (
    <>
      <Box
        paddingBlock="300"
        paddingInline="400"
        width="100%"
      >
        <InlineStack
          align="space-between"
          wrap={false}
          gap="200"
        >
          {/* Thumbnail */}
          <Box>
            {isFetching ? (
              <SkeletonThumbnail size="small" />
            ) : (
              <Thumbnail
                source={thumbnail}
                alt={alt_text}
                size="small"
              />
            )}
          </Box>
          <Box width="100%">
            <BlockStack gap="300">
              {/* Basic info */}
              <BlockStack gap="100">
                {isFetching ? (
                  <SkeletonBodyText lines={1} />
                ) : (
                  <Text
                    as="span"
                    variant="bodyMd"
                    fontWeight="medium"
                  >
                    {minifyTitle(title, 24)}
                  </Text>
                )}
                {isFetching ? (
                  <SkeletonBodyText lines={1} />
                ) : (
                  <Text
                    as="p"
                    variant="bodySm"
                    fontWeight="semibold"
                  >
                    {t(resourceTile)}:{" "}
                    <Text
                      as="span"
                      fontWeight="regular"
                    >
                      {resources.map((/** @type {any} */ p) => p?.title).join(", ")}
                    </Text>
                  </Text>
                )}
              </BlockStack>
              {/* Status and actions */}
              <BlockStack gap="200">
                {/* Status */}
                <BlockStack
                  gap="200"
                  align="start"
                >
                  {isFetching ? (
                    <SkeletonBodyText lines={1} />
                  ) : (
                    <Box>
                      <Badge tone={optimizationsStatusColor[optimization_status]}>
                        {t(imageOptimization.labels[optimization_status])}
                      </Badge>
                    </Box>
                  )}

                  {isFetching ? (
                    <SkeletonBodyText lines={1} />
                  ) : optimization_meta?.percentage_optimized ? (
                    <BlockStack
                      inlineAlign="start"
                      gap={"100"}
                    >
                      <InlineStack
                        align="start"
                        gap={"100"}
                        blockAlign="center"
                      >
                        <Text
                          as="p"
                          fontWeight="semibold"
                          variant="bodySm"
                          tone={textTone(optimization_meta?.percentage_optimized, "before")}
                        >
                          {optimization_meta?.original_size}
                        </Text>
                        <div style={{ transform: "scale(0.9)" }}>
                          <Icon
                            tone="subdued"
                            source={ArrowRightIcon}
                          />
                        </div>
                        <Text
                          as="p"
                          fontWeight="semibold"
                          variant="bodySm"
                          tone={textTone(optimization_meta?.percentage_optimized, "after")}
                        >
                          {optimization_meta?.processed_size} ({optimization_meta?.percentage_optimized})
                        </Text>
                      </InlineStack>
                    </BlockStack>
                  ) : (
                    <Box width="max-content">
                      {image.file_size ? (
                        <Text as="p">{bytesToSize(image.file_size || 0)}</Text>
                      ) : (
                        <Icon source={MinusIcon} />
                      )}
                    </Box>
                  )}
                </BlockStack>
                {/* Actions */}
                {isFetching ? (
                  <SkeletonDisplayText size={"small"} />
                ) : (
                  <div onClick={(e) => (e.stopPropagation(), e.preventDefault())}>
                    <ButtonGroup noWrap>
                      {optimization_status === imageOptimization.OPTIMIZED ? (
                        <TooltipWrapper content="Compare">
                          <Button
                            icon={ImagesIcon}
                            onClick={() => setImageToCompare(image)}
                            disabled={selectedResources.includes(id)}
                            variant="tertiary"
                          >
                            {t("Compare")}
                          </Button>
                        </TooltipWrapper>
                      ) : (
                        <TooltipWrapper
                          content={
                            isSvgImage(src)
                              ? "SVG image optimization is not available right now. We're working to make it available soon..."
                              : "Optimize"
                          }
                        >
                          <Button
                            icon={ImageMagicIcon}
                            onClick={() => optimizeImage(image)}
                            loading={isOptimizingImage}
                            disabled={
                              selectedResources.includes(id) ||
                              optimization_status === imageOptimization.PENDING ||
                              imageOptimizerUsageCount >= imageOptimizerUsageLimit ||
                              isSvgImage(src)
                            }
                            variant="tertiary"
                          >
                            {t("Optimize")}
                          </Button>
                        </TooltipWrapper>
                      )}
                      <TooltipWrapper content="Restore">
                        <Button
                          loading={isRestoringImage}
                          icon={UndoIcon}
                          onClick={() => restoreImage(image)}
                          disabled={
                            selectedResources.includes(id) ||
                            optimization_status === imageOptimization.PENDING ||
                            !image.optimization_meta?.original_image_url
                          }
                          variant="tertiary"
                        >
                          {t("Restore")}
                        </Button>
                      </TooltipWrapper>
                    </ButtonGroup>
                  </div>
                )}
              </BlockStack>
            </BlockStack>
          </Box>
        </InlineStack>
      </Box>
    </>
  );
};

const RowsCellForLargeDevices = () => {
  const { t } = useTranslation();
  const {
    image,
    thumbnail,
    isOptimizingImage,
    isRestoringImage,
    optimizeImage,
    restoreImage,
    selectedResources,
    optimizationsStatusColor,
    isFetching,
    resourceType,
  } = useImageRowsContext();

  // Get compare function directly from layout context
  const { setImageToCompare } = useImageOptimizerLayoutContext();
  const { src, title, alt_text, id, resources = [], optimization_status, optimization_meta } = image;

  const { imageOptimizerUsageCount, imageOptimizerUsageLimit } = useUserAddon();

  return (
    <>
      <IndexTable.Cell className="width-50">
        {isFetching ? (
          <SkeletonThumbnail size="small" />
        ) : (
          <Thumbnail
            source={thumbnail}
            alt={alt_text}
            size="small"
          />
        )}
      </IndexTable.Cell>

      <IndexTable.Cell className="break_coll_content width-resource_table_title">
        {isFetching ? (
          <SkeletonBodyText lines={1} />
        ) : (
          <Text
            as="h4"
            variant="bodyMd"
            fontWeight="semibold"
          >
            {minifyTitle(title, 24)}
          </Text>
        )}
      </IndexTable.Cell>

      <IndexTable.Cell>
        {isFetching ? (
          <SkeletonBodyText lines={1} />
        ) : (
          <Badge tone={optimizationsStatusColor[optimization_status]}>
            {t(imageOptimization.labels[optimization_status])}
          </Badge>
        )}
      </IndexTable.Cell>

      <IndexTable.Cell className="break_coll_content">
        {isFetching ? (
          <SkeletonBodyText lines={1} />
        ) : optimization_meta?.percentage_optimized ? (
          <BlockStack gap={"100"}>
            <Text
              as="p"
              fontWeight="semibold"
              variant="bodySm"
              tone={textTone(optimization_meta?.percentage_optimized, "before")}
              textDecorationLine="line-through"
            >
              {optimization_meta?.original_size}
            </Text>

            <Text
              as="p"
              fontWeight="semibold"
              variant="bodySm"
              tone={textTone(optimization_meta?.percentage_optimized, "after")}
            >
              {optimization_meta?.processed_size} ({optimization_meta?.percentage_optimized})
            </Text>
          </BlockStack>
        ) : (
          <>
            <BlockStack gap={"100"}>
              {image.file_size ? bytesToSize(image.file_size || 0) : <Icon source={MinusIcon} />}
            </BlockStack>
          </>
        )}
      </IndexTable.Cell>

      <IndexTable.Cell className="break_coll_content width-resource_table_title">
        {isFetching ? (
          <SkeletonBodyText lines={1} />
        ) : (
          <TableReferences
            resources={resources}
            resourceType={resourceType}
          />
        )}
      </IndexTable.Cell>

      <IndexTable.Cell>
        {isFetching ? (
          <SkeletonDisplayText size={"small"} />
        ) : (
          <div onClick={(e) => (e.stopPropagation(), e.preventDefault())}>
            <BlockStack
              align="center"
              inlineAlign="center"
            >
              <ButtonGroup noWrap>
                {optimization_status === imageOptimization.OPTIMIZED ? (
                  <TooltipWrapper content={t("Compare")}>
                    <Button
                      icon={ImagesIcon}
                      onClick={() => setImageToCompare(image)}
                      disabled={selectedResources.includes(id)}
                      variant="tertiary"
                    />
                  </TooltipWrapper>
                ) : (
                  <TooltipWrapper
                    content={
                      isSvgImage(src)
                        ? t(
                            "SVG image optimization is not available right now. We're working to make it available soon..."
                          )
                        : t("Optimize")
                    }
                  >
                    <Button
                      icon={ImageMagicIcon}
                      onClick={() => optimizeImage(image)}
                      loading={isOptimizingImage}
                      disabled={
                        selectedResources.includes(id) ||
                        optimization_status === imageOptimization.PENDING ||
                        imageOptimizerUsageCount >= imageOptimizerUsageLimit ||
                        isSvgImage(src)
                      }
                      variant="tertiary"
                    />
                  </TooltipWrapper>
                )}
                <TooltipWrapper content={t("Restore")}>
                  <Button
                    loading={isRestoringImage}
                    icon={UndoIcon}
                    onClick={() => restoreImage(image)}
                    disabled={
                      selectedResources.includes(id) ||
                      optimization_status === imageOptimization.PENDING ||
                      !image.optimization_meta?.original_image_url
                    }
                    variant="tertiary"
                  />
                </TooltipWrapper>
              </ButtonGroup>
            </BlockStack>
          </div>
        )}
      </IndexTable.Cell>
    </>
  );
};

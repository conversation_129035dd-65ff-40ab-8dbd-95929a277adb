//@ts-check
import { BlockStack, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import HintText from "../common/HintText";
import RadioGroup from "../common/RadioGroup";

/**
 * Reusable Image Compression Settings Component
 * 
 * @param {{
 *   compressionOptions: Array<{label: string, value: string, hint: string}>,
 *   selectedCompression: string,
 *   onCompressionChange: (item: {value: string}) => void,
 *   disabled?: boolean,
 *   title?: string,
 *   description?: string,
 *   showHint?: boolean
 * }} props
 */
export default function ImageCompressionSettings({
  compressionOptions,
  selectedCompression,
  onCompressionChange,
  disabled = false,
  title,
  description,
  showHint = true,
}) {
  const { t } = useTranslation();

  const defaultTitle = title || t("Image Compression Settings");
  const defaultDescription = description || t("Choose your default image compression settings");

  const selectedOption = compressionOptions?.find((ct) => ct.value === selectedCompression);

  return (
    <BlockStack gap="300">
      <BlockStack gap="050">
        <Text
          as="h3"
          variant="headingMd"
        >
          {defaultTitle}
        </Text>
        <Text
          as="p"
          tone="subdued"
        >
          {defaultDescription}
        </Text>
      </BlockStack>
      
      <BlockStack gap="050">
        <Text as="p">{t("Compression Types")}</Text>
        <RadioGroup
          items={compressionOptions}
          selected={selectedCompression}
          onChange={onCompressionChange}
          disabled={disabled}
        />
        
        {showHint && selectedOption?.hint && (
          <HintText content={t(selectedOption.hint)} />
        )}
      </BlockStack>
    </BlockStack>
  );
}

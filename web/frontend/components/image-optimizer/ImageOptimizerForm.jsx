//@ts-check
import { <PERSON>leed, BlockStack, Card, Divider, InlineError } from "@shopify/polaris";
import { Controller } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { IMAGE_OPTIMIZER_OPTIONS } from "../../config";

import ImageCompressionSettings from "./ImageCompressionSettings";
import ImageFormatSettings from "./ImageFormatSettings";
import ImageResizeSettings from "./ImageResizeSettings";

/**
 * Comprehensive Image Optimizer Form Component
 *
 * @param {{
 *   control: import('react-hook-form').Control<any>,
 *   formState: import('react-hook-form').FormState<any>,
 *   disabled?: boolean,
 *   showFormat?: boolean,
 *   showHints?: boolean,
 *   customTitles?: {
 *     compression?: string,
 *     format?: string,
 *     resize?: string
 *   },
 *   customDescriptions?: {
 *     compression?: string,
 *     format?: string,
 *     resize?: string
 *   }
 * }} props
 */
export default function ImageOptimizerForm({
  control,
  formState,
  disabled = false,
  showFormat = false,
  showHints = true,
  customTitles = {},
  customDescriptions = {},
}) {
  const { t } = useTranslation();
  const { errors } = formState;

  return (
    <BlockStack gap="400">
      {/* Optimization Settings Form */}
      <Card>
        <BlockStack gap="400">
          {/* Global form error */}
          {errors.optimizationSettings && (
            <InlineError
              message={t(typeof errors.optimizationSettings.message === 'string' ? errors.optimizationSettings.message : 'Validation error')}
              fieldID="optimizationSettings"
            />
          )}

          {/* Compression Settings */}
          <Controller
            name="compressionType"
            control={control}
            render={({ field }) => (
              <ImageCompressionSettings
                compressionOptions={IMAGE_OPTIMIZER_OPTIONS.COMPRESSION_TYPES}
                selectedCompression={field.value}
                onCompressionChange={(item) => field.onChange(item.value)}
                disabled={disabled}
                title={customTitles.compression}
                description={customDescriptions.compression}
                showHint={showHints}
              />
            )}
          />

          {/* Format Settings (optional) */}
          {showFormat && (
            <>
              <Bleed marginInline="400">
                <Divider />
              </Bleed>
              <Controller
                name="format"
                control={control}
                render={({ field }) => (
                  <ImageFormatSettings
                    formatOptions={IMAGE_OPTIMIZER_OPTIONS.FORMATS}
                    selectedFormat={field.value}
                    onFormatChange={field.onChange}
                    disabled={disabled}
                    title={customTitles.format}
                    description={customDescriptions.format}
                    showHint={showHints}
                  />
                )}
              />
            </>
          )}

          {/* Resize Settings */}
          <Bleed marginInline="400">
            <Divider />
          </Bleed>
          <Controller
            name="resize"
            control={control}
            render={({ field }) => (
              <ImageResizeSettings
                resizeOptions={IMAGE_OPTIMIZER_OPTIONS.RESIZES}
                selectedResize={field.value}
                onResizeChange={field.onChange}
                disabled={disabled}
                title={customTitles.resize}
                description={customDescriptions.resize}
                showHint={showHints}
              />
            )}
          />

          {/* Individual field errors */}
          {errors.compressionType && (
            <InlineError
              message={t(typeof errors.compressionType.message === 'string' ? errors.compressionType.message : 'Validation error')}
              fieldID="compressionType"
            />
          )}
          {errors.format && (
            <InlineError
              message={t(typeof errors.format.message === 'string' ? errors.format.message : 'Validation error')}
              fieldID="format"
            />
          )}
          {errors.resize && (
            <InlineError
              message={t(typeof errors.resize.message === 'string' ? errors.resize.message : 'Validation error')}
              fieldID="resize"
            />
          )}
        </BlockStack>
      </Card>
    </BlockStack>
  );
}

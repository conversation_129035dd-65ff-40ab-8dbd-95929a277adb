import { useMultiLanguageSetting } from "@/hooks/useMultiLanguageSetting.jsx";
import AIOptimizationStatusBadge from "@/modules/optimize-seo/AIOptimizationStatusBadge.jsx";
import OptimizeSeoStatusBadge from "@/modules/optimize-seo/OptimizeSeoStatusBadge";
import {
  Badge,
  BlockStack,
  Box,
  Button,
  IndexTable,
  InlineStack,
  Link,
  Text,
  Thumbnail,
  useBreakpoints,
} from "@shopify/polaris";
import { ComposeIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";
import { useLabelColor } from "../../hooks/useLabelColor.js";
import { formatDate, prepareThumbnailURL } from "../../utility/helpers.jsx";
import RadialChart from "../charts/RadialChart.jsx";
import TitleColumn from "../common/TitleColumn.jsx";
import TooltipWrapper from "../common/TooltipWrapper.jsx";
import ProductRowItemSkeleton from "./ProductRowItemSkeleton.jsx";

export default function ProductRowItem({ item, isFetching }) {
  const { smDown: isSmallDevice } = useBreakpoints();

  if (isFetching) return <ProductRowItemSkeleton />;
  return <>{isSmallDevice ? <RowsCellForSmallDevices item={item} /> : <RowsCellForLargeDevices item={item} />}</>;
}

const RowsCellForSmallDevices = ({ item }) => {
  const { title, focus_keyword, featuredImage, created_at, score, issues, shopifyId, ai_optimization_status } = item;
  const { t } = useTranslation();
  const labelColor = useLabelColor(issues);
  const thumbnail = prepareThumbnailURL(featuredImage?.src);
  const { usingMultiLanguageMode, selectedLanguage } = useMultiLanguageSetting();

  return (
    <Link
      monochrome
      removeUnderline
      url={`/optimize-seo/products/${shopifyId}?languageCode=${selectedLanguage}`}
      onClick={(e) => e.stopPropagation()}
    >
      <Box
        paddingBlock="300"
        paddingInline="400"
        width="100%"
      >
        <BlockStack gap="150">
          <InlineStack
            align="space-between"
            wrap={false}
            gap="200"
          >
            {/* Thumbnail */}
            <Box>
              <Thumbnail
                source={thumbnail}
                alt={featuredImage?.altText}
                size="small"
              />
            </Box>
            <Box width="100%">
              <BlockStack gap="300">
                {/* Basic info */}
                <BlockStack gap="100">
                  <InlineStack
                    wrap={false}
                    gap="200"
                    blockAlign="center"
                    align="start"
                  >
                    <Text
                      as={"h4"}
                      fontWeight="semibold"
                    >
                      {title}
                    </Text>
                    {!usingMultiLanguageMode && (
                      <AIOptimizationStatusBadge
                        status={ai_optimization_status}
                        noBadge
                      />
                    )}
                  </InlineStack>
                  <Text
                    as="span"
                    variant="bodySm"
                  >
                    {focus_keyword}
                  </Text>
                </BlockStack>
                {/* Scoring */}
                <InlineStack
                  align="space-between"
                  blockAlign="center"
                  gap="200"
                >
                  <BlockStack
                    gap="200"
                    blockAlign="center"
                  >
                    <InlineStack gap="200">
                      <Badge tone={labelColor}>
                        {issues} {t("Issues")}
                      </Badge>

                      <OptimizeSeoStatusBadge score={score} />
                    </InlineStack>

                    <Text
                      as="span"
                      variant="bodySm"
                      tone="subdued"
                    >
                      {formatDate(created_at)}
                    </Text>
                  </BlockStack>

                  <RadialChart
                    score={score}
                    dimension={40}
                  />
                </InlineStack>
              </BlockStack>
            </Box>
          </InlineStack>
        </BlockStack>
      </Box>
    </Link>
  );
};

const RowsCellForLargeDevices = ({ item }) => {
  const { title, focus_keyword, featuredImage, created_at, score, issues, shopifyId, ai_optimization_status } = item;
  const { t } = useTranslation();
  const labelColor = useLabelColor(issues);
  const thumbnail = prepareThumbnailURL(featuredImage?.src);
  const { selectedLanguage, usingMultiLanguageMode } = useMultiLanguageSetting();

  return (
    <>
      <IndexTable.Cell className="width-table_thumbnail">
        <Thumbnail
          source={thumbnail}
          alt={featuredImage?.altText}
          size="small"
        />
      </IndexTable.Cell>
      <IndexTable.Cell className="break_coll_content width-resource_table_title">
        <InlineStack
          wrap={false}
          gap="200"
          blockAlign="center"
        >
          <TitleColumn
            title={title}
            url={`/optimize-seo/products/${shopifyId}?languageCode=${selectedLanguage}`}
          />
          {!usingMultiLanguageMode && (
            <AIOptimizationStatusBadge
              status={ai_optimization_status}
              noBadge
            />
          )}
        </InlineStack>
      </IndexTable.Cell>
      <IndexTable.Cell className="width-150">
        <OptimizeSeoStatusBadge score={score} />
      </IndexTable.Cell>
      <IndexTable.Cell className="break_coll_content width-resource_table_title">{focus_keyword}</IndexTable.Cell>

      <IndexTable.Cell>
        <Badge tone={labelColor}>
          {issues} {t("Issues")}
        </Badge>
      </IndexTable.Cell>
      <IndexTable.Cell>
        <RadialChart
          score={score}
          dimension={40}
        />
      </IndexTable.Cell>
      <IndexTable.Cell className="width-120">{formatDate(created_at)}</IndexTable.Cell>
      <IndexTable.Cell className="width-80">
        <InlineStack align="center">
          <TooltipWrapper content="Fix issue">
            <Button
              icon={ComposeIcon}
              onClick={(e) => e.stopPropagation()}
              variant="tertiary"
              url={`/optimize-seo/products/${shopifyId}?languageCode=${selectedLanguage}`}
            />
          </TooltipWrapper>
        </InlineStack>
      </IndexTable.Cell>
    </>
  );
};

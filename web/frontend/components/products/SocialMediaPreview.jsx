import { useSelector } from "react-redux";
import { useProductApi, useProductFeaturedImage } from "../../hooks";
import { getMetaTitleAndDescription } from "../../utility/helpers";
import AccordionCard from "../common/AccordionCard";
import GoogleSearchPreview from "../previewers/GoogleSearchPreview";
import FacebookLinkPreview from "../previewers/FacebookLinkPreview";
import TwitterCardPreview from "../previewers/TwitterCardPreview";
import { useTranslation } from "react-i18next";
import { useState } from "react";
import ImgCropper from "../common/ImgCropper";
import { useMutation, useQueryClient } from "react-query";
import { useParams } from "react-router-dom";
import queryKeys from "../../utility/queryKeys";

const previewImgType = {
  FACEBOOK: "facebook_preview_image",
  TWITTER: "twitter_preview_image",
};

export default function SocialMediaPreview({ product, viewMode = false }) {
  const user = useSelector((state) => state.user);
  const { t } = useTranslation();
  const featuredImage = useProductFeaturedImage(product);
  const productApi = useProductApi();
  const { id } = useParams();
  const queryClient = useQueryClient();
  const QUERY_KEY = [queryKeys.TEMP_PRODUCT_DATA, { id }];

  const { metaTitle: title, metaDescription: description } = getMetaTitleAndDescription(product);
  const featuredImageURL = featuredImage?.src;
  const productURL = product?.online_store_url || product?.online_store_preview_url;
  const domain = user?.url?.replace("https://", "").split("/")[0];

  const [showCropper, setShowCropper] = useState(false);
  const [inputImage, setInputImage] = useState(null);
  const [imageInputFor, setImageInputFor] = useState(null);

  const { mutate: uploadImg } = useMutation({
    mutationFn: ({ dataURL, file }) => productApi.uploadSocialMediaPreviewImage(id, file, imageInputFor),
    onMutate: ({ dataURL, file }) => {
      setShowCropper(false);
      const { product, optimizationData, focusKeywordSuggestions } = queryClient.getQueryData(QUERY_KEY);
      queryClient.setQueryData(QUERY_KEY, {
        product: {
          ...product,
          facebookPreviewImage: imageInputFor === previewImgType.FACEBOOK ? dataURL : product.facebookPreviewImage,
          twitterPreviewImage: imageInputFor === previewImgType.TWITTER ? dataURL : product.twitterPreviewImage,
          isOriginalData: false,
        },
        optimizationData,
        focusKeywordSuggestions,
      });
    },
    onSuccess: () => {
      const data = queryClient.getQueryData(QUERY_KEY);
      queryClient.setQueryData([queryKeys.PRODUCT_DATA, { id }], data);
    },
  });

  const handleImageInput = (e, previewImgFor) => {
    setInputImage(e?.target?.files[0]);
    setImageInputFor(previewImgFor);
    setShowCropper(true);
  };

  const handleCropCancel = () => {
    setInputImage(null);
    setImageInputFor(null);
    setShowCropper(false);
  };

  return (
    <div className="p30">
      <AccordionCard
        title="Google Search Preview"
        defaultOpen={true}
      >
        <GoogleSearchPreview
          url={productURL}
          title={title}
          description={description}
          previewImageURL={featuredImageURL}
        />
      </AccordionCard>

      <AccordionCard title="Facebook Link Preview">
        <FacebookLinkPreview
          url={domain}
          title={title}
          descripton={description}
          previewImageURL={product.facebookPreviewImage}
        />

        {!viewMode && (
          <div className="form__group">
            <label>{t("Facebook Image")}</label>
            <div className="logo__uploader">
              <label className="input__button">
                <input
                  type="file"
                  accept="image/*"
                  value=""
                  onChange={(e) => handleImageInput(e, previewImgType.FACEBOOK)}
                />
                <span>{t("Select Image")}</span>
              </label>
            </div>
          </div>
        )}
      </AccordionCard>

      <AccordionCard title="X Link Preview">
        <TwitterCardPreview
          url={domain}
          title={title}
          descripton={description}
          previewImageURL={product.twitterPreviewImage}
        />

        {!viewMode && (
          <div className="form__group">
            <label>{t("X Image")}</label>
            <div className="logo__uploader">
              <label className="input__button">
                <input
                  type="file"
                  accept="image/*"
                  value=""
                  onChange={(e) => handleImageInput(e, previewImgType.TWITTER)}
                />
                <span>{t("Select Image")}</span>
              </label>
            </div>
          </div>
        )}
      </AccordionCard>

      {showCropper && inputImage && (
        <ImgCropper
          imageSrc={URL.createObjectURL(inputImage)}
          onCancel={handleCropCancel}
          onCropComplete={uploadImg}
          hintMessage="Please use an image with minimum 600 x 314 pixels size."
          aspectRatio={1.91}
        />
      )}
    </div>
  );
}

// @ts-check
import useIsRunningBackupRestore from "@/lib/hooks/useIsRunningBackupRestore.jsx";
import { IndexTable } from "@shopify/polaris";
import AiOptimizationStatus from "storeseo-enums/aiOptimization";
import ProductRowItem from "./ProductRowItem.jsx";

const ProductRows = ({ products, isFetching = false, selectedResources }) => {
  const { isRunning: isRunningBackupOrRestore } = useIsRunningBackupRestore();
  return (
    <>
      {products?.map(({ id, ...product }, index) => {
        const isDisableRow = [
          AiOptimizationStatus.PENDING,
          AiOptimizationStatus.DISPATCHED,
          AiOptimizationStatus.SAVING,
          AiOptimizationStatus.PROCESSING,
        ].includes(product.ai_optimization_status);

        return (
          <IndexTable.Row
            id={id}
            key={index}
            position={index}
            selected={selectedResources?.includes(id)}
            disabled={isDisableRow || isRunningBackupOrRestore}
          >
            <ProductRowItem
              item={product}
              isFetching={isFetching}
            />
          </IndexTable.Row>
        );
      })}
    </>
  );
};

export default ProductRows;

import { <PERSON><PERSON>, I<PERSON>, Tooltip } from "@shopify/polaris";
import { ResetIcon } from "@shopify/polaris-icons";
import { isEmpty } from "lodash";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { useDispatch, useSelector } from "react-redux";
import socketEvents from "storeseo-enums/socketEvents";
import { useProductApi } from "../../hooks";
import { usePusher } from "../../providers/PusherProvider";
import { setProductCount } from "../../store/features/ProductCount";
import { setProductSyncInfo, setProductSyncStatus } from "../../store/features/ProductSync";
import ConfirmationModal from "../modals/ConfirmationModal.jsx";

const ProductSyncButton = ({ onSyncErr = () => {}, isDisable = false }) => {
  const productSync = useSelector((state) => state.productSync);
  const user = useSelector((state) => state.user);
  const productCount = useSelector((state) => state.productCount);

  const dispatch = useDispatch();
  const { pusherChannel } = usePusher();

  const { t } = useTranslation();
  const productApi = useProductApi();

  const [isTooltipActive, setIsTooltipActive] = useState(false);
  const [tooltipContent, setTooltipContent] = useState("");
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  let { mutate: startSyncOperation, isLoading } = useMutation(productApi.syncProductsFromShopify, {
    onSuccess: (status) => {
      syncStatusCallback(status);
    },
    onError: (err) => {
      onSyncErr();
    },
    onSettled: () => setShowConfirmModal(false),
  });

  const syncUpdateEventListener = (message) => {
    // console.log("SYNC UPDATE...", message);
    dispatch(setProductSyncInfo({ total: message.total, synced: message.synced }));
    dispatch(setProductCount(message.synced));
  };

  useEffect(() => {
    if (!pusherChannel) return;

    // console.log("subscribing to sync update/complete events...");

    pusherChannel.bind(socketEvents.PRODUCT_SYNC_UPDATE, syncUpdateEventListener);
    // console.log("Done!");
  }, [pusherChannel]);

  const syncStatusCallback = (status) => {
    dispatch(setProductSyncStatus(status));
  };

  const handleSyncProduct = async () => {
    setShowConfirmModal(true);
  };

  useEffect(() => {
    const permittedProductCount =
      user?.permission?.products === null ? Number.POSITIVE_INFINITY : user?.permission?.products;

    if (permittedProductCount <= productCount) {
      const message = t(
        "You've already synced {{productCount}}/{{permittedProductCount}} products. Please upgrade your subscription to sync more products.",
        {
          productCount,
          permittedProductCount,
        }
      );

      setTooltipContent(message);
      // setIsTooltipActive(true);
    } else if (!isEmpty(productSync) && productSync.ongoing) {
      const message =
        t("Syncing Products") + (productSync.total > 0 ? ` ${productSync.synced}/${productSync.total}` : "...");
      setTooltipContent(message);
      setIsTooltipActive(true);
    } else {
      setTooltipContent("");
      setIsTooltipActive(false);
    }
  }, [user, productSync]);

  const disableSyncBtn = !user?.isSubscribed || isTooltipActive || isDisable;

  return (
    <>
      <Tooltip
        active={isTooltipActive}
        content={t(tooltipContent)}
        persistOnClick
        activatorWrapper={"div"}
        zIndexOverride={99}
      >
        <Button
          icon={<Icon source={ResetIcon} />}
          onClick={handleSyncProduct}
          disabled={disableSyncBtn}
          loading={isLoading || productSync?.ongoing}
          // variant="tertiary"
        >
          {t("Sync products")}
        </Button>
      </Tooltip>

      <ConfirmationModal
        show={showConfirmModal}
        onClose={setShowConfirmModal}
        title={"Sync products"}
        content={"Are you sure you want to sync products from Shopify?"}
        primaryAction={startSyncOperation}
        loading={isLoading}
      />
    </>
  );
};

export default ProductSyncButton;

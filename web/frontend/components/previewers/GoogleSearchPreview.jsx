import { BlockStack, Box, Button, ButtonGroup, Card, Icon, InlineStack, Text } from "@shopify/polaris";
import { ChevronRightIcon, MenuVerticalIcon } from "@shopify/polaris-icons";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useFeaturedImage } from "../../hooks/useFeaturedImage";
import { getMetaTitleAndDescription, prepareThumbnailURL, textReduce } from "../../utility/helpers";

/**
 * @typedef {Object} GoogleSearchPreviewProps
 * @property {any} item - The item to be displayed in the preview
 * @property {string} type - The type of the item
 * @property {string} url - The URL to be displayed in the preview
 */

/**
 * A component that displays a Google search result preview
 * @param {GoogleSearchPreviewProps} props - The component props
 * @returns {JSX.Element} The rendered component
 */

const GoogleSearchPreview = ({ item, type, url }) => {
  const [showMobilePreview, setShowMobilePreview] = useState(false);
  const { t } = useTranslation();

  const featuredImage = useFeaturedImage(item, type);
  const previewImageURL = prepareThumbnailURL(featuredImage?.src, "100x100@2x", false);
  const { metaTitle: title, metaDescription: description } = getMetaTitleAndDescription(item);

  const getURLPartsForPreview = () => {
    const urlArr = [];
    const splited = textReduce(url, 55, "..").split("//");
    const parts = splited.pop().split("/");
    urlArr.push(`${splited.shift()}//${parts.shift()}`);

    if (parts.length > 0) {
      parts.forEach((part) => urlArr.push(part));
    }

    return urlArr;
  };

  return (
    <Box padding="400">
      <BlockStack gap="200">
        <InlineStack align="space-between">
          <Text
            as="h6"
            variant="headingSm"
          >
            {t("Google Search Preview")}
          </Text>
          <ButtonGroup variant="segmented">
            <Button
              pressed={!showMobilePreview}
              onClick={() => setShowMobilePreview(false)}
            >
              {t("Desktop")}
            </Button>
            <Button
              pressed={showMobilePreview}
              onClick={() => setShowMobilePreview(true)}
            >
              {t("Mobile")}
            </Button>
          </ButtonGroup>
        </InlineStack>
        {!showMobilePreview ? (
          <Card>
            <Box>
              <InlineStack
                gap="200"
                align="space-between"
              >
                <InlineStack
                  gap="050"
                  wrap={false}
                >
                  {getURLPartsForPreview().map((urlPart, idx) => (
                    <InlineStack
                      key={idx}
                      gap="025"
                      align="center"
                    >
                      <Text
                        as="span"
                        variant="bodySm"
                        tone="subdued"
                      >
                        {urlPart}
                      </Text>
                      {idx < getURLPartsForPreview().length - 1 && (
                        <Icon
                          source={ChevronRightIcon}
                          tone="subdued"
                        />
                      )}
                    </InlineStack>
                  ))}
                </InlineStack>
                <Box as="span">
                  <Icon
                    source={MenuVerticalIcon}
                    tone="base"
                  />
                </Box>
              </InlineStack>
              <Box paddingBlockStart="200">
                <InlineStack
                  gap="400"
                  align="space-between"
                >
                  <Box style={{ flex: 1 }}>
                    <Text
                      as="h3"
                      variant="headingMd"
                      style={{
                        fontSize: 20,
                        fontWeight: 500,
                        color: "#1a0dab",
                        marginBottom: 3,
                      }}
                    >
                      {title}
                    </Text>
                    <Text
                      as="p"
                      variant="bodyMd"
                      style={{
                        fontSize: 14,
                        fontWeight: 400,
                        lineHeight: 1.7,
                        color: "#4d5156",
                      }}
                    >
                      {description}
                    </Text>
                  </Box>
                  {previewImageURL && (
                    <Box
                      style={{
                        flex: "0 0 92px",
                        height: 92,
                        width: 92,
                        overflow: "hidden",
                        borderRadius: 10,
                      }}
                    >
                      <img
                        src={previewImageURL}
                        alt=""
                        style={{
                          width: "100%",
                          height: "100%",
                          objectFit: "cover",
                        }}
                      />
                    </Box>
                  )}
                </InlineStack>
              </Box>
            </Box>
          </Card>
        ) : (
          <Box style={{ maxWidth: 450 }}>
            <Card>
              <Box>
                <InlineStack
                  gap="200"
                  align="space-between"
                >
                  <InlineStack
                    gap="050"
                    wrap={false}
                  >
                    {getURLPartsForPreview().map((urlPart, idx) => (
                      <InlineStack
                        key={idx}
                        gap="025"
                        align="center"
                      >
                        <Text
                          as="span"
                          variant="bodySm"
                          tone="subdued"
                        >
                          {urlPart}
                        </Text>
                        {idx < getURLPartsForPreview().length - 1 && (
                          <Icon
                            source={ChevronRightIcon}
                            tone="subdued"
                          />
                        )}
                      </InlineStack>
                    ))}
                  </InlineStack>
                  <Box as="span">
                    <Icon
                      source={MenuVerticalIcon}
                      tone="base"
                    />
                  </Box>
                </InlineStack>
                <Box paddingBlockStart="200">
                  <Text
                    as="h3"
                    variant="headingMd"
                    style={{
                      fontSize: 20,
                      fontWeight: 500,
                      color: "#1a0dab",
                      marginBottom: 3,
                    }}
                  >
                    {title}
                  </Text>
                  <InlineStack
                    gap="400"
                    align="space-between"
                  >
                    <Text
                      as="p"
                      variant="bodyMd"
                      style={{
                        fontSize: 14,
                        fontWeight: 400,
                        lineHeight: 1.7,
                        color: "#4d5156",
                        flex: 1,
                      }}
                    >
                      {description}
                    </Text>
                    {previewImageURL && (
                      <Box
                        style={{
                          flex: "0 0 92px",
                          height: 92,
                          width: 92,
                          overflow: "hidden",
                          borderRadius: 10,
                        }}
                      >
                        <img
                          src={previewImageURL}
                          alt=""
                          style={{
                            width: "100%",
                            height: "100%",
                            objectFit: "cover",
                          }}
                        />
                      </Box>
                    )}
                  </InlineStack>
                </Box>
              </Box>
            </Card>
          </Box>
        )}
      </BlockStack>
    </Box>
  );
};

export default GoogleSearchPreview;

import { Tabs } from "@shopify/polaris";
import { useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { useItemUrl } from "../../hooks/useItemUrl";
import CollapsibleCard from "../common/CollapsibleCard";
import FacebookLinkPreview from "./FacebookLinkPreview";
import GoogleSearchPreview from "./GoogleSearchPreview";
import TwitterCardPreview from "./TwitterCardPreview";

/**
 * @typedef {object} Props
 * @property {any} item
 * @property {string} type
 */

/**
 *
 * @param {Props} param0
 * @returns
 */
export default function SocialMediaPreview({ item, type }) {
  const user = useSelector((state) => state.user);
  const { t } = useTranslation();
  let itemUrl = useItemUrl(item, type);
  const domain = user?.url?.replace("https://", "").split("/")[0];

  const [selected, setSelected] = useState(0);
  const [cardOpen, setCardOpen] = useState(true);

  const handleTabChange = useCallback((selectedTabIndex) => setSelected(selectedTabIndex), []);

  const tabs = [
    {
      id: "google-search-preview",
      panelID: "google-search-preview",
      content: t("Google Search Preview"),
      accessibilityLabel: t("Google Search Preview"),
      children: (
        <GoogleSearchPreview
          item={item}
          type={type}
          url={itemUrl}
        />
      ),
    },
    {
      id: "facebook-link-preview",
      panelID: "facebook-link-preview",
      content: t("Facebook Link Preview"),
      accessibilityLabel: t("Facebook Link Preview"),
      children: (
        <FacebookLinkPreview
          item={item}
          type={type}
          url={domain}
        />
      ),
    },
    {
      id: "x-link-preview",
      panelID: "x-link-preview",
      content: t("X Link Preview"),
      accessibilityLabel: t("X Link Preview"),
      children: (
        <TwitterCardPreview
          item={item}
          type={type}
          url={domain}
        />
      ),
    },
  ];

  return (
    <CollapsibleCard
      title={t("Preview Snippet")}
      cardOpen={cardOpen}
      setCardOpen={setCardOpen}
    >
      <Tabs
        tabs={tabs}
        selected={selected}
        onSelect={handleTabChange}
        fitted
      >
        {tabs[selected].children}
      </Tabs>
    </CollapsibleCard>
  );
}

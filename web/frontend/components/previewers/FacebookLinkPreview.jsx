import { BlockStack, Box, Card, InlineStack, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useFeaturedImage } from "../../hooks/useFeaturedImage";
import { getMetaTitleAndDescription, prepareThumbnailURL } from "../../utility/helpers";

/**
 * @typedef {Object} FacebookLinkPreviewProps
 * @property {any} item - The item to be displayed in the preview
 * @property {string} type - The type of the item
 * @property {string} url - The URL to be displayed in the preview
 * @property {React.ReactNode} [action] - Optional action to be displayed in the preview
 */

/**
 * A component that displays a Facebook link preview
 * @param {FacebookLinkPreviewProps} props - The component props
 * @returns {JSX.Element} The rendered component
 */
const FacebookLinkPreview = ({ item, type, url, action }) => {
  const { t } = useTranslation();

  const featuredImage = useFeaturedImage(item, type);
  const previewImageURL = prepareThumbnailURL(featuredImage?.src, "512x512@2x", false);
  const { metaTitle: title, metaDescription: description } = getMetaTitleAndDescription(item);

  return (
    <Box padding="400">
      <BlockStack gap="200">
        <InlineStack align="space-between">
          <Text
            as="h6"
            variant="headingSm"
          >
            {t("Facebook Link Preview")}
          </Text>
          {action}
        </InlineStack>
        <Card padding="0">
          {previewImageURL && (
            <Box
              style={{
                height: 350,
                fontSize: 0,
                overflow: "hidden",
              }}
            >
              <img
                style={{
                  width: "100%",
                  // height: "100%",
                  // objectFit: "cover",
                }}
                src={previewImageURL}
                alt=""
              />
            </Box>
          )}
          <Box padding="400">
            <Text
              as="p"
              variant="bodyMd"
              style={{
                color: "#65676b",
                fontSize: 14,
                fontWeight: 400,
              }}
            >
              {url?.toUpperCase()}
            </Text>
            <Text
              as="h3"
              variant="headingMd"
              style={{
                fontSize: 16,
                fontWeight: 700,
                color: "#29294f",
              }}
            >
              {title}
            </Text>
            <Text
              as="p"
              variant="bodyMd"
              style={{
                fontSize: 14,
                fontWeight: 400,
                color: "#65676b",
              }}
            >
              {description}
            </Text>
          </Box>
        </Card>
      </BlockStack>
    </Box>
  );
};

export default FacebookLinkPreview;

import React from "react";

const Support = () => {
  return (
    <svg
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle
        cx="20"
        cy="20"
        r="20"
        fill="#998A00"
      />
      <path
        d="M29.6783 20.7281C29.7092 21.4665 29.4459 22.187 28.9463 22.7316C28.4466 23.2761 27.7514 23.6002 27.0131 23.6328C26.9699 23.6348 26.9268 23.6358 26.8838 23.6358C26.6876 23.6357 26.492 23.6144 26.3004 23.5725C25.7318 24.4504 24.9862 25.2 24.1113 25.7733C23.2364 26.3465 22.2515 26.7309 21.2196 26.9018C21.0976 27.2603 20.8541 27.565 20.5312 27.7631C20.2084 27.9612 19.8264 28.0402 19.4515 27.9866C19.0765 27.933 18.7321 27.75 18.4777 27.4693C18.2234 27.1887 18.075 26.828 18.0584 26.4496C18.0418 26.0712 18.1579 25.6988 18.3866 25.397C18.6154 25.0951 18.9424 24.8826 19.3112 24.7963C19.68 24.7099 20.0674 24.7552 20.4064 24.9242C20.7454 25.0931 21.0147 25.3753 21.1677 25.7217C22.6205 25.453 23.933 24.6831 24.8764 23.546C25.8197 22.409 26.3342 20.9769 26.33 19.4995C26.33 16.0091 23.4904 13.1694 20 13.1694C16.5095 13.1694 13.6699 16.0091 13.6699 19.4995C13.6697 20.5788 13.9455 21.6402 14.4712 22.5829C14.489 22.614 14.5024 22.6474 14.5109 22.6822C14.5617 22.8234 14.5563 22.9786 14.4957 23.1158C14.4352 23.2531 14.3243 23.3618 14.1859 23.4195C13.8469 23.5621 13.4828 23.6356 13.1151 23.6355C13.0718 23.6355 13.0284 23.6345 12.985 23.6325C12.247 23.5995 11.5521 23.2752 11.0529 22.7307C10.5536 22.1862 10.2907 21.4659 10.3216 20.7278C10.3341 20.4335 10.3272 20.1709 10.3204 19.9169C10.314 19.6778 10.3074 19.4306 10.3183 19.1726C10.3507 18.4519 10.6607 17.7717 11.1834 17.2745C11.7061 16.7772 12.4009 16.5015 13.1223 16.5051C14.2807 13.8546 16.9273 11.9976 20 11.9976C23.0726 11.9976 25.7193 13.8546 26.8777 16.5051C27.5992 16.501 28.2942 16.7765 28.817 17.2737C29.3397 17.771 29.6496 18.4514 29.6816 19.1721C29.6925 19.4305 29.6859 19.6778 29.6795 19.9169C29.6728 20.1708 29.6658 20.4336 29.6783 20.7281ZM24.9142 19.4995C24.9139 20.2524 24.7407 20.9951 24.408 21.6705C24.0752 22.3458 23.5918 22.9357 22.995 23.3947C22.3982 23.8536 21.704 24.1694 20.9659 24.3176C20.2277 24.4658 19.4654 24.4425 18.7377 24.2495L16.7486 25.3982C16.6482 25.4562 16.5329 25.4831 16.4173 25.4755C16.3016 25.4679 16.1908 25.4262 16.0989 25.3557C16.0069 25.2851 15.938 25.1889 15.9007 25.0791C15.8635 24.9694 15.8596 24.8511 15.8896 24.7391L16.398 22.8427C15.5561 21.9327 15.0877 20.7392 15.0859 19.4995C15.0859 16.789 17.2902 14.5841 20 14.5841C22.7097 14.5841 24.9142 16.789 24.9142 19.4995ZM18.6338 19.4995C18.6338 19.3441 18.572 19.1951 18.4621 19.0852C18.3523 18.9753 18.2032 18.9136 18.0478 18.9136H18.0468C17.931 18.9138 17.8178 18.9483 17.7216 19.0129C17.6254 19.0774 17.5505 19.169 17.5063 19.276C17.4621 19.3831 17.4507 19.5009 17.4734 19.6145C17.4961 19.7281 17.552 19.8324 17.634 19.9142C17.716 19.9961 17.8204 20.0517 17.934 20.0743C18.0477 20.0968 18.1654 20.0851 18.2724 20.0407C18.3794 19.9964 18.4709 19.9213 18.5352 19.8249C18.5995 19.7286 18.6339 19.6154 18.6339 19.4995H18.6338ZM20.586 19.4995C20.586 19.4803 20.5849 19.4612 20.5829 19.4421C20.5811 19.4229 20.5782 19.4039 20.5743 19.3851C20.5708 19.3663 20.5661 19.3478 20.5602 19.3296C20.5548 19.3112 20.5482 19.2933 20.5411 19.2753C20.5341 19.2573 20.5255 19.2405 20.5165 19.2237C20.5076 19.2068 20.4978 19.1904 20.4872 19.1745C20.4766 19.1584 20.4651 19.1429 20.4528 19.128C20.4408 19.1131 20.4279 19.0989 20.4142 19.0854C20.4009 19.0718 20.3865 19.0589 20.3716 19.0464C20.3567 19.0347 20.3411 19.0229 20.3251 19.0124C20.3092 19.0018 20.2928 18.992 20.2759 18.9831C20.2591 18.9741 20.2415 18.9659 20.2239 18.9585C20.2063 18.9511 20.1883 18.9447 20.17 18.9394C20.1335 18.9276 20.0958 18.92 20.0575 18.9167C20.0002 18.9112 19.9423 18.9139 19.8857 18.9249C19.8668 18.9287 19.8482 18.9336 19.8299 18.9394C19.8115 18.9447 19.7935 18.9511 19.7759 18.9585C19.7584 18.9659 19.7408 18.9741 19.724 18.9831C19.7072 18.9921 19.6908 19.0019 19.6747 19.0124C19.6587 19.0229 19.6431 19.0347 19.6287 19.0464C19.6134 19.0589 19.5994 19.0718 19.5857 19.0854C19.572 19.0989 19.5591 19.1131 19.547 19.128C19.5349 19.1429 19.5236 19.1585 19.5131 19.1745C19.5023 19.1904 19.4924 19.2068 19.4834 19.2237C19.4744 19.2405 19.4663 19.2577 19.4591 19.2753C19.4517 19.2931 19.4452 19.3112 19.4396 19.3296C19.4341 19.3476 19.4295 19.3663 19.4256 19.3851C19.4217 19.4039 19.4188 19.4229 19.4169 19.4421C19.415 19.4612 19.4142 19.4804 19.4142 19.4995C19.4142 19.5187 19.415 19.5382 19.4169 19.5573C19.4188 19.5763 19.4217 19.5952 19.4256 19.614C19.4295 19.6327 19.4341 19.6515 19.4396 19.6698C19.4452 19.6881 19.4517 19.7061 19.4591 19.7237C19.4663 19.7415 19.4744 19.7588 19.4834 19.7757C19.4923 19.7925 19.5025 19.8089 19.5131 19.8249C19.5236 19.8408 19.5349 19.8562 19.547 19.871C19.5592 19.886 19.5721 19.9003 19.5857 19.914C19.5994 19.9272 19.6134 19.9405 19.6287 19.9526C19.6434 19.9648 19.6587 19.9762 19.6747 19.9866C19.6908 19.9972 19.7072 20.0073 19.724 20.0163C19.7579 20.034 19.7933 20.0486 19.8299 20.0601C19.8482 20.0655 19.8669 20.0702 19.8857 20.0741C19.9233 20.0818 19.9617 20.0856 20.0001 20.0854C20.1553 20.085 20.3041 20.0234 20.4142 19.914C20.4412 19.8866 20.4656 19.8567 20.4872 19.8249C20.4978 19.8089 20.5075 19.7925 20.5165 19.7757C20.5255 19.7589 20.5337 19.7413 20.5411 19.7237C20.5486 19.7062 20.5548 19.6878 20.5602 19.6698C20.5661 19.6515 20.5708 19.6329 20.5743 19.614C20.5782 19.5952 20.581 19.5763 20.5829 19.5573C20.5849 19.5381 20.5859 19.5188 20.5859 19.4994L20.586 19.4995ZM22.5391 19.4995C22.5391 19.3441 22.4774 19.1951 22.3675 19.0852C22.2576 18.9753 22.1086 18.9136 21.9532 18.9136H21.9521C21.8362 18.9138 21.7231 18.9484 21.6269 19.0129C21.5307 19.0774 21.4557 19.169 21.4116 19.2761C21.3674 19.3832 21.3559 19.5009 21.3787 19.6145C21.4014 19.7281 21.4573 19.8324 21.5393 19.9142C21.6213 19.9961 21.7257 20.0518 21.8393 20.0743C21.9529 20.0968 22.0707 20.0851 22.1777 20.0407C22.2847 19.9964 22.3761 19.9213 22.4405 19.8249C22.5048 19.7286 22.5391 19.6154 22.5391 19.4995Z"
        fill="white"
      />
    </svg>
  );
};

export default Support;

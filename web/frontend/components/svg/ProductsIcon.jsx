const ProductsIcon = () => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.57841 1.18028L1 4.52055H5.98393L6.89659 1.00002H2.86296C2.80317 0.999265 2.74444 1.0159 2.69392 1.0479C2.6434 1.0799 2.60327 1.12589 2.57841 1.18028Z"
        fill="#D7B6A9"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.89649 1L5.98389 4.52053V7.97655C5.98387 8.04123 6.00254 8.10454 6.03764 8.15886C6.07275 8.21318 6.1228 8.25621 6.18178 8.28276C6.24076 8.30931 6.30615 8.31826 6.37009 8.30853C6.43404 8.2988 6.49381 8.27081 6.54222 8.22792L7.90382 7.02254L9.35746 8.33992C9.40561 8.38356 9.46538 8.41229 9.52952 8.42262C9.59367 8.43296 9.65944 8.42446 9.71885 8.39815C9.77826 8.37184 9.82876 8.32885 9.86423 8.27441C9.89969 8.21996 9.91859 8.1564 9.91863 8.09142L9.92118 4.52053L9.02096 1H6.89649Z"
        fill="#FFE5DB"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.02112 1.00002L9.92114 4.52055H15.0009L13.5105 1.18631C13.4864 1.13039 13.4462 1.08286 13.3951 1.04971C13.3439 1.01657 13.2841 0.999278 13.2232 1.00002H9.02112Z"
        fill="#D7B6A9"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.91853 8.09042C9.91849 8.1554 9.89959 8.21897 9.86413 8.27341C9.82866 8.32786 9.77816 8.37084 9.71875 8.39715C9.65934 8.42346 9.59357 8.43196 9.52943 8.42163C9.46528 8.41129 9.40551 8.38256 9.35736 8.33893L7.90374 7.02163L6.54215 8.227C6.49374 8.2699 6.43397 8.29789 6.37002 8.30762C6.30608 8.31735 6.24069 8.3084 6.18171 8.28185C6.12273 8.25529 6.07268 8.21227 6.03757 8.15795C6.00247 8.10363 5.9838 8.04032 5.98382 7.97564V4.51953H1V13.9613C1.0008 14.2365 1.11048 14.5002 1.30507 14.6948C1.49966 14.8894 1.76336 14.999 2.03856 14.9998H13.9623C14.2375 14.999 14.5012 14.8894 14.6958 14.6948C14.8904 14.5002 15 14.2365 15.0008 13.9613V4.51953H9.92108L9.91853 8.09042ZM5.81663 10.7004V12.7146C5.81631 12.8397 5.76647 12.9596 5.67801 13.0481C5.58955 13.1366 5.46967 13.1864 5.34457 13.1867H3.33032C3.20521 13.1864 3.08532 13.1366 2.99686 13.0481C2.90839 12.9596 2.85855 12.8397 2.85823 12.7146V10.7004C2.85859 10.5753 2.90845 10.4554 2.9969 10.367C3.08536 10.2785 3.20522 10.2287 3.33032 10.2283H5.34457C5.46966 10.2287 5.58951 10.2785 5.67796 10.367C5.76641 10.4554 5.81626 10.5753 5.81663 10.7004Z"
        fill="#AA6F57"
      />
      <path
        d="M5.34449 10.2285H3.33024C3.06952 10.2285 2.85815 10.4399 2.85815 10.7006V12.7149C2.85815 12.9756 3.06952 13.1869 3.33024 13.1869H5.34449C5.60522 13.1869 5.81658 12.9756 5.81658 12.7149V10.7006C5.81658 10.4399 5.60522 10.2285 5.34449 10.2285Z"
        fill="#FFE5DB"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.51912 11.5255C3.46497 11.5255 3.41303 11.504 3.37474 11.4657C3.33645 11.4274 3.31494 11.3755 3.31494 11.3214C3.31494 11.2672 3.33645 11.2153 3.37474 11.177C3.41303 11.1387 3.46497 11.1172 3.51912 11.1172H5.06133C5.11548 11.1172 5.16742 11.1387 5.20571 11.177C5.244 11.2153 5.26551 11.2672 5.26551 11.3214C5.26551 11.3755 5.244 11.4274 5.20571 11.4657C5.16742 11.504 5.11548 11.5255 5.06133 11.5255H3.51912Z"
        fill="#AA6F57"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.51913 12.299C3.46498 12.299 3.41304 12.2775 3.37475 12.2392C3.33645 12.2009 3.31494 12.149 3.31494 12.0948C3.31494 12.0407 3.33645 11.9887 3.37475 11.9504C3.41304 11.9121 3.46498 11.8906 3.51913 11.8906H5.06135C5.1155 11.8906 5.16744 11.9121 5.20573 11.9504C5.24402 11.9887 5.26554 12.0407 5.26554 12.0948C5.26554 12.149 5.24402 12.2009 5.20573 12.2392C5.16744 12.2775 5.1155 12.299 5.06135 12.299H3.51913Z"
        fill="#AA6F57"
      />
    </svg>
  );
};

export default ProductsIcon;

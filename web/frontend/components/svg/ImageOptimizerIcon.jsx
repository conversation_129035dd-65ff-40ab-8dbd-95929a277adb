const ImageOptimizerIcon = ({ size = 30 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M21.7531 1.5C22.1733 1.5 22.5376 1.79073 22.6307 2.20044L23.1287 4.39032C23.359 5.40321 24.1499 6.19413 25.1628 6.42444L27.3527 6.92241C27.7624 7.01556 28.0531 7.37984 28.0531 7.8C28.0531 8.22017 27.7624 8.58444 27.3527 8.67759L25.1628 9.17556C24.1499 9.40587 23.359 10.1968 23.1287 11.2097L22.6307 13.3996C22.5376 13.8093 22.1733 14.1 21.7531 14.1C21.333 14.1 20.9687 13.8093 20.8755 13.3996L20.3776 11.2097C20.1473 10.1968 19.3563 9.40587 18.3434 9.17556L16.1535 8.67759C15.7439 8.58444 15.4531 8.22017 15.4531 7.8C15.4531 7.37984 15.7439 7.01556 16.1535 6.92241L18.3434 6.42444C19.3563 6.19413 20.1473 5.40321 20.3776 4.39032L20.8755 2.20044C20.9687 1.79073 21.333 1.5 21.7531 1.5Z"
        fill="#0D79A7"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.4359 10.9492C15.081 10.9492 13.9609 12.0453 13.9609 13.4242C13.9609 14.8031 15.081 15.8992 16.4359 15.8992C17.7909 15.8992 18.9109 14.8031 18.9109 13.4242C18.9109 12.0453 17.7909 10.9492 16.4359 10.9492Z"
        fill="#0D79A7"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.55313 6.89844H14.2908C14.1696 7.17573 14.1031 7.48069 14.1031 7.79844C14.1031 8.11618 14.1696 8.42115 14.2908 8.69844H5.55313C4.55901 8.69844 3.75313 9.50434 3.75313 10.4984V18.1582C7.71425 17.1562 11.7101 18.2234 14.6054 20.6954C16.5182 19.4715 18.7733 19.1675 20.8531 19.6809V15.2608C21.1304 15.3819 21.4354 15.4484 21.7531 15.4484C22.0709 15.4484 22.3758 15.3819 22.6531 15.2608V20.9069C22.6534 20.9205 22.6534 20.9341 22.6531 20.9477V24.8984C22.6531 26.8867 21.0414 28.4984 19.0531 28.4984H5.55313C3.5649 28.4984 1.95312 26.8867 1.95312 24.8984V10.4984C1.95312 8.5102 3.5649 6.89844 5.55313 6.89844Z"
        fill="#0D79A7"
      />
    </svg>
  );
};

export default ImageOptimizerIcon;

const AiCreditGift = (props) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.88489 5.64825C5.86405 4.7996 5.16826 4.11771 4.31464 4.11771H4.31266C4.13995 4.11671 4 3.97676 4 3.80306C4 3.63035 4.14094 3.4894 4.31464 3.4894C5.18215 3.4894 5.88589 2.78469 5.88589 1.91717C5.88589 1.74447 6.02683 1.60352 6.20053 1.60352C6.37324 1.60352 6.51419 1.74447 6.51419 1.91717C6.51419 2.78469 7.21891 3.4894 8.08642 3.4894C8.25912 3.4894 8.40007 3.63035 8.40007 3.80306C8.40007 3.97676 8.25912 4.11771 8.08642 4.11771C7.23181 4.11771 6.53602 4.7996 6.51518 5.64825L6.51419 5.68895C6.51419 5.86265 6.37324 6.0036 6.20053 6.0036C6.02683 6.0036 5.88589 5.86265 5.88589 5.68895L5.88489 5.64825Z"
      fill="url(#paint0_linear_8387_80160)"
    />
    <g filter="url(#filter0_d_8387_80160)">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M18.1835 9.59173C18.1835 9.8953 17.9371 10.1416 17.6353 10.1416C14.6031 10.1416 12.1416 12.6031 12.1416 15.6336C12.1416 15.9371 11.8953 16.1835 11.5917 16.1835C11.2899 16.1835 11.0436 15.9371 11.0436 15.6336L11.0418 15.5624C11.0054 12.5632 8.5578 10.1416 5.54989 10.1416H5.54641C5.24458 10.1399 5 9.89356 5 9.59173C5 9.28816 5.24632 9.04183 5.54989 9.04183H5.56723C8.59076 9.03317 11.0436 6.57514 11.0436 3.54988C11.0436 3.24631 11.2899 3 11.5917 3C11.8953 3 12.1416 3.24631 12.1416 3.54988V3.56724C12.152 6.59075 14.6083 9.04183 17.6353 9.04183C17.9371 9.04183 18.1835 9.28816 18.1835 9.59173Z"
        fill="url(#paint1_linear_8387_80160)"
      />
    </g>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.1599 5.82952C17.1599 5.9793 17.0392 6.09999 16.8903 6.10084H16.8886C16.4401 6.10084 16.0755 6.46546 16.0755 6.91396C16.0755 7.06375 15.9539 7.18528 15.805 7.18528C15.6552 7.18528 15.5337 7.06375 15.5337 6.91396V6.89342C15.5225 6.45434 15.1622 6.10084 14.7205 6.10084C14.5708 6.10084 14.4492 5.9793 14.4492 5.82952C14.4492 5.68059 14.5708 5.55905 14.7205 5.55905C15.169 5.55905 15.5337 5.19442 15.5337 4.74593C15.5337 4.59614 15.6552 4.47461 15.805 4.47461L15.9103 4.49601C15.9753 4.5234 16.0267 4.5756 16.0541 4.63979L16.1396 5.06176C16.2629 5.35363 16.5522 5.55905 16.8886 5.55905C17.0384 5.55905 17.1599 5.68059 17.1599 5.82952Z"
      fill="url(#paint2_linear_8387_80160)"
    />
    <defs>
      <filter
        id="filter0_d_8387_80160"
        x="0"
        y="0"
        width="23.1836"
        height="23.1836"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood
          floodOpacity="0"
          result="BackgroundImageFix"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="2" />
        <feGaussianBlur stdDeviation="2.5" />
        <feComposite
          in2="hardAlpha"
          operator="out"
        />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.259083 0 0 0 0 0.659576 0 0 0 0 0.799748 0 0 0 0.2 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_8387_80160"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_8387_80160"
          result="shape"
        />
      </filter>
      <linearGradient
        id="paint0_linear_8387_80160"
        x1="6.20004"
        y1="1.60352"
        x2="6.20004"
        y2="6.0036"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#8051FF" />
        <stop
          offset="1"
          stopColor="#5ED0F9"
        />
      </linearGradient>
      <linearGradient
        id="paint1_linear_8387_80160"
        x1="11.5917"
        y1="3"
        x2="11.5917"
        y2="16.1835"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#8051FF" />
        <stop
          offset="1"
          stopColor="#5ED0F9"
        />
      </linearGradient>
      <linearGradient
        id="paint2_linear_8387_80160"
        x1="15.8046"
        y1="4.47461"
        x2="15.8046"
        y2="7.18528"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#8051FF" />
        <stop
          offset="1"
          stopColor="#5ED0F9"
        />
      </linearGradient>
    </defs>
  </svg>
);

export default AiCreditGift;

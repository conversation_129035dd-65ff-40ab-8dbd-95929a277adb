/**
 * @typedef {Object} NoProductProps
 * @property {string} [width="200"]
 * @property {string} [height="200"]
 */

/**
 * @param {NoProductProps} props
 */
const NoProduct = ({ width = "200", height = "200" }) => (
  <svg
    id="Capa_1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    x="0px"
    y="0px"
    viewBox="0 0 209 209"
    enableBackground="new 0 0 209 209"
    xmlSpace="preserve"
    width={width}
    height={height}
  >
    <g>
      <path
        opacity={0.4}
        fill="#00CC76"
        d="M189.5,200.2L176.8,58.6c-0.3-3.9-3.6-6.8-7.5-6.8h-26.2V40c0-22-17.9-40-40-40c-22,0-40,17.9-40,40v11.8H37
		c-3.9,0-7.1,3-7.5,6.8L16.7,200.8c-0.2,2.1,0.5,4.2,1.9,5.7s3.4,2.4,5.5,2.4h157.9c0,0,0,0,0,0c4.1,0,7.5-3.4,7.5-7.5
		C189.6,201,189.6,200.6,189.5,200.2z M78.2,40c0-13.8,11.2-25,25-25c13.8,0,25,11.2,25,25v11.8H78.2V40z M32.4,194L43.8,66.8h19.4
		v13.4c0,4.1,3.4,7.5,7.5,7.5s7.5-3.4,7.5-7.5V66.8h49.9v13.4c0,4.1,3.4,7.5,7.5,7.5s7.5-3.4,7.5-7.5V66.8h19.4L173.9,194L32.4,194
		L32.4,194z"
      />
      <g opacity={0.4}>
        <path
          fill="#00CC76"
          d="M65.8,132.2l6.4-6.4l6.4,6.4l6.4-6.4l-6.4-6.4l6.4-6.4l-6.4-6.4l-6.4,6.4l-6.4-6.4l-6.4,6.4l6.4,6.4l-6.4,6.4
			L65.8,132.2z"
        />
        <path
          fill="#00CC76"
          d="M129.1,132.2l6.4-6.4l6.4,6.4l6.4-6.4l-6.4-6.4l6.4-6.4l-6.4-6.4l-6.4,6.4l-6.4-6.4l-6.4,6.4l6.4,6.4
			l-6.4,6.4L129.1,132.2z"
        />
        <path
          fill="#00CC76"
          d="M73.7,158.3c17.6-12.1,41.3-12.1,58.9,0l12,8.3l5.2-7.5l-12-8.3c-10.2-7.1-22.2-10.8-34.6-10.8
			s-24.4,3.7-34.6,10.8l-12,8.3l5.2,7.5L73.7,158.3z"
        />
      </g>
    </g>
  </svg>
);

export default NoProduct;

const StoreVerify = () => {
  return (
    <svg
      width="148"
      height="171"
      viewBox="0 0 148 171"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M148 97.002C148 107.992 145.6 118.432 141.29 127.812C129.61 153.302 103.87 171.002 74 171.002C44.13 171.002 18.39 153.302 6.71 127.812C2.4 118.432 0 107.992 0 97.002C0 56.132 33.13 23.002 74 23.002C114.87 23.002 148 56.132 148 97.002Z"
        fill="#F0F1F2"
      />
      <mask
        id="mask0_1119_58901"
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="148"
        height="171"
      >
        <path
          d="M148 40.5C148 51.49 145.6 118.43 141.29 127.81C129.61 153.3 103.87 171 74 171C44.13 171 18.39 153.3 6.71 127.81C2.4 118.43 0 51.49 0 40.5C0 -0.369999 33.13 0.000117124 74 0.000117124C114.87 0.000117124 148 -0.369999 148 40.5Z"
          fill="#F0F1F2"
        />
      </mask>
      <g mask="url(#mask0_1119_58901)">
        <g filter="url(#filter0_d_1119_58901)">
          <path
            d="M17.8926 19.623C17.8926 18.726 18.6186 18 19.5156 18H128.27C129.167 18 129.893 18.726 129.893 19.623V169.826C129.893 170.723 129.167 171.449 128.27 171.449H19.5156C18.6186 171.449 17.8926 170.723 17.8926 169.826V19.623Z"
            fill="#5BA7B1"
          />
          <path
            d="M129.893 75.2754H17.8926V105.71H129.893V75.2754Z"
            fill="#7FBEC6"
          />
          <path
            d="M17.8926 105.71H129.893V169.826C129.893 170.723 129.167 171.449 128.27 171.449H19.5156C18.6186 171.449 17.8926 170.723 17.8926 169.826V105.71Z"
            fill="white"
          />
          <path
            d="M76.8926 117H30.8926C29.7876 117 28.8926 117.895 28.8926 119C28.8926 120.105 29.7876 121 30.8926 121H76.8926C77.9976 121 78.8926 120.105 78.8926 119C78.8926 117.895 77.9966 117 76.8926 117Z"
            fill="#D2D5D9"
          />
          <path
            d="M63.8926 132H33.8926C31.1316 132 28.8926 134.239 28.8926 137C28.8926 139.761 31.1316 142 33.8926 142H63.8926C66.6536 142 68.8926 139.761 68.8926 137C68.8926 134.239 66.6536 132 63.8926 132Z"
            fill="#00735C"
          />
        </g>
      </g>
      <path
        d="M94.7049 69.08C98.4649 53.62 98.0349 45.07 98.0349 45.07C98.0349 45.07 95.1349 44.22 86.8449 42.08C78.0749 39.82 74.0249 39 74.0249 39C74.0249 39 69.9849 39.81 61.2049 42.08C52.9149 44.22 50.0149 45.07 50.0149 45.07C50.0149 45.07 49.5849 53.61 53.3449 69.08C53.9149 71.42 54.7749 73.56 55.8349 75.52H92.2049C93.2649 73.56 94.1249 71.42 94.6949 69.08H94.7049Z"
        fill="white"
      />
      <path
        d="M55.8447 75.5195C60.4547 84.0395 68.8947 89.1995 74.0347 92.3895C79.1747 89.2095 87.6147 84.0495 92.2247 75.5195H55.8547H55.8447Z"
        fill="#D3D5D9"
      />
      <g clipPath="url(#clip0_1119_58901)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M84.0352 59.4004C84.0352 64.9232 79.558 69.4004 74.0352 69.4004C68.5123 69.4004 64.0352 64.9232 64.0352 59.4004C64.0352 53.8775 68.5123 49.4004 74.0352 49.4004C79.558 49.4004 84.0352 53.8775 84.0352 59.4004ZM79.309 57.3409C79.6995 56.9504 79.6995 56.3172 79.309 55.9267C78.9185 55.5362 78.2853 55.5362 77.8948 55.9267L72.7686 61.0529L70.809 59.0933C70.4185 58.7028 69.7853 58.7028 69.3948 59.0933C69.0043 59.4839 69.0043 60.117 69.3948 60.5076L72.0614 63.1742C72.452 63.5648 73.0851 63.5648 73.4757 63.1742L79.309 57.3409Z"
          fill="#29845A"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_1119_58901"
          x="7.89258"
          y="10"
          width="132"
          height="173.449"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood
            floodOpacity="0"
            result="BackgroundImageFix"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="2" />
          <feGaussianBlur stdDeviation="5" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_1119_58901"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_1119_58901"
            result="shape"
          />
        </filter>
        <clipPath id="clip0_1119_58901">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(64.0352 49.4004)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default StoreVerify;

const AiThumbnail = () => {
  return (
    <svg
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g filter="url(#filter0_i_972_72613)">
        <g clipPath="url(#clip0_972_72613)">
          <rect
            width="40"
            height="40"
            fill="#8051FF"
          />
          <path
            d="M13.9818 11.9539C14.3137 11.3471 15.1853 11.3471 15.5172 11.9539L15.7917 12.4559C16.0808 12.9844 16.5154 13.419 17.0439 13.708L17.5459 13.9826C18.1527 14.3145 18.1527 15.186 17.5459 15.5179L17.0439 15.7925C16.5154 16.0815 16.0808 16.5161 15.7917 17.0446L15.5172 17.5466C15.1853 18.1535 14.3137 18.1535 13.9818 17.5466L13.7073 17.0446C13.4182 16.5161 12.9837 16.0815 12.4551 15.7925L11.9532 15.5179C11.3463 15.186 11.3463 14.3145 11.9532 13.9826L12.4551 13.708C12.9837 13.419 13.4182 12.9844 13.7073 12.4559L13.9818 11.9539Z"
            fill="white"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M24.127 17.0451C23.8748 15.4123 21.534 15.3802 21.2372 17.0055L21.1954 17.2345C20.7962 19.4208 19.0655 21.1199 16.8722 21.4789C15.2632 21.7422 15.2299 24.0444 16.8307 24.3542L16.9776 24.3826C19.1236 24.7979 20.8017 26.476 21.217 28.6221L21.2618 28.8533C21.5891 30.545 24.0099 30.545 24.3373 28.8533L24.3721 28.6731C24.7926 26.5005 26.5027 24.8082 28.6796 24.4107C30.3375 24.108 30.2466 21.7106 28.58 21.4602C26.2972 21.1173 24.4794 19.3265 24.127 17.0451ZM22.8055 26.6872C23.5169 25.0684 24.7815 23.7554 26.3633 22.982C24.7559 22.2681 23.4457 21.0007 22.6852 19.4142C21.9641 20.952 20.7302 22.1941 19.2018 22.9263C20.8117 23.7037 22.0951 25.0395 22.8055 26.6872Z"
            fill="white"
          />
        </g>
      </g>
      <defs>
        <filter
          id="filter0_i_972_72613"
          x="0"
          y="0"
          width="40"
          height="40"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood
            floodOpacity="0"
            result="BackgroundImageFix"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="1"
            operator="erode"
            in="SourceAlpha"
            result="effect1_innerShadow_972_72613"
          />
          <feOffset />
          <feComposite
            in2="hardAlpha"
            operator="arithmetic"
            k2="-1"
            k3="1"
          />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"
          />
          <feBlend
            mode="normal"
            in2="shape"
            result="effect1_innerShadow_972_72613"
          />
        </filter>
        <clipPath id="clip0_972_72613">
          <path
            d="M0 8C0 3.58172 3.58172 0 8 0H32C36.4183 0 40 3.58172 40 8V32C40 36.4183 36.4183 40 32 40H8C3.58172 40 0 36.4183 0 32V8Z"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default AiThumbnail;

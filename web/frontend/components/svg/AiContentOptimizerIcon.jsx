const AiContentOptimizerIcon = ({ size = 30 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 27 27"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.81371 4.23397L6.14549 4.47538C6.35824 4.51379 6.55417 4.6163 6.70703 4.76917C6.8599 4.92203 6.96241 5.11796 7.00082 5.33071L7.24223 6.66249C7.2499 6.70449 7.27206 6.74247 7.30486 6.76982C7.33765 6.79716 7.379 6.81214 7.42169 6.81214C7.46439 6.81214 7.50574 6.79716 7.53853 6.76982C7.57132 6.74247 7.59349 6.70449 7.60116 6.66249L7.84257 5.33071C7.88098 5.11796 7.98349 4.92203 8.13635 4.76917C8.28922 4.6163 8.48515 4.51379 8.69789 4.47538L10.0297 4.23397C10.0717 4.2263 10.1097 4.20414 10.137 4.17134C10.1643 4.13855 10.1793 4.0972 10.1793 4.05451C10.1793 4.01181 10.1643 3.97046 10.137 3.93767C10.1097 3.90488 10.0717 3.88271 10.0297 3.87504L8.69789 3.63363C8.48529 3.59488 8.28955 3.49226 8.13674 3.33946C7.98394 3.18665 7.88132 2.99091 7.84257 2.77831L7.60116 1.44653C7.59349 1.40452 7.57132 1.36654 7.53853 1.3392C7.50574 1.31185 7.46439 1.29688 7.42169 1.29688C7.379 1.29688 7.33765 1.31185 7.30486 1.3392C7.27206 1.36654 7.2499 1.40452 7.24223 1.44653L7.00082 2.77831C6.96207 2.99091 6.85945 3.18665 6.70664 3.33946C6.55384 3.49226 6.35809 3.59488 6.14549 3.63363L4.81371 3.87504C4.77171 3.88271 4.73373 3.90488 4.70638 3.93767C4.67904 3.97046 4.66406 4.01181 4.66406 4.05451C4.66406 4.0972 4.67904 4.13855 4.70638 4.17134C4.73373 4.20414 4.77171 4.2263 4.81371 4.23397Z"
        fill="#8051FF"
      />
      <path
        d="M1.40153 8.67436L2.63191 8.89753C2.82811 8.93316 3.00877 9.0278 3.14977 9.1688C3.29078 9.30981 3.38541 9.49047 3.42105 9.68667L3.64421 10.917C3.65276 10.9545 3.67378 10.988 3.70385 11.012C3.73391 11.036 3.77122 11.049 3.80968 11.049C3.84813 11.049 3.88545 11.036 3.91551 11.012C3.94557 10.988 3.9666 10.9545 3.97514 10.917L4.19831 9.68667C4.23394 9.49047 4.32858 9.30981 4.46958 9.1688C4.61059 9.0278 4.79125 8.93316 4.98745 8.89753L6.21783 8.67436C6.25532 8.66582 6.2888 8.64479 6.31278 8.61473C6.33676 8.58467 6.34982 8.54735 6.34982 8.5089C6.34982 8.47044 6.33676 8.43313 6.31278 8.40306C6.2888 8.373 6.25532 8.35198 6.21783 8.34343L4.98745 8.12027C4.79125 8.08463 4.61059 7.98999 4.46958 7.84899C4.32858 7.70799 4.23394 7.52733 4.19831 7.33113L3.97514 6.10075C3.9666 6.06325 3.94557 6.02977 3.91551 6.00579C3.88545 5.98181 3.84813 5.96875 3.80968 5.96875C3.77122 5.96875 3.73391 5.98181 3.70385 6.00579C3.67378 6.02977 3.65276 6.06325 3.64421 6.10075L3.42105 7.33113C3.38541 7.52733 3.29078 7.70799 3.14977 7.84899C3.00877 7.98999 2.82811 8.08463 2.63191 8.12027L1.40153 8.34343C1.36403 8.35198 1.33055 8.373 1.30657 8.40306C1.28259 8.43313 1.26953 8.47044 1.26953 8.5089C1.26953 8.54735 1.28259 8.58467 1.30657 8.61473C1.33055 8.64479 1.36403 8.66582 1.40153 8.67436Z"
        fill="#8051FF"
      />
      <path
        d="M21.1715 19.8148L21.4129 18.4831C21.4513 18.2703 21.5538 18.0744 21.7067 17.9215C21.8595 17.7686 22.0555 17.6661 22.2682 17.6277L23.6 17.3863C23.642 17.3786 23.68 17.3565 23.7073 17.3237C23.7347 17.2909 23.7496 17.2495 23.7496 17.2069C23.7496 17.1642 23.7347 17.1228 23.7073 17.09C23.68 17.0572 23.642 17.0351 23.6 17.0274L22.2682 16.786C22.0556 16.7472 21.8599 16.6446 21.7071 16.4918C21.5542 16.339 21.4516 16.1433 21.4129 15.9307L21.1715 14.5989C21.1638 14.5569 21.1416 14.5189 21.1088 14.4915C21.0761 14.4642 21.0347 14.4492 20.992 14.4492C20.9493 14.4492 20.908 14.4642 20.8752 14.4915C20.8424 14.5189 20.8202 14.5569 20.8125 14.5989L20.5711 15.9307C20.5324 16.1433 20.4298 16.339 20.277 16.4918C20.1241 16.6446 19.9284 16.7472 19.7158 16.786L18.384 17.0274C18.342 17.0351 18.304 17.0572 18.2767 17.09C18.2494 17.1228 18.2344 17.1642 18.2344 17.2069C18.2344 17.2495 18.2494 17.2909 18.2767 17.3237C18.304 17.3565 18.342 17.3786 18.384 17.3863L19.7158 17.6277C19.9286 17.6661 20.1245 17.7686 20.2773 17.9215C20.4302 18.0744 20.5327 18.2703 20.5711 18.4831L20.8125 19.8148C20.8202 19.8568 20.8424 19.8948 20.8752 19.9222C20.908 19.9495 20.9493 19.9645 20.992 19.9645C21.0347 19.9645 21.0761 19.9495 21.1088 19.9222C21.1416 19.8948 21.1638 19.8568 21.1715 19.8148Z"
        fill="#8051FF"
      />
      <path
        d="M16.21 22.0499C16.4062 22.0855 16.5869 22.1801 16.7279 22.3211C16.8689 22.4622 16.9635 22.6428 16.9992 22.839L17.2223 24.0694C17.2309 24.1069 17.2519 24.1404 17.282 24.1643C17.312 24.1883 17.3493 24.2014 17.3878 24.2014C17.4263 24.2014 17.4636 24.1883 17.4936 24.1643C17.5237 24.1404 17.5447 24.1069 17.5533 24.0694L17.7764 22.839C17.8121 22.6428 17.9067 22.4622 18.0477 22.3211C18.1887 22.1801 18.3694 22.0855 18.5656 22.0499L19.796 21.8267C19.8334 21.8182 19.8669 21.7971 19.8909 21.7671C19.9149 21.737 19.9279 21.6997 19.9279 21.6612C19.9279 21.6228 19.9149 21.5855 19.8909 21.5554C19.8669 21.5253 19.8334 21.5043 19.796 21.4958L18.5656 21.2726C18.3694 21.237 18.1887 21.1423 18.0477 21.0013C17.9067 20.8603 17.8121 20.6797 17.7764 20.4835L17.5533 19.2531C17.5447 19.2156 17.5237 19.1821 17.4936 19.1581C17.4636 19.1342 17.4263 19.1211 17.3878 19.1211C17.3493 19.1211 17.312 19.1342 17.282 19.1581C17.2519 19.1821 17.2309 19.2156 17.2223 19.2531L16.9992 20.4835C16.9635 20.6797 16.8689 20.8603 16.7279 21.0013C16.5869 21.1423 16.4062 21.237 16.21 21.2726L14.9797 21.4958C14.9422 21.5043 14.9087 21.5253 14.8847 21.5554C14.8607 21.5855 14.8477 21.6228 14.8477 21.6612C14.8477 21.6997 14.8607 21.737 14.8847 21.7671C14.9087 21.7971 14.9422 21.8182 14.9797 21.8267L16.21 22.0499Z"
        fill="#8051FF"
      />
      <path
        d="M3.17317 25.4935C3.85539 24.3802 9.66787 14.936 16.3429 8.79425C16.4209 8.72349 16.4832 8.63712 16.5257 8.54073C16.5682 8.44434 16.59 8.34009 16.5896 8.23475C16.5892 8.12941 16.5666 8.02533 16.5234 7.92927C16.4802 7.83321 16.4172 7.74731 16.3386 7.67715C16.2031 7.55534 16.028 7.48686 15.8458 7.48444C15.6636 7.48202 15.4867 7.54582 15.348 7.664C8.14941 13.8846 2.64113 23.1184 1.30978 25.451C1.28062 25.502 1.2668 25.5603 1.26998 25.619C1.27316 25.6776 1.2932 25.7341 1.3277 25.7817C1.36219 25.8292 1.40968 25.8658 1.46445 25.887C1.51922 25.9082 1.57895 25.9131 1.63647 25.9012L2.97376 25.6339C3.01491 25.6256 3.05387 25.6088 3.0882 25.5846C3.12252 25.5604 3.15146 25.5294 3.17317 25.4935Z"
        fill="#8051FF"
      />
      <path
        d="M25.599 1.29764C23.414 1.48008 16.5833 1.86616 11.3139 5.76095C11.2389 5.81543 11.1779 5.88689 11.1359 5.96949C11.0939 6.05208 11.072 6.14346 11.0721 6.23613L11.0381 7.75501C11.0375 7.82155 11.0176 7.88648 10.981 7.94204C10.9444 7.9976 10.8926 8.04143 10.8317 8.06829C10.7708 8.09515 10.7035 8.1039 10.6378 8.09348C10.572 8.08307 10.5107 8.05394 10.4611 8.00957L10.0029 7.60228C9.88902 7.49933 9.7397 7.44447 9.58625 7.44922C9.4328 7.45396 9.28714 7.51794 9.17983 7.62773C6.79969 10.1267 5.03897 13.6311 4.69531 18.5611C7.2579 14.8997 10.7581 10.5085 14.7929 7.02103C15.0242 6.82278 15.3072 6.69461 15.6087 6.65156C15.9102 6.60851 16.2178 6.65236 16.4953 6.77796C16.7728 6.90357 17.0087 7.10572 17.1754 7.3607C17.342 7.61567 17.4324 7.91289 17.4361 8.21746C17.4385 8.44265 17.3936 8.66583 17.3045 8.87263C17.2153 9.07943 17.0838 9.26526 16.9185 9.41815C13.0407 12.9862 9.46834 17.6617 7.02031 21.1746C9.27103 20.4521 11.3797 19.3454 13.2528 17.9035C13.3753 17.8106 13.4571 17.6738 13.4808 17.5219C13.5045 17.37 13.4684 17.2148 13.3801 17.0889L13.1255 16.7241C13.0885 16.6707 13.0671 16.6081 13.0637 16.5433C13.0604 16.4784 13.0752 16.4139 13.1065 16.357C13.1378 16.3001 13.1844 16.2531 13.241 16.2213C13.2975 16.1894 13.3619 16.174 13.4268 16.1767L14.9881 16.2489C15.148 16.2545 15.3036 16.1965 15.4208 16.0877C16.0954 15.4555 16.787 14.7597 17.4785 13.979C23.8765 6.77919 22.7352 4.47966 25.7857 1.75161C25.8273 1.71635 25.8567 1.66882 25.8696 1.61584C25.8825 1.56286 25.8782 1.50716 25.8575 1.45673C25.8368 1.4063 25.8006 1.36373 25.7541 1.33515C25.7077 1.30657 25.6534 1.29344 25.599 1.29764Z"
        fill="#8051FF"
      />
    </svg>
  );
};

export default AiContentOptimizerIcon;

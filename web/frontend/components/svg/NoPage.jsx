/**
 * @typedef {Object} NoPageProps
 * @property {string} [width="200"]
 * @property {string} [height="200"]
 */

/**
 * @param {NoPageProps} props
 */
const NoPage = ({ width = "200", height = "200" }) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 209 209"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g
      opacity="0.4"
      clipPath="url(#clip0_1187_860)"
    >
      <path
        d="M10.45 188.1H96.1888C107.054 191.591 118.589 192.474 129.859 190.677C141.129 188.88 151.818 184.455 161.059 177.758L189.622 206.321C190.462 207.172 191.463 207.847 192.566 208.307C193.67 208.767 194.854 209.003 196.049 209H196.084C197.266 209 198.437 208.763 199.527 208.305C200.618 207.847 201.606 207.176 202.434 206.332L206.314 202.455C207.164 201.618 207.838 200.621 208.299 199.521C208.76 198.422 208.998 197.241 209 196.049C209.002 194.857 208.767 193.676 208.309 192.575C207.851 191.474 207.179 190.476 206.332 189.636L177.768 161.073C184.601 151.621 189.069 140.668 190.796 129.134C192.523 117.599 191.46 105.818 187.696 94.7791C183.931 83.7401 177.575 73.7643 169.161 65.6881C160.746 57.612 150.518 51.6708 139.333 48.3627V29.2949C139.337 28.3825 139.159 27.4785 138.811 26.6353C138.462 25.7922 137.95 25.0267 137.303 24.3834L114.95 2.03084C114.307 1.38386 113.541 0.87117 112.698 0.522547C111.855 0.173925 110.951 -0.00367785 110.039 5.77262e-05H10.45C7.67849 5.77262e-05 5.02049 1.10104 3.06073 3.06079C1.10098 5.02055 0 7.67855 0 10.4501L0 177.65C0 180.422 1.10098 183.08 3.06073 185.039C5.02049 186.999 7.67849 188.1 10.45 188.1ZM201.406 194.562C201.605 194.756 201.763 194.989 201.871 195.246C201.978 195.502 202.034 195.778 202.033 196.056C202.031 196.328 201.974 196.596 201.867 196.846C201.759 197.095 201.602 197.32 201.406 197.509L197.505 201.427C197.319 201.619 197.096 201.771 196.85 201.875C196.605 201.979 196.34 202.033 196.073 202.033C195.796 202.033 195.522 201.977 195.267 201.869C195.011 201.762 194.78 201.604 194.586 201.406L166.57 173.393C168.999 171.263 171.286 168.977 173.418 166.549L201.406 194.562ZM184.617 118.433C184.617 131.523 180.735 144.319 173.463 155.203C166.19 166.087 155.854 174.57 143.761 179.579C131.667 184.588 118.36 185.899 105.522 183.345C92.6833 180.791 80.8906 174.488 71.6346 165.232C62.3787 155.976 56.0754 144.183 53.5217 131.345C50.968 118.507 52.2786 105.2 57.2879 93.1061C62.2972 81.0127 70.7801 70.6763 81.6638 63.404C92.5476 56.1317 105.344 52.2501 118.433 52.2501C135.98 52.2694 152.803 59.2485 165.211 71.6561C177.618 84.0637 184.597 100.886 184.617 118.433ZM114.95 11.8887L127.452 24.3834H118.433C117.509 24.3834 116.623 24.0164 115.97 23.3631C115.317 22.7099 114.95 21.8239 114.95 20.9001V11.8887ZM6.96667 10.4501C6.96667 9.52622 7.33366 8.64022 7.98691 7.98697C8.64016 7.33372 9.52616 6.96673 10.45 6.96673H107.983V20.9001C107.983 23.6716 109.084 26.3296 111.044 28.2893C113.004 30.2491 115.662 31.3501 118.433 31.3501H132.367V46.6523C115.478 43.3504 97.9652 46.1156 82.9153 54.4605C67.8653 62.8055 56.2437 76.1947 50.0984 92.2687C43.9531 108.343 43.6784 126.07 49.3228 142.327C54.9672 158.583 66.1684 172.326 80.9527 181.133H10.45C9.52616 181.133 8.64016 180.766 7.98691 180.113C7.33366 179.46 6.96667 178.574 6.96667 177.65V10.4501Z"
        fill="#00CC76"
      />
      <path
        d="M59.2168 118.433C59.2172 129.345 62.2323 140.044 67.9296 149.35C73.6268 158.656 81.7845 166.207 91.5022 171.17C101.22 176.132 112.12 178.313 122.999 177.472C133.878 176.631 144.313 172.8 153.152 166.402C161.992 160.005 168.892 151.29 173.09 141.218C177.289 131.147 178.624 120.111 176.946 109.33C175.269 98.5478 170.646 88.439 163.586 80.119C156.527 71.799 147.305 65.5913 136.94 62.1811C136.063 61.892 135.106 61.9633 134.281 62.3794C133.456 62.7955 132.83 63.5224 132.541 64.4C132.252 65.2776 132.323 66.2342 132.739 67.0593C133.155 67.8843 133.882 68.5103 134.76 68.7994C147.246 72.9058 157.727 81.566 164.114 93.0544C170.501 104.543 172.325 118.016 169.222 130.789C166.119 143.562 158.318 154.696 147.372 161.975C136.427 169.253 123.141 172.14 110.162 170.06C97.1833 167.98 85.4645 161.087 77.341 150.753C69.2174 140.42 65.2856 127.405 66.3289 114.302C67.3723 101.199 73.3142 88.9705 82.9707 80.0527C92.6271 71.135 105.289 66.1829 118.433 66.1835C119.357 66.1835 120.243 65.8165 120.897 65.1632C121.55 64.51 121.917 63.624 121.917 62.7001C121.917 61.7763 121.55 60.8903 120.897 60.237C120.243 59.5838 119.357 59.2168 118.433 59.2168C102.734 59.2343 87.6818 65.4788 76.5803 76.5803C65.4788 87.6818 59.2343 102.734 59.2168 118.433V118.433Z"
        fill="#00CC76"
      />
      <path
        d="M140.968 138.141H96.7349C93.4094 138.141 90.7031 135.435 90.7031 132.109C90.7031 130.927 91.0288 129.821 91.6481 128.896L113.716 92.782C114.758 91.0086 116.732 89.8867 118.852 89.8867C120.971 89.8867 122.945 91.0086 124.003 92.8142L146.099 128.973C146.674 129.821 147 130.927 147 132.109C147 135.435 144.294 138.141 140.968 138.141ZM118.852 93.9079C118.152 93.9079 117.508 94.2698 117.171 94.8449L95.034 131.068C94.8128 131.398 94.7243 131.731 94.7243 132.109C94.7243 133.215 95.6291 134.12 96.7349 134.12H140.968C142.074 134.12 142.979 133.215 142.979 132.109C142.979 131.731 142.89 131.398 142.717 131.144L120.553 94.877C120.195 94.2698 119.551 93.9079 118.852 93.9079V93.9079Z"
        fill="#00CC76"
      />
      <path
        d="M118.852 122.056C117.743 122.056 116.842 121.155 116.842 120.046V103.961C116.842 102.851 117.743 101.95 118.852 101.95C119.962 101.95 120.863 102.851 120.863 103.961V120.046C120.863 121.155 119.962 122.056 118.852 122.056Z"
        fill="#00CC76"
      />
      <path
        d="M118.852 130.099C119.963 130.099 120.863 129.199 120.863 128.088C120.863 126.978 119.963 126.078 118.852 126.078C117.742 126.078 116.842 126.978 116.842 128.088C116.842 129.199 117.742 130.099 118.852 130.099Z"
        fill="#00CC76"
      />
    </g>
    <defs>
      <clipPath id="clip0_1187_860">
        <rect
          width={width}
          height={height}
          fill="white"
        />
      </clipPath>
    </defs>
  </svg>
);

export default NoPage;

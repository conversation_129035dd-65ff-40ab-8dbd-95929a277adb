import React from 'react';

const Icon6 = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="50.38" height="59.992" viewBox="0 0 50.38 59.992">
    <g id="copy" transform="translate(-10.166 -6)">
      <g id="Group_495" data-name="Group 495" transform="translate(12.473 15.23)">
        <path id="Path_5627" data-name="Path 5627" d="M21.4,18.615H14.473a2.307,2.307,0,1,1,0-4.615H21.4a2.307,2.307,0,0,1,0,4.615Z" transform="translate(-12.166 -14)" fill="#0f8af9"/>
      </g>
      <g id="Group_496" data-name="Group 496" transform="translate(46.701 52.148)">
        <path id="Path_5628" data-name="Path 5628" d="M44.141,57.537a2.308,2.308,0,0,1-2.307-2.307V48.307a2.307,2.307,0,0,1,4.615,0V55.23A2.308,2.308,0,0,1,44.141,57.537Z" transform="translate(-41.834 -46)" fill="#0f8af9"/>
      </g>
      <g id="Group_497" data-name="Group 497" transform="translate(10.166 15.23)">
        <path id="Path_5629" data-name="Path 5629" d="M12.472,20.922a2.307,2.307,0,0,1-2.306-2.307A4.619,4.619,0,0,1,14.781,14a2.307,2.307,0,1,1,0,4.615h0a2.307,2.307,0,0,1-2.306,2.307Z" transform="translate(-10.166 -14)" fill="#0f8af9"/>
      </g>
      <g id="Group_498" data-name="Group 498" transform="translate(10.166 17.537)">
        <path id="Path_5630" data-name="Path 5630" d="M12.473,62.148a2.308,2.308,0,0,1-2.307-2.307V18.307a2.307,2.307,0,1,1,4.615,0V59.841A2.307,2.307,0,0,1,12.473,62.148Z" transform="translate(-10.166 -16)" fill="#0f8af9"/>
      </g>
      <g id="Group_499" data-name="Group 499" transform="translate(44.394 59.07)">
        <path id="Path_5631" data-name="Path 5631" d="M42.141,58.922a2.307,2.307,0,0,1,0-4.615h0a2.306,2.306,0,1,1,4.612,0A4.619,4.619,0,0,1,42.141,58.922Z" transform="translate(-39.834 -52)" fill="#0f8af9"/>
      </g>
      <g id="Group_500" data-name="Group 500" transform="translate(12.473 61.378)">
        <path id="Path_5632" data-name="Path 5632" d="M46.394,58.615H14.473a2.307,2.307,0,0,1,0-4.615H46.394a2.307,2.307,0,0,1,0,4.615Z" transform="translate(-12.166 -54)" fill="#0f8af9"/>
      </g>
      <g id="Group_501" data-name="Group 501" transform="translate(10.166 59.07)">
        <path id="Path_5633" data-name="Path 5633" d="M14.781,58.922a4.619,4.619,0,0,1-4.615-4.615,2.307,2.307,0,1,1,4.615,0v0a2.306,2.306,0,1,1,0,4.612Z" transform="translate(-10.166 -52)" fill="#0f8af9"/>
      </g>
      <g id="Group_502" data-name="Group 502" transform="translate(19.396 6)">
        <path id="Path_5634" data-name="Path 5634" d="M20.472,12.922a2.307,2.307,0,0,1-2.306-2.307A4.619,4.619,0,0,1,22.781,6a2.307,2.307,0,1,1,0,4.615h0a2.307,2.307,0,0,1-2.306,2.307Z" transform="translate(-18.166 -6)" fill="#0f8af9"/>
      </g>
      <g id="Group_503" data-name="Group 503" transform="translate(19.396 8.307)">
        <path id="Path_5635" data-name="Path 5635" d="M20.473,54.148a2.308,2.308,0,0,1-2.307-2.307V10.307a2.307,2.307,0,1,1,4.615,0V51.841A2.307,2.307,0,0,1,20.473,54.148Z" transform="translate(-18.166 -8)" fill="#0f8af9"/>
      </g>
      <g id="Group_504" data-name="Group 504" transform="translate(21.703 6)">
        <path id="Path_5636" data-name="Path 5636" d="M47.086,10.615H22.473a2.307,2.307,0,0,1,0-4.615H47.086a2.307,2.307,0,0,1,0,4.615Z" transform="translate(-20.166 -6)" fill="#0f8af9"/>
      </g>
      <g id="Group_505" data-name="Group 505" transform="translate(53.624 49.841)">
        <path id="Path_5637" data-name="Path 5637" d="M50.141,50.922a2.307,2.307,0,0,1,0-4.615h0a2.306,2.306,0,1,1,4.612,0A4.619,4.619,0,0,1,50.141,50.922Z" transform="translate(-47.834 -44)" fill="#0f8af9"/>
      </g>
      <g id="Group_506" data-name="Group 506" transform="translate(55.931 15.614)">
        <path id="Path_5638" data-name="Path 5638" d="M52.141,53.175a2.308,2.308,0,0,1-2.307-2.307V16.64a2.307,2.307,0,0,1,4.615,0V50.867A2.308,2.308,0,0,1,52.141,53.175Z" transform="translate(-49.834 -14.333)" fill="#0f8af9"/>
      </g>
      <g id="Group_507" data-name="Group 507" transform="translate(21.703 52.148)">
        <path id="Path_5639" data-name="Path 5639" d="M54.394,50.615H22.473a2.307,2.307,0,1,1,0-4.615H54.394a2.307,2.307,0,1,1,0,4.615Z" transform="translate(-20.166 -46)" fill="#0f8af9"/>
      </g>
      <g id="Group_508" data-name="Group 508" transform="translate(19.396 49.841)">
        <path id="Path_5640" data-name="Path 5640" d="M22.781,50.922a4.619,4.619,0,0,1-4.615-4.615,2.307,2.307,0,0,1,4.615,0v0a2.306,2.306,0,1,1,0,4.612Z" transform="translate(-18.166 -44)" fill="#0f8af9"/>
      </g>
      <g id="Group_509" data-name="Group 509" transform="translate(46.316 6)">
        <path id="Path_5641" data-name="Path 5641" d="M53.422,20.229a2.3,2.3,0,0,1-1.631-.676L42.176,9.939a2.307,2.307,0,0,1,3.263-3.263l9.615,9.614a2.307,2.307,0,0,1-1.631,3.939Z" transform="translate(-41.5 -6)" fill="#0f8af9"/>
      </g>
      <g id="Group_510" data-name="Group 510" transform="translate(46.316 6)">
        <path id="Path_5642" data-name="Path 5642" d="M43.807,18.306A2.308,2.308,0,0,1,41.5,16V8.307a2.307,2.307,0,1,1,4.615,0V16A2.308,2.308,0,0,1,43.807,18.306Z" transform="translate(-41.5 -6)" fill="#0f8af9"/>
      </g>
      <g id="Group_511" data-name="Group 511" transform="translate(48.238 15.614)">
        <path id="Path_5643" data-name="Path 5643" d="M53.166,18.948H45.473a2.307,2.307,0,1,1,0-4.615h7.693a2.307,2.307,0,0,1,0,4.615Z" transform="translate(-43.166 -14.333)" fill="#0f8af9"/>
      </g>
      <g id="Group_512" data-name="Group 512" transform="translate(46.316 13.692)">
        <path id="Path_5644" data-name="Path 5644" d="M45.729,19.2A4.235,4.235,0,0,1,41.5,14.974a2.308,2.308,0,0,1,4.587-.359,2.308,2.308,0,0,1-.358,4.588Z" transform="translate(-41.5 -12.667)" fill="#0f8af9"/>
      </g>
    </g>
  </svg>
);

export default Icon6;

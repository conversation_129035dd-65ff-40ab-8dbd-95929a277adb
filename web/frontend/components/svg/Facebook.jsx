/**
 * @typedef {Object} FacebookProps
 * @property {string} [width="19.991"]
 * @property {string} [height="19.991"]
 */

/**
 * @param {FacebookProps} props
 */
const Facebook = ({ width = "19.991", height = "19.991" }) => (
  <svg
    id="facebook"
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 19.991 19.991"
  >
    <path
      id="Path_5613"
      data-name="Path 5613"
      d="M19.991,10A10,10,0,1,1,10,0,10,10,0,0,1,19.991,10Zm0,0"
      fill="#4267b2"
    />
    <path
      id="Path_5614"
      data-name="Path 5614"
      d="M149.069,92.35a10,10,0,0,0,9.567-9.983c0-.039,0-.078,0-.117l-6.946-6.937-7.427,7.753,4.126,4.121-1.9,2.584Zm0,0"
      transform="translate(-138.645 -72.369)"
      fill="#345aa7"
    />
    <path
      id="Path_5616"
      data-name="Path 5616"
      d="M258.718,73.558V71.3a7.572,7.572,0,0,0-2.56-.084,3.97,3.97,0,0,0-.513.131V85.749h.471V79.088h2.056l.294-2.556h-2.35V74.857C256.116,73.181,258.718,73.558,258.718,73.558Zm0,0"
      transform="translate(-245.665 -68.352)"
      fill="#dce1eb"
    />
    <path
      id="Path_5615"
      data-name="Path 5615"
      d="M151.685,71.3v2.262s-2.6-.377-2.6,1.3v1.676h2.35l-.294,2.556h-2.056V85.75h-2.6V79.088l-2.224-.042V76.533h2.182V74.605a3.29,3.29,0,0,1,2.685-3.394A7.571,7.571,0,0,1,151.685,71.3Zm0,0"
      transform="translate(-138.629 -68.352)"
      fill="#fff"
    />
  </svg>
);

export default Facebook;

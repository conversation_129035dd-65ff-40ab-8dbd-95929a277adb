import React from "react";

const StoreSeoLogoWhite = () => {
  return (
    <svg
      width="281"
      height="93"
      viewBox="0 0 281 93"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask
        id="mask0_135_207"
        style="mask-type:luminance"
        maskUnits="userSpaceOnUse"
        x="8"
        y="8"
        width="265"
        height="77"
      >
        <path
          d="M272.85 8H8V85H272.85V8Z"
          fill="white"
        />
      </mask>
      <g mask="url(#mask0_135_207)">
        <path
          d="M108.733 64.2452C105.463 64.2452 102.66 63.3923 101.165 62.6816V58.1799C103.314 59.175 105.696 59.7437 107.659 59.7437C111.022 59.7437 112.564 58.3221 112.564 55.2421C112.564 52.0199 111.349 51.1198 107.472 48.8927C103.314 46.476 100.791 44.6754 100.791 39.4158C100.791 33.4453 104.435 30.2705 111.256 30.2705C113.592 30.2705 115.975 30.6496 118.03 31.3129L117.423 35.4829C116.021 35.0089 114.106 34.7247 112.331 34.7247C109.901 34.7247 107.425 35.1985 107.425 38.6102C107.425 40.79 108.313 41.548 112.19 43.9647C116.816 46.8078 119.198 48.5609 119.198 54.247C119.245 61.023 115.834 64.2452 108.733 64.2452Z"
          fill="white"
        />
        <path
          d="M132.504 64.245C127.692 64.245 125.543 62.0179 125.543 57.0425V43.2536H122.366V38.9416H125.729L126.15 32.8763H131.756V38.9416H137.176L136.989 43.2536H131.756V56.8055C131.756 59.1274 132.317 60.3594 135.213 60.3594C135.774 60.3594 136.335 60.2646 136.802 60.0752L136.615 63.5341C135.587 64.0081 134.092 64.245 132.504 64.245Z"
          fill="white"
        />
        <path
          d="M150.822 64.2453C142.086 64.2453 140.871 59.27 140.871 54.342V48.182C140.871 41.5482 144.141 38.2785 150.822 38.2785C157.55 38.2785 160.82 41.5007 160.82 48.182V54.342C160.82 61.3074 157.83 64.2453 150.822 64.2453ZM150.822 42.2114C147.458 42.2114 147.085 44.8176 147.085 46.7603V55.9056C147.085 57.8011 147.458 60.3598 150.822 60.3598C154.186 60.3598 154.56 57.8011 154.56 55.9056V46.7603C154.607 43.6803 153.392 42.2114 150.822 42.2114Z"
          fill="white"
        />
        <path
          d="M165.22 63.6763V38.9415H170.92V43.3484C172.135 39.7944 174.704 38.373 177.788 38.373C177.881 38.373 177.975 38.373 178.021 38.373V44.1064C177.928 44.1064 177.881 44.1064 177.788 44.1064C175.405 44.1064 172.929 45.0068 171.527 46.4284L171.434 46.5231V63.7236H165.22V63.6763Z"
          fill="white"
        />
        <path
          d="M192.316 64.2453C185.401 64.2453 181.477 60.644 181.477 54.3893V48.1345C181.477 41.6427 184.7 38.3732 191.007 38.3732C197.409 38.3732 200.397 41.2636 200.397 47.5185V52.4465H187.597V54.6262C187.597 58.2274 189.98 60.2649 194.091 60.2649C195.773 60.2649 197.548 59.9333 199.697 59.1276L199.509 63.0133C197.827 63.7715 194.978 64.2453 192.316 64.2453ZM191.054 42.0218C188.017 42.0218 187.597 44.2489 187.597 46.476V49.4613H194.465V46.476C194.465 44.2489 194.044 42.0218 191.054 42.0218Z"
          fill="white"
        />
        <path
          d="M212.339 64.2449C209.069 64.2449 206.267 63.392 204.771 62.6813V58.1796C206.92 59.1747 209.304 59.7434 211.265 59.7434C214.629 59.7434 216.17 58.3218 216.17 55.2418C216.17 52.0196 214.955 51.1195 211.079 48.8924C206.92 46.4757 204.397 44.6751 204.397 39.4155C204.397 33.4449 208.042 30.2702 214.862 30.2702C217.199 30.2702 219.581 30.6493 221.637 31.3126L221.029 35.4826C219.628 35.0086 217.713 34.7244 215.938 34.7244C213.507 34.7244 211.031 35.1982 211.031 38.6098C211.031 40.7897 211.92 41.5477 215.796 43.9644C220.422 46.8075 222.804 48.5606 222.804 54.2467C222.852 61.0227 219.395 64.2449 212.339 64.2449Z"
          fill="white"
        />
        <path
          d="M227.358 63.6768V30.9815H245.066V35.2935H233.992V45.0546H243.242V49.3666H233.992V59.2701H245.485V63.6768H227.358Z"
          fill="white"
        />
        <path
          d="M260.907 64.2451C253.198 64.2451 248.946 59.7911 248.946 51.7357V42.8273C248.946 34.8667 253.291 30.3178 260.907 30.3178C268.756 30.3178 272.867 34.6298 272.867 42.8273V51.8304C272.867 59.7437 268.475 64.2451 260.907 64.2451ZM260.907 34.772C257.357 34.772 255.861 36.8095 255.861 41.5953V53.2993C255.861 57.7535 257.404 59.7911 260.907 59.7911C265.392 59.7911 265.952 56.569 265.952 53.4415V41.5953C265.952 36.8095 264.458 34.772 260.907 34.772Z"
          fill="white"
        />
        <path
          d="M88.8827 12.3592C88.5557 10.8903 87.6212 9.61088 86.3598 8.80534C85.4722 8.23672 84.4443 7.95242 83.4166 7.95242C81.4543 7.95242 79.6323 8.94749 78.6045 10.6533L48.1437 61.497C46.5085 64.1981 47.3495 67.7045 50.0125 69.3156C50.9001 69.8842 51.9279 70.1685 52.9558 70.1685C54.918 70.1685 56.7401 69.1734 57.7678 67.4676L88.2753 16.7186C89.0228 15.3918 89.2565 13.8755 88.8827 12.3592Z"
          fill="white"
        />
        <path
          d="M57.1157 21.8835C56.7886 20.4145 55.8543 19.1352 54.5929 18.3296C53.7053 17.761 52.6774 17.4767 51.6496 17.4767C49.6874 17.4767 47.8653 18.4718 46.8374 20.1776L23.2912 58.8435C21.656 61.5444 22.4969 65.0508 25.1599 66.6619C26.0476 67.2306 27.0753 67.5148 28.1032 67.5148C30.0655 67.5148 31.8874 66.5197 32.9153 64.8139L56.4617 26.1481C57.2559 24.8687 57.4894 23.3524 57.1157 21.8835Z"
          fill="white"
        />
        <path
          d="M30.6175 25.4373C30.2906 23.9684 29.3561 22.689 28.0947 21.8835C27.207 21.3148 26.1791 21.0305 25.1514 21.0305C23.1892 21.0305 21.3672 22.0256 20.3394 23.7315L8.84651 41.8324C8.05228 43.1117 7.81869 44.6755 8.19244 46.1444C8.51947 47.6133 9.40713 48.8926 10.6685 49.6982C11.5562 50.2668 12.584 50.5512 13.6118 50.5512C15.574 50.5512 17.3961 49.5561 18.4239 47.8503L29.9635 29.7493C30.7576 28.4699 30.9914 26.9535 30.6175 25.4373Z"
          fill="white"
        />
        <path
          d="M24.4118 78.5081C24.038 75.7125 21.6554 73.6276 18.8989 73.6276C18.6186 73.6276 18.385 73.6276 18.1047 73.6749C15.068 74.1014 12.9189 76.9918 13.3861 80.1192C13.7599 82.9149 16.1425 84.9998 18.8989 84.9998C19.1792 84.9998 19.4129 84.9998 19.6932 84.9525C22.6832 84.526 24.8322 81.6356 24.4118 78.5081Z"
          fill="white"
        />
        <path
          d="M49.3498 78.5081C48.9761 75.7125 46.5934 73.6276 43.8369 73.6276C43.5567 73.6276 43.323 73.6276 43.0428 73.6749C40.0059 74.1014 37.8569 76.9918 38.3241 80.1192C38.698 82.9149 41.0805 84.9998 43.8369 84.9998C44.1174 84.9998 44.3509 84.9998 44.6311 84.9525C46.1263 84.7156 47.4343 83.9574 48.322 82.7254C49.2096 81.5407 49.5835 80.0245 49.3498 78.5081Z"
          fill="white"
        />
        <path
          d="M66.1779 31.645C64.4959 34.0615 61.2724 34.8197 58.7495 33.2561C56.2266 31.6924 55.339 28.3755 56.6472 25.7219L56.0865 26.6223L38.0996 56.1902C38.1465 56.1427 38.1465 56.1427 38.1932 56.0955C38.2399 56.048 38.2866 55.9533 38.3333 55.9058C38.38 55.8584 38.4267 55.8111 38.4734 55.7637C39.5012 54.6264 40.9963 53.9631 42.5847 53.9631C45.715 53.9631 48.2844 56.5218 48.2844 59.744C48.2844 60.5022 48.1442 61.2604 47.864 61.9236L58.5627 44.2019L66.1779 31.645Z"
          fill="white"
        />
        <path
          d="M38.1816 56.1426C38.2283 56.0952 38.2751 56.0004 38.3218 55.9532C38.3218 56.0004 38.2283 56.0479 38.1816 56.1426Z"
          fill="white"
        />
      </g>
    </svg>
  );
};

export default StoreSeoLogoWhite;

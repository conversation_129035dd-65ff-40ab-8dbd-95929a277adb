/**
 * @typedef {Object} ShieldProps
 * @property {string} [width="43.946"]
 * @property {string} [height="50"]
 */

/**
 * @param {ShieldProps} props
 */
const Shield = ({ width = "43.946", height = "50" }) => (
  <svg
    id="shield_1_"
    data-name="shield (1)"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    width={width}
    height={height}
    viewBox="0 0 43.946 50"
  >
    <defs>
      <linearGradient
        id="linear-gradient"
        x1="0.5"
        y1="1"
        x2="0.5"
        gradientUnits="objectBoundingBox"
      >
        <stop
          offset="0"
          stopColor="#ffc2cc"
        />
        <stop
          offset="1"
          stopColor="#fff2f4"
        />
      </linearGradient>
      <linearGradient
        id="linear-gradient-2"
        x1="0.5"
        y1="1"
        x2="0.5"
        gradientUnits="objectBoundingBox"
      >
        <stop
          offset="0"
          stopColor="#fd3a84"
        />
        <stop
          offset="1"
          stopColor="#ffa68d"
        />
      </linearGradient>
    </defs>
    <path
      id="Path_5611"
      data-name="Path 5611"
      d="M96.141,91.185a3.384,3.384,0,0,1-1.214-.224A38.367,38.367,0,0,1,83.758,84.4C78.61,79.893,76,74.509,76,68.394V53.155a3.313,3.313,0,0,1,1.672-2.863,10.116,10.116,0,0,0,3.692-3.642A3.375,3.375,0,0,1,84.267,45H108a3.375,3.375,0,0,1,2.9,1.649,10.666,10.666,0,0,0,3.692,3.863,3.312,3.312,0,0,1,1.672,2.863V68.394c0,6.129-2.6,11.516-7.734,16.012a38.121,38.121,0,0,1-11.191,6.559A3.382,3.382,0,0,1,96.141,91.185Z"
      transform="translate(-74.163 -43.163)"
      fill="url(#linear-gradient)"
    />
    <g
      id="Group_467"
      data-name="Group 467"
    >
      <g
        id="Group_466"
        data-name="Group 466"
      >
        <path
          id="Path_5612"
          data-name="Path 5612"
          d="M73.481,5.859a4.4,4.4,0,0,1-4.395-4.395A1.465,1.465,0,0,0,67.621,0h-29.3a1.465,1.465,0,0,0-1.465,1.465,4.4,4.4,0,0,1-4.395,4.395A1.465,1.465,0,0,0,31,7.324V25.1a21.754,21.754,0,0,0,3.433,11.864A27.913,27.913,0,0,0,41.867,44.6C46.647,48.014,52.212,50,52.973,50c.8,0,6.394-2.035,11.105-5.4a27.912,27.912,0,0,0,7.434-7.638A21.755,21.755,0,0,0,74.946,25.1V7.324A1.465,1.465,0,0,0,73.481,5.859ZM69.086,25.1c0,4.987-2.106,9.386-6.259,13.075A31.673,31.673,0,0,1,53.5,43.705a1.465,1.465,0,0,1-1.056,0,31.885,31.885,0,0,1-9.308-5.538c-4.167-3.7-6.28-8.094-6.28-13.068V11.611a1.465,1.465,0,0,1,.73-1.267,10.358,10.358,0,0,0,3.755-3.755,1.465,1.465,0,0,1,1.267-.73H63.334a1.465,1.465,0,0,1,1.267.73,10.856,10.856,0,0,0,3.755,3.951,1.465,1.465,0,0,1,.73,1.267ZM52.973,13.379A10.254,10.254,0,1,0,63.227,23.633,10.266,10.266,0,0,0,52.973,13.379Zm3.965,12.148A1.465,1.465,0,1,1,54.867,27.6L52.973,25.7,51.079,27.6a1.465,1.465,0,0,1-2.072-2.072L50.9,23.633l-1.894-1.894a1.465,1.465,0,0,1,2.072-2.072l1.894,1.894,1.894-1.894a1.465,1.465,0,0,1,2.072,2.072l-1.894,1.894Z"
          transform="translate(-31)"
          fill="url(#linear-gradient-2)"
        />
      </g>
    </g>
  </svg>
);

export default Shield;

export const meterIcon = (color) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="20"
    viewBox="0 0 24 20"
  >
    <g clipPath="url(#clip0_44334_53169)">
      <path
        d="M21.6032 10.1085C21.2872 7.55913 20.0108 5.22625 18.0342 3.58537C16.0577 1.94449 13.5298 1.11913 10.9658 1.27751C8.40181 1.4359 5.99471 2.56611 4.23515 4.43778C2.4756 6.30944 1.49603 8.78167 1.49612 11.3506C1.49167 13.4096 2.12211 15.42 3.30153 17.1079C3.66904 17.6216 4.15509 18.0392 4.71841 18.325C5.28173 18.6109 5.9057 18.7566 6.53736 18.7498H16.6309C17.2893 18.7502 17.9376 18.5885 18.5187 18.279C19.0998 17.9696 19.5958 17.5218 19.963 16.9753C21.3153 14.9571 21.8972 12.5201 21.6032 10.1085ZM18.847 16.2233C18.6029 16.5869 18.2731 16.8848 17.8866 17.0908C17.5001 17.2968 17.0689 17.4046 16.6309 17.4045H6.53736C6.12241 17.4113 5.71194 17.3179 5.34074 17.1323C4.96955 16.9467 4.64857 16.6744 4.40503 16.3383C3.18555 14.584 2.65082 12.4437 2.90185 10.3219C3.15288 8.20015 4.1723 6.24377 5.7675 4.8224C7.3614 3.3891 9.43102 2.59918 11.5746 2.60599C11.9245 2.60597 12.2742 2.62596 12.6219 2.66586C14.5606 2.90118 16.3654 3.77672 17.7502 5.15376C19.135 6.53079 20.0207 8.33056 20.267 10.2679C20.5338 12.3597 20.0289 14.477 18.847 16.2233Z"
        fill={color}
        stroke={color}
      />
      <path
        d="M13.9748 7.2358L12.4499 9.17104C11.9243 8.9623 11.3411 8.94973 10.807 9.13562C10.2729 9.32151 9.82355 9.69345 9.54114 10.1834C9.25873 10.6734 9.16212 11.2486 9.26898 11.804C9.37584 12.3593 9.67903 12.8576 10.1231 13.2078C10.5672 13.558 11.1225 13.7366 11.6874 13.711C12.2524 13.6854 12.7892 13.4573 13.1998 13.0684C13.6104 12.6795 13.8673 12.1558 13.9235 11.5931C13.9797 11.0304 13.8315 10.4662 13.5059 10.0038L15.0309 8.06855C15.0882 7.99949 15.1311 7.91965 15.157 7.83376C15.183 7.74786 15.1915 7.65763 15.1821 7.56839C15.1726 7.47915 15.1455 7.3927 15.1021 7.31413C15.0588 7.23556 15.0001 7.16645 14.9297 7.11089C14.8592 7.05533 14.7783 7.01442 14.6918 6.99059C14.6053 6.96676 14.5149 6.96047 14.4259 6.97211C14.3369 6.98375 14.2512 7.01308 14.1737 7.05837C14.0962 7.10365 14.0286 7.16398 13.9748 7.2358ZM12.3796 11.9747C12.2561 12.1315 12.0889 12.2482 11.8991 12.3099C11.7094 12.3717 11.5055 12.3758 11.3134 12.3217C11.1213 12.2677 10.9495 12.1578 10.8199 12.0061C10.6902 11.8544 10.6084 11.6676 10.5849 11.4694C10.5613 11.2713 10.5971 11.0705 10.6877 10.8927C10.7782 10.7148 10.9195 10.5678 11.0936 10.4703C11.2677 10.3727 11.4669 10.329 11.6658 10.3446C11.8648 10.3602 12.0547 10.4345 12.2114 10.5581C12.4211 10.724 12.5564 10.9662 12.588 11.2317C12.6195 11.4971 12.5445 11.7643 12.3796 11.9747Z"
        fill={color}
        stroke={color}
      />
    </g>
    <defs>
      <clipPath id="clip0_44334_53169">
        <rect
          width="23.0146"
          height="20"
          fill="white"
          transform="translate(0.0820312)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const docIcon = (color) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="18"
    height="20"
    viewBox="0 0 18 20"
    fill="none"
  >
    <g clipPath="url(#clip0_44334_53183)">
      <path
        d="M4.12936 4.61563H1.82005C1.71903 4.61563 1.619 4.59573 1.52566 4.55707C1.43233 4.51841 1.34753 4.46175 1.2761 4.39031C1.20466 4.31888 1.148 4.23408 1.10934 4.14074C1.07068 4.04741 1.05078 3.94738 1.05078 3.84636C1.05078 3.74534 1.07068 3.6453 1.10934 3.55197C1.148 3.45864 1.20466 3.37383 1.2761 3.3024C1.34753 3.23097 1.43233 3.1743 1.52566 3.13564C1.619 3.09699 1.71903 3.07709 1.82005 3.07709H4.12936C4.33338 3.07709 4.52905 3.15814 4.67331 3.3024C4.81758 3.44667 4.89863 3.64233 4.89863 3.84636C4.89863 4.05038 4.81758 4.24605 4.67331 4.39031C4.52905 4.53458 4.33338 4.61563 4.12936 4.61563Z"
        fill={color}
      />
      <path
        d="M13.23 19.2309C13.0261 19.2308 12.8305 19.1498 12.6863 19.0056C12.5421 18.8614 12.461 18.6658 12.4609 18.4618V16.1539C12.4609 15.9498 12.542 15.7542 12.6863 15.6099C12.8305 15.4656 13.0262 15.3846 13.2302 15.3846C13.4342 15.3846 13.6299 15.4656 13.7742 15.6099C13.9184 15.7542 13.9995 15.9498 13.9995 16.1539V18.4618C13.9994 18.6658 13.9183 18.8615 13.774 19.0057C13.6297 19.1499 13.434 19.2309 13.23 19.2309Z"
        fill={color}
      />
      <path
        d="M1.05002 5.38473C0.846098 5.38464 0.65056 5.30357 0.506397 5.15935C0.362234 5.01512 0.28125 4.81955 0.28125 4.61563C0.281603 4.20769 0.443812 3.81656 0.732267 3.5281C1.02072 3.23965 1.41185 3.07744 1.81979 3.07709C2.02381 3.07709 2.21948 3.15814 2.36374 3.3024C2.50801 3.44667 2.58906 3.64233 2.58906 3.84636C2.58906 4.05038 2.50801 4.24605 2.36374 4.39031C2.21948 4.53458 2.02381 4.61563 1.81979 4.61563C1.81979 4.81955 1.7388 5.01512 1.59464 5.15935C1.45048 5.30357 1.25494 5.38464 1.05102 5.38473H1.05002Z"
        fill={color}
      />
      <path
        d="M1.05035 19.2309C0.846401 19.2308 0.650828 19.1497 0.506612 19.0055C0.362397 18.8613 0.281338 18.6657 0.28125 18.4618V4.61528C0.28125 4.41125 0.362298 4.21559 0.506564 4.07132C0.65083 3.92706 0.846496 3.84601 1.05052 3.84601C1.25454 3.84601 1.45021 3.92706 1.59447 4.07132C1.73874 4.21559 1.81979 4.41125 1.81979 4.61528V18.4618C1.81979 18.5628 1.79988 18.6629 1.76121 18.7562C1.72254 18.8495 1.66586 18.9343 1.59441 19.0057C1.52295 19.0772 1.43813 19.1338 1.34478 19.1724C1.25143 19.2111 1.15138 19.2309 1.05035 19.2309Z"
        fill={color}
      />
      <path
        d="M12.4607 20C12.2567 20 12.061 19.9189 11.9167 19.7747C11.7725 19.6304 11.6914 19.4347 11.6914 19.2307C11.6914 19.0267 11.7725 18.831 11.9167 18.6868C12.061 18.5425 12.2567 18.4615 12.4607 18.4615C12.4607 18.2576 12.5417 18.062 12.6858 17.9179C12.83 17.7737 13.0256 17.6927 13.2294 17.6927C13.4333 17.6927 13.6289 17.7737 13.773 17.9179C13.9172 18.062 13.9982 18.2576 13.9982 18.4615C13.9979 18.8692 13.8358 19.2602 13.5475 19.5486C13.2593 19.8371 12.8684 19.9994 12.4607 20Z"
        fill={color}
      />
      <path
        d="M12.4618 20.0003H1.82005C1.61603 20.0003 1.42036 19.9192 1.2761 19.775C1.13183 19.6307 1.05078 19.435 1.05078 19.231C1.05078 19.027 1.13183 18.8313 1.2761 18.687C1.42036 18.5428 1.61603 18.4617 1.82005 18.4617H12.4618C12.6658 18.4617 12.8615 18.5428 13.0058 18.687C13.15 18.8313 13.2311 19.027 13.2311 19.231C13.2311 19.435 13.15 19.6307 13.0058 19.775C12.8615 19.9192 12.6658 20.0003 12.4618 20.0003Z"
        fill={color}
      />
      <path
        d="M1.81979 20C1.41185 19.9997 1.02072 19.8374 0.732267 19.549C0.443812 19.2605 0.281603 18.8694 0.28125 18.4615C0.28125 18.2574 0.362298 18.0618 0.506564 17.9175C0.65083 17.7732 0.846496 17.6922 1.05052 17.6922C1.25454 17.6922 1.45021 17.7732 1.59447 17.9175C1.73874 18.0618 1.81979 18.2574 1.81979 18.4615C2.02368 18.4615 2.21922 18.5425 2.36339 18.6866C2.50756 18.8308 2.58856 19.0263 2.58856 19.2302C2.58856 19.4341 2.50756 19.6297 2.36339 19.7738C2.21922 19.918 2.02368 19.999 1.81979 19.999V20Z"
        fill={color}
      />
      <path
        d="M4.12814 2.3077C3.92422 2.30761 3.72868 2.22654 3.58452 2.08232C3.44036 1.93809 3.35937 1.74252 3.35938 1.5386C3.35973 1.13066 3.52194 0.739534 3.81039 0.451078C4.09885 0.162623 4.48998 0.000414151 4.89791 6.10352e-05C5.10194 6.10352e-05 5.2976 0.0811087 5.44187 0.225375C5.58614 0.369641 5.66718 0.565307 5.66718 0.76933C5.66718 0.973353 5.58614 1.16902 5.44187 1.31329C5.2976 1.45755 5.10194 1.5386 4.89791 1.5386C4.89791 1.74252 4.81693 1.93809 4.67277 2.08232C4.5286 2.22654 4.33307 2.30761 4.12914 2.3077H4.12814Z"
        fill={color}
      />
      <path
        d="M4.12848 16.1537C3.92453 16.1537 3.72895 16.0726 3.58474 15.9284C3.44052 15.7842 3.35946 15.5886 3.35938 15.3846V1.53813C3.35937 1.43711 3.37927 1.33707 3.41793 1.24374C3.45659 1.15041 3.51326 1.06561 3.58469 0.994174C3.65612 0.92274 3.74093 0.866076 3.83426 0.827417C3.92759 0.788758 4.02762 0.76886 4.12864 0.76886C4.22967 0.76886 4.3297 0.788758 4.42303 0.827417C4.51636 0.866076 4.60117 0.92274 4.6726 0.994174C4.74403 1.06561 4.8007 1.15041 4.83936 1.24374C4.87802 1.33707 4.89791 1.43711 4.89791 1.53813V15.3846C4.89791 15.4857 4.87801 15.5857 4.83934 15.679C4.80067 15.7724 4.74398 15.8572 4.67253 15.9286C4.60108 16 4.51625 16.0567 4.4229 16.0953C4.32955 16.1339 4.22951 16.1538 4.12848 16.1537Z"
        fill={color}
      />
      <path
        d="M13.1036 1.53854H4.89818C4.69415 1.53854 4.49849 1.45749 4.35422 1.31322C4.20995 1.16896 4.12891 0.973292 4.12891 0.769269C4.12891 0.565246 4.20995 0.36958 4.35422 0.225314C4.49849 0.0810477 4.69415 0 4.89818 0H13.1036C13.3076 0 13.5033 0.0810477 13.6476 0.225314C13.7918 0.36958 13.8729 0.565246 13.8729 0.769269C13.8729 0.973292 13.7918 1.16896 13.6476 1.31322C13.5033 1.45749 13.3076 1.53854 13.1036 1.53854Z"
        fill={color}
      />
      <path
        d="M15.5388 16.9232C15.3348 16.9232 15.1391 16.8422 14.9948 16.6979C14.8506 16.5536 14.7695 16.358 14.7695 16.1539C14.7695 15.9499 14.8506 15.7543 14.9948 15.61C15.1391 15.4657 15.3348 15.3847 15.5388 15.3847C15.5388 15.1808 15.6198 14.9852 15.764 14.8411C15.9081 14.6969 16.1037 14.6159 16.3076 14.6159C16.5115 14.6159 16.707 14.6969 16.8512 14.8411C16.9953 14.9852 17.0763 15.1808 17.0763 15.3847C17.076 15.7924 16.9139 16.1834 16.6257 16.4718C16.3374 16.7603 15.9466 16.9226 15.5388 16.9232Z"
        fill={color}
      />
      <path
        d="M16.3082 16.1542C16.1042 16.1542 15.9086 16.0731 15.7644 15.9289C15.6202 15.7847 15.5392 15.5891 15.5391 15.3851V3.97429C15.5391 3.77026 15.6201 3.5746 15.7644 3.43033C15.9086 3.28606 16.1043 3.20502 16.3083 3.20502C16.5124 3.20502 16.708 3.28606 16.8523 3.43033C16.9966 3.5746 17.0776 3.77026 17.0776 3.97429V15.3848C17.0776 15.5889 16.9965 15.7846 16.8522 15.9289C16.7079 16.0732 16.5122 16.1542 16.3082 16.1542Z"
        fill={color}
      />
      <path
        d="M15.5399 16.9232H4.89818C4.69415 16.9232 4.49849 16.8422 4.35422 16.6979C4.20995 16.5537 4.12891 16.358 4.12891 16.154C4.12891 15.95 4.20995 15.7543 4.35422 15.61C4.49849 15.4658 4.69415 15.3847 4.89818 15.3847H15.5399C15.744 15.3847 15.9396 15.4658 16.0839 15.61C16.2282 15.7543 16.3092 15.95 16.3092 16.154C16.3092 16.358 16.2282 16.5537 16.0839 16.6979C15.9396 16.8422 15.744 16.9232 15.5399 16.9232Z"
        fill={color}
      />
      <path
        d="M4.89791 16.9233C4.48998 16.923 4.09885 16.7608 3.81039 16.4723C3.52194 16.1839 3.35973 15.7927 3.35938 15.3848C3.35938 15.1808 3.44042 14.9851 3.58469 14.8409C3.72895 14.6966 3.92462 14.6155 4.12864 14.6155C4.33267 14.6155 4.52833 14.6966 4.6726 14.8409C4.81687 14.9851 4.89791 15.1808 4.89791 15.3848C5.1018 15.3848 5.29734 15.4658 5.44152 15.61C5.58569 15.7541 5.66668 15.9497 5.66668 16.1536C5.66668 16.3575 5.58569 16.553 5.44152 16.6972C5.29734 16.8414 5.1018 16.9223 4.89791 16.9223V16.9233Z"
        fill={color}
      />
      <path
        d="M16.3065 4.74375C16.2055 4.74385 16.1054 4.72399 16.0121 4.68531C15.9188 4.64664 15.8341 4.58991 15.7628 4.51838L12.5573 1.31329C12.4131 1.16904 12.332 0.973386 12.332 0.769382C12.332 0.565378 12.4131 0.369729 12.5573 0.225476C12.7016 0.0812235 12.8972 0.000183109 13.1012 0.000183105C13.3052 0.000183101 13.5009 0.0812235 13.6451 0.225476L16.8506 3.43057C16.9582 3.53811 17.0315 3.67517 17.0612 3.82439C17.091 3.97361 17.0758 4.1283 17.0176 4.26887C16.9593 4.40945 16.8607 4.52961 16.7342 4.61414C16.6077 4.69867 16.4586 4.74377 16.3065 4.74375Z"
        fill={color}
      />
      <path
        d="M13.1011 4.10259C12.8972 4.1025 12.7017 4.02149 12.5575 3.87735C12.4133 3.7332 12.3322 3.53772 12.332 3.33382V0.769147C12.332 0.565124 12.4131 0.369458 12.5573 0.225192C12.7016 0.0809258 12.8973 -0.00012207 13.1013 -0.00012207C13.3053 -0.00012207 13.501 0.0809258 13.6453 0.225192C13.7895 0.369458 13.8706 0.565124 13.8706 0.769147V3.33382C13.8704 3.53777 13.7893 3.73331 13.645 3.87746C13.5007 4.02162 13.3051 4.10259 13.1011 4.10259Z"
        fill={color}
      />
      <path
        d="M16.3066 4.74368H13.7419C13.5379 4.74368 13.3422 4.66263 13.198 4.51836C13.0537 4.3741 12.9727 4.17843 12.9727 3.97441C12.9727 3.77039 13.0537 3.57472 13.198 3.43045C13.3422 3.28619 13.5379 3.20514 13.7419 3.20514H16.3066C16.5106 3.20514 16.7063 3.28619 16.8506 3.43045C16.9948 3.57472 17.0759 3.77039 17.0759 3.97441C17.0759 4.17843 16.9948 4.3741 16.8506 4.51836C16.7063 4.66263 16.5106 4.74368 16.3066 4.74368Z"
        fill={color}
      />
      <path
        d="M13.7419 4.74232C13.3683 4.74179 13.0102 4.59322 12.7459 4.32915C12.4816 4.06508 12.3328 3.70704 12.332 3.33346C12.3325 3.14011 12.4058 2.95403 12.5372 2.81224C12.6687 2.67045 12.8487 2.58335 13.0414 2.56827C13.2342 2.55318 13.4256 2.61122 13.5775 2.73083C13.7294 2.85044 13.8307 3.02286 13.8612 3.21378C14.0526 3.24386 14.2255 3.34499 14.3456 3.49698C14.4656 3.64897 14.5239 3.84064 14.5089 4.03374C14.4938 4.22683 14.4064 4.40714 14.2643 4.53867C14.1221 4.6702 13.9356 4.74328 13.7419 4.74332V4.74232Z"
        fill={color}
      />
    </g>
    <defs>
      <clipPath id="clip0_44334_53183">
        <rect
          width="16.7956"
          height="20"
          fill="white"
          transform="translate(0.28125)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const archiveIcon = (color) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
  >
    <g clipPath="url(#clip0_44334_53229)">
      <path
        d="M20.7191 15.6499C20.5246 16.694 20.0191 17.6549 19.269 18.4067C18.5189 19.1585 17.5591 19.6662 16.5155 19.8631C15.4719 20.06 14.3931 19.937 13.4206 19.5103C12.4481 19.0835 11.6272 18.3728 11.0655 17.4715C10.5039 16.5701 10.2277 15.5201 10.2731 14.459C10.3186 13.3979 10.6835 12.3754 11.3202 11.5253C11.9568 10.6753 12.8354 10.0374 13.8409 9.69531C14.8463 9.35324 15.9316 9.32292 16.9546 9.60834C17.1521 9.66584 17.319 9.7989 17.4189 9.97866C17.5189 10.1584 17.544 10.3704 17.4887 10.5685C17.4334 10.7666 17.3022 10.9349 17.1235 11.0369C16.9449 11.1389 16.7332 11.1663 16.5345 11.1132C15.8141 10.9115 15.0495 10.9323 14.3411 11.1728C13.6326 11.4132 13.0134 11.8622 12.5646 12.4608C12.1158 13.0593 11.8583 13.7795 11.826 14.527C11.7937 15.2744 11.988 16.0142 12.3834 16.6493C12.7789 17.2843 13.3571 17.7851 14.0421 18.0858C14.7271 18.3866 15.487 18.4733 16.2222 18.3346C16.9574 18.1959 17.6335 17.8382 18.1618 17.3086C18.6902 16.7789 19.0461 16.1019 19.183 15.3664C19.1996 15.2637 19.2366 15.1654 19.2918 15.0772C19.3469 14.989 19.4191 14.9126 19.5042 14.8527C19.5892 14.7928 19.6854 14.7505 19.787 14.7282C19.8886 14.7059 19.9937 14.7042 20.096 14.7231C20.1983 14.7419 20.2958 14.7811 20.3828 14.8381C20.4697 14.8952 20.5445 14.9691 20.6025 15.0554C20.6605 15.1418 20.7008 15.2388 20.7208 15.3409C20.7408 15.443 20.7403 15.548 20.7191 15.6499ZM14.5227 13.7843C14.4505 13.7101 14.3643 13.6509 14.2691 13.6103C14.1739 13.5697 14.0715 13.5484 13.968 13.5477C13.8645 13.5469 13.7619 13.5668 13.6661 13.606C13.5703 13.6453 13.4833 13.7032 13.4101 13.7764C13.3369 13.8496 13.279 13.9367 13.2397 14.0324C13.2004 14.1282 13.1806 14.2309 13.1813 14.3344C13.182 14.4379 13.2033 14.5402 13.2439 14.6354C13.2846 14.7307 13.3437 14.8169 13.4179 14.889L14.5683 16.0394C14.8242 16.2942 15.1704 16.4377 15.5316 16.4386C15.8927 16.4395 16.2396 16.2979 16.4969 16.0444L19.9096 12.6649C19.9825 12.5928 20.0405 12.5069 20.0802 12.4123C20.12 12.3177 20.1407 12.2162 20.1412 12.1136C20.1417 12.011 20.122 11.9094 20.0832 11.8144C20.0444 11.7194 19.9873 11.633 19.9151 11.5601C19.8429 11.4872 19.7571 11.4292 19.6625 11.3894C19.5679 11.3497 19.4664 11.329 19.3638 11.3285C19.2612 11.328 19.1595 11.3477 19.0645 11.3865C18.9695 11.4253 18.8831 11.4824 18.8102 11.5546L15.5344 14.7967L14.5227 13.7843ZM20.7305 2.34379V4.68746C20.7305 4.89465 20.6482 5.09336 20.5017 5.23987C20.3551 5.38638 20.1564 5.46868 19.9492 5.46868V8.59458C19.9492 8.80177 19.8669 9.00048 19.7204 9.14699C19.5739 9.2935 19.3752 9.3758 19.168 9.3758C18.9608 9.3758 18.7621 9.2935 18.6156 9.14699C18.4691 9.00048 18.3868 8.80177 18.3868 8.59458V5.46968H3.07414V16.8752C3.07502 17.2893 3.23992 17.6862 3.53274 17.9791C3.82557 18.2719 4.22247 18.4368 4.63659 18.4377H9.59778C9.70037 18.4377 9.80196 18.4579 9.89674 18.4971C9.99152 18.5364 10.0776 18.5939 10.1502 18.6665C10.2227 18.739 10.2803 18.8251 10.3195 18.9199C10.3588 19.0147 10.379 19.1163 10.379 19.2189C10.379 19.3215 10.3588 19.4231 10.3195 19.5179C10.2803 19.6126 10.2227 19.6988 10.1502 19.7713C10.0776 19.8438 9.99152 19.9014 9.89674 19.9406C9.80196 19.9799 9.70037 20.0001 9.59778 20.0001H4.63659C3.80811 19.9991 3.01385 19.6696 2.42802 19.0838C1.8422 18.498 1.51266 17.7037 1.51169 16.8752V5.46902C1.3045 5.46902 1.10579 5.38671 0.959284 5.2402C0.812776 5.09369 0.730469 4.89499 0.730469 4.68779V2.34379C0.730998 1.72231 0.978068 1.12643 1.41746 0.686911C1.85685 0.247396 2.45266 0.000156384 3.07414 -0.000549316L18.3871 -0.000549316C19.0086 0.000244552 19.6043 0.247523 20.0436 0.687029C20.4829 1.12653 20.7299 1.72237 20.7305 2.34379ZM2.29292 3.90624H19.168V2.34379C19.1678 2.13673 19.0854 1.93823 18.939 1.79179C18.7927 1.64535 18.5942 1.56292 18.3871 1.56256H3.07414C2.86703 1.56283 2.66847 1.64522 2.52202 1.79167C2.37557 1.93812 2.29318 2.13668 2.29292 2.34379V3.90624ZM13.2307 7.81269C13.2307 7.6055 13.1484 7.40679 13.0019 7.26028C12.8554 7.11377 12.6567 7.03147 12.4495 7.03147H8.93382C8.72663 7.03147 8.52792 7.11377 8.38141 7.26028C8.2349 7.40679 8.1526 7.6055 8.1526 7.81269C8.1526 8.01988 8.2349 8.21859 8.38141 8.3651C8.52792 8.51161 8.72663 8.59391 8.93382 8.59391H12.4492C12.5518 8.59396 12.6534 8.57378 12.7482 8.53454C12.843 8.4953 12.9292 8.43776 13.0018 8.36522C13.0744 8.29267 13.1319 8.20653 13.1712 8.11173C13.2105 8.01692 13.2307 7.91531 13.2307 7.81269Z"
        fill={color}
      />
    </g>
    <defs>
      <clipPath id="clip0_44334_53229">
        <rect
          width="20.0003"
          height="20"
          fill="white"
          transform="translate(0.730469)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const barIcon = (color) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="25"
    height="20"
    viewBox="0 0 25 20"
  >
    <g clipPath="url(#clip0_44334_53251)">
      <path
        opacity="0.1"
        d="M20.482 0H3.68203C2.79838 0 2.08203 0.716344 2.08203 1.6V18.4C2.08203 19.2837 2.79838 20 3.68203 20H20.482C21.3657 20 22.082 19.2837 22.082 18.4V1.6C22.082 0.716344 21.3657 0 20.482 0Z"
        fill={color}
      />
      <path
        d="M20.8092 0.182007H3.35483C2.48683 0.182007 1.65438 0.526819 1.04061 1.14059C0.426843 1.75436 0.0820313 2.58681 0.0820312 3.45481V16.5456C0.0820313 17.4136 0.426843 18.2461 1.04061 18.8598C1.65438 19.4736 2.48683 19.8184 3.35483 19.8184H20.8092C21.6772 19.8184 22.5097 19.4736 23.1234 18.8598C23.7372 18.2461 24.082 17.4136 24.082 16.5456V3.45481C24.082 2.58681 23.7372 1.75436 23.1234 1.14059C22.5097 0.526819 21.6772 0.182007 20.8092 0.182007ZM21.9 16.5456C21.9 16.8349 21.7851 17.1124 21.5805 17.3169C21.376 17.5215 21.0985 17.6364 20.8092 17.6364H3.35483C3.06553 17.6364 2.78808 17.5215 2.58352 17.3169C2.37895 17.1124 2.26403 16.8349 2.26403 16.5456V3.45481C2.26403 3.16551 2.37895 2.88806 2.58352 2.6835C2.78808 2.47893 3.06553 2.36401 3.35483 2.36401H20.8092C21.0985 2.36401 21.376 2.47893 21.5805 2.6835C21.7851 2.88806 21.9 3.16551 21.9 3.45481V16.5456Z"
        fill={color}
      />
      <path
        d="M7.71971 8.90918C7.43041 8.90918 7.15296 9.0241 6.94839 9.22867C6.74383 9.43323 6.62891 9.71068 6.62891 9.99998V14.3636C6.62891 14.6529 6.74385 14.9304 6.94845 15.135C7.15305 15.3396 7.43055 15.4546 7.71991 15.4546C8.00926 15.4546 8.28676 15.3396 8.49136 15.135C8.69596 14.9304 8.81091 14.6529 8.81091 14.3636V9.99998C8.81091 9.8567 8.78268 9.71482 8.72784 9.58246C8.67299 9.45009 8.59261 9.32982 8.49128 9.22853C8.38994 9.12723 8.26965 9.04689 8.13726 8.9921C8.00487 8.9373 7.86299 8.90913 7.71971 8.90918Z"
        fill={color}
      />
      <path
        d="M12.0791 6.72754C11.7898 6.72754 11.5123 6.84246 11.3078 7.04703C11.1032 7.25159 10.9883 7.52904 10.9883 7.81834V14.3639C10.9883 14.6533 11.1032 14.9308 11.3078 15.1354C11.5124 15.34 11.7899 15.4549 12.0793 15.4549C12.3686 15.4549 12.6461 15.34 12.8507 15.1354C13.0553 14.9308 13.1703 14.6533 13.1703 14.3639V7.81834C13.1703 7.67506 13.1421 7.53318 13.0872 7.40082C13.0324 7.26845 12.952 7.14818 12.8507 7.04689C12.7493 6.94559 12.629 6.86525 12.4966 6.81046C12.3642 6.75566 12.2224 6.72749 12.0791 6.72754Z"
        fill={color}
      />
      <path
        d="M16.4463 4.54553C16.157 4.54553 15.8795 4.66046 15.675 4.86502C15.4704 5.06958 15.3555 5.34703 15.3555 5.63633V14.3635C15.3555 14.6529 15.4704 14.9304 15.675 15.135C15.8796 15.3396 16.1571 15.4545 16.4465 15.4545C16.7358 15.4545 17.0133 15.3396 17.2179 15.135C17.4225 14.9304 17.5375 14.6529 17.5375 14.3635V5.63633C17.5375 5.49305 17.5092 5.35118 17.4544 5.21881C17.3996 5.08644 17.3192 4.96617 17.2178 4.86488C17.1165 4.76358 16.9962 4.68324 16.8638 4.62845C16.7314 4.57366 16.5895 4.54548 16.4463 4.54553Z"
        fill={color}
      />
    </g>
    <defs>
      <clipPath id="clip0_44334_53251">
        <rect
          width="24"
          height="20"
          fill="white"
          transform="translate(0.0820312)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const chartIcon = (color) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="17"
    height="16"
    viewBox="0 0 17 16"
  >
    <g clipPath="url(#clip0_44334_53266)">
      <path
        opacity="0.1"
        d="M10.6979 0H5.36458C2.41906 0 0.03125 2.38781 0.03125 5.33333V10.6667C0.03125 13.6122 2.41906 16 5.36458 16H10.6979C13.6434 16 16.0312 13.6122 16.0312 10.6667V5.33333C16.0312 2.38781 13.6434 0 10.6979 0Z"
        fill={color}
      />
      <path
        d="M13.3913 0H2.67685C1.97519 0 1.30227 0.278732 0.806128 0.774878C0.309982 1.27102 0.03125 1.94394 0.03125 2.6456V13.36C0.0327332 14.0607 0.31212 14.7322 0.808106 15.2271C1.30409 15.722 1.97616 16 2.67685 16H13.3913C14.091 15.9985 14.7616 15.7199 15.2564 15.2251C15.7512 14.7304 16.0298 14.0597 16.0312 13.36V2.6456C16.0313 1.94491 15.7533 1.27284 15.2584 0.776856C14.7634 0.28087 14.0919 0.00148316 13.3913 0ZM14.8883 13.36C14.8868 13.7566 14.7285 14.1365 14.4481 14.4169C14.1677 14.6973 13.7878 14.8555 13.3913 14.8571H2.67685C2.27931 14.857 1.89799 14.6994 1.61636 14.4189C1.33473 14.1383 1.17574 13.7575 1.17418 13.36V2.6456C1.17418 2.247 1.33253 1.86472 1.61438 1.58287C1.89624 1.30101 2.27851 1.14267 2.67712 1.14267H13.3913C13.7888 1.14422 14.1696 1.30325 14.4502 1.58493C14.7308 1.86662 14.8883 2.24801 14.8883 2.6456V13.36Z"
        fill={color}
      />
      <path
        d="M13.7448 5.22292C13.6796 5.18513 13.6075 5.16062 13.5328 5.1508C13.458 5.14098 13.3821 5.14605 13.3093 5.1657C13.2365 5.18536 13.1684 5.21922 13.1087 5.26533C13.0491 5.31145 12.9992 5.3689 12.9619 5.43439L11.2008 8.47999L10.1264 7.61146C10.0639 7.5603 9.99117 7.52313 9.91311 7.50247C9.83504 7.48181 9.75345 7.47812 9.67384 7.49167C9.59423 7.50522 9.51845 7.53568 9.45161 7.581C9.38477 7.62632 9.32843 7.68544 9.28639 7.75439L7.62478 10.4L6.04185 8.98852C5.98527 8.93845 5.91936 8.90006 5.84789 8.87554C5.77643 8.85101 5.70082 8.84085 5.62542 8.84564C5.55002 8.85042 5.47631 8.87005 5.40852 8.9034C5.34072 8.93676 5.28019 8.98317 5.23039 9.03999L2.01332 12.7659C1.91406 12.8818 1.86492 13.0324 1.87672 13.1846C1.88853 13.3368 1.9603 13.4781 2.07625 13.5773C2.1922 13.6766 2.34284 13.7257 2.49502 13.7139C2.6472 13.7021 2.78846 13.6303 2.88772 13.5144L5.74478 10.2285L7.40185 11.7029C7.46406 11.7579 7.53748 11.7986 7.61702 11.8223C7.69656 11.8459 7.7803 11.852 7.86242 11.8401C7.94454 11.8281 8.02308 11.7984 8.09256 11.753C8.16205 11.7077 8.22082 11.6477 8.26479 11.5773L9.94479 8.89732L11.0363 9.77732C11.1005 9.82869 11.1751 9.86559 11.2549 9.88549C11.3347 9.90539 11.4178 9.90783 11.4987 9.89264C11.5795 9.87745 11.6561 9.84499 11.7233 9.79747C11.7904 9.74996 11.8465 9.68851 11.8877 9.61732L13.9848 5.99439C14.0552 5.86025 14.0695 5.70364 14.0245 5.55897C13.9795 5.41431 13.8789 5.29343 13.7448 5.22292Z"
        fill={color}
      />
    </g>
    <defs>
      <clipPath id="clip0_44334_53266">
        <rect
          width="16"
          height="16"
          fill="white"
          transform="translate(0.03125)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const waveIcon = (color) => (
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.77903 4.5C8.56302 4.49999 8.35385 4.57574 8.18794 4.71406C8.02202 4.85238 7.90988 5.04451 7.87103 5.257L6.86903 10.744C6.75342 11.3771 6.41939 11.9497 5.92512 12.3619C5.43085 12.7741 4.80764 12.9999 4.16403 13H4.08203C3.88312 13 3.69235 12.921 3.5517 12.7803C3.41105 12.6397 3.33203 12.4489 3.33203 12.25C3.33203 12.0511 3.41105 11.8603 3.5517 11.7197C3.69235 11.579 3.88312 11.5 4.08203 11.5H4.16403C4.4566 11.5001 4.73992 11.3976 4.96468 11.2103C5.18943 11.023 5.34137 10.7628 5.39403 10.475L6.39503 4.988C6.50077 4.40824 6.81419 3.88688 7.27661 3.52154C7.73903 3.1562 8.31875 2.97191 8.90725 3.00317C9.49575 3.03443 10.0527 3.27911 10.4738 3.69139C10.8949 4.10367 11.1513 4.6553 11.195 5.243L11.911 14.855C11.9233 15.0253 11.9976 15.1852 12.1199 15.3043C12.2422 15.4235 12.404 15.4936 12.5745 15.5014C12.7451 15.5091 12.9126 15.454 13.0452 15.3465C13.1778 15.239 13.2664 15.0865 13.294 14.918L13.814 11.798C13.921 11.156 14.2523 10.5727 14.7488 10.1519C15.2454 9.73115 15.8751 9.50014 16.526 9.5H16.582C16.7809 9.5 16.9717 9.57902 17.1124 9.71967C17.253 9.86032 17.332 10.0511 17.332 10.25C17.332 10.4489 17.253 10.6397 17.1124 10.7803C16.9717 10.921 16.7809 11 16.582 11H16.526C16.2302 11 15.944 11.1049 15.7182 11.296C15.4924 11.4872 15.3418 11.7522 15.293 12.044L14.773 15.164C14.6847 15.6939 14.4052 16.173 13.9876 16.5109C13.5699 16.8487 13.0429 17.0218 12.5062 16.9974C11.9696 16.973 11.4605 16.7529 11.0751 16.3785C10.6898 16.0042 10.455 15.5017 10.415 14.966L9.69903 5.354C9.68163 5.12187 9.57716 4.90489 9.40656 4.74653C9.23595 4.58816 9.01181 4.50011 8.77903 4.5Z"
      fill={color}
    />
  </svg>
);

export const searchIcon = (color) => (
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.33203 12C6.12303 12 4.33203 10.209 4.33203 8C4.33203 5.791 6.12303 4 8.33203 4C10.541 4 12.332 5.791 12.332 8C12.332 10.209 10.541 12 8.33203 12ZM18.039 16.293L13.219 11.473C13.917 10.492 14.332 9.296 14.332 8C14.332 4.687 11.645 2 8.33203 2C5.01903 2 2.33203 4.687 2.33203 8C2.33203 11.313 5.01903 14 8.33203 14C9.62803 14 10.824 13.585 11.805 12.887L16.625 17.707C16.82 17.902 17.076 18 17.332 18C17.588 18 17.844 17.902 18.039 17.707C18.43 17.316 18.43 16.684 18.039 16.293Z"
      fill={color}
    />
  </svg>
);

export const magicIcon = (color) => (
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.77071 15.5617C3.48981 15.2804 3.33203 14.8992 3.33203 14.5017C3.33203 14.1042 3.48981 13.7229 3.77071 13.4417L10.5627 6.64868L13.6837 9.76868L6.89171 16.5627C6.75241 16.7021 6.58701 16.8126 6.40496 16.8881C6.22291 16.9635 6.02778 17.0024 5.83071 17.0024C5.63365 17.0024 5.43851 16.9635 5.25646 16.8881C5.07441 16.8126 4.90901 16.7021 4.76971 16.5627L3.76971 15.5627L3.77071 15.5617ZM15.0987 8.35568L16.8907 6.56168C17.0301 6.42238 17.1407 6.25698 17.2161 6.07493C17.2916 5.89288 17.3304 5.69774 17.3304 5.50068C17.3304 5.30362 17.2916 5.10848 17.2161 4.92643C17.1407 4.74438 17.0301 4.57898 16.8907 4.43968L15.8907 3.43968C15.7514 3.30029 15.586 3.18971 15.404 3.11427C15.2219 3.03883 15.0268 3 14.8297 3C14.6326 3 14.4375 3.03883 14.2555 3.11427C14.0734 3.18971 13.908 3.30029 13.7687 3.43968L11.9757 5.23268L15.0977 8.35468L15.0987 8.35568ZM13.3307 12.0017C14.3307 12.0017 15.3307 11.0017 15.3307 10.0017C15.3307 11.0017 16.3307 12.0017 17.3307 12.0017C16.3307 12.0017 15.3307 13.0017 15.3307 14.0017C15.3307 13.0017 14.3307 12.0017 13.3307 12.0017ZM6.33071 5.00168C7.33071 5.00168 8.33071 4.00168 8.33071 3.00168C8.33071 4.00168 9.33071 5.00168 10.3307 5.00168C9.33071 5.00168 8.33071 6.00168 8.33071 7.00168C8.33071 6.00168 7.33071 5.00168 6.33071 5.00168Z"
      fill={color}
    />
  </svg>
);
export const globeIcon = (color) => (
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12.679 15.521C13.378 14.361 14.07 12.809 14.268 11H16.242C15.898 13.038 14.529 14.732 12.679 15.521ZM4.42203 11H6.39703C6.59503 12.809 7.28603 14.361 7.98603 15.521C6.13603 14.732 4.76703 13.038 4.42203 11ZM7.98603 4.479C7.28603 5.639 6.59503 7.191 6.39703 9H4.42203C4.76703 6.962 6.13603 5.268 7.98603 4.479ZM8.40903 9C8.67803 7.084 9.64803 5.48 10.334 4.557C11.021 5.477 11.989 7.076 12.256 9H8.40903ZM10.33 15.443C9.64403 14.524 8.67603 12.924 8.40803 11H12.255C11.986 12.916 11.016 14.521 10.33 15.443ZM16.242 9H14.268C14.07 7.191 13.378 5.639 12.679 4.479C14.529 5.268 15.898 6.962 16.242 9ZM10.332 2C5.92103 2 2.33203 5.589 2.33203 10C2.33203 14.411 5.92103 18 10.332 18C14.743 18 18.332 14.411 18.332 10C18.332 5.589 14.743 2 10.332 2Z"
      fill={color}
    />
  </svg>
);
export const lineIcon = (color) => (
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5.33203 13C5.33203 12.4477 5.77975 12 6.33203 12C6.88432 12 7.33203 12.4477 7.33203 13V18C7.33203 18.5523 6.88432 19 6.33203 19C5.77975 19 5.33203 18.5523 5.33203 18V13Z"
      fill={color}
    />
    <path
      d="M13.332 8C13.332 7.44772 13.7797 7 14.332 7C14.8843 7 15.332 7.44772 15.332 8V18C15.332 18.5523 14.8843 19 14.332 19C13.7797 19 13.332 18.5523 13.332 18V8Z"
      fill={color}
    />
    <path
      d="M17.332 6C17.332 5.44772 17.7797 5 18.332 5C18.8843 5 19.332 5.44772 19.332 6V18C19.332 18.5523 18.8843 19 18.332 19C17.7797 19 17.332 18.5523 17.332 18V6Z"
      fill={color}
    />
    <path
      d="M1.33203 10C1.33203 9.44772 1.77975 9 2.33203 9C2.88432 9 3.33203 9.44772 3.33203 10V18C3.33203 18.5523 2.88432 19 2.33203 19C1.77975 19 1.33203 18.5523 1.33203 18V10Z"
      fill={color}
    />
    <path
      d="M9.33203 11C9.33203 10.4477 9.77975 10 10.332 10C10.8843 10 11.332 10.4477 11.332 11V18C11.332 18.5523 10.8843 19 10.332 19C9.77975 19 9.33203 18.5523 9.33203 18V11Z"
      fill={color}
    />
  </svg>
);

export const checkIcon = () => (
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0.667969 10C0.667969 15.514 5.15397 20 10.668 20C16.182 20 20.668 15.514 20.668 10C20.668 4.486 16.182 0 10.668 0C5.15397 0 0.667969 4.486 0.667969 10ZM15.8751 8.20711C16.2656 7.81658 16.2656 7.18342 15.8751 6.79289C15.4846 6.40237 14.8514 6.40237 14.4609 6.79289L9.66797 11.5858L7.37508 9.29289C6.98455 8.90237 6.35139 8.90237 5.96086 9.29289C5.57034 9.68342 5.57034 10.3166 5.96086 10.7071L8.96086 13.7071C9.35139 14.0976 9.98455 14.0976 10.3751 13.7071L15.8751 8.20711Z"
      fill="var(--p-color-bg-fill-inverse)"
    />
  </svg>
);

export const dotIcon = (color) => (
  <svg
    width="7"
    height="7"
    viewBox="0 0 7 7"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle
      cx="3.5"
      cy="3.39551"
      r="3"
      fill={color}
    />
  </svg>
);

export const CakeIcon = () => (
  <svg
    width="192"
    height="85"
    viewBox="0 0 192 85"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M55.5605 82.6371C55.5605 81.6661 56.3477 80.8789 57.3188 80.8789H89.3188V84.3954H57.3188C56.3477 84.3954 55.5605 83.6082 55.5605 82.6371Z"
      fill="#9FA5AB"
    />
    <path
      d="M136.439 82.6373C136.439 83.6083 135.652 84.3955 134.681 84.3955L89.3186 84.3955L89.3186 80.879L134.681 80.879C135.652 80.879 136.439 81.6662 136.439 82.6373Z"
      fill="#B6BABF"
    />
    <path
      d="M62.5938 40.6816C62.5938 39.5771 63.4892 38.6816 64.5938 38.6816H89.319V80.8794H62.5938V40.6816Z"
      fill="#7FBEC6"
    />
    <path
      d="M75.9564 59.0772C67.2887 59.3585 63.4364 56.1468 62.5938 54.8574V80.8794H75.9564V59.0772Z"
      fill="#399C97"
    />
    <path
      d="M75.9557 59.0772C84.6234 59.3585 88.4757 56.1468 89.3184 54.8574V80.8794H75.9557V59.0772Z"
      fill="#5AB2AC"
    />
    <path
      d="M89.3184 38.6816H130.219C131.324 38.6816 132.219 39.5771 132.219 40.6816V55.5608H89.3184V38.6816Z"
      fill="#FCCD86"
    />
    <path
      d="M89.3184 55.5605H132.219V66.8133H89.3184V55.5605Z"
      fill="#E9AF58"
    />
    <path
      d="M89.3184 66.8135H132.219V80.8794H89.3184V66.8135Z"
      fill="#FCCD86"
    />
    <path
      d="M89.3184 16.7695C89.3184 15.665 90.2138 14.7695 91.3184 14.7695H94.3513C95.4559 14.7695 96.3513 15.665 96.3513 16.7695V38.6816H89.3184V16.7695Z"
      fill="#7FBEC6"
    />
    <path
      d="M89.3184 23.9116L96.3513 21.8018V31.6479L89.3184 33.7578V23.9116Z"
      fill="#5BA7B1"
    />
    <path
      d="M96.3513 9.37729C96.3513 11.1899 94.7769 12.6593 92.8348 12.6593C90.8927 12.6593 89.3184 11.1899 89.3184 9.37729C89.3184 7.56466 90.8927 0 92.8348 0C94.7769 0 96.3513 7.56466 96.3513 9.37729Z"
      fill="#F7D097"
    />
    <path
      d="M95.6488 9.14298C95.6488 10.6967 94.3893 11.9562 92.8356 11.9562C91.282 11.9562 90.0225 10.6967 90.0225 9.14298C90.0225 7.58929 91.282 3.5166 92.8356 3.5166C94.3893 3.5166 95.6488 7.58929 95.6488 9.14298Z"
      fill="#E9AF58"
    />
    <line
      x1="13.9092"
      y1="14.2227"
      x2="20.552"
      y2="14.2227"
      stroke="#D2D5D9"
      strokeWidth="2.5"
      strokeLinecap="round"
    />
    <line
      x1="36.0304"
      y1="35.3817"
      x2="40.6293"
      y2="35.3817"
      stroke="#D2D5D9"
      strokeWidth="1.73077"
      strokeLinecap="round"
    />
    <line
      x1="153.162"
      y1="45.8711"
      x2="159.805"
      y2="45.8711"
      stroke="#D2D5D9"
      strokeWidth="2.5"
      strokeLinecap="round"
    />
    <circle
      cx="162.813"
      cy="19.3406"
      r="2.16484"
      stroke="#D2D5D9"
      strokeWidth="2"
    />
    <circle
      cx="3.16484"
      cy="46.7693"
      r="2.16484"
      stroke="#D2D5D9"
      strokeWidth="2"
    />
    <circle
      cx="189.187"
      cy="35.1647"
      r="1.81319"
      stroke="#D2D5D9"
      strokeWidth="2"
    />
    <line
      x1="17.4258"
      y1="11.0957"
      x2="17.4258"
      y2="17.7386"
      stroke="#D2D5D9"
      strokeWidth="2.5"
      strokeLinecap="round"
    />
    <line
      x1="38.464"
      y1="33.2169"
      x2="38.464"
      y2="37.8158"
      stroke="#D2D5D9"
      strokeWidth="1.73077"
      strokeLinecap="round"
    />
    <line
      x1="156.679"
      y1="42.7441"
      x2="156.679"
      y2="49.387"
      stroke="#D2D5D9"
      strokeWidth="2.5"
      strokeLinecap="round"
    />
  </svg>
);

export const meterIconDanger = (color) => (
  <svg
    width="60"
    height="60"
    viewBox="0 0 60 60"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      width="60"
      height="60"
      rx="8"
      fill="#FEEFEC"
    />
    <g clip-path="url(#clip0_2703_40549)">
      <path
        d="M40.4426 30.108C40.113 27.5586 38.782 25.2258 36.7208 23.5849C34.6597 21.944 32.0235 21.1186 29.3498 21.277C26.676 21.4354 24.1658 22.5656 22.3309 24.4373C20.496 26.309 19.4745 28.7812 19.4746 31.3501C19.47 33.4091 20.1274 35.4195 21.3573 37.1074C21.7406 37.6211 22.2474 38.0387 22.8349 38.3245C23.4223 38.6104 24.073 38.7561 24.7317 38.7493H35.2575C35.944 38.7497 36.6201 38.588 37.2261 38.2785C37.8321 37.9691 38.3493 37.5213 38.7321 36.9748C40.1424 34.9566 40.7492 32.5196 40.4426 30.108ZM37.5684 36.2228C37.3139 36.5864 36.9699 36.8844 36.5669 37.0903C36.1638 37.2963 35.7142 37.4041 35.2575 37.404H24.7317C24.299 37.4108 23.871 37.3174 23.4839 37.1318C23.0968 36.9462 22.7621 36.6739 22.5081 36.3378C21.2364 34.5835 20.6788 32.4432 20.9406 30.3214C21.2023 28.1997 22.2654 26.2433 23.9289 24.8219C25.591 23.3886 27.7493 22.5987 29.9846 22.6055C30.3495 22.6055 30.7142 22.6255 31.0768 22.6654C33.0985 22.9007 34.9805 23.7762 36.4246 25.1533C37.8687 26.5303 38.7924 28.3301 39.0492 30.2674C39.3274 32.3592 38.8009 34.4765 37.5684 36.2228Z"
        fill={color}
        stroke={color}
      />
      <path
        d="M26.1681 32.3174L28.449 31.2822C28.3892 30.6988 28.5345 30.1081 28.8582 29.6182C29.1819 29.1283 29.6624 28.772 30.2119 28.6143C30.7614 28.4567 31.3431 28.5082 31.8507 28.7596C32.3583 29.011 32.7578 29.4454 32.9762 29.9833C33.1946 30.5212 33.2172 31.1268 33.04 31.6892C32.8628 32.2517 32.4976 32.7334 32.0111 33.0464C31.5247 33.3593 30.9495 33.4825 30.3908 33.3935C29.832 33.3045 29.3271 33.0091 28.9683 32.5615L26.6874 33.5967C26.6053 33.6365 26.5166 33.659 26.4266 33.663C26.3366 33.6669 26.2472 33.6521 26.1635 33.6195C26.0799 33.5869 26.0037 33.5372 25.9395 33.4732C25.8753 33.4092 25.8244 33.3322 25.7897 33.2469C25.7551 33.1615 25.7374 33.0694 25.7377 32.9761C25.738 32.8828 25.7564 32.7901 25.7916 32.7035C25.8269 32.6169 25.8784 32.5381 25.943 32.4718C26.0077 32.4055 26.0842 32.353 26.1681 32.3174ZM31.1761 31.9371C31.3608 31.8533 31.5187 31.7151 31.6296 31.5399C31.7404 31.3647 31.7994 31.1604 31.799 30.9529C31.7987 30.7454 31.7389 30.544 31.6274 30.3741C31.5158 30.2042 31.3575 30.0735 31.1724 29.9985C30.9873 29.9235 30.7838 29.9076 30.5876 29.9528C30.3913 29.998 30.2112 30.1022 30.07 30.2524C29.9288 30.4025 29.8328 30.5918 29.7942 30.7962C29.7556 31.0007 29.7761 31.2112 29.8531 31.401C29.9568 31.6551 30.1542 31.8542 30.4021 31.9546C30.65 32.0551 30.9283 32.0488 31.1761 31.9371Z"
        fill={color}
        stroke={color}
      />
    </g>
    <defs>
      <clipPath id="clip0_2703_40549">
        <rect
          width="24"
          height="20"
          fill="white"
          transform="translate(18 20)"
        />
      </clipPath>
    </defs>
  </svg>
);

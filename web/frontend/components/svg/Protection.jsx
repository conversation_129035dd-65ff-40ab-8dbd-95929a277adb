/**
 * @typedef {Object} ProtectionProps
 * @property {string} [width="43.946"]
 * @property {string} [height="50"]
 */

/**
 * @param {ProtectionProps} props
 */
const Protection = ({ width = "43.946", height = "50" }) => (
  <svg
    id="protection"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    width={width}
    height={height}
    viewBox="0 0 43.946 50"
  >
    <defs>
      <linearGradient
        id="protection-gradient"
        x1="0.5"
        y1="1"
        x2="0.5"
        gradientUnits="objectBoundingBox"
      >
        <stop
          offset="0"
          stopColor="#c3ffe8"
        />
        <stop
          offset="0.997"
          stopColor="#f0fff4"
        />
      </linearGradient>
      <linearGradient
        id="protection-gradient-2"
        x1="0.5"
        y1="1"
        x2="0.5"
        gradientUnits="objectBoundingBox"
      >
        <stop
          offset="0"
          stopColor="#00b59c"
        />
        <stop
          offset="1"
          stopColor="#9cffac"
        />
      </linearGradient>
    </defs>
    <path
      id="Path_5609"
      data-name="Path 5609"
      d="M96.141,91.185a3.383,3.383,0,0,1-1.214-.224A38.367,38.367,0,0,1,83.758,84.4C78.61,79.893,76,74.509,76,68.394V53.155a3.312,3.312,0,0,1,1.672-2.863,10.116,10.116,0,0,0,3.692-3.642A3.375,3.375,0,0,1,84.267,45H108a3.375,3.375,0,0,1,2.9,1.649,10.116,10.116,0,0,0,3.692,3.642,3.313,3.313,0,0,1,1.672,2.863V68.394c0,6.129-2.6,11.516-7.734,16.012a38.12,38.12,0,0,1-11.191,6.559A3.383,3.383,0,0,1,96.141,91.185Z"
      transform="translate(-74.163 -43.163)"
      fill="url(#protection-gradient)"
    />
    <g
      id="Group_465"
      data-name="Group 465"
    >
      <g
        id="Group_464"
        data-name="Group 464"
      >
        <path
          id="Path_5610"
          data-name="Path 5610"
          d="M73.481,5.859a4.4,4.4,0,0,1-4.395-4.395A1.465,1.465,0,0,0,67.621,0h-29.3a1.465,1.465,0,0,0-1.465,1.465,4.4,4.4,0,0,1-4.395,4.395A1.465,1.465,0,0,0,31,7.324V25.1a21.754,21.754,0,0,0,3.433,11.864A27.913,27.913,0,0,0,41.867,44.6c4.711,3.365,10.3,5.4,11.105,5.4.761,0,6.326-1.986,11.105-5.4a27.912,27.912,0,0,0,7.434-7.638A21.755,21.755,0,0,0,74.946,25.1V7.324A1.465,1.465,0,0,0,73.481,5.859ZM69.086,25.1c0,4.987-2.106,9.386-6.259,13.075A31.673,31.673,0,0,1,53.5,43.705a1.465,1.465,0,0,1-1.056,0,31.885,31.885,0,0,1-9.308-5.538c-4.167-3.7-6.28-8.094-6.28-13.068V11.611a1.465,1.465,0,0,1,.73-1.267,10.358,10.358,0,0,0,3.755-3.755,1.465,1.465,0,0,1,1.267-.73H63.334a1.465,1.465,0,0,1,1.267.73,10.358,10.358,0,0,0,3.755,3.755,1.465,1.465,0,0,1,.73,1.267ZM52.973,13.379A10.254,10.254,0,1,0,63.227,23.633,10.266,10.266,0,0,0,52.973,13.379Zm4.258,9.092-4.395,4.395a1.465,1.465,0,0,1-2.072,0l-2.2-2.2A1.465,1.465,0,1,1,50.639,22.6L51.8,23.758,55.16,20.4a1.465,1.465,0,1,1,2.071,2.072Z"
          transform="translate(-31)"
          fill="url(#protection-gradient-2)"
        />
      </g>
    </g>
  </svg>
);

export default Protection;

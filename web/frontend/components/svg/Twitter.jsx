/**
 * @typedef {Object} TwitterProps
 * @property {string} [width="19.991"]
 * @property {string} [height="19.991"]
 */

/**
 * @param {TwitterProps} props
 */
const Twitter = ({ width = "19.991", height = "19.991" }) => (
  <svg
    id="twitter"
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 19.991 19.991"
  >
    <path
      id="Path_5617"
      data-name="Path 5617"
      d="M19.991,10A10,10,0,1,1,10,0,10,10,0,0,1,19.991,10Zm0,0"
      fill="#4a7aff"
    />
    <path
      id="Path_5618"
      data-name="Path 5618"
      d="M19.991,10A10,10,0,1,1,10,0,10,10,0,0,1,19.991,10Zm0,0"
      fill="#1da1f2"
    />
    <path
      id="Path_5619"
      data-name="Path 5619"
      d="M100.962,126.568a10.4,10.4,0,0,0-.165-1.847l-3.6-3.6-5.465,5.638-6.792.048,2.337,2.337-1.072.013,1.627,1.627-.694.716-3.465-.564,5.909,5.909a10.462,10.462,0,0,0,1.046.052,10.331,10.331,0,0,0,10.331-10.331Zm0,0"
      transform="translate(-80.971 -116.909)"
      fill="#1591dc"
    />
    <path
      id="Path_5621"
      data-name="Path 5621"
      d="M262.613,117.209a5.991,5.991,0,0,1-1.639.421,2.725,2.725,0,0,0,1.255-1.481,5.95,5.95,0,0,1-1.812.65,2.942,2.942,0,0,0-2.083-.846,2.849,2.849,0,0,0-2.69,1.783V126.2a7.714,7.714,0,0,0,5.554-7.26c0-.116,0-.231-.008-.346a5.627,5.627,0,0,0,1.424-1.387Zm0,0"
      transform="translate(-245.833 -111.273)"
      fill="#dce1eb"
    />
    <path
      id="Path_5620"
      data-name="Path 5620"
      d="M87.843,126.585a7.627,7.627,0,0,0,7.727-7.637c0-.116,0-.232-.008-.347a5.5,5.5,0,0,0,1.355-1.39,5.466,5.466,0,0,1-1.56.422,2.7,2.7,0,0,0,1.194-1.485,5.485,5.485,0,0,1-1.724.652,2.729,2.729,0,0,0-1.982-.848,2.7,2.7,0,0,0-2.716,2.684,2.655,2.655,0,0,0,.07.612,7.742,7.742,0,0,1-5.6-2.8,2.666,2.666,0,0,0,.841,3.583,2.719,2.719,0,0,1-1.23-.336c0,.011,0,.023,0,.034a2.693,2.693,0,0,0,2.179,2.631,2.744,2.744,0,0,1-1.226.046,2.715,2.715,0,0,0,2.537,1.864,5.491,5.491,0,0,1-3.373,1.149,5.564,5.564,0,0,1-.648-.037,7.754,7.754,0,0,0,4.163,1.206"
      transform="translate(-80.303 -111.273)"
      fill="#fff"
    />
  </svg>
);

export default Twitter;

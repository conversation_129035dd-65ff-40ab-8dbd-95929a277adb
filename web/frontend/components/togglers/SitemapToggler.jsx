import React from "react";
import { useTranslation } from "react-i18next";
import { useAppQuery, useProductApi } from "../../hooks";
import queryKeys from "../../utility/queryKeys";
import { useMutation, useQueryClient } from "react-query";
import { sSEOConfirm } from "../../utility/helpers";
import { useSearchParams } from "react-router-dom";
import { useSelector } from "react-redux";

const SitemapToggler = ({ productId, showLabel = true, value = null, onUpdate = () => {} }) => {
  const productApi = useProductApi();
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const user = useSelector((state) => state.user);

  const queryKey = [queryKeys.SITEMAP_STATUS, { id: productId }];

  const { data } = useAppQuery({
    queryKey,
    queryFn: () => null,
    reactQueryOptions: {
      enabled: value === null,
    },
  });

  const status = data ?? value;

  const { mutate } = useMutation({
    mutationFn: ({ status }) => {
      const query = Object.fromEntries(searchParams.entries());
      return productApi.toggleSitemapStatus(productId, status, query);
    },
    onMutate: () => {
      queryClient.setQueryData(queryKey, !status);
    },
    onSuccess: ({ sitemaps, pagination }) => {
      onUpdate(sitemaps, pagination);
    },
    onError: () => queryClient.setQueryData(queryKey, !status),
  });

  const handleChange = () => {
    if (!user?.isSubscribed) {
      return;
    }

    const popUpMessage = `Are you sure you want to ${status ? "disable" : "enable"} sitemap for this product?`;
    const confirmBtnText = status ? "Disable Sitemap" : "Enable Sitemap";
    const confirmBtnClassname = status ? "button__danger" : "button__primary";

    sSEOConfirm({
      msg: popUpMessage,
      button: {
        confirm: { text: confirmBtnText, class: confirmBtnClassname },
      },
      callback: () => mutate({ status }),
    });
  };

  return (
    <label className="custom__switch">
      <input
        type="checkbox"
        checked={status}
        onChange={handleChange}
      />
      <span className="switch"></span>
      {showLabel && <span className="text">{t("Sitemap")}</span>}
    </label>
  );
};

export default SitemapToggler;

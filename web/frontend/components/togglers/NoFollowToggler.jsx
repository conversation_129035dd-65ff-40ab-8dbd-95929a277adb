import Modal from "@/modules/components/Modal";
import { Box, Text } from "@shopify/polaris";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import ToggleCard from "../common/ToggleCard";

const NoFollowToggler = ({ status, loading, onToggle = () => {} }) => {
  const { t } = useTranslation();

  const [showModal, setShowModal] = useState(false);

  const modalText = t(`Are you sure about ${status ? "disabling" : "enabling"} 'Nofollow' attribute?`);
  const modalActionText = `${status ? "Disable" : "Enable"}`;
  const isDestructiveAction = !status;

  const handleAction = () => {
    onToggle();
    setShowModal(false);
  };

  const handleBtnClick = () => setShowModal(true);

  return (
    <>
      <ToggleCard
        label="Nofollow"
        content="Prevent passing PageRank to this link"
        status={status}
        onButtonClick={handleBtnClick}
        loading={loading}
      />

      <Modal
        type="app-bridge"
        open={showModal}
        setOpen={setShowModal}
        variant="small"
      >
        <Modal.Section>
          <Box padding="400">
            <Text>{t(modalText)}</Text>
          </Box>

          <Modal.TitleBar title={t("Confirm!")}>
            <button
              variant="primary"
              tone={isDestructiveAction ? "critical" : undefined}
              onClick={handleAction}
            >
              {t(modalActionText)}
            </button>
            <button onClick={() => setShowModal(false)}>{t("Cancel")}</button>
          </Modal.TitleBar>
        </Modal.Section>
      </Modal>
    </>
  );
};

export default NoFollowToggler;

import { useTranslation } from "react-i18next";
import { BlockSta<PERSON>, <PERSON><PERSON>, Card, InlineStack, Text } from "@shopify/polaris";
import React from "react";
import { useAppEmbed } from "../../hooks/useAppEmbed";

export default function AppEmbedBlockToggler() {
  const { t } = useTranslation();

  const { appEmbedUrl } = useAppEmbed();

  return (
    <>
      <Card>
        <BlockStack gap="400">
          <Text
            variant="headingMd"
            as="h5"
          >
            {t("Enable app embed block")}
          </Text>
          <Text
            as="p"
            tone="subdued"
          >
            {t(
              "App embed block helps StoreSEO to output relevant SEO data to each page of your online store! Enable it to have correct SEO meta to your website."
            )}
          </Text>
          <Text
            as="p"
            tone="subdued"
          >
            {t("Note: please click 'Save' after opening the theme editor.")}
          </Text>
          <InlineStack gap="400">
            <Button
              // variant="primary"
              url={appEmbedUrl}
              target="_blank"
            >
              {t("Enable app embed")}
            </Button>
          </InlineStack>
        </BlockStack>
      </Card>
    </>
  );
}

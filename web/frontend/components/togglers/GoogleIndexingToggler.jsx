import { Trans, useTranslation } from "react-i18next";
import { useAppQuery } from "../../hooks";
import queryKeys from "../../utility/queryKeys";
import { useSeoApi } from "../../hooks/apiHooks/useSeoApi";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useMutation, useQueryClient } from "react-query";
import { Button, Checkbox, Link, Text } from "@shopify/polaris";
import ConfirmationModal from "../modals/ConfirmationModal.jsx";
import InlineProHint from "../common/InlineProHint.jsx";
import { useBanner } from "../../hooks/useBanner.jsx";

export default function GoogleIndexingToggler({ isProUser = false }) {
  const { t } = useTranslation();
  const seoApi = useSeoApi();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const [showConfirmation, setShowConfirmation] = useState(false);

  const [googleIndexingEnabled, setGoogleIndexingEnabled] = useState(false);
  const [userHasAuthenticatedForIndexing, setUserHasAuthenticatedForIndexing] = useState(false);

  const { data } = useAppQuery({
    queryKey: queryKeys.GOOGLE_INDEX_STATUS,
    queryFn: seoApi.getGoogleIndexingStatus,
  });

  useEffect(() => {
    if (data) {
      setGoogleIndexingEnabled(data.isEnabled);
      setUserHasAuthenticatedForIndexing(data.hasIndexingPermission);
    }
  }, [data]);

  const { mutate: updateGoogleIndexingStatus, isLoading } = useMutation({
    mutationFn: () => seoApi.updateGoogleIndexingStatus(!googleIndexingEnabled),
    onMutate: () => {
      setGoogleIndexingEnabled(!googleIndexingEnabled);
    },
    onSuccess: (status) => {
      queryClient.setQueryData(queryKeys.GOOGLE_INDEX_STATUS, {
        ...data,
        isEnabled: status,
      });
      setShowConfirmation(false);
    },
    onError: () => setGoogleIndexingEnabled(!googleIndexingEnabled),
  });

  const { mutate: reSubmitGoogleIndexing, isLoading: isResubmitting } = useMutation({
    mutationFn: () => seoApi.updateGoogleIndexingStatus(true),
  });

  const { Banner, showBanner } = useBanner({
    title: (
      <Trans
        t={t}
        i18nKey="bannerForGoogleIntegration"
      >
        Your haven't set up Google integration yet. Please{" "}
        <Link onClick={() => navigate("/settings/google-integration")}>setup Google integration</Link> first then try
        again.
      </Trans>
    ),
    noMargin: true,
  });

  const checkIndexingPermission = () => {
    if (!userHasAuthenticatedForIndexing) {
      showBanner(true);
      return;
    }
    return true;
  };

  const handleResubmit = () => {
    if (checkIndexingPermission()) {
      reSubmitGoogleIndexing();
    }
  };

  const handleChange = () => {
    if (!checkIndexingPermission()) return;

    setShowConfirmation(true);
  };

  const reSubmitButton = googleIndexingEnabled && isProUser && (
    <Button
      onClick={handleResubmit}
      size={"slim"}
      loading={isResubmitting}
    >
      {t("Resubmit")}
    </Button>
  );

  const helpTextMarkup = (
    <>
      <Text as="p">{t("Turn on Google Indexing to auto index your products to Google")}</Text>
      {reSubmitButton}
      {!isProUser && <InlineProHint />}
    </>
  );

  return (
    <>
      <Banner />
      <Checkbox
        label={t("Google Indexing")}
        helpText={helpTextMarkup}
        checked={googleIndexingEnabled}
        onChange={handleChange}
        disabled={!isProUser}
      />
      <ConfirmationModal
        show={showConfirmation}
        content={`Are you sure you want to ${googleIndexingEnabled ? "disable" : "enable"} Google Indexing?`}
        primaryActionIsDestructive={googleIndexingEnabled}
        primaryActionText={googleIndexingEnabled ? "Disable" : "Enable"}
        primaryAction={updateGoogleIndexingStatus}
        onClose={setShowConfirmation}
        loading={isLoading}
      />
    </>
  );
}

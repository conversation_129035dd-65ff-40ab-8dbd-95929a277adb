import { useTranslation } from "react-i18next";
import { useSeo<PERSON><PERSON> } from "../../hooks/apiHooks/useSeoApi";
import { useAppQuery } from "../../hooks";
import queryKeys from "../../utility/queryKeys";
import React, { useEffect, useState } from "react";
import { useMutation, useQueryClient } from "react-query";
import { Checkbox, Text } from "@shopify/polaris";
import ConfirmationModal from "../modals/ConfirmationModal.jsx";
import { useNavigate } from "react-router-dom";
import InlineProHint from "../common/InlineProHint.jsx";

export default function JsonLDToggler({ isProUser = false }) {
  const { t } = useTranslation();
  const seoApi = useSeoApi();
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  const [status, setStatus] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  const { data: jsonldEnabled = false } = useAppQuery({
    queryKey: queryKeys.JSONLD_STATUS,
    queryFn: seoApi.getJSONLDStatus,
  });

  const { mutate: updateStatus, isLoading } = useMutation({
    mutationFn: seoApi.toggleJSONLDStatus,
    onSuccess: (status) => {
      queryClient.setQueryData(queryKeys.JSONLD_STATUS, status);
      setShowConfirmation(false);
    },
  });

  useEffect(() => {
    setStatus(jsonldEnabled || false);
  }, [jsonldEnabled]);

  const handleChange = () => {
    if (!isProUser) {
      return;
    }

    setShowConfirmation(true);
  };

  const helpTextMarkup = (
    <>
      <Text as="p">{t("Turn on JSON-LD to get rich results on web search")}</Text>
      {!isProUser && <InlineProHint />}
    </>
  );

  return (
    <>
      <Checkbox
        label={t("JSON-LD")}
        checked={status}
        onChange={handleChange}
        disabled={!isProUser}
        helpText={helpTextMarkup}
      />

      <ConfirmationModal
        show={showConfirmation}
        content={`Are you sure you want to ${status ? "disable" : "enable"} JSON-LD for SEO?`}
        primaryActionIsDestructive={status}
        primaryActionText={status ? "Disable JSON-LD" : "Enable JSON-LD"}
        primaryAction={updateStatus}
        onClose={setShowConfirmation}
        loading={isLoading}
      />
    </>
  );
}

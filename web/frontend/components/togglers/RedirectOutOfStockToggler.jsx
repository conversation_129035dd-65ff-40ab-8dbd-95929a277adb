import { Block<PERSON>tack, But<PERSON>, Checkbox, Text, TextField } from "@shopify/polaris";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation, useQueryClient } from "react-query";
import { useAppQuery } from "../../hooks";
import { useSeoApi } from "../../hooks/apiHooks/useSeoApi";
import useConfirmation from "../../hooks/useConfirmation.jsx";
import queryKeys from "../../utility/queryKeys";
import InlineProHint from "../common/InlineProHint.jsx";

export default function RedirectOutOfStockToggler({ isProUser = false }) {
  const { t } = useTranslation();
  const seoApi = useSeoApi();
  const queryClient = useQueryClient();
  const { renderConfirmation, showConfirmation, hideConfirmation } = useConfirmation();

  const [status, setStatus] = useState(false);
  const [redirectURL, setRedirectURL] = useState(null);

  const { data } = useAppQuery({
    queryKey: queryKeys.REDIRECT_OUT_OF_STOCK_INFO,
    queryFn: seoApi.getRedirectOutOfStockInfo,
  });

  const { mutate: updateRedirectInfo, isLoading } = useMutation({
    mutationFn: ({ status, redirectURL = data.redirectURL }) =>
      seoApi.updateRedirectOutOfStockInfo({
        status,
        redirectURL,
      }),
    onSuccess: (data) => {
      queryClient.setQueryData(queryKeys.REDIRECT_OUT_OF_STOCK_INFO, data);
      hideConfirmation();
    },
  });

  useEffect(() => {
    if (data) {
      // console.log("data:? ", data);
      setStatus(data?.status);
      setRedirectURL(data?.redirectURL);
    }
  }, [data]);

  const handleChange = useCallback((newChecked) => {
    if (newChecked === false) {
      showConfirmation();
    } else {
      setStatus(newChecked);
    }
  }, []);

  const redirectURLMarkup = status && (
    <TextField
      label={t("Redirect URL")}
      labelHidden
      value={redirectURL}
      onChange={setRedirectURL}
      placeholder={t("Enter the redirect URL")}
      autoComplete="off"
      connectedRight={
        <Button
          onClick={() => updateRedirectInfo({ status, redirectURL })}
          loading={isLoading}
        >
          {t("Update")}
        </Button>
      }
    />
  );

  const helpTextMarkup = (
    <BlockStack gap="200">
      <Text as="p">{t("Turn this on to redirect users to another page when a product is out of stock")}</Text>
      {redirectURLMarkup}
      {!isProUser && <InlineProHint />}
    </BlockStack>
  );

  return (
    <>
      <Checkbox
        label={t("Redirect on out of stock")}
        checked={status}
        onChange={handleChange}
        helpText={helpTextMarkup}
        disabled={!isProUser}
      />
      {renderConfirmation({
        content: "Do you want to disable redirect on out of stock?",
        primaryActionText: "Disable",
        primaryActionIsDestructive: true,
        primaryAction: () => {
          setStatus(false);
          updateRedirectInfo({ status: false });
        },
        loading: isLoading,
      })}
    </>
  );
}

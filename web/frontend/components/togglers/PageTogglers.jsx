import analysisEntityTypes from "storeseo-enums/analysisEntityTypes";
import queryKeys from "../../utility/queryKeys";
import <PERSON>FollowToggler from "./NoFollowToggler";
import { usePageApi } from "../../hooks/apiHooks/usePageApi";
import NoIndexToggler from "./NoIndexToggler";

const NoFollow = ({ id, showLabel = true, value = null, onUpdate = () => {} }) => {
  const pageApi = usePageApi();
  const queryKey = [queryKeys.NO_FOLLOW_STATUS, { id, type: analysisEntityTypes.PAGE }];

  const props = { showLabel, value, onUpdate };
  return (
    <NoFollowToggler
      queryKey={queryKey}
      fetcher={() => false}
      updater={() => {
        return pageApi.toggleNoFollowStatus(id);
      }}
      {...props}
    />
  );
};

const NoIndex = ({ id, showLabel = true, value = null, onUpdate = () => {} }) => {
  const pageApi = usePageApi();
  const queryKey = [queryKeys.NO_INDEX_STATUS, { id, type: analysisEntityTypes.PAGE }];

  const props = { showLabel, value, onUpdate };
  return (
    <NoIndexToggler
      queryKey={queryKey}
      fetcher={() => false}
      updater={() => {
        return pageApi.toggleNoIndexStatus(id);
      }}
      {...props}
    />
  );
};

export default {
  NoFollow,
  NoIndex,
};

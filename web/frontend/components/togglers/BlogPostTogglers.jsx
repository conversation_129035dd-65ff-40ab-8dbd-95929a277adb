import analysisEntityTypes from "storeseo-enums/analysisEntityTypes";
import queryKeys from "../../utility/queryKeys";
import <PERSON><PERSON><PERSON>owToggler from "./NoFollowToggler";
import { useBlogApi } from "../../hooks/apiHooks/useBlogApi";
import NoIndexToggler from "./NoIndexToggler";

const NoFollow = ({ id, articleId, showLabel = true, value = null, onUpdate = () => {} }) => {
  const blogApi = useBlogApi();
  const queryKey = [queryKeys.NO_FOLLOW_STATUS, { id, articleId, type: analysisEntityTypes.ARTICLE }];

  const props = { showLabel, value, onUpdate };
  return (
    <NoFollowToggler
      queryKey={queryKey}
      fetcher={() => false}
      updater={() => {
        return blogApi.toggleNoFollowStatus(id, articleId);
      }}
      {...props}
    />
  );
};

const NoIndex = ({ id, articleId, showLabel = true, value = null, onUpdate = () => {} }) => {
  const blogApi = useBlogApi();
  const queryKey = [queryKeys.NO_INDEX_STATUS, { id, articleId, type: analysisEntityTypes.ARTICLE }];

  const props = { showLabel, value, onUpdate };
  return (
    <NoIndexToggler
      queryKey={queryKey}
      fetcher={() => false}
      updater={() => {
        return blogApi.toggleNoIndexStatus(id, articleId);
      }}
      {...props}
    />
  );
};

export default {
  NoFollow,
  NoIndex,
};

import analysisEntityTypes from "storeseo-enums/analysisEntityTypes";
import { useProductApi } from "../../hooks";
import queryKeys from "../../utility/queryKeys";
import NoFollowToggler from "./NoFollowToggler";
import { useSearchParams } from "react-router-dom";
import NoIndexToggler from "./NoIndexToggler";

const NoFollow = ({ id, showLabel = true, value = null, onUpdate = () => {} }) => {
  const productApi = useProductApi();
  const [searchParams] = useSearchParams();
  const queryKey = [queryKeys.NO_FOLLOW_STATUS, { id, type: analysisEntityTypes.PRODUCT }];

  const props = { showLabel, value, onUpdate };
  return (
    <NoFollowToggler
      queryKey={queryKey}
      // fetcher={() => productApi.getNoFollowStatus(id)}
      updater={() => {
        const queryString = searchParams.toString();
        return productApi.toggleNoFollowStatus(id, queryString);
      }}
      {...props}
    />
  );
};

const NoIndex = ({ id, showLabel = true, value = null, onUpdate = () => {} }) => {
  const productApi = useProductApi();
  const [searchParams] = useSearchParams();
  const queryKey = [queryKeys.NO_INDEX_STATUS, { id, type: analysisEntityTypes.PRODUCT }];

  const props = { showLabel, value, onUpdate };
  return (
    <NoIndexToggler
      queryKey={queryKey}
      // fetcher={() => productApi.getNoIndexStatus(id)}
      updater={() => {
        const queryString = searchParams.toString();
        return productApi.toggleNoIndexStatus(id, queryString);
      }}
      {...props}
    />
  );
};

export default {
  NoFollow,
  NoIndex,
};

import { Checkbox, TextField } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import MarkedText from "../common/MarkedText";

const URLHandleInput = ({
  handle = "",
  originalHandle = "",
  urlPrefix = "",
  error = "",
  onChange = (key, val) => {},
  createRedirectUrl = true,
  isURLChanged = false,
  disabled = false,
}) => {
  const { t } = useTranslation();

  return (
    <TextField
      label={t("URL handle")}
      value={handle}
      prefix={urlPrefix.replace(/\/?$/, "/")}
      placeholder={""}
      onChange={(val) => onChange("handle", val)}
      autoComplete="off"
      error={t(error)}
      disabled={disabled}
      helpText={
        isURLChanged && (
          <Checkbox
            label={
              <>
                {t("Create a URL redirect for")} <MarkedText text={originalHandle} />
                →
                <MarkedText text={handle} />
              </>
            }
            checked={createRedirectUrl}
            onChange={(val) => onChange("createRedirectUrl", val)}
            disabled={disabled}
          />
        )
      }
    />
  );
};

export default URLHandleInput;

import { useAppQuery, useShopApi } from "@/hooks";
import { useUnsavedChanges } from "@/hooks/useUnsavedChanges";
import ContextualSaveBar from "@/modules/components/ContextualSaveBar";
import queryKeys from "@/utility/queryKeys";
import { BlockStack, Box, Button, Card, InlineStack, Link, Text, TextField } from "@shopify/polaris";
import { SortIcon } from "@shopify/polaris-icons";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation, useQueryClient } from "react-query";

export default function AnalyticsPropertyIdInput({ isPremium }) {
  const { t } = useTranslation();
  const shopApi = useShopApi();
  const queryClient = useQueryClient();

  const [analyticsPropertyId, setAnalyticsPropertyId] = useState("");
  const [originalAnalyticsPropertyId, setOriginalAnalyticsPropertyId] = useState("");
  const [error, setError] = useState("");

  const { data, isFetching } = useAppQuery({
    queryKey: queryKeys.GOOGLE_INTEGRATION_INFO,
    queryFn: shopApi.getGoogleIntegrationInfo,
  });

  const { analyticsPropertyId: savedPropertyId } = data?.integrationInfo || {};

  const { mutate: updatePropertyId, isLoading: isUpdating } = useMutation({
    mutationFn: () => shopApi.updateGoogleIntegrationInfo({ analyticsPropertyId }),
    onSuccess: (updatedData) => {
      queryClient.setQueryData(queryKeys.GOOGLE_INTEGRATION_INFO, {
        ...(data || {}),
        integrationInfo: updatedData,
      });
      // Update original data after successful save
      setOriginalAnalyticsPropertyId(analyticsPropertyId);
    },
    onError: () => setError("Invalid analytics property id"),
  });

  const { mutateAsync: verifyPropertyId, isLoading: isVerifying } = useMutation({
    mutationFn: () => shopApi.verifyGoogleAnalyticsPropertyId(analyticsPropertyId),
  });

  useEffect(() => {
    if (savedPropertyId !== undefined) {
      setAnalyticsPropertyId(savedPropertyId || "");
      setOriginalAnalyticsPropertyId(savedPropertyId || "");
    }
  }, [savedPropertyId]);

  useEffect(() => setError(""), [analyticsPropertyId]);

  // Track unsaved changes
  const { hasUnsavedChanges, DiscardChangesModal, setShowDiscardChangeModal } = useUnsavedChanges({
    originalData: { analyticsPropertyId: originalAnalyticsPropertyId },
    currentData: { analyticsPropertyId },
    onDiscardAction: () => {
      setAnalyticsPropertyId(originalAnalyticsPropertyId);
      setError("");
    },
  });

  const handleUpdate = async () => {
    const isValid = await verifyPropertyId();
    if (isValid) updatePropertyId();
    else setError("Invalid analytics id");
  };

  return (
    <>
      <DiscardChangesModal />

      <ContextualSaveBar
        id="google-analytics-property-id"
        open={hasUnsavedChanges}
        isLoading={isUpdating || isVerifying}
        onSave={handleUpdate}
        onDiscard={() => setShowDiscardChangeModal(true)}
      />

      <Card>
        <Box paddingBlockEnd="200">
          <Text
            as="h4"
            fontWeight="bold"
          >
            {t("Google Analytics Property ID")}
          </Text>
        </Box>

        <Box color="text-subdued">
          <BlockStack gap="300">
            <Text>
              {t(
                "Please enter your GA4 Google Analytics property ID so that we can create customized reports for your store!"
              )}{" "}
              {t("You may")}{" "}
              <Link
                url="https://storeseo.com/docs/how-to-collect-ga4-google-analytics-property-id/"
                target="_blank"
              >
                {t("follow this link")}
              </Link>{" "}
              {t("to setup analytics in your site!")} <br />
            </Text>
            <Text>
              {t(
                "Note: It may take upto 24 hours before analytics data is reported by Google after the setup is complete."
              )}
            </Text>
            <InlineStack gap="300">
              <div style={{ flex: 1 }}>
                <TextField
                  label="propertyID"
                  labelHidden
                  value={analyticsPropertyId}
                  disabled={!isPremium || isFetching}
                  onChange={(val) => setAnalyticsPropertyId(val)}
                  error={error}
                />
              </div>
              <div style={{ width: "150px" }}>
                <Button
                  icon={SortIcon}
                  loading={isUpdating || isVerifying}
                  disabled={!isPremium || isFetching || !analyticsPropertyId}
                  onClick={handleUpdate}
                  fullWidth
                >
                  {t("Update")}
                </Button>
              </div>
            </InlineStack>
          </BlockStack>
        </Box>
      </Card>
    </>
  );
}

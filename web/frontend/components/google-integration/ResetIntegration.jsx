import React from "react";
import { useTranslation } from "react-i18next";
import { BlockStack, Button, Card, InlineStack, Text } from "@shopify/polaris";
import useConfirmation from "../../hooks/useConfirmation";

export default function ResetIntegration({ loading, callback = () => {} }) {
  const { t } = useTranslation();
  const { renderConfirmation, showConfirmation, hideConfirmation } = useConfirmation();

  const confirmAction = () => {
    callback();
    hideConfirmation();
  };

  return (
    <>
      <Card>
        <BlockStack gap="400">
          <Text
            variant="headingMd"
            as="h5"
          >
            {t("Disconnect Google Integration")}
          </Text>
          <Text
            as="p"
            tone="subdued"
          >
            {t("This will remove all the given Google account permission.")}
          </Text>
          <InlineStack gap="400">
            <Button
              onClick={showConfirmation}
              loading={loading}
              variant="primary"
              tone="critical"
            >
              {t("Disconnect Now")}
            </Button>
          </InlineStack>
        </BlockStack>
      </Card>

      {renderConfirmation({
        content: "Are you sure you want to disconnect Google integration?",
        primaryAction: confirmAction,
        primaryActionIsDestructive: true,
        primaryActionText: "Disconnect",
        loading,
      })}
    </>
  );
}

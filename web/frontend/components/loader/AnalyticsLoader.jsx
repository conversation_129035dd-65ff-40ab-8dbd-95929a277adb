import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Grid, IndexTable, InlineStack, Page, Text } from "@shopify/polaris";
import React from "react";
import SkeletonLoader from "./SkeletonLoader";

const AnalyticsLoader = () => {
  return (
    <Page
      title="Analytics"
      backAction={{ url: "#" }}
    >
      <BlockStack gap="400">
        <Card>
          <BlockStack gap="200">
            <Text as="p">
              Integrate StoreSEO with Google Search Console and Google Analytics to track your store traffic, and
              understand how people find and interact with your store.
            </Text>
            <Box>
              <Button disabled>Connect to Google</Button>
            </Box>
          </BlockStack>
        </Card>
        <Grid columns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}>
          <Grid.Cell>
            <Card>
              <Text
                as="h4"
                variant="bodyMd"
                fontWeight="semibold"
              >
                Search Traffic
              </Text>
              <SkeletonLoader
                style={{ height: 20, width: 60, marginTop: 14 }}
                lineHeight={0}
              />
            </Card>
          </Grid.Cell>
          <Grid.Cell>
            <Card>
              <Text
                as="h4"
                variant="bodyMd"
                fontWeight="semibold"
              >
                Search Impressions
              </Text>
              <SkeletonLoader
                style={{ height: 20, width: 60, marginTop: 14 }}
                lineHeight={0}
              />
            </Card>
          </Grid.Cell>
          <Grid.Cell>
            <Card>
              <Text
                as="h4"
                variant="bodyMd"
                fontWeight="semibold"
              >
                Avg. Position
              </Text>
              <SkeletonLoader
                style={{ height: 20, width: 60, marginTop: 14 }}
                lineHeight={0}
              />
            </Card>
          </Grid.Cell>
        </Grid>
        <Grid columns={{ sm: 3, md: 3, lg: 3 }}>
          <Grid.Cell columnSpan={{ lg: 2, md: 3, sm: 3 }}>
            <Card>
              <BlockStack gap="400">
                <Text
                  as="p"
                  fontWeight="medium"
                  variant="bodySm"
                >
                  Users
                </Text>
                <SkeletonLoader
                  style={{ height: 200, width: "100%" }}
                  lineHeight={0}
                />
                <InlineStack
                  blockAlign="center"
                  align="space-between"
                >
                  <SkeletonLoader
                    style={{ height: 20, width: 100 }}
                    lineHeight={0}
                  />
                  <InlineStack gap="200">
                    <SkeletonLoader
                      style={{ height: 20, width: 70 }}
                      lineHeight={0}
                    />
                    <SkeletonLoader
                      style={{ height: 20, width: 70 }}
                      lineHeight={0}
                    />
                  </InlineStack>
                </InlineStack>
              </BlockStack>
            </Card>
          </Grid.Cell>
          <Grid.Cell columnSpan={{ lg: 1, md: 3, sm: 3 }}>
            <Card>
              <BlockStack gap="400">
                <Text
                  as="p"
                  fontWeight="medium"
                  variant="bodySm"
                >
                  Avr. Engagement Time
                </Text>
                <SkeletonLoader
                  style={{ height: 200, width: "100%" }}
                  lineHeight={0}
                />
                <InlineStack
                  blockAlign="center"
                  align="space-between"
                >
                  <SkeletonLoader
                    style={{ height: 20, width: 70 }}
                    lineHeight={0}
                  />
                  <SkeletonLoader
                    style={{ height: 20, width: 100 }}
                    lineHeight={0}
                  />
                </InlineStack>
              </BlockStack>
            </Card>
          </Grid.Cell>
        </Grid>
        <BlockStack gap="400">
          <Text
            as="h3"
            variant="headingLg"
          >
            Top Products
          </Text>
          <Card padding={0}>
            <IndexTable
              resourceName={{ singular: "order", plural: "orders" }}
              itemCount={5}
              headings={[{ title: "" }, { title: "Products" }]}
              selectable={false}
            >
              {[1, 2, 3, 4, 5].map((_, idx) => (
                <IndexTable.Row
                  key={idx}
                  position={idx}
                >
                  <IndexTable.Cell className="width-24">
                    <SkeletonLoader
                      style={{ height: 20, width: 20 }}
                      lineHeight={0}
                    />
                  </IndexTable.Cell>
                  <IndexTable.Cell>
                    <SkeletonLoader
                      style={{ height: 10, width: 200 }}
                      lineHeight={0}
                    />
                  </IndexTable.Cell>
                </IndexTable.Row>
              ))}
            </IndexTable>
          </Card>
        </BlockStack>
      </BlockStack>
    </Page>
  );
};

export default AnalyticsLoader;

import SkeletonLoader from "./SkeletonLoader";

export default function ContainerSkeleton() {
  return (
    <div className="ss-container">
      <div className="add__store__form">
        <div className="background__white onboard__card radius-20">
          <div className="optimization__list">
            {Array.from({ length: 10 }).map((_, index) => (
              <div
                className="optimization__item"
                key={index}
              >
                <div className="icon">
                  <SkeletonLoader style={{ height: 50, width: 44 }} />
                </div>
                <div className="optimization__content">
                  <SkeletonLoader
                    style={{
                      height: 20,
                      width: `${Math.floor(Math.random() * (40 - 30 + 1)) + 30}%`,
                      marginBottom: 10,
                    }}
                  />
                  <SkeletonLoader style={{ height: 10, width: `${Math.floor(Math.random() * (90 - 60 + 1)) + 60}%` }} />
                </div>
                <div className="optimization__chart">
                  <SkeletonLoader style={{ height: 60, width: 60 }} />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

import SkeletonLoader from "./SkeletonLoader";

export default function DummyProductFilter() {
  return (
    <div className="background__white mb5">
      <form className="filter__form">
        <div className="form__group w-230px">
          <label>From Date</label>
          <SkeletonLoader style={{ height: 50, width: "100%", borderRadius: 12 }} />
        </div>
        <div className="form__group w-230px">
          <label>To Date</label>
          <SkeletonLoader style={{ height: 50, width: "100%", borderRadius: 12 }} />
        </div>
        <div className="form__group w-230px">
          <label>Status</label>
          <SkeletonLoader style={{ height: 50, width: "100%", borderRadius: 12 }} />
        </div>
        <div className="form__group w-230px">
          <label>Show Product</label>
          <SkeletonLoader style={{ height: 50, width: "100%", borderRadius: 12 }} />
        </div>
        <div className="button__group d__flex">
          <SkeletonLoader style={{ height: 50, width: 108, borderRadius: 12, marginRight: 10 }} />
          <SkeletonLoader style={{ height: 50, width: 108, borderRadius: 12 }} />
        </div>
      </form>
    </div>
  );
}

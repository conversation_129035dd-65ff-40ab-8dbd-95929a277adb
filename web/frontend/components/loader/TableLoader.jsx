import React from "react";
import SkeletonLoader from "./SkeletonLoader";

const TableLoader = ({ showHeader = true }) => {
  const TableRow = () => {
    return (
      <div className="table__row">
        <div className="table__cell table__check">
          <SkeletonLoader style={{ height: 10, width: 20 }} />
        </div>
        <div className="table__cell table__thumb">
          <div className="thumb__wrap">
            <SkeletonLoader style={{ height: 50, width: 50 }} />
          </div>
        </div>
        <div className="table__cell table__product">
          <SkeletonLoader style={{ height: 10, width: `${Math.floor(Math.random() * (80 - 30 + 1)) + 30}%` }} />
        </div>
        <div className="table__cell table__focuskey">
          <SkeletonLoader style={{ height: 25, width: 80, borderRadius: 20 }} />
        </div>
        <div className="table__cell table__date">
          <SkeletonLoader style={{ height: 10, width: 120 }} />
        </div>
        <div className="table__cell table__status">
          <SkeletonLoader style={{ height: 25, width: 80, borderRadius: 20 }} />
        </div>
        <div className="table__cell table__score">
          <SkeletonLoader style={{ height: 50, width: 50, borderRadius: 50 }} />
        </div>
        <div className="table__cell table__action d__flex">
          <SkeletonLoader style={{ height: 26, width: 55, borderRadius: 12, marginRight: 5 }} />
          <SkeletonLoader style={{ height: 26, width: 55, borderRadius: 12 }} />
        </div>
      </div>
    );
  };

  const renderRow = () => {
    const printedRow = [];
    for (let i = 0; i < 20; i++) {
      printedRow.push(<TableRow key={i} />);
    }
    return printedRow;
  };

  return (
    <div className="ss__table product__list__table">
      <div className="ss__table__body background__white bbrr bblr">
        {showHeader && (
          <div className="background__white table__row ss__table__head mb5">
            <div className="table__cell table__check">
              <p>#</p>
            </div>
            <div className="table__cell table__thumb">
              <p></p>
            </div>
            <div className="table__cell table__product">
              <p className="c-pointer">
                Products{" "}
                <i
                  className="ss-icon ss-arrow-down"
                  style={{ fontSize: "63%", marginLeft: 5, color: "grey" }}
                ></i>
              </p>
            </div>
            <div className="table__cell table__focuskey">
              <p>Focus Keyword</p>
            </div>
            <div className="table__cell table__date">
              <p className="c-pointer">
                Date{" "}
                <i
                  className="ss-icon ss-arrow-down"
                  style={{ fontSize: "63%", marginLeft: 5, color: "rgb(21, 44, 91)" }}
                ></i>
              </p>
            </div>
            <div className="table__cell table__status">
              <p className="c-pointer">
                Issues{" "}
                <i
                  className="ss-icon ss-arrow-down"
                  style={{ fontSize: "63%", marginLeft: 5, color: "grey" }}
                ></i>
              </p>
            </div>
            <div className="table__cell table__score">
              <p className="c-pointer">
                Score{" "}
                <i
                  className="ss-icon ss-arrow-down"
                  style={{ fontSize: "63%", marginLeft: 5, color: "grey" }}
                ></i>
              </p>
            </div>
            <div className="table__cell table__action">
              <p>Action</p>
            </div>
          </div>
        )}
        {renderRow()}
      </div>
    </div>
  );
};

export default TableLoader;

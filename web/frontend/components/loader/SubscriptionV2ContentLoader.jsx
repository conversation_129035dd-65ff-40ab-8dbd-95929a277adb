import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Grid,
  InlineGrid,
  InlineStack,
  Link,
  Text,
} from "@shopify/polaris";
import { ArrowDownIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";
import { SUBSCRIPTION_ADDONS } from "../subscription/AvailableAddonsCard";
import SkeletonLoader from "./SkeletonLoader";

export default function SubscriptionV2ContentLoader() {
  const arr = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18];
  const arr2 = [1, 2, 3];
  const { t } = useTranslation();

  return (
    <BlockStack gap="400">
      {/* <InlineStack align="center">
        <ButtonGroup variant="segmented">
          <Button
            pressed={true}
            disabled
          >
            Monthly
          </Button>
          <Button
            pressed={false}
            disabled
          >
            Yearly
          </Button>
        </ButtonGroup>
      </InlineStack> */}

      <Grid columns={{ xs: 1, sm: 1, md: 2, lg: 3 }}>
        {arr2.map((item, index) => (
          <Grid.Cell key={index}>
            <Card>
              <BlockStack gap={"400"}>
                <BlockStack gap={"200"}>
                  <InlineStack gap={"100"}>
                    <SkeletonLoader
                      style={{ width: 100, height: 26 }}
                      lineHeight={0}
                    />
                  </InlineStack>
                  <SkeletonLoader
                    style={{ width: 180, height: 13 }}
                    lineHeight={0}
                  />
                  <SkeletonLoader
                    style={{ width: 150, height: 13 }}
                    lineHeight={0}
                  />
                </BlockStack>
                <Bleed marginInline={"400"}>
                  <Divider />
                </Bleed>
                <BlockStack gap={"200"}>
                  <InlineStack
                    gap={"200"}
                    blockAlign="end"
                  >
                    <SkeletonLoader
                      style={{ width: 80, height: 26 }}
                      lineHeight={0}
                    />
                  </InlineStack>

                  <SkeletonLoader style={{ width: 120, height: 13 }} />
                </BlockStack>
                <Button
                  variant="primary"
                  disabled
                >
                  Choose
                </Button>
              </BlockStack>
            </Card>
          </Grid.Cell>
        ))}
      </Grid>

      <InlineStack
        gap={"400"}
        wrap={false}
        align="center"
        blockAlign="center"
      >
        <Box width="40%">
          <Divider borderColor="border-tertiary" />
        </Box>
        <Button
          variant="plain"
          icon={ArrowDownIcon}
        >
          {t("See plan comparison")}
        </Button>
        <Box width="40%">
          <Divider borderColor="border-tertiary" />
        </Box>
      </InlineStack>

      <Card>
        <BlockStack gap="400">
          <InlineStack
            gap="400"
            align="space-between"
          >
            <BlockStack gap="300">
              <SkeletonLoader
                style={{ width: 100, height: 26 }}
                lineHeight={0}
              />
              <SkeletonLoader
                style={{ width: 380, height: 13 }}
                lineHeight={0}
              />
              <SkeletonLoader
                style={{ width: 80, height: 13 }}
                lineHeight={0}
              />
            </BlockStack>
            <BlockStack
              gap="300"
              inlineAlign="end"
            >
              <SkeletonLoader
                style={{ width: "100px", height: "20px" }}
                lineHeight={0}
              />
              <SkeletonLoader
                style={{ width: "200px", height: "10px" }}
                lineHeight={0}
              />

              <Button
                variant="primary"
                disabled
              >
                Choose
              </Button>
            </BlockStack>
          </InlineStack>
        </BlockStack>
      </Card>

      <Card>
        <BlockStack gap="400">
          <InlineStack
            gap="400"
            align="space-between"
          >
            <BlockStack gap="300">
              <SkeletonLoader
                style={{ width: 100, height: 26 }}
                lineHeight={0}
              />
              <SkeletonLoader
                style={{ width: 380, height: 13 }}
                lineHeight={0}
              />
              <SkeletonLoader
                style={{ width: 80, height: 13 }}
                lineHeight={0}
              />
            </BlockStack>
            <BlockStack
              gap="300"
              inlineAlign="end"
            >
              <SkeletonLoader
                style={{ width: "100px", height: "20px" }}
                lineHeight={0}
              />
              <SkeletonLoader
                style={{ width: "200px", height: "10px" }}
                lineHeight={0}
              />

              <Button
                variant="primary"
                disabled
              >
                Choose
              </Button>
            </BlockStack>
          </InlineStack>
        </BlockStack>
      </Card>

      <Card>
        <BlockStack gap="400">
          <SkeletonLoader style={{ width: 140, height: 26 }} />
          <Bleed marginInline={"400"}>
            <Divider />
          </Bleed>

          <InlineGrid
            columns={{ xs: 1, sm: 2, md: 2 }}
            gap={"400"}
          >
            {SUBSCRIPTION_ADDONS.map((addon, idx) => (
              <InlineStack
                key={`loader-${addon.key}-${idx}`}
                wrap={false}
                gap={"400"}
              >
                <SkeletonLoader
                  style={{ width: 48, height: 48 }}
                  lineHeight={0}
                />
                <BlockStack gap={"200"}>
                  <SkeletonLoader style={{ width: 160, height: 18 }} />
                  <BlockStack gap={"100"}>
                    <SkeletonLoader
                      style={{ width: 360, height: 13 }}
                      lineHeight={0}
                    />
                    <SkeletonLoader
                      style={{ width: 260, height: 13 }}
                      lineHeight={0}
                    />
                  </BlockStack>
                </BlockStack>
              </InlineStack>
            ))}
          </InlineGrid>
        </BlockStack>
      </Card>

      <Text>
        <Link
          monochrome
          disabled
        >
          Click here
        </Link>{" "}
        {t("to")} <Text as="strong">cancel your subscription plan</Text>
      </Text>
    </BlockStack>
  );
}

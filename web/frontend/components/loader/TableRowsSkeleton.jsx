import { Box, IndexTable, SkeletonBodyText } from "@shopify/polaris";

export default function TableRowsSkeleton({ rows = 5, cols = 5 }) {
  const rowsCount = Array(rows).fill(0);
  const colsCount = Array(cols).fill(0);

  return (
    <>
      {rowsCount.map((n, idx) => {
        return (
          <IndexTable.Row
            id={idx}
            position={idx}
            key={idx}
          >
            {colsCount.map((n, idx1) => (
              <IndexTable.Cell key={`${idx}-${idx1}`}>
                <Box padding={"1"}>
                  <SkeletonBodyText lines={1} />
                </Box>
              </IndexTable.Cell>
            ))}
          </IndexTable.Row>
        );
      })}
    </>
  );
}

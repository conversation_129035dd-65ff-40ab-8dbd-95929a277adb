import { HIGHLIGHTED_FEAUTERES, STEPS } from "@/config/onboarding";
import { BlockStack, Box, Card, Grid, InlineStack, Page, ProgressBar, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import interpolatedTranslationKeys from "storeseo-enums/interpolatedTranslationKeys";
import SkeletonLoader from "./SkeletonLoader";

function OnboardingSkeleton() {
  const { t } = useTranslation();
  return (
    <Page>
      <BlockStack gap={"400"}>
        <BlockStack gap={"200"}>
          <SkeletonLoader
            style={{ width: 135, height: 32 }}
            lineHeight={0}
          />
          <SkeletonLoader
            style={{ width: "50%", height: 13 }}
            lineHeight={0}
          />
          <InlineStack
            gap={"400"}
            wrap={false}
            blockAlign="center"
          >
            <Text as="p">
              {t(interpolatedTranslationKeys.ONBOARD_PROGRESS_MSG, {
                "?": 1,
                "??": STEPS.length,
              })}
            </Text>

            <div style={{ flex: 1 }}>
              <ProgressBar
                progress={0}
                tone={"highlight"}
                size="small"
              />
            </div>
          </InlineStack>
        </BlockStack>

        <Card>
          <BlockStack gap={"400"}>
            <SkeletonLoader
              style={{ width: "220px", height: 24 }}
              lineHeight={1}
            />

            <Grid columns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 2 }}>
              {HIGHLIGHTED_FEAUTERES.map((feature, index) => (
                <Grid.Cell key={feature.title}>
                  <InlineStack
                    gap={"200"}
                    align="start"
                    blockAlign="start"
                    wrap={false}
                  >
                    <Box width="70%">
                      <BlockStack gap={"400"}>
                        <SkeletonLoader
                          style={{ width: "50%", height: 20 }}
                          lineHeight={10}
                        />
                        <SkeletonLoader
                          style={{ width: "80%", height: 13 }}
                          lineHeight={0}
                        />
                      </BlockStack>
                    </Box>
                  </InlineStack>
                </Grid.Cell>
              ))}
            </Grid>
          </BlockStack>
        </Card>
      </BlockStack>
    </Page>
  );
}

export default OnboardingSkeleton;

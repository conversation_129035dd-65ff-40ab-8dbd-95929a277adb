import React from "react";
import { BlockStack, Card, Grid, InlineGrid, SkeletonDisplayText, Spinner, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";

function FeaturedHighlightLoader() {
  const { t } = useTranslation();

  return (
    <>
      <Grid.Cell columnSpan={{ lg: 2, md: 2, sm: 3 }}>
        <Card>
          <BlockStack gap="200">
            <Text
              as="p"
              tone="subdued"
            >
              {t("Users")}
            </Text>

            <SkeletonDisplayText size="medium" />

            <div style={{ minHeight: "250px", display: "flex", alignItems: "center", justifyContent: "center" }}>
              <Spinner />
            </div>
          </BlockStack>
        </Card>
      </Grid.Cell>
      <Grid.Cell columnSpan={{ lg: 1, md: 1, sm: 3 }}>
        <Card>
          <BlockStack gap="200">
            <Text
              as="p"
              tone="subdued"
            >
              {t("Avr. Engagement Time")}
            </Text>

            <SkeletonDisplayText size="medium" />

            <div style={{ minHeight: "250px", display: "flex", alignItems: "center", justifyContent: "center" }}>
              <Spinner />
            </div>
          </BlockStack>
        </Card>
      </Grid.Cell>
    </>
  );
}

export default FeaturedHighlightLoader;

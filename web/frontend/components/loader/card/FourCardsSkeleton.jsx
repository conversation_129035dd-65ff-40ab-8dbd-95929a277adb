import { BlockStack, Card, Grid, SkeletonBodyText, SkeletonDisplayText } from "@shopify/polaris";

export default function FourCardsSkeletion() {
  return (
    <Grid columns={{ xs: 1, sm: 2, md: 4, lg: 4, xl: 4 }}>
      <Grid.Cell>
        <Card>
          <BlockStack gap="400">
            <SkeletonDisplayText size="small" />
            <SkeletonBodyText lines={3} />
            <SkeletonBodyText lines={1} />
          </BlockStack>
        </Card>
      </Grid.Cell>

      <Grid.Cell>
        <Card>
          <BlockStack gap="400">
            <SkeletonDisplayText size="small" />
            <SkeletonBodyText lines={3} />
            <SkeletonBodyText lines={1} />
          </BlockStack>
        </Card>
      </Grid.Cell>
      <Grid.Cell>
        <Card>
          <BlockStack gap="400">
            <SkeletonDisplayText size="small" />
            <SkeletonBodyText lines={3} />
            <SkeletonBodyText lines={1} />
          </BlockStack>
        </Card>
      </Grid.Cell>
      <Grid.Cell>
        <Card>
          <BlockStack gap="400">
            <SkeletonDisplayText size="small" />
            <SkeletonBodyText lines={3} />
            <SkeletonBodyText lines={1} />
          </BlockStack>
        </Card>
      </Grid.Cell>
    </Grid>
  );
}

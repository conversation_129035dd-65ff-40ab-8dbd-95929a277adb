import { BlockStack, Card, Grid, SkeletonBodyText, SkeletonDisplayText } from "@shopify/polaris";

export default function TwoCardsSkeletion() {
  return (
    <Grid columns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 2 }}>
      <Grid.Cell>
        <Card>
          <BlockStack gap="600">
            <SkeletonDisplayText size="small" />
            <SkeletonBodyText lines={3} />
            <SkeletonBodyText lines={2} />
          </BlockStack>
        </Card>
      </Grid.Cell>

      <Grid.Cell>
        <Card>
          <BlockStack gap="600">
            <SkeletonDisplayText size="small" />
            <SkeletonBodyText lines={3} />
            <SkeletonBodyText lines={2} />
          </BlockStack>
        </Card>
      </Grid.Cell>
    </Grid>
  );
}

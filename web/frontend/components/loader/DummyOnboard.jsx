import React from "react";
import SkeletonLoader from "./SkeletonLoader.jsx";

const DummyOnboard = () => {
  return (
    <>
      <div className="template--wrapper p50 pt30 background__ghostWhite">
        <header className="ss-header header--center pb30">
          <div className="header__logo">
            <img
              src="data:image/svg+xml;base64,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"
              alt="StoreSEO"
            />
          </div>
        </header>

        <div className="ss-container--fluid">
          <div className="ss-content__area">
            <ul className="onboarding__tab">
              <li className="onboarding__tab__item">
                <span className="onboarding__tab__link">
                  <span className="item__text__wrapper">
                    <span className="item__number">1</span> Store Information
                  </span>
                </span>
              </li>
              <li className="onboarding__tab__item false">
                <span className="onboarding__tab__link">
                  <span className="item__text__wrapper">
                    <span className="item__number">2</span> SEO Settings
                  </span>
                </span>
              </li>
              <li className="onboarding__tab__item false">
                <span className="onboarding__tab__link">
                  <span className="item__text__wrapper">
                    <span className="item__number">3</span> Analysis
                  </span>
                </span>
              </li>
              <li className="onboarding__tab__item false">
                <span className="onboarding__tab__link">
                  <span className="item__text__wrapper">
                    <span className="item__number">4</span> Optimization
                  </span>
                </span>
              </li>
              <li className="onboarding__tab__item false">
                <span className="onboarding__tab__link">
                  <span className="item__text__wrapper">
                    <span className="item__number">5</span> Success
                  </span>
                </span>
              </li>
            </ul>
            <div className="ss-container">
              <div className="add__store__form mt50">
                <div className="background__white onboard__card p50 radius-20">
                  <h4 className="card__header">Store Information</h4>
                  <p className="mb30">
                    Check your store URL and select your industry. Then follow the on-screen instructions to set up
                    StoreSEO for your business. We'll have you setup in a minute or two! Get ready to optimize your
                    store!! 🚀🚀
                  </p>
                  <div className="optimization__list">
                    {Array.from({ length: 2 }).map((_, index) => (
                      <div className="optimization__item">
                        <div className="icon">
                          <SkeletonLoader style={{ height: 50, width: 44 }} />
                        </div>
                        <div className="optimization__content">
                          <SkeletonLoader
                            style={{
                              height: 20,
                              width: `${Math.floor(Math.random() * (40 - 30 + 1)) + 30}%`,
                              marginBottom: 10,
                            }}
                          />
                          <SkeletonLoader
                            style={{ height: 10, width: `${Math.floor(Math.random() * (90 - 60 + 1)) + 60}%` }}
                          />
                        </div>
                        <div className="optimization__chart">
                          <SkeletonLoader style={{ height: 60, width: 60 }} />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="button__group d__flex flex__wrap justify__end mb35 mt50">
                  {/* <SkeletonLoader style={{ height: 50, width: 100, borderRadius: 20 }} /> */}
                  <SkeletonLoader style={{ height: 50, width: 100, borderRadius: 20 }} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default DummyOnboard;

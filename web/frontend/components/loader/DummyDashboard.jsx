import SkeletonLoader from "./SkeletonLoader";
import { Grid, Page, BlockStack, Text, Card, Box, InlineStack, Icon, Button } from "@shopify/polaris";
import { Flex } from "../common/CustomComponent.jsx";
import { CalendarIcon } from "@shopify/polaris-icons";

export default function DummyDashboard() {
  return (
    <Page>
      <BlockStack gap="600">
        <Grid columns={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 2 }}>
          <div style={{ height: "100px", display: "flex" }}>
            <BlockStack
              gap="300"
              align="center"
            >
              <Text
                variant="heading2xl"
                as="h1"
              >
                Welcome to StoreSEO
              </Text>
              <Text
                variant="bodyLg"
                as={"p"}
              >
                Drive sales and traffic with the power of StoreSEO and improve your search engine ranking
              </Text>
            </BlockStack>
          </div>
        </Grid>
        <Card>
          <Flex
            justifyContent="space-between"
            alignItems="center"
            gap="400"
          >
            <BlockStack gap="200">
              <Text
                variant="headingMd"
                as="h6"
              >
                Get started
              </Text>
              <Text
                as={"p"}
                tone={"subdued"}
              >
                Solve your SEO issues and optimize your products instantly
              </Text>
            </BlockStack>

            <Box paddingBlockStart={{ xs: 200, sm: 0 }}>
              <Button disabled>Optimize your products</Button>
            </Box>
          </Flex>
        </Card>

        <BlockStack gap="400">
          <Flex
            alignItems="center"
            justifyContent="space-between"
          >
            <Text
              variant="headingLg"
              as="h3"
            >
              Statistics
            </Text>
            <Button
              slim
              disabled
              icon={CalendarIcon}
            >
              {" "}
              All the time
            </Button>
          </Flex>
          <Grid columns={{ xs: 1, sm: 2, md: 4, lg: 4, xl: 4 }}>
            <Grid.Cell>
              <Card>
                <BlockStack gap="200">
                  <SkeletonLoader style={{ height: 22, width: 120 }} />
                  <Text
                    variant="headingSm"
                    as="h6"
                  >
                    Total products
                  </Text>
                </BlockStack>
              </Card>
            </Grid.Cell>
            <Grid.Cell>
              <Card>
                <BlockStack gap="200">
                  <SkeletonLoader style={{ height: 22, width: 120 }} />
                  <Text
                    variant="headingSm"
                    as="h6"
                  >
                    Overall Score
                  </Text>
                </BlockStack>
              </Card>
            </Grid.Cell>
            <Grid.Cell>
              <Card>
                <BlockStack gap="200">
                  <SkeletonLoader style={{ height: 22, width: 120 }} />
                  <Text
                    variant="headingSm"
                    as="h6"
                  >
                    SEO Issues
                  </Text>
                </BlockStack>
              </Card>
            </Grid.Cell>
            <Grid.Cell>
              <Card>
                <BlockStack gap="200">
                  <SkeletonLoader style={{ height: 22, width: 120 }} />
                  <Text
                    variant="headingSm"
                    as="h6"
                  >
                    Already Optimized
                  </Text>
                </BlockStack>
              </Card>
            </Grid.Cell>
          </Grid>
        </BlockStack>

        <div className="dashboard-quick-card">
          <Grid columns={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2 }}>
            <Grid.Cell>
              <Card>
                <InlineStack
                  wrap={false}
                  align="space-between"
                  blockAlign="center"
                >
                  <BlockStack gap="300">
                    <Text
                      as="h4"
                      variant="headingMd"
                    >
                      Tutorial
                    </Text>
                    <Text as="p">Check out our collection of video tutorials to get started</Text>

                    <Box>
                      <Button
                        slim
                        disabled
                      >
                        See tutorials
                      </Button>
                    </Box>
                  </BlockStack>
                  <div className="ss-dash-illustration">
                    <SkeletonLoader style={{ height: 115, width: 181 }} />
                  </div>
                </InlineStack>
              </Card>
            </Grid.Cell>
            <Grid.Cell>
              <Card>
                <InlineStack
                  wrap={false}
                  align="space-between"
                  blockAlign="center"
                >
                  <BlockStack gap="300">
                    <Text
                      as="h4"
                      variant="headingMd"
                    >
                      Documentation
                    </Text>
                    <Text as="p">Read our step-by-step guidelines for each and every feature</Text>

                    <Box>
                      <Button
                        slim
                        disabled
                      >
                        Read documentation
                      </Button>
                    </Box>
                  </BlockStack>
                  <div className="ss-dash-illustration">
                    <SkeletonLoader style={{ height: 115, width: 181 }} />
                  </div>
                </InlineStack>
              </Card>
            </Grid.Cell>
            <Grid.Cell>
              <Card>
                <InlineStack
                  wrap={false}
                  align="space-between"
                  blockAlign="center"
                >
                  <BlockStack gap="300">
                    <Text
                      as="h4"
                      variant="headingMd"
                    >
                      Support
                    </Text>
                    <Text as="p">Feel free to reach out to us at any time to solve your issue</Text>

                    <Box>
                      <Button
                        slim
                        disabled
                      >
                        Contact support
                      </Button>
                    </Box>
                  </BlockStack>
                  <div className="ss-dash-illustration">
                    <SkeletonLoader style={{ height: 115, width: 181 }} />
                  </div>
                </InlineStack>
              </Card>
            </Grid.Cell>
            <Grid.Cell>
              <Card>
                <InlineStack
                  wrap={false}
                  align="space-between"
                  blockAlign="center"
                >
                  <BlockStack gap="300">
                    <Text
                      as="h4"
                      variant="headingMd"
                    >
                      StoreSEO Walkthrough
                    </Text>
                    <Text as="p">Talk with our experts and get a complete StoreSEO Walkthrough</Text>

                    <Box>
                      <Button
                        slim
                        disabled
                      >
                        Schedule a call
                      </Button>
                    </Box>
                  </BlockStack>
                  <div className="ss-dash-illustration">
                    <SkeletonLoader style={{ height: 115, width: 181 }} />
                  </div>
                </InlineStack>
              </Card>
            </Grid.Cell>
          </Grid>
        </div>
        <Card>
          <Flex
            justifyContent="space-between"
            alignItems="center"
            gap="400"
          >
            <BlockStack gap="200">
              <Text
                variant="headingMd"
                as="h6"
              >
                Get our free shopify SEO guide
              </Text>
              <Text
                as={"p"}
                tone={"subdued"}
              >
                Grab your copy today & discover the secrets to optimizing your Shopify store for search engines
              </Text>
            </BlockStack>

            <Box paddingBlockStart={{ xs: 200, sm: 0 }}>
              <Button
                slim
                disabled
              >
                Download Now
              </Button>
            </Box>
          </Flex>
        </Card>
        {/* <BetterDocs /> */}
      </BlockStack>

      {/* {user?.isNewlyUpgraded && <SubscriptionSuccessModal />} */}
    </Page>
  );
}

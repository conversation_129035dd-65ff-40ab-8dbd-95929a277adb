import { <PERSON>ge, BlockStack, Card, Divider, Grid, InlineGrid, InlineStack, Text } from "@shopify/polaris";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import { tabItemsStyle } from "../../styles/common";
import DataComparison from "../analytics/DataComparison";
import {
  convertSecondsToReadableTimeValue,
  formatDate,
  getReadableStringFormatFromNumber,
} from "../../utility/helpers";
import ShortHighlightCard from "../analytics/ShortHighlightCard";
import LineChart from "../charts/LineChart";
import EmptyMsgCard from "../common/EmptyMsgCard.jsx";

const Tab = ({ active = false, onClick, ...item }) => {
  const { t } = useTranslation();

  const { label, data, dataKey } = item;

  return (
    <div
      className={classNames("tab-nav-item", { active })}
      style={tabItemsStyle}
      onClick={onClick}
    >
      <BlockStack gap="200">
        <Text
          as="p"
          tone="subdued"
        >
          {t(label)}
        </Text>
        <InlineStack
          blockAlign="center"
          gap="200"
        >
          <Text
            variant="headingXl"
            as="h3"
          >
            {item.currentValue}
          </Text>
          <DataComparison
            currentValue={item.currentValue}
            prevValue={item?.prevValue}
            formatter={getReadableStringFormatFromNumber}
            boldVersion
          />
        </InlineStack>
      </BlockStack>
    </div>
  );
};

export default function DummyAnalytics() {
  const { t } = useTranslation();

  const TOP_PRODUCTS_TABLE_COLUMNS = [
    { label: "#", isActive: true, className: "w__50" },
    { label: "Product Name", isActive: true, className: "flex__1" },
    { label: "Impressions", isActive: true, className: "w__200" },
    { label: "Users", isActive: true, className: "w__200" },
    { label: "Average Session", isActive: true, className: "w__200" },
  ];

  const KEYWORDS_TABLE_COLUMNS = [
    { label: "#", isActive: true, className: "w__50" },
    { label: "Keywords", isActive: true, className: "flex__1" },
    { label: "Impressions", isActive: true, className: "w__200" },
    { label: "Clicks", isActive: true, className: "w__200" },
    { label: "Position", isActive: true, className: "w__200" },
    { label: "CTR", isActive: true, className: "w__200" },
  ];

  const DUMMY_DATA = [
    ["1", "Nike Air Max 95", "2128", "28", "28"],
    ["1", "Nike Air Max 95", "2128", "28", "28"],
    ["3", "Nike Air Max 95 Nike Air Max 95", "2128", "28", "28"],
    ["4", "Nike Air Max 95", "2128", "28", "28"],
    ["5", "Nike Air Max 95 Nike Air Max 95", "2128", "28", "28"],
  ];

  const featuredHighlights = [
    {
      label: "Users",
      currentValue: 0,
      prevValue: 0,
      color: "#00E396",
    },
    {
      label: "New Users",
      currentValue: 0,
      prevValue: 0,
      color: "#008FFB",
    },
    {
      label: "Avr. Engagement Time",
      currentValue: 0,
      prevValue: 0,
      color: "#FEB019",
    },
  ];

  const highlights = [
    {
      label: "Search Traffic",
      showGraph: true,
      graphColor: "#00E396",
      data: {
        total: 0,
        prevData: {
          total: 0,
        },
      },
    },
    {
      label: "Search Impressions",
      showGraph: true,
      graphColor: "#FEB019",
      data: {
        total: 0,
        prevData: {
          total: 0,
        },
      },
    },
    // {
    //   label: "Total Keywords",
    //   showGraph: false,
    //   totalKey: "totalUsers",
    //   currentValue: 0,
    //   prevValue: 0,
    // },
    {
      label: "Avg. Position",
      showGraph: true,
      graphColor: "#FF4560",
      currentValue: 0,
      prevValue: 0,
      data: {
        total: 0,
        prevData: {
          total: 0,
        },
      },
    },
  ];

  const chartData = {
    xaxisType: "datetime",
    categories: ["2022-02-01", "2022-02-02", "2022-02-03", "2022-02-04", "2022-02-05", "2022-02-06", "2022-02-07"],
    series: [
      { name: "Users", data: [0, 0, 0, 0, 0, 0, 0] },
      { name: "New Users", data: [0, 0, 0, 0, 0, 0, 0] },
    ],
  };

  const avgTimechartData = {
    xaxisType: "datetime",
    categories: ["2022-02-01", "2022-02-02", "2022-02-03", "2022-02-04", "2022-02-05", "2022-02-06", "2022-02-07"],
    series: [{ name: "Avr. Engagement Time", data: [0, 0, 0, 0, 0, 0, 0] }],
  };

  const startDate = "2022-02-01";
  const endDate = "2022-02-07";

  return (
    <>
      <BlockStack gap="400">
        <InlineGrid
          columns={{ xs: 1, sm: 3, md: 3, lg: 3, xl: 3 }}
          gap="400"
        >
          {highlights.map((highlight, idx) => (
            <ShortHighlightCard
              key={idx}
              {...highlight}
            />
          ))}
        </InlineGrid>

        <InlineGrid
          columns={{
            xs: 1,
            sm: 1,
            md: ["twoThirds", "oneThird"],
            lg: ["twoThirds", "oneThird"],
            xl: ["twoThirds", "oneThird"],
          }}
          gap="400"
        >
          <Card>
            <BlockStack gap="300">
              <Text
                as="p"
                fontWeight="medium"
                variant="bodySm"
              >
                {t("Users")}
              </Text>

              <InlineStack
                blockAlign="end"
                gap="025"
              >
                <Text
                  variant="headingLg"
                  as="h3"
                >
                  {getReadableStringFormatFromNumber(featuredHighlights[0].currentValue || 0)}
                </Text>
                <DataComparison
                  currentValue={featuredHighlights[0].currentValue}
                  prevValue={featuredHighlights[0].prevValue}
                  formatter={getReadableStringFormatFromNumber}
                />
              </InlineStack>
              <Divider />
              <div style={{ width: "100%", height: "fit-content" }}>
                <LineChart
                  width="100%"
                  height="200px"
                  showAxisLabel
                  colors={[featuredHighlights[0].color, featuredHighlights[1].color]}
                  {...chartData}
                />
              </div>

              <InlineStack
                gap="400"
                align="space-between"
              >
                <Badge>
                  {formatDate(startDate, "DD MMM")} - {formatDate(endDate, "DD MMM")}
                </Badge>

                <InlineStack
                  align="end"
                  gap="400"
                >
                  <InlineStack
                    align="end"
                    blockAlign="center"
                    gap={"150"}
                  >
                    <div style={{ width: "10px", height: "10px", backgroundColor: featuredHighlights[0].color }}></div>
                    <Text
                      as="p"
                      tone="subdued"
                      variant="bodySm"
                    >
                      Users
                    </Text>
                  </InlineStack>

                  <InlineStack
                    align="end"
                    blockAlign="center"
                    gap={"150"}
                  >
                    <div style={{ width: "10px", height: "10px", backgroundColor: featuredHighlights[1].color }}></div>
                    <Text
                      as="p"
                      tone="subdued"
                      variant="bodySm"
                    >
                      New Users
                    </Text>
                  </InlineStack>
                </InlineStack>
              </InlineStack>
            </BlockStack>
          </Card>
          <Card>
            <BlockStack gap="200">
              <Text
                as="p"
                fontWeight="medium"
                variant="bodySm"
              >
                {t("Avr. Engagement Time")}
              </Text>

              <InlineStack
                blockAlign="end"
                gap="025"
              >
                <Text
                  variant="headingLg"
                  as="h3"
                >
                  {convertSecondsToReadableTimeValue(featuredHighlights[2].currentValue)}
                </Text>
                <DataComparison
                  currentValue={featuredHighlights[2].currentValue}
                  prevValue={featuredHighlights[2].currentValue}
                  formatter={convertSecondsToReadableTimeValue}
                />
              </InlineStack>
              <Divider />
              <div style={{ width: "100%", height: "fit-content" }}>
                <LineChart
                  width="100%"
                  height="218px"
                  showAxisLabel
                  color={featuredHighlights[2].color}
                  {...avgTimechartData}
                />
              </div>

              <InlineStack align="space-between">
                <Badge>
                  {formatDate(startDate, "DD MMM")} - {formatDate(endDate, "DD MMM")}
                </Badge>
                <InlineStack
                  align="end"
                  blockAlign="center"
                  gap={"150"}
                >
                  <div style={{ width: "10px", height: "10px", backgroundColor: featuredHighlights[2].color }}></div>
                  <Text
                    as="p"
                    tone="subdued"
                    variant="bodySm"
                  >
                    Avr. Engagement Time
                  </Text>
                </InlineStack>
              </InlineStack>
            </BlockStack>
          </Card>
        </InlineGrid>

        <Text
          as="h3"
          variant="headingLg"
        >
          {t("Top Products")}
        </Text>

        <EmptyMsgCard message={t("No product analytics found.")} />

        {/* <Text
          as="h3"
          variant="headingLg"
        >
          {t("Keywords")}
        </Text>

        <EmptyMsgCard message={t("No keyword analytics found.")} /> */}
      </BlockStack>

      {/*<Box
        paddingBlockStart="600"
        paddingBlockEnd="500"
      >
        <Text
          as="h3"
          variant="headingLg"
        >
          {"Top Products"}
        </Text>
      </Box>

      <Card padding={"0"}>
        <IndexTable
          headings={TOP_PRODUCTS_TABLE_COLUMNS.map((c) => ({ title: c.label }))}
          itemCount={5}
          resourceName={{
            singular: "Top product",
            plural: "Top products",
          }}
          selectable={false}
        >
          {DUMMY_DATA.map((data, idx) => (
            <IndexTable.Row
              id={idx}
              key={idx}
              position={idx}
            >
              {data.map((cellData, idx) => (
                <IndexTable.Cell>
                  <Text>{cellData}</Text>
                </IndexTable.Cell>
              ))}
            </IndexTable.Row>
          ))}
        </IndexTable>
      </Card>

      <Box
        paddingBlockStart="600"
        paddingBlockEnd="500"
      >
        <Text
          as="h3"
          variant="headingLg"
        >
          {"Keywords"}
        </Text>
      </Box>

      <Card padding={"0"}>
        <IndexTable
          headings={KEYWORDS_TABLE_COLUMNS.map((c) => ({ title: c.label }))}
          itemCount={5}
          resourceName={{
            singular: "keyword analytic",
            plural: "keyword analytics",
          }}
          selectable={false}
        >
          {DUMMY_DATA.map((data, idx) => (
            <IndexTable.Row
              id={idx}
              key={idx}
              position={idx}
            >
              {[...data, data[2]].map((cellData, idx) => (
                <IndexTable.Cell>
                  <Text>{cellData}</Text>
                </IndexTable.Cell>
              ))}
            </IndexTable.Row>
          ))}
        </IndexTable>
      </Card>*/}
    </>
  );
}

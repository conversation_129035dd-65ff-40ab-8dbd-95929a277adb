//@ts-check
import useIsRunningBackupRestore from "@/lib/hooks/useIsRunningBackupRestore";
import { IndexTable } from "@shopify/polaris";
import AiOptimizationStatus from "storeseo-enums/aiOptimization";
import CollectionRowsItem from "./CollectionRowsItem";

const CollectionRows = ({ collections, isFetching = false, selectedResources }) => {
  const { isRunning: isRunningBackupOrRestore } = useIsRunningBackupRestore();
  return (
    <>
      {collections?.map(({ id, ...collection }, index) => {
        const item = { ...collection };
        const isDisableRow = [
          AiOptimizationStatus.PENDING,
          AiOptimizationStatus.DISPATCHED,
          AiOptimizationStatus.SAVING,
          AiOptimizationStatus.PROCESSING,
        ].includes(collection.ai_optimization_status);

        return (
          <IndexTable.Row
            id={id}
            key={index}
            position={index}
            selected={selectedResources?.includes(id)}
            disabled={isDisableRow || isRunningBackupOrRestore}
          >
            <CollectionRowsItem
              item={item}
              isFetching={isFetching}
            />
          </IndexTable.Row>
        );
      })}
    </>
  );
};

export default CollectionRows;

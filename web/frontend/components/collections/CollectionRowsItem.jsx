import AIOptimizationStatusBadge from "@/modules/optimize-seo/AIOptimizationStatusBadge";
import OptimizeSeoStatusBadge from "@/modules/optimize-seo/OptimizeSeoStatusBadge";
import {
  Badge,
  BlockStack,
  Box,
  Button,
  IndexTable,
  InlineStack,
  Link,
  Text,
  Thumbnail,
  useBreakpoints,
} from "@shopify/polaris";
import { ComposeIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";
import { useLabelColor } from "../../hooks/useLabelColor";
import { formatDate, prepareThumbnailURL } from "../../utility/helpers";
import Radial<PERSON>hart from "../charts/RadialChart";
import TitleColumn from "../common/TitleColumn";
import TooltipWrapper from "../common/TooltipWrapper";
import CollectionRowsItemSkeleton from "./CollectionRowsItemSkeleton";

export default function CollectionRowsItem({ item, isFetching }) {
  const { smDown: isSmallDevice } = useBreakpoints();

  if (isFetching) return <CollectionRowsItemSkeleton />;

  return <>{isSmallDevice ? <RowsCellForSmallDevices item={item} /> : <RowsCellForLargeDevices item={item} />}</>;
}

const RowsCellForSmallDevices = ({ item }) => {
  const { title, focus_keyword, featuredImage, created_at, score, issues, shopifyId } = item;
  const { t } = useTranslation();
  const labelColor = useLabelColor(issues);
  const thumbnail = prepareThumbnailURL(featuredImage?.src);

  return (
    <Link
      monochrome
      removeUnderline
      url={`/optimize-seo/collections/${shopifyId}`}
      onClick={(e) => e.stopPropagation()}
    >
      <Box
        paddingBlock="300"
        paddingInline="400"
        width="100%"
      >
        <BlockStack gap="150">
          <InlineStack
            align="space-between"
            wrap={false}
            gap="200"
          >
            {/* Thumbnail */}
            <Box>
              <Thumbnail
                source={thumbnail}
                alt={featuredImage?.altText}
                size="small"
              />
            </Box>
            <Box width="100%">
              <BlockStack gap="300">
                {/* Basic info */}
                <BlockStack gap="100">
                  <Text
                    as={"h4"}
                    fontWeight="semibold"
                  >
                    {title}
                  </Text>

                  <Text
                    as="span"
                    variant="bodySm"
                  >
                    {focus_keyword}
                  </Text>
                </BlockStack>
                {/* Scoring */}
                <InlineStack
                  align="space-between"
                  blockAlign="center"
                  gap="200"
                >
                  <BlockStack
                    gap="200"
                    blockAlign="center"
                  >
                    <InlineStack gap="200">
                      <Badge tone={labelColor}>
                        {issues} {t("Issues")}
                      </Badge>

                      <OptimizeSeoStatusBadge score={score} />
                    </InlineStack>

                    <Text
                      as="span"
                      variant="bodySm"
                      tone="subdued"
                    >
                      {formatDate(created_at)}
                    </Text>
                  </BlockStack>

                  <RadialChart
                    score={score}
                    dimension={40}
                  />
                </InlineStack>
              </BlockStack>
            </Box>
          </InlineStack>
        </BlockStack>
      </Box>
    </Link>
  );
};

const RowsCellForLargeDevices = ({ item }) => {
  const { title, focus_keyword, featuredImage, created_at, score, issues, shopifyId, ai_optimization_status } = item;
  const { t } = useTranslation();
  const labelColor = useLabelColor(issues);
  const thumbnail = prepareThumbnailURL(featuredImage?.src);
  return (
    <>
      <IndexTable.Cell className="width-table_thumbnail">
        <Thumbnail
          source={thumbnail}
          alt={featuredImage?.altText}
          size="small"
        />
      </IndexTable.Cell>
      <IndexTable.Cell className="break_coll_content width-resource_table_title">
        <InlineStack
          wrap={false}
          gap="200"
          blockAlign="center"
        >
          <TitleColumn
            title={title}
            url={`/optimize-seo/collections/${shopifyId}`}
          />

          <AIOptimizationStatusBadge
            status={ai_optimization_status}
            noBadge
          />
        </InlineStack>
      </IndexTable.Cell>
      <IndexTable.Cell className="width-150">
        <OptimizeSeoStatusBadge score={score} />
      </IndexTable.Cell>
      <IndexTable.Cell className="break_coll_content width-resource_table_title">{focus_keyword}</IndexTable.Cell>
      <IndexTable.Cell>
        <Badge tone={labelColor}>
          {issues} {t("Issues")}
        </Badge>
      </IndexTable.Cell>
      <IndexTable.Cell>
        <RadialChart
          score={score}
          dimension={40}
        />
      </IndexTable.Cell>
      <IndexTable.Cell className="width-120">{formatDate(created_at)}</IndexTable.Cell>
      <IndexTable.Cell className="width-80">
        <InlineStack align="center">
          <TooltipWrapper content="Fix issue">
            <Button
              icon={ComposeIcon}
              onClick={(e) => e.stopPropagation()}
              variant="tertiary"
              url={`/optimize-seo/collections/${shopifyId}`}
            />
          </TooltipWrapper>
        </InlineStack>
      </IndexTable.Cell>
    </>
  );
};

// // @ts-check
import { isEmpty } from "lodash";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import socketEvents from "storeseo-enums/socketEvents";
import { usePusher } from "../../providers/PusherProvider";
import { Button, Icon, Tooltip } from "@shopify/polaris";
import { ResetIcon } from "@shopify/polaris-icons";
import ConfirmationModal from "../modals/ConfirmationModal.jsx";
import { useGetShopifyCollectionsCount, useSyncCollections } from "../../hooks/collections/index.jsx";
import { setCollectionCount } from "../../store/features/CollectionCount.js";
import { setCollectionSyncInfo, setCollectionSyncStatus } from "../../store/features/CollectionSync.js";

const CollectionSyncButton = ({ onSyncErr = () => {}, isDisable = false }) => {
  const collectionSync = useSelector((state) => state.collectionSync);
  const user = useSelector((state) => state.user);
  const collectionCount = useSelector((state) => state.collectionCount);

  const dispatch = useDispatch();
  const { pusherChannel } = usePusher();

  const { t } = useTranslation();

  const [isTooltipActive, setIsTooltipActive] = useState(false);
  const [tooltipContent, setTooltipContent] = useState("");
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const { data: totalCollectionsCount } = useGetShopifyCollectionsCount();

  let { mutate: startSyncOperation, isLoading } = useSyncCollections({
    onSuccess: (status) => {
      dispatch(setCollectionSyncStatus(status));
    },
    onError: () => {
      onSyncErr();
    },
    onSettled: () => setShowConfirmModal(false),
  });

  const allCollectionsSynced = collectionCount >= totalCollectionsCount;

  const syncUpdateEventListener = (message) => {
    dispatch(setCollectionSyncInfo({ total: message.total, synced: message.synced }));
    dispatch(setCollectionCount(message.synced));
  };

  useEffect(() => {
    if (!pusherChannel) return;

    pusherChannel.bind(socketEvents.COLLECTION_SYNC_UPDATE, syncUpdateEventListener);
  }, [pusherChannel]);

  const handleSyncCollection = async () => {
    setShowConfirmModal(true);
  };

  useEffect(() => {
    if (!isEmpty(collectionSync) && collectionSync.ongoing) {
      const message =
        t("Syncing collections") +
        (collectionSync.total > 0 ? ` ${collectionSync.synced}/${collectionSync.total}` : "...");
      setTooltipContent(message);
      setIsTooltipActive(true);
    } else if (allCollectionsSynced && collectionSync.ongoing === false) {
      setTooltipContent(t("All collections are synced"));
      setIsTooltipActive(true);
    } else {
      setTooltipContent("");
      setIsTooltipActive(false);
    }
  }, [user, collectionSync, allCollectionsSynced]);

  const disableSyncBtn = !user?.isSubscribed || allCollectionsSynced || isTooltipActive || isDisable;

  return (
    <>
      <Tooltip
        active={isTooltipActive}
        content={t(tooltipContent)}
        persistOnClick
        activatorWrapper={"div"}
        zIndexOverride={99}
      >
        <Button
          icon={<Icon source={ResetIcon} />}
          onClick={handleSyncCollection}
          disabled={disableSyncBtn}
          loading={isLoading || collectionSync?.ongoing}
        >
          {t("Sync collections")}
        </Button>
      </Tooltip>

      <ConfirmationModal
        show={showConfirmModal}
        onClose={setShowConfirmModal}
        title="Sync collections"
        content="Are you sure you want to sync collections from Shopify?"
        primaryAction={startSyncOperation}
        loading={isLoading}
      />
    </>
  );
};

export default CollectionSyncButton;

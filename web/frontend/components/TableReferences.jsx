// @ts-check
import { useResourceTitle } from "@/lib/hooks/image-optimizer";
import { pluralizeText } from "@/utility/helpers";
import { BlockStack, Box, Button, Link, Popover, Text } from "@shopify/polaris";
import React, { useEffect, useId, useMemo, useState } from "react";
import ResourceType from "storeseo-enums/resourceType";
import { useTableReferences } from "./TableReferencesContext";

/**
 * @typedef {keyof Pick<typeof ResourceType, "PRODUCT" | "COLLECTION" | "PAGE" | "ARTICLE">} ImageResourceType
 */

/**
 * TableReferences component that displays a list of resources references.
 *
 * @param {{resources: Array<any>, resourceType: ImageResourceType}} props - The component props containing resources.
 * @returns {React.ReactElement} - Rendered TableReferences component.
 */
const TableReferences = (props) => {
  const { resources, resourceType } = props;
  const [expanded, setExpanded] = useState(false);
  const { singular, plural } = useResourceTitle({ resourceType });
  const { activePopoverId, openPopover, closePopover } = useTableReferences();
  const baseId = useId();

  // Generate unique ID for this popover instance
  const popoverId = useMemo(() => `${resourceType}-${baseId}`, [resourceType, baseId]);

  // Close this popover if another one is opened
  useEffect(() => {
    if (activePopoverId && activePopoverId !== popoverId && expanded) {
      setExpanded(false);
    }
  }, [activePopoverId, popoverId, expanded]);

  const pluralizedText = useMemo(
    () => pluralizeText(resources.length, singular, plural),
    [resources.length, singular, plural]
  );

  const resourceLinkUrl = useMemo(() => {
    switch (resourceType) {
      case "PRODUCT":
        return "/optimize-seo/products/:id";
      case "COLLECTION":
        return "/optimize-seo/collections/:id";
      case "PAGE":
        return "/optimize-seo/pages/:id";
      case "ARTICLE":
        return "/optimize-seo/articles/:id";
      default:
        return "";
    }
  }, [resourceType]);

  const handlePopoverToggle = () => {
    const newExpandedState = !expanded;
    setExpanded(newExpandedState);

    // Update the active popover ID in the context
    if (newExpandedState) {
      openPopover(popoverId);
    } else {
      closePopover();
    }
  };

  return (
    <div onClick={(e) => (e.stopPropagation(), e.preventDefault())}>
      <Popover
        active={expanded}
        activator={
          <Button
            textAlign="left"
            disclosure={expanded ? "up" : "down"}
            onClick={handlePopoverToggle}
            variant="tertiary"
          >
            {`${resources.length}`} {pluralizedText.toLowerCase()}
          </Button>
        }
        autofocusTarget="first-node"
        onClose={() => {
          setExpanded(false);
          closePopover();
        }}
      >
        <Box
          padding="200"
          width="100%"
        >
          <BlockStack gap="100">
            <Text
              as="p"
              variant="headingSm"
            >
              {pluralizedText}
            </Text>

            {resources.map((item) => (
              <Link
                key={item.id}
                url={resourceLinkUrl.replace(":id", item.resource_id.split("/").reverse()[0])}
              >
                {item.title}
              </Link>
            ))}
          </BlockStack>
        </Box>
      </Popover>
    </div>
  );
};

export default TableReferences;

import { InlineStack, Box } from "@shopify/polaris";

export default function AddonLoader({ items = 4 }) {
  const width = Math.floor(100 / items);

  return (
    <InlineStack
      gap="400"
      columns={items}
      wrap={false}
    >
      {Array.from({ length: items }).map((val, index) => (
        <Box
          key={index}
          borderWidth="025"
          borderColor="border"
          borderRadius="200"
          width={`${width}%`}
          minHeight="80px"
          background="bg-fill-disabled"
        ></Box>
      ))}
    </InlineStack>
  );
}

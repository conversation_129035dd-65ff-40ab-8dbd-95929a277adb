import { formatNumber } from "@/utility/helpers";
import { BlockStack, Card, Icon, RadioButton, Text } from "@shopify/polaris";
import { CheckCircleIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";
import { MONTHLY } from "storeseo-enums/subscriptionAddonInterval";
import { FREE } from "storeseo-enums/subscriptionAddonType";

export default function AddonCard({ addon, selectedAddons, handleAddonSelect }) {
  const { t } = useTranslation();
  const isChecked = Number(addon.id) === Number(selectedAddons[addon.group]);

  return (
    <div className="addon-select">
      <RadioButton
        fill
        name={addon.group}
        id={`${addon.group}:${addon.id}`}
        value={addon.id}
        checked={isChecked}
        onChange={handleAddonSelect}
        label={
          <Card background={"bg-fill"}>
            <BlockStack gap="200">
              <Text
                as="p"
                variant="headingSm"
              >
                ${addon.subtotal.toFixed(2)}
                {addon.type !== FREE && addon.interval === MONTHLY ? "/M" : ""}
              </Text>
              {addon?.features?.map((feature) => (
                <Text
                  key={feature.key}
                  as="p"
                  tone="subdued"
                >
                  <strong>{formatNumber(feature.value)}</strong> {t(feature.label)}
                </Text>
              ))}
            </BlockStack>
            {isChecked && (
              <span style={{ position: "absolute", right: "5px", top: "5px" }}>
                <Icon
                  source={CheckCircleIcon}
                  tone="info"
                />
              </span>
            )}
          </Card>
        }
      />
    </div>
  );
}

import { Badge, BlockStack, Button, ButtonGroup, Grid, Icon, InlineStack, Text } from "@shopify/polaris";
import { CheckIcon } from "@shopify/polaris-icons";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { USAGE } from "storeseo-enums/planInterval";
import { useSubscriptionApi } from "../../hooks/apiHooks/useSubscriptionApi.js";
import { useAppQuery } from "../../hooks/index.js";
import SubscriptionContentLoader from "../loader/SubscriptionContentLoader.jsx";
import PlanCard from "./PlanCard.jsx";

function SubscriptionPageContent() {
  const subscriptionApi = useSubscriptionApi();
  const { t } = useTranslation();

  const [isYearly, setIsYearly] = useState(false);
  const user = useSelector((state) => state.user);

  const { data = {}, isFetching } = useAppQuery({
    queryKey: ["SUBSCRIPTION_DATA", user.planId],
    queryFn: () => subscriptionApi.getSubscriptionData(),
    reactQueryOptions: {
      // staleTime: 0,
      // refetchInterval: false,
      // onSuccess: (data) => {
      //   setPlans(data.plans);
      // },
    },
  });

  const { plans = [] } = data;

  const visiblePlans = plans.filter((plan) => {
    if (isYearly) return plan.isYearly === true;
    return plan.isMonthly === true;
  });

  const freePlan = plans.find((plan) => plan.isFree);
  const visionaryPlan = plans.find((plan) => plan.interval === USAGE);

  if (isFetching) {
    return <SubscriptionContentLoader />;
  }

  return (
    <BlockStack gap="400">
      <PlanCard
        plan={visionaryPlan}
        fullWidth
      />

      <InlineStack align="end">
        <ButtonGroup variant="segmented">
          <Button
            onClick={() => setIsYearly(false)}
            pressed={!isYearly}
          >
            {t("Monthly")}
          </Button>
          <Button
            onClick={() => setIsYearly(true)}
            pressed={isYearly}
          >
            {t("Yearly")}
          </Button>
        </ButtonGroup>
      </InlineStack>

      <div className="pricing-wrap">
        <Grid columns={{ xs: 1, sm: 1, md: 2, lg: 3 }}>
          {visiblePlans.map((plan) => (
            <Grid.Cell key={plan.slug}>
              <PlanCard plan={plan} />
            </Grid.Cell>
          ))}
        </Grid>
      </div>
      <Text
        as="p"
        tone="subdued"
        alignment="center"
      >
        {t("Alternatively")}
      </Text>

      <PlanCard
        plan={freePlan}
        fullWidth
      />
    </BlockStack>
  );
}

export default SubscriptionPageContent;

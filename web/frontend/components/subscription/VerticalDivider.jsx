import { Box } from "@shopify/polaris";

/**
 * A vertical divider component for separating plan features
 * @component
 * @param {Object} props
 * @param {number|string} [props.height=20] - The height of the divider (px, %, em, etc.)
 * @param {string} [props.color='#EBEBEB'] - The color of the divider
 * @param {number|string} [props.thickness=1] - The thickness of the divider (px, em, etc.)
 * @param {string} [props.className] - Additional class names for styling
 * @returns {JSX.Element} A vertical line element
 */
const VerticalDivider = ({ height = 20, color = "var(--p-color-bg-surface-brand)", thickness = 1 }) => (
  <Box
    as="span"
    aria-hidden="true"
    style={{
      display: "inline-block",
      width: thickness,
      height,
      backgroundColor: color,
      verticalAlign: "middle",
    }}
  />
);

export default VerticalDivider;

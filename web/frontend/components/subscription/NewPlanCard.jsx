// @ts-check
import { useAppBridgeModal } from "@/hooks/useAppBridgeModal";
import { useSubscribeNowAction } from "@/lib/hooks/subscription";
import { setCheckout } from "@/store/features/Checkout";
import { Bleed, BlockStack, Card, Divider, InlineStack, Text } from "@shopify/polaris";
import PropTypes from "prop-types";
import { useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import planInterval from "storeseo-enums/planInterval";
import ChooseButton from "./ChooseButton";
import PlanHeader from "./PlanHeader";
import PlanLimits from "./PlanLimits";
import PlanPrice from "./PlanPrice";

/**
 * A component that renders a subscription plan card with pricing and features
 * @component
 * @param {Object} props - The component props
 * @param {Object} props.plan - The plan object containing subscription details
 * @param {string} props.plan.slug - Unique identifier for the plan
 * @param {string} props.plan.name - Name of the plan
 * @param {string} props.plan.subtitle - Subtitle/description of the plan
 * @param {number} props.plan.price - Original price of the plan
 * @param {string} props.plan.intervalText - Billing interval text (e.g. "Monthly", "Yearly")
 * @param {boolean} props.plan.isSubscribed - Whether user is currently subscribed to this plan
 * @param {boolean} props.plan.featured - Whether the plan is featured or not
 * @param {Object} props.plan.meta - Additional plan metadata
 * @param {string} props.plan.meta.save - Savings percentage to display if applicable
 * @param {number} props.plan.meta.trialDays - Number of trial days for the plan
 * @param {object} props.plan.meta.trialRules - Trial usage limits
 * @param {object} props.plan.meta.trialRules.products - Product limit during trial
 * @param {object} props.plan.meta.trialRules.image_optimizer - Image optimizer limit during trial
 * @param {object} props.plan.meta.trialRules.ai_optimizer - AI optimizer limit during trial
 * @param {string|number} props.plan.products - Number of products included ("Unlimited" or number)
 * @param {string} props.plan.coupon_code - Coupon code associated with the plan if any
 * @param {string} [props.plan.description] - Optional description text shown for usage-based plans
 * @param {string} [props.plan.interval] - Billing interval type
 * @param {boolean} [props.fullWidth=false] - Whether the card should take full width
 * @returns {React.ReactElement} A card component displaying plan details and subscription options
 */
const NewPlanCard = ({ plan, fullWidth = false }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { showCheckoutModal } = useAppBridgeModal();
  // @ts-ignore
  const user = useSelector((state) => state.user);
  const subcribeNow = useSubscribeNowAction();

  // const isCurrentPlanActive = !user?.trialData?.isActive && user?.planSlug === plan.slug;
  const directCheckout = user?.planSlug === plan.slug && user?.trialData?.isActive && user.isSubscribed;

  // Early return if plan data is missing
  if (!plan || !plan.slug || !plan.name) {
    return null;
  }

  // Memoize computed values for performance
  const productCount = useMemo(() => plan?.products || "Unlimited", [plan?.products]);

  // Memoize event handler to prevent unnecessary re-renders
  const handleChoose = useCallback(
    ({ withTrial = false }) => {
      if (directCheckout) {
        subcribeNow.mutate();
      } else {
        dispatch(setCheckout({ slug: plan.slug, coupon_code: plan.coupon_code, isTrial: withTrial }));
        showCheckoutModal();
      }
    },
    [dispatch, plan.slug, plan.coupon_code, showCheckoutModal, directCheckout]
  );

  const chooseButton = (
    <ChooseButton
      fullWidth={!fullWidth}
      planName={plan.name}
      onAction={handleChoose}
      variant={plan.featured ? "primary" : ""}
      meta={plan.meta}
      loading={plan.isSubscribed ? subcribeNow.isLoading : false}
    />
  );

  // Early return for compact layout
  if (!fullWidth) {
    return (
      <Card>
        <BlockStack
          gap={"400"}
          inlineAlign="center"
        >
          <PlanHeader plan={plan} />
          <Bleed marginInline={"400"}>
            <Divider />
          </Bleed>
          <BlockStack gap={"200"}>
            <InlineStack
              gap={"200"}
              blockAlign="end"
            >
              <PlanPrice plan={plan} />
            </InlineStack>
            <PlanLimits
              plan={plan}
              productCount={productCount}
            />
          </BlockStack>
          {chooseButton}
        </BlockStack>
      </Card>
    );
  }

  // Full-width layout
  return (
    <Card>
      <InlineStack
        gap={"400"}
        align="space-between"
      >
        <BlockStack gap={"200"}>
          <PlanHeader plan={plan} />
          <PlanLimits
            plan={plan}
            productCount={productCount}
            fullWidth
          />
        </BlockStack>
        <BlockStack
          gap={"200"}
          inlineAlign="end"
        >
          <InlineStack
            gap={"200"}
            blockAlign="end"
          >
            <PlanPrice plan={plan} />
          </InlineStack>
          {plan.description && plan.interval === planInterval.USAGE ? (
            <Text
              variant="bodySm"
              as="p"
              tone="subdued"
              alignment="end"
            >
              *{t(plan.description)}
            </Text>
          ) : null}
          {chooseButton}
        </BlockStack>
      </InlineStack>
    </Card>
  );
};

// PropTypes for NewPlanCard
NewPlanCard.propTypes = {
  plan: PropTypes.shape({
    slug: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    subtitle: PropTypes.string.isRequired,
    price: PropTypes.number.isRequired,
    intervalText: PropTypes.string,
    isSubscribed: PropTypes.bool,
    featured: PropTypes.bool,
    products: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    coupon_code: PropTypes.string,
    description: PropTypes.string,
    interval: PropTypes.string,
    meta: PropTypes.shape({
      save: PropTypes.string,
      info: PropTypes.string,
    }),
    combinedAddons: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
        limit: PropTypes.number,
        group: PropTypes.string.isRequired,
        interval: PropTypes.string,
      })
    ),
  }).isRequired,
  fullWidth: PropTypes.bool,
};

export default NewPlanCard;

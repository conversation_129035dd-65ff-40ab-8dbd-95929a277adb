import { Box, InlineStack, Text } from "@shopify/polaris";
import PropTypes from "prop-types";
import { Fragment } from "react";
import SubscriptionAddonGroup from "storeseo-enums/subscriptionAddonGroup";
import AiContentOptimizerIcon from "../svg/AiContentOptimizerIcon";
import ImageOptimizerIcon from "../svg/ImageOptimizerIcon";
import ProductsIcon from "../svg/ProductsIcon";
import PlanLimitItem from "./PlanLimitItem";
import VerticalDivider from "./VerticalDivider";

const getAddonIcon = (group) => {
  switch (group) {
    case SubscriptionAddonGroup.IMAGE_OPTIMIZER:
      return <ImageOptimizerIcon size={16} />;
    case SubscriptionAddonGroup.AI_OPTIMIZER:
      return <AiContentOptimizerIcon size={16} />;
    default:
      return null;
  }
};

const ADDON_LABELS = {
  [SubscriptionAddonGroup.IMAGE_OPTIMIZER]: "imageOptimizerPlanLabel",
  [SubscriptionAddonGroup.AI_OPTIMIZER]: "aiContentOptimizerPlanLabel",
};

const PlanLimits = ({ plan, productCount, fullWidth = false }) => {
  return (
    <Box
      padding={"300"}
      background="bg-surface-secondary"
      borderRadius="200"
    >
      <InlineStack
        wrap
        gap={"300"}
      >
        <Text as="p">
          <PlanLimitItem
            count={productCount}
            label="productsLimitLabel"
            icon={<ProductsIcon />}
          />
        </Text>
        {plan.combinedAddons
          ?.filter((addon) => addon.id && addon.group && ADDON_LABELS[addon.group])
          .sort((a, b) => b.group.localeCompare(a.group))
          .map((addon, idx) => {
            const uniqueKey = `addon-${addon.id || "unknown"}-${addon.group || "unknown"}-${idx}`;
            return (
              <Fragment key={uniqueKey}>
                {fullWidth && idx > 0 && <VerticalDivider />}
                <Text as="p">
                  <PlanLimitItem
                    count={addon.limit}
                    label={ADDON_LABELS[addon.group]}
                    icon={getAddonIcon(addon.group)}
                    interval={addon.interval}
                  />
                </Text>
              </Fragment>
            );
          })}
      </InlineStack>
    </Box>
  );
};

PlanLimits.propTypes = {
  plan: PropTypes.shape({
    combinedAddons: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
        limit: PropTypes.number,
        group: PropTypes.string.isRequired,
        interval: PropTypes.string,
      })
    ),
  }).isRequired,
  productCount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  fullWidth: PropTypes.bool,
};

export default PlanLimits;

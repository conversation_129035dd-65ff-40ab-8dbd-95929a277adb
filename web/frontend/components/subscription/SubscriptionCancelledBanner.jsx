import { sendRequestToCrisp } from "@/utility/crisp.js";
import { Banner, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import appSubscriptionStatus from "storeseo-enums/appSubscriptionStatus";

const SubscriptionCancelledBanner = () => {
  const { t } = useTranslation();
  const user = useSelector((state) => state.user);

  // Check if subscription is cancelled or not being renewed
  const isSubscriptionCancelled =
    user.subscriptionStatus !== appSubscriptionStatus.ACTIVE && user.trialData?.isTrialUser;


  if (!isSubscriptionCancelled) return null;

  const handleChatWithUs = () => {
    const message = "Hey! I think my subscription got cancelled. Can you help me sort this out?";
    sendRequestToCrisp(message);
  };

  return (
    <Banner
      tone="warning"
      title={t("Subscription cancelled")}
      action={{
        content: t("Chat with Us Live"),
        onAction: handleChatWithUs,
      }}
    >
      <Text as="p">
        {t(
          "It looks like your subscription was cancelled or didn't renew successfully. Don't worry — our support team is here to help. Reach out to us, and we'll verify your status and get everything sorted."
        )}
      </Text>
    </Banner>
  );
};

export default SubscriptionCancelledBanner;

import { Badge, BlockStack, Box, InlineStack, Text } from "@shopify/polaris";
import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";

const PlanHeader = ({ plan }) => {
  const { t } = useTranslation();
  const user = useSelector((state) => state.user);

  return (
    <BlockStack gap={"200"}>
      <InlineStack
        gap={"200"}
        blockAlign="center"
        align="start"
      >
        <Text
          as="h4"
          variant="headingLg"
        >
          {t(plan.name)}
        </Text>
        {user?.isSubscribed && plan.isSubscribed ? (
          user?.trialData?.isActive ? (
            <Badge
              tone="attention"
              progress="partiallyComplete"
            >
              {t("In Trial")}
            </Badge>
          ) : (
            <Box as="span">
              <Badge tone="success">{t("Current Plan")}</Badge>
            </Box>
          )
        ) : plan.meta?.info ? (
          <Box as="span">
            <Badge tone="info">{t(plan.meta?.info)}</Badge>
          </Box>
        ) : null}
      </InlineStack>
      <Text
        as="p"
        variant="bodyMd"
        tone="subdued"
      >
        {t(plan.subtitle)}
      </Text>
    </BlockStack>
  );
};

PlanHeader.propTypes = {
  plan: PropTypes.shape({
    name: PropTypes.string.isRequired,
    subtitle: PropTypes.string,
    isSubscribed: PropTypes.bool,
    meta: PropTypes.shape({
      save: PropTypes.string,
      info: PropTypes.string,
    }),
  }).isRequired,
};

export default PlanHeader;

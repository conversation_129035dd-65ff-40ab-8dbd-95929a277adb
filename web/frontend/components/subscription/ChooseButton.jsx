import { <PERSON><PERSON>, <PERSON> } from "@shopify/polaris";
import { isEmpty, isNull } from "lodash";
import PropTypes from "prop-types";
import { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";

const ChooseButton = ({ planName, onAction, variant = "", fullWidth = false, meta, loading = false }) => {
  const { t } = useTranslation();
  const user = useSelector((state) => state.user);

  const isTrialUser = !isEmpty(user.trialData) && user.trialData?.isTrialUser;
  const showTrialButton = isNull(user.subscriptionStatus) && !isTrialUser && !user.isSubscribed && meta?.trialDays > 0;

  // Memoize the translated text to prevent unnecessary re-renders
  // const buttonText = useMemo(() => t("Choose {{PLAN_NAME}}", { PLAN_NAME: planName }), [t, planName]);
  const trialButtonText = useMemo(
    () => t("Start {{NUM_OF}}-day free trial", { NUM_OF: meta?.trialDays }),
    [t, meta?.trialDays]
  );

  return (
    <>
      <Button
        variant={variant || undefined}
        onClick={onAction}
        aria-label={t("Subscribe Now")}
        fullWidth={fullWidth}
        loading={loading}
      >
        {t("Subscribe Now")}
      </Button>
      {showTrialButton && (
        <Link
          monochrome
          onClick={() => onAction({ withTrial: true })}
        >
          {trialButtonText}
        </Link>
      )}
    </>
  );
};

ChooseButton.propTypes = {
  planName: PropTypes.string.isRequired,
  onAction: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  variant: PropTypes.string,
  fullWidth: PropTypes.bool,
};

export default ChooseButton;

// @ts-check
import { BlockStack, Box, Card, Icon, IndexTable, Text, useBreakpoints } from "@shopify/polaris";
import { CheckIcon, XIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";

const WhatsIncludedCard = ({ headings, features }) => {
  const { t } = useTranslation();

  return (
    <Box id="whats-included">
      <Card>
        <BlockStack gap="400">
          <BlockStack gap={"200"}>
            <Text
              as="h5"
              variant="headingSm"
            >
              {t("What’s Included")}
            </Text>

            <Text
              as="p"
              tone="subdued"
            >
              {t(
                "Choose the plan that aligns perfectly with your goals, knowing that as you grow, we'll be right here for you"
              )}
            </Text>
          </BlockStack>

          <Card padding={"0"}>
            <IndexTable
              condensed={useBreakpoints().smDown}
              itemCount={features.length}
              headings={headings.map((heading, idx) => ({
                title: (
                  <Text
                    as="h4"
                    variant="headingXs"
                  >
                    {t(heading)}
                  </Text>
                ),
                alignment: idx > 0 ? "center" : "start",
              }))}
              selectable={false}
              hasZebraStriping
            >
              {features.map((feature, index) => (
                <IndexTable.Row
                  id={`feature-${index}`}
                  key={`feature-row-${index}`}
                  position={index}
                >
                  {feature.slice(1).map((cell, cellIndex) => (
                    <IndexTable.Cell
                      key={`feature-${index}-cell-${cellIndex}`}
                      className={cellIndex !== 0 && "width-100"}
                    >
                      {typeof cell === "boolean" ? (
                        <Icon source={cell ? CheckIcon : XIcon} />
                      ) : (
                        <Text
                          as="p"
                          alignment={cellIndex > 0 ? "center" : "start"}
                          fontWeight={cellIndex === 0 ? "medium" : "regular"}
                        >
                          {t(cell)}
                        </Text>
                      )}
                    </IndexTable.Cell>
                  ))}
                </IndexTable.Row>
              ))}
            </IndexTable>
          </Card>
        </BlockStack>
      </Card>
    </Box>
  );
};

export default WhatsIncludedCard;

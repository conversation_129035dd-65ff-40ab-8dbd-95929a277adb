import { useAppBridgeModal } from "@/hooks/useAppBridgeModal";
import { setCheckout } from "@/store/features/Checkout";
import {
  Badge,
  Bleed,
  BlockStack,
  Box,
  Button,
  Card,
  Collapsible,
  Divider,
  Icon,
  InlineGrid,
  InlineStack,
  Text,
  Tooltip,
} from "@shopify/polaris";
import { CheckIcon, CircleChevronDownIcon, CircleChevronUpIcon, XIcon } from "@shopify/polaris-icons";
import { useState } from "react";
import { Trans, useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { USAGE } from "storeseo-enums/planInterval";
import { SUBSCRIPTION_ADDONS } from "../../config";
import { getPrice, hasDiscount } from "../../utility/helpers.jsx";

export default function PlanCard({ plan, fullWidth = false, basicPlan = {} }) {
  const { t } = useTranslation();
  const [open, setOpen] = useState(!plan.isFree);
  const dispatch = useDispatch();
  const { showCheckoutModal } = useAppBridgeModal();

  const productCount = plan?.products || "Unlimited";

  const Rows = ({ children }) =>
    fullWidth ? (
      <InlineGrid
        columns={{ xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }}
        gap="100"
      >
        {children}
      </InlineGrid>
    ) : (
      <>{children}</>
    );

  const handleChoose = () => {
    dispatch(setCheckout({ slug: plan.slug, coupon_code: plan.coupon_code }));
    showCheckoutModal();
  };

  return (
    <Card>
      <BlockStack gap="400">
        {/* plan header */}
        <InlineStack
          gap="200"
          wrap={false}
          align="space-between"
        >
          <BlockStack gap="200">
            <InlineStack
              gap="100"
              align="start"
            >
              <Text
                as="h3"
                variant="headingMd"
              >
                {t(plan.name)}
              </Text>
              {plan.isFree && (
                <Button
                  variant="monochromePlain"
                  icon={open ? CircleChevronUpIcon : CircleChevronDownIcon}
                  onClick={() => setOpen(!open)}
                />
              )}
              {plan.isSubscribed ? (
                <Tooltip content={t("Current Plan")}>
                  <Badge tone="success">{t("Active")}</Badge>
                </Tooltip>
              ) : plan.meta?.save ? (
                <Badge tone="attention">{t("Save {{PERCENT_VALUE}}", { PERCENT_VALUE: plan.meta?.save })}</Badge>
              ) : null}
            </InlineStack>
            <Text as="p">
              <Trans
                t={t}
                i18nKey="productsLimitInPlan"
                productCount={productCount}
              >
                <strong>{{ productCount }}</strong> products
              </Trans>
            </Text>
          </BlockStack>
          <InlineStack
            gap="400"
            align="end"
            blockAlign="center"
          >
            {!plan.isFree && (
              <BlockStack>
                {hasDiscount(plan) && (
                  <Text
                    variant="headingSm"
                    as="p"
                    textDecorationLine="line-through"
                    tone="subdued"
                    alignment="end"
                  >
                    ${plan.price.toFixed(2)}
                  </Text>
                )}
                <Text
                  variant="headingLg"
                  as="h4"
                  alignment="end"
                >
                  ${getPrice(plan)}
                  <Text
                    as="span"
                    tone="subdued"
                    variant="bodySm"
                  >
                    {plan.intervalText && `/${t(plan.intervalText).substring(0, 1).toUpperCase()}`}
                  </Text>
                </Text>
                {plan.description && plan.interval === USAGE ? (
                  <Text
                    variant="bodySm"
                    as="p"
                    tone="subdued"
                    alignment="end"
                  >
                    *{t(plan.description)}
                  </Text>
                ) : null}
              </BlockStack>
            )}

            {fullWidth && (
              <BlockStack align="center">
                {plan.isFree ? (
                  <InlineStack gap="200">
                    <Button
                      variant="primary"
                      onClick={handleChoose}
                    >
                      {t("Continue with Free plan")}
                    </Button>
                    {/* <Button url={`/checkout/${plan.slug}`}>{t("Get started with limited features")}</Button> */}
                    {/* <SubscriptionButton
                      plan={plan}
                      buttonText="Get started with Free plan"
                      // isPrimaryBtn={false}
                    /> */}

                    {/* <SubscriptionButton
                      plan={basicPlan}
                      buttonText={`Access All Features at $${basicPlan?.subtotal}/${basicPlan?.interval?.slice(
                        0,
                        1
                      )} Only`}
                    /> */}
                  </InlineStack>
                ) : (
                  // <SubscriptionButton
                  //   plan={plan}
                  //   fullWidth
                  // />

                  <Button
                    variant="primary"
                    onClick={handleChoose}
                  >
                    {t("Choose")}
                  </Button>
                )}
              </BlockStack>
            )}
          </InlineStack>
        </InlineStack>
        <Collapsible
          open={open}
          id={`collapsible-${plan.slug}`}
          transition={{ duration: "300ms", timingFunction: "ease-in-out" }}
          expandOnPrint
        >
          <BlockStack gap="400">
            {/* Plan body */}
            <Bleed marginInline="400">
              <Divider />
            </Bleed>
            <BlockStack gap="100">
              <Rows>
                {/* {!plan.isFree && <PlanFeatureItem title="Everything in Free" />} */}
                {plan.features.map((feature) => (
                  <PlanFeatureItem
                    key={feature.key}
                    title={feature.title}
                    enabled={feature.value}
                  />
                ))}
              </Rows>
            </BlockStack>

            {/* Addons */}
            <Bleed marginInline="400">
              <Divider />
            </Bleed>

            <Box
              padding="400"
              background="bg-fill-hover"
              borderRadius="400"
            >
              <BlockStack gap="200">
                <Text variant="headingMd">{t("Available Add-on")}</Text>
                {!fullWidth ? (
                  <BlockStack gap="100">
                    {SUBSCRIPTION_ADDONS.map((addon, idx) => (
                      <PlanAddonItem
                        key={addon.key}
                        title={addon.title}
                        icon={addon.icon}
                        type={addon.type}
                      />
                    ))}
                  </BlockStack>
                ) : (
                  <InlineStack gap="400">
                    {SUBSCRIPTION_ADDONS.map((addon, idx) => (
                      <PlanAddonItem
                        key={addon.key}
                        title={addon.title}
                        icon={addon.icon}
                        type={addon.type}
                      />
                    ))}
                  </InlineStack>
                )}
              </BlockStack>
            </Box>

            {!fullWidth && (
              // <SubscriptionButton
              //   plan={plan}
              //   fullWidth
              // />
              <Button
                variant="primary"
                onClick={handleChoose}
              >
                {t("Choose")}
              </Button>
            )}
          </BlockStack>
        </Collapsible>
      </BlockStack>
    </Card>
  );
}

const PlanAddonItem = ({ title, icon }) => {
  const { t } = useTranslation();

  return (
    <InlineStack
      gap="200"
      wrap={false}
    >
      <span>
        <Icon source={icon} />
      </span>
      <Text as="p">{t(title)}</Text>
      {/* <Badge tone="info">{t("Add-on")}</Badge> */}
    </InlineStack>
  );
};

const PlanFeatureItem = ({ title, enabled = true }) => {
  const { t } = useTranslation();

  return (
    <InlineStack
      gap="200"
      wrap={false}
    >
      <Box>
        <Icon
          source={enabled ? CheckIcon : XIcon}
          tone="base"
          // tone={enabled ? "success" : "critical"}
          accessibilityLabel={enabled ? "Check mark" : "Cross mark"}
        />
      </Box>
      <Text as="p">{t(title)}</Text>
    </InlineStack>
  );
};

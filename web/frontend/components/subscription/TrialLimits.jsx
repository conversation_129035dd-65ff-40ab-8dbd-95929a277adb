import { Box, InlineStack, Text } from "@shopify/polaris";
import { Fragment } from "react";
import SubscriptionAddonGroup from "storeseo-enums/subscriptionAddonGroup";
import AiContentOptimizerIcon from "../svg/AiContentOptimizerIcon";
import ImageOptimizerIcon from "../svg/ImageOptimizerIcon";
import ProductsIcon from "../svg/ProductsIcon";
import PlanLimitItem from "./PlanLimitItem";
import VerticalDivider from "./VerticalDivider";

const ADDON_ICONS = {
  products: <ProductsIcon size={16} />,
  [SubscriptionAddonGroup.IMAGE_OPTIMIZER.toLocaleLowerCase()]: <ImageOptimizerIcon size={16} />,
  [SubscriptionAddonGroup.AI_OPTIMIZER.toLocaleLowerCase()]: <AiContentOptimizerIcon size={16} />,
};

const ADDON_LABELS = {
  products: "productsTrialLimitLabel",
  [SubscriptionAddonGroup.IMAGE_OPTIMIZER.toLocaleLowerCase()]: "imageOptimizerPlanLabel",
  [SubscriptionAddonGroup.AI_OPTIMIZER.toLocaleLowerCase()]: "aiContentOptimizerPlanLabel",
};

/**
 * A component that renders a subscription plan card with pricing and features
 * @component
 * @param {Object} props - The component props
 * @param {{products: number, image_optimizer: number, ai_optimizer: number}} props.limits - The plan object containing subscription details
 * @param {number} props.duration - The plan object containing subscription details
 */
const TrialLimits = ({ limits, duration }) => {
  const trailLimits = Object.entries(limits).map(([key, value]) => ({
    id: key,
    limit: value,
    group: key,
    interval: "day",
  }));

  return (
    <Box
      padding={"300"}
      background="bg-surface-secondary"
      borderRadius="200"
    >
      <InlineStack
        wrap
        gap={"300"}
      >
        {trailLimits
          .sort((a, b) => b.group.localeCompare(a.group))
          .map((addon, idx) => {
            return (
              <Fragment key={addon.id}>
                {idx > 0 && idx < trailLimits.length && <VerticalDivider />}
                <Text as="p">
                  <PlanLimitItem
                    count={addon.limit}
                    label={ADDON_LABELS[addon.group]}
                    icon={ADDON_ICONS[addon.group]}
                    interval={addon.interval}
                  />
                </Text>
              </Fragment>
            );
          })}
      </InlineStack>
    </Box>
  );
};

export default TrialLimits;

import { formatPrice, getPrice, hasDiscount } from "@/utility/helpers";
import { Text } from "@shopify/polaris";
import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";

const PlanPrice = ({ plan }) => {
  const { t } = useTranslation();
  return (
    <>
      <Text
        variant="headingLg"
        as="h4"
        alignment="end"
      >
        {formatPrice(getPrice(plan))}
        <Text
          as="span"
          tone="subdued"
          variant="bodySm"
        >
          {plan.intervalText && `/${t(plan.intervalText).substring(0, 1).toUpperCase()}`}
        </Text>
      </Text>
      {hasDiscount(plan) && (
        <Text
          variant="headingSm"
          as="p"
          textDecorationLine="line-through"
          tone="subdued"
          alignment="end"
        >
          {formatPrice(plan.price)}
        </Text>
      )}
    </>
  );
};

PlanPrice.propTypes = {
  plan: PropTypes.shape({
    price: PropTypes.number.isRequired,
    intervalText: PropTypes.string,
  }).isRequired,
};

export default PlanPrice;

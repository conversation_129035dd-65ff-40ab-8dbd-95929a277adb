import { useSubscribeNowAction } from "@/lib/hooks/subscription";
import { <PERSON>, Button, InlineStack, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";

const TrialUpgradeBanner = () => {
  const { t } = useTranslation();
  const subscribeNow = useSubscribeNowAction();
  const user = useSelector((state) => state.user);

  const isTrialActive = !!user.trialData?.isActive;

  if (!isTrialActive || !user.isSubscribed) return null;

  return (
    <Banner tone="warning">
      <InlineStack
        align="start"
        gap="100"
      >
        <Text
          as="p"
          tone="subdued"
          variant="bodyMd"
        >
          {t("You have only {{DAYS_LEFT}} days left in your free trial.", { DAYS_LEFT: user.trialData?.remainingDays })}
        </Text>
        <Text
          as="p"
          tone="subdued"
          variant="headingSm"
        >
          {t("To continue using all the advanced features without limits.")}
        </Text>
        <Button
          onClick={() => subscribeNow.mutate()}
          loading={subscribeNow.isLoading}
          variant="plain"
        >
          {t("Subscribe now")}
        </Button>
      </InlineStack>
    </Banner>
  );
};

export default TrialUpgradeBanner;

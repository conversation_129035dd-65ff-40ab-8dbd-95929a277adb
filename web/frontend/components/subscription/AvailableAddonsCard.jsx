// @ts-check
import { <PERSON>leed, BlockStack, Box, Card, Divider, InlineGrid, InlineStack, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import SubscriptionAddonGroup from "storeseo-enums/subscriptionAddonGroup";
import SubscriptionAddonInterval from "storeseo-enums/subscriptionAddonInterval";
import AiContentOptimizerIcon from "../svg/AiContentOptimizerIcon";
import ImageOptimizerIcon from "../svg/ImageOptimizerIcon";

const getAddonIcon = (key) => {
  switch (key) {
    case SubscriptionAddonGroup.IMAGE_OPTIMIZER:
      return <ImageOptimizerIcon />;
    case SubscriptionAddonGroup.AI_OPTIMIZER:
      return <AiContentOptimizerIcon />;
    default:
      return null;
  }
};

export const SUBSCRIPTION_ADDONS = [
  {
    key: SubscriptionAddonGroup.IMAGE_OPTIMIZER,
    title: "Advanced Image Optimizer",
    desc: "Boost performance with AI-powered image optimization—faster load times, better SEO, and crisp visuals.",
    type: SubscriptionAddonInterval.MONTHLY,
  },
  {
    key: SubscriptionAddonGroup.AI_OPTIMIZER,
    title: "Powerful AI Content Optimizer",
    desc: "An AI-powered feature that optimizes content effortlessly, boosts SEO and rankings by saving time, driving results.",
    type: SubscriptionAddonInterval.CREDIT,
  },
];

const AvailableAddonsCard = () => {
  const { t } = useTranslation();

  return (
    <Card>
      <BlockStack gap="400">
        <Text
          as="h4"
          variant="headingLg"
        >
          {t("Available Add-ons")}
        </Text>
        <Bleed marginInline={"400"}>
          <Divider />
        </Bleed>

        <InlineGrid
          columns={{ xs: 1, sm: 2, md: 2 }}
          gap={"400"}
        >
          {SUBSCRIPTION_ADDONS.map((addon, idx) => (
            <InlineStack
              key={addon.key || `addon-${idx}`}
              wrap={false}
              gap={"400"}
            >
              <Box as="span">
                <Box
                  as="div"
                  width="48px"
                  minHeight="48px"
                  padding={"200"}
                  borderRadius={"200"}
                  borderWidth={"025"}
                  borderColor="border"
                >
                  <InlineStack
                    align="center"
                    blockAlign="center"
                  >
                    {getAddonIcon(addon.key)}
                  </InlineStack>
                </Box>
              </Box>
              <BlockStack gap={"300"}>
                <Text
                  as="h5"
                  variant="headingMd"
                >
                  {t(addon.title)}
                </Text>
                <Text
                  as="p"
                  tone="subdued"
                >
                  {t(addon.desc)}
                </Text>
              </BlockStack>
            </InlineStack>
          ))}
        </InlineGrid>
      </BlockStack>
    </Card>
  );
};

export default AvailableAddonsCard;

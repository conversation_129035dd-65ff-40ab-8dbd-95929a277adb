// @ts-check
import {
  BlockStack,
  Box,
  Button,
  ButtonGroup,
  Card,
  Divider,
  Grid,
  Icon,
  InlineStack,
  Link,
  Text,
} from "@shopify/polaris";
import { useState } from "react";
import { Trans, useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
// @ts-ignore
import { ArrowDownIcon, InfoIcon } from "@shopify/polaris-icons";
// @ts-ignore
import { capitalizedString, getQueryFromUrlSearchParam } from "@/utility/helpers.jsx";
import planInterval from "storeseo-enums/planInterval";
import { useSubscriptionApi } from "../../hooks/apiHooks/useSubscriptionApi.js";
import { useUserApi } from "../../hooks/apiHooks/useUserApi.js";
import { useAppQuery } from "../../hooks/index.js";
import UseConfirmation from "../../hooks/useConfirmation.jsx";
import { updateUser } from "../../store/features/User.js";
import SubscriptionV2ContentLoader from "../loader/SubscriptionV2ContentLoader.jsx";
import AvailableAddonsCard from "./AvailableAddonsCard.jsx";
import NewPlanCard from "./NewPlanCard.jsx";
import SubscriptionCancelledBanner from "./SubscriptionCancelledBanner.jsx";
import TrialUpgradeBanner from "./TrialUpgradeBanner.jsx";
import WhatsIncludedCard from "./WhatsIncludedCard.jsx";
const { ANNUALLY, MONTHLY, USAGE } = planInterval;

const SubscriptionV2Contents = () => {
  const { t } = useTranslation();
  const subscriptionApi = useSubscriptionApi();
  const userApi = useUserApi();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams, setSearchParams] = useSearchParams();
  const query = getQueryFromUrlSearchParam(searchParams);

  const [showInterval, setShowInterval] = useState(MONTHLY);
  // @ts-ignore
  const user = useSelector((state) => state.user);

  const { data: { plans = [], features = [], headings = [], activePlan } = {}, isFetching } = useAppQuery({
    queryKey: ["SUBSCRIPTION_DATA", user.planId, query],
    queryFn: () => subscriptionApi.getSubscriptionData(query),
    reactQueryOptions: {
      // staleTime: 0,
      // refetchInterval: false,
      // onSuccess: (data) => {
      //   setPlans(data.plans);
      // },
    },
  });

  const allPlans = plans.filter((plan) => plan.interval === showInterval);
  const visiblePlans = allPlans.slice(0, Math.floor(allPlans.length / 3) * 3);
  const remainingPlans = allPlans.slice(Math.floor(allPlans.length / 3) * 3);

  const visionaryPlan = plans.find((plan) => plan.interval === USAGE);
  const freePlan = plans.find((plan) => plan.type === "FREE");

  const isLegaceyPlan = !plans.some((plan) => plan.id === user.planId);

  const { planName, planInterval } = user;
  const PLAN_NAME = `${planName}(${capitalizedString(planInterval)})`;

  const { renderConfirmation, showConfirmation, hideConfirmation } = UseConfirmation();
  const { mutate: cancelSubscription, isLoading: isCancelling } = useMutation({
    mutationFn: subscriptionApi.handleSubscriptionCancel,
    onSuccess: async () => {
      hideConfirmation();
      userApi.getAuthUserData().then(({ user }) => {
        dispatch(updateUser({ ...user, isNewlyUpgraded: false }));
      });
      navigate("/onboarding/pricing");
    },
  });

  if (isFetching) return <SubscriptionV2ContentLoader />;

  return (
    <BlockStack gap="400">
      <SubscriptionCancelledBanner />
      <TrialUpgradeBanner />
      <BlockStack gap="400">
        {query?.type === "grandfathered" && (
          <InlineStack align="center">
            <ButtonGroup variant="segmented">
              <Button
                onClick={() => setShowInterval(MONTHLY)}
                pressed={showInterval === MONTHLY}
              >
                {t("Monthly")}
              </Button>
              <Button
                onClick={() => setShowInterval(ANNUALLY)}
                pressed={showInterval === ANNUALLY}
              >
                {t("Yearly")}
              </Button>
            </ButtonGroup>
          </InlineStack>
        )}

        {isLegaceyPlan && user.planId && (
          <Card>
            <InlineStack
              gap={"200"}
              align="start"
              blockAlign="center"
            >
              <Box>
                <Icon source={InfoIcon} />
              </Box>
              <Box>
                <Trans
                  i18nKey="leagecyPlanBannerContent"
                  values={{ PLAN_NAME }}
                />
              </Box>
            </InlineStack>
          </Card>
        )}

        <Grid columns={{ xs: 1, sm: 1, md: 2, lg: 3 }}>
          {visiblePlans.map((plan, index) => (
            <Grid.Cell key={plan.slug || `visible-plan-${index}`}>
              <NewPlanCard plan={plan} />
            </Grid.Cell>
          ))}
        </Grid>

        {remainingPlans.length > 0 &&
          remainingPlans.map((plan, index) => (
            <NewPlanCard
              key={plan.slug || `remaining-plan-${index}`}
              plan={plan}
              fullWidth
            />
          ))}

        {visionaryPlan && (
          <NewPlanCard
            plan={visionaryPlan}
            fullWidth
          />
        )}

        {freePlan && location.pathname === "/subscription" && (
          <NewPlanCard
            plan={freePlan}
            fullWidth
          />
        )}

        <InlineStack
          gap={"400"}
          wrap={false}
          align="center"
          blockAlign="center"
        >
          <Box width="40%">
            <Divider borderColor="border-tertiary" />
          </Box>
          <Button
            variant="plain"
            icon={ArrowDownIcon}
            url="#whats-included"
          >
            {t("See plan comparison")}
          </Button>
          <Box width="40%">
            <Divider borderColor="border-tertiary" />
          </Box>
        </InlineStack>

        <AvailableAddonsCard />

        <WhatsIncludedCard
          headings={headings}
          features={features}
        />

        {user.isPremium && (
          <Text as="p">
            <Link
              monochrome
              onClick={showConfirmation}
            >
              {t("Click here")}
            </Link>{" "}
            {t("to")} <Text as="strong">{t("cancel your subscription plan")}</Text>
          </Text>
        )}
      </BlockStack>
      {renderConfirmation({
        title: "Cancel subscription!",
        content:
          "Are you sure you want to cancel your subscription? This action will take effect immediately, and you will lose access to all features of the app.",
        primaryAction: cancelSubscription,
        primaryActionIsDestructive: true,
        primaryActionText: "Yes, cancel!",
        loading: isCancelling,
      })}
    </BlockStack>
  );
};

export default SubscriptionV2Contents;

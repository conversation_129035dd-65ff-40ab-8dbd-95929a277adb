import { formatNumber } from "@/utility/helpers";
import { BlockStack, Box, InlineStack, Text } from "@shopify/polaris";
import PropTypes from "prop-types";
import { Trans, useTranslation } from "react-i18next";
import SubscriptionAddonInterval from "storeseo-enums/subscriptionAddonInterval";

const PlanLimitItem = ({ count, label, icon, interval }) => {
  const { t } = useTranslation();
  return (
    <InlineStack
      gap={"200"}
      align="start"
      blockAlign="center"
      wrap={false}
    >
      {icon && (
        <Box width="16px">
          <BlockStack
            align="center"
            inlineAlign="center"
          >
            {icon}
          </BlockStack>
        </Box>
      )}
      <Text
        as="span"
        tone="subdued"
      >
        <Trans
          i18nKey={label}
          count={typeof count === "number" ? formatNumber(count) : count}
        />
        {interval === SubscriptionAddonInterval.LIFETIME && t(" (onetime)")}
      </Text>
    </InlineStack>
  );
};

PlanLimitItem.propTypes = {
  count: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  label: PropTypes.string.isRequired,
  icon: PropTypes.element,
  interval: PropTypes.string,
};

export default PlanLimitItem;

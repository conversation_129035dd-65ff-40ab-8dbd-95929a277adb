import { Button } from "@shopify/polaris";
import { useTranslation } from "react-i18next";

const SubscriptionButton = ({ plan, fullWidth = false, isPrimaryBtn = true, buttonText = "Choose" }) => {
  const { t } = useTranslation();

  const route = `/checkout/${plan.slug}`.concat(plan.isDiscountApplicable ? `?coupon=${plan.coupon_code}` : "");

  return (
    <Button
      url={route}
      variant={isPrimaryBtn ? "primary" : "secondary"}
      fullWidth={fullWidth}
    >
      {t(buttonText)}
    </Button>
  );
};

export default SubscriptionButton;

//@ts-check
import { useUnsavedChanges } from "@/hooks/useUnsavedChanges";
import { useLocalSEOLocalBusinessSchema, useUpdateLocalSEOLocalBusinessSchema } from "@/lib/hooks/local-seo";
import { defaultValues } from "@/lib/form-default-value/localSEOLocalBusinessConst";
import { yupResolver } from "@hookform/resolvers/yup";
import { BlockStack, Button, InlineStack } from "@shopify/polaris";
import { Form, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import BasicInformationFormFields from "../OrganizationSchema/BasicInformationFormFields";
import AddressFormFields from "./AddressFormFields";
import WorkingDaysFormFields from "./WorkingDaysFormFields";
import { localBusinessSchema } from "storeseo-schema/local-seo/localBusinessSchema";
import { useEffect } from "react";
import ContextualSaveBar from "@/modules/components/ContextualSaveBar";

/**
 * @param {{isDisable: boolean}} props
 * @returns
 */
export default function LocalBusinessInformationForm(props) {
  const { isDisable } = props;
  const { t } = useTranslation();
  const { data: localBusinessInformationData, isLoading } = useLocalSEOLocalBusinessSchema();
  const { mutate, isLoading: isUpdateLoading, isError, error } = useUpdateLocalSEOLocalBusinessSchema();

  const { watch, control, setError, handleSubmit, reset } = useForm({
    resolver: yupResolver(localBusinessSchema),
    defaultValues,
    values: {
      basicInformation: {
        businessName: localBusinessInformationData?.settings?.basicInformation?.businessName || "",
        businessType: localBusinessInformationData?.settings?.basicInformation?.businessType || "",
        businessPriceRange: localBusinessInformationData?.settings?.basicInformation?.businessPriceRange || "",
      },
      address: {
        streetAddress: localBusinessInformationData?.settings?.address?.streetAddress || "",
        locality: localBusinessInformationData?.settings?.address?.locality || "",
        region: localBusinessInformationData?.settings?.address?.region || "",
        postalCode: localBusinessInformationData?.settings?.address?.postalCode || "",
        telephone: localBusinessInformationData?.settings?.address?.telephone || "",
      },
      workingDays: localBusinessInformationData?.settings?.workingDays?.length
        ? localBusinessInformationData?.settings?.workingDays
        : [],
    },
  });

  useEffect(() => {
    if (isError) {
      // @ts-ignore
      Object.keys(error.errors).map((key) =>
        // @ts-ignore
        setError(key.replace("settings.", ""), {
          // @ts-ignore
          message: error.errors[key] ?? t("Something went wrong"),
        })
      );
    }
  }, [isError, error]);

  /**
   *
   * @param {import("yup").InferType<typeof localBusinessSchema>} data
   * return {void}
   */
  const onSubmit = (data) => {
    mutate({ ...localBusinessInformationData, settings: data });
  };

  const { DiscardChangesModal, hasUnsavedChanges, setShowDiscardChangeModal } = useUnsavedChanges({
    originalData: isDisable ? defaultValues : { ...defaultValues, ...localBusinessInformationData?.settings },
    currentData: isDisable ? defaultValues : watch(),
    onDiscardAction: reset,
  });
  return (
    <>
      <DiscardChangesModal />

      <ContextualSaveBar
        id="local-seo-local-business-schema"
        open={hasUnsavedChanges}
        isLoading={isUpdateLoading}
        onSave={handleSubmit(onSubmit)}
        onDiscard={() => setShowDiscardChangeModal(true)}
      />

      <Form
        control={control}
        onSubmit={({ data }) => {
          onSubmit(data);
        }}
      >
        <BlockStack gap="400">
          <BasicInformationFormFields
            control={control}
            isDisable={isDisable}
          />

          <AddressFormFields
            control={control}
            isDisable={isDisable}
          />

          <WorkingDaysFormFields
            control={control}
            isDisable={isDisable}
          />

          <InlineStack align="end">
            <Button
              variant="primary"
              loading={isLoading || isUpdateLoading}
              disabled={isDisable || !hasUnsavedChanges}
              submit
            >
              {t("Save")}
            </Button>
          </InlineStack>
        </BlockStack>
      </Form>
    </>
  );
}

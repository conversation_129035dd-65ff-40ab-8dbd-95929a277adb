//@ts-check

import { dayRangeOptions, openingAndEndingTimes } from "@/lib/util/localSEOLocalBusinessSchema";
import { Card, BlockStack, Grid, Select, InlineStack, Button, Text, Box, Icon } from "@shopify/polaris";
import { PlusIcon } from "@shopify/polaris-icons";
import { Controller, useFieldArray } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { localBusinessSchema } from "storeseo-schema/local-seo/localBusinessSchema";

/**
 *
 * @param {{control: import('react-hook-form').Control<import("yup").InferType<typeof localBusinessSchema>>, isDisable: boolean}} props
 * @returns
 */
export default function WorkingDaysFormFields(props) {
  const { control, isDisable } = props;
  const { t } = useTranslation();
  const { fields, append, remove } = useFieldArray({
    control,
    name: "workingDays",
  });

  return (
    <Card>
      <BlockStack gap="400">
        <Text
          as="p"
          variant="headingMd"
        >
          {t("Working Days")}
        </Text>

        {/* Working days form fields */}
        {fields.map((field, index) => (
          <Card
            padding="200"
            key={field.id}
          >
            <BlockStack gap="200">
              <InlineStack
                blockAlign="center"
                align="space-between"
              >
                <Text
                  as="p"
                  variant="headingMd"
                >
                  {t("Working day {{dayCount}}", { dayCount: index + 1 })}
                </Text>
                <Button
                  variant="plain"
                  tone="critical"
                  onClick={() => remove(index)}
                  disabled={isDisable}
                >
                  {t("Remove")}
                </Button>
              </InlineStack>
              <Grid columns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}>
                <Controller
                  name={`workingDays.${index}.day`}
                  control={control}
                  disabled={isDisable}
                  render={({ field, fieldState: { error } }) => (
                    <Select
                      label="Day"
                      options={dayRangeOptions}
                      {...field}
                      onChange={(value) => field.onChange(value)}
                      error={error?.message}
                    />
                  )}
                />

                <Controller
                  name={`workingDays.${index}.openingTime`}
                  control={control}
                  disabled={isDisable}
                  render={({ field, fieldState: { error } }) => (
                    <Select
                      label="Opening time"
                      options={openingAndEndingTimes}
                      {...field}
                      onChange={(value) => field.onChange(value)}
                      error={error?.message}
                    />
                  )}
                />

                <Controller
                  name={`workingDays.${index}.closingTime`}
                  control={control}
                  disabled={isDisable}
                  render={({ field, fieldState: { error } }) => (
                    <Select
                      label="Closing time"
                      options={openingAndEndingTimes}
                      {...field}
                      onChange={(value) => field.onChange(value)}
                      error={error?.message}
                    />
                  )}
                />
              </Grid>
            </BlockStack>
          </Card>
        ))}

        {/* Add more working day action */}
        <InlineStack align="center">
          <Button
            icon={PlusIcon}
            variant="tertiary"
            disabled={isDisable}
            onClick={() =>
              append({
                day: dayRangeOptions[0].value,
                openingTime: openingAndEndingTimes[0].value,
                closingTime: openingAndEndingTimes[0].value,
              })
            }
          >
            {t("Add working day")}
          </Button>
        </InlineStack>
      </BlockStack>
    </Card>
  );
}

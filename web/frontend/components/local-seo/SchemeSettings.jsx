//@ts-check
import { <PERSON>ge, Banner, BlockStack, Button, Card, InlineStack, Link, Text } from "@shopify/polaris";

import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import TooltipWrapper from "../common/TooltipWrapper";

/**
 *
 * @param {{title: string, description: string, isOn: boolean, onHandle: () => void, action?: string, isLoading?: boolean, isDisable?: boolean, disableMessage?: string}} param0
 * @returns
 */
export const SchemeSettings = ({
  title,
  description,
  isOn,
  onHandle,
  action,
  isLoading = false,
  isDisable = false,
  disableMessage,
}) => {
  const { t } = useTranslation();
  // @ts-ignore
  const user = useSelector((state) => state.user);

  return (
    <Card>
      <BlockStack gap="300">
        <InlineStack blockAlign="start">
          <div style={{ flex: 1 }}>
            <BlockStack gap="300">
              <InlineStack
                blockAlign="center"
                gap="200"
              >
                <Text
                  as="h3"
                  variant="headingSm"
                >
                  {t(title)}
                </Text>
                <Badge tone={isOn ? "success" : "enabled"}>{isOn ? "On" : "Off"}</Badge>
              </InlineStack>
              <Text
                as="p"
                variant="bodyMd"
              >
                {t(description)}&nbsp;
                {action && (
                  <Link
                    url={action}
                    target="_blank"
                  >
                    {t("Know more")}
                  </Link>
                )}
              </Text>
            </BlockStack>
          </div>
          <TooltipWrapper
            show={!!disableMessage && isDisable && !isLoading}
            content={disableMessage}
          >
            <Button
              onClick={onHandle}
              loading={isLoading}
              disabled={!user.isPremium || isDisable}
            >
              {!isOn ? t("Turn on") : t("Turn off")}
            </Button>
          </TooltipWrapper>
        </InlineStack>
        {isDisable && disableMessage && !isLoading && (
          <Banner tone="info">
            <Text as="p">{disableMessage}</Text>
          </Banner>
        )}
      </BlockStack>
    </Card>
  );
};

export default SchemeSettings;

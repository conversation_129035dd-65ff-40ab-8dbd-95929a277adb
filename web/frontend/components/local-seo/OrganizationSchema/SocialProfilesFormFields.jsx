//@ts-check

import { <PERSON>, BlockStack, Grid, TextField, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { Controller } from "react-hook-form";
import { organizationSchema } from "storeseo-schema/local-seo/organizationSchema";

/**
 *
 * @param {{control: import('react-hook-form').Control<import("yup").InferType<typeof organizationSchema>>, isDisable: boolean}} props
 * @returns
 */
export default function SocialProfilesFormFields(props) {
  const { control, isDisable } = props;
  const { t } = useTranslation();
  return (
    <Card>
      <BlockStack gap="300">
        <Text
          as="h3"
          variant="headingMd"
        >
          {t("Social Profile URLs")}
        </Text>
        <BlockStack gap="300">
          <Grid columns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}>
            <Controller
              name="socialProfiles.twitter"
              control={control}
              disabled={isDisable}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  label={t("Twitter Profile URL")}
                  autoComplete="off"
                  {...field}
                  onChange={(value) => field.onChange(value)}
                  error={error?.message}
                />
              )}
            />

            <Controller
              name="socialProfiles.facebook"
              control={control}
              disabled={isDisable}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  label={t("Facebook Profile URL")}
                  autoComplete="off"
                  {...field}
                  onChange={(value) => field.onChange(value)}
                  error={error?.message}
                />
              )}
            />

            <Controller
              name="socialProfiles.instagram"
              control={control}
              disabled={isDisable}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  label={t("Instagram Profile URL")}
                  autoComplete="off"
                  {...field}
                  onChange={(value) => field.onChange(value)}
                  error={error?.message}
                />
              )}
            />
          </Grid>

          <Grid columns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 2 }}>
            <Controller
              name="socialProfiles.pinterest"
              control={control}
              disabled={isDisable}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  label={t("Pinterest Profile URL")}
                  autoComplete="off"
                  {...field}
                  onChange={(value) => field.onChange(value)}
                  error={error?.message}
                />
              )}
            />

            <Controller
              name="socialProfiles.linkedin"
              control={control}
              disabled={isDisable}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  label={t("Linkedin Profile URL")}
                  autoComplete="off"
                  {...field}
                  onChange={(value) => field.onChange(value)}
                  error={error?.message}
                />
              )}
            />
          </Grid>
        </BlockStack>
      </BlockStack>
    </Card>
  );
}

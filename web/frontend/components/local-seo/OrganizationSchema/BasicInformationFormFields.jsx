//@ts-check
import { <PERSON>, BlockStack, TextField, Grid, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { Controller } from "react-hook-form";
import { organizationSchema } from "storeseo-schema/local-seo/organizationSchema";

/**
 *
 * @param {{control: import('react-hook-form').Control<import("yup").InferType<typeof organizationSchema>>, isDisable: boolean}} props
 * @returns
 */
export default function BasicInformationFormFields(props) {
  const { control, isDisable } = props;
  const { t } = useTranslation();

  return (
    <Card>
      <BlockStack gap="300">
        <Text
          as="h3"
          variant="headingMd"
        >
          {t("Basic Information")}
        </Text>
        <BlockStack gap="300">
          <Controller
            name="basicInformation.businessName"
            control={control}
            disabled={isDisable}
            render={({ field, fieldState: { error } }) => (
              <TextField
                label={t("Business name")}
                autoComplete="off"
                {...field}
                onChange={(value) => field.onChange(value)}
                error={error?.message}
              />
            )}
          />

          <Grid columns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 2 }}>
            <Controller
              name="basicInformation.businessType"
              control={control}
              disabled={isDisable}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  label={t("Business Type")}
                  autoComplete="off"
                  {...field}
                  onChange={(value) => field.onChange(value)}
                  error={error?.message}
                />
              )}
            />

            <Controller
              name="basicInformation.businessPriceRange"
              control={control}
              disabled={isDisable}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  label={t("Price Range")}
                  autoComplete="off"
                  placeholder={t("(ex. $$$)")}
                  {...field}
                  onChange={(value) => field.onChange(value)}
                  error={error?.message}
                />
              )}
            />
          </Grid>
        </BlockStack>
      </BlockStack>
    </Card>
  );
}

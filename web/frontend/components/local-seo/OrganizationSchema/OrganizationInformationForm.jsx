//@ts-check
import BasicInformationFormFields from "./BasicInformationFormFields";
import AddressFormFields from "./AddressFormFields";
import SocialProfilesFormFields from "./SocialProfilesFormFields";
import { BlockStack, Button, InlineStack } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { Form, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { defaultValues } from "@/lib/form-default-value/localSEOOrganizationConst";
import { useUnsavedChanges } from "@/hooks/useUnsavedChanges";
import { useLocalSEOOrganizationSchema, useUpdateLocalSEOOrganizationSchema } from "@/lib/hooks/local-seo";
import ContextualSaveBar from "@/modules/components/ContextualSaveBar";
import { organizationSchema } from "storeseo-schema/local-seo/organizationSchema";
import { useEffect } from "react";

/**
 * @param {{isDisable: boolean}} props
 * @returns
 */
export default function OrganizationInformationForm(props) {
  const { isDisable } = props;
  const { t } = useTranslation();
  const { data: organizationInformationData, isLoading } = useLocalSEOOrganizationSchema();
  const { mutate, isLoading: isUpdateLoading, isError, error } = useUpdateLocalSEOOrganizationSchema();

  const { watch, control, setError, handleSubmit, reset } = useForm({
    resolver: yupResolver(organizationSchema),
    defaultValues,
    values: {
      basicInformation: {
        businessName: organizationInformationData?.settings?.basicInformation?.businessName || "",
        businessType: organizationInformationData?.settings?.basicInformation?.businessType || "",
        businessPriceRange: organizationInformationData?.settings?.basicInformation?.businessPriceRange || "",
      },
      address: {
        streetAddress: organizationInformationData?.settings?.address?.streetAddress || "",
        city: organizationInformationData?.settings?.address?.city || "",
        state: organizationInformationData?.settings?.address?.state || "",
        country: organizationInformationData?.settings?.address?.country || "",
        postalCode: organizationInformationData?.settings?.address?.postalCode || "",
        telephone: organizationInformationData?.settings?.address?.telephone || "",
      },
      socialProfiles: {
        twitter: organizationInformationData?.settings?.socialProfiles?.twitter || "",
        facebook: organizationInformationData?.settings?.socialProfiles?.facebook || "",
        instagram: organizationInformationData?.settings?.socialProfiles?.instagram || "",
        pinterest: organizationInformationData?.settings?.socialProfiles?.pinterest || "",
        linkedin: organizationInformationData?.settings?.socialProfiles?.linkedin || "",
      },
    },
  });

  useEffect(() => {
    if (isError) {
      // @ts-ignore
      Object.keys(error.errors).map((key) =>
        // @ts-ignore
        setError(key.replace("settings.", ""), {
          // @ts-ignore
          message: error.errors[key] ?? t("Something went wrong"),
        })
      );
    }
  }, [isError, error]);

  /**
   *
   * @param {import("yup").InferType<typeof organizationSchema>} data
   * return {void}
   */
  const onSubmit = (data) => {
    mutate({ ...organizationInformationData, settings: data });
  };

  const { DiscardChangesModal, hasUnsavedChanges, setShowDiscardChangeModal } = useUnsavedChanges({
    originalData: isDisable ? defaultValues : { ...defaultValues, ...organizationInformationData?.settings },
    currentData: isDisable ? defaultValues : watch(),
    onDiscardAction: reset,
  });

  return (
    <>
      <DiscardChangesModal />

      <ContextualSaveBar
        id="local-seo-organization-schema"
        open={hasUnsavedChanges}
        isLoading={isUpdateLoading}
        onSave={handleSubmit(onSubmit)}
        onDiscard={() => setShowDiscardChangeModal(true)}
      />

      <Form
        control={control}
        onSubmit={({ data }) => {
          onSubmit(data);
        }}
      >
        <BlockStack gap="400">
          <BasicInformationFormFields
            control={control}
            isDisable={isDisable}
          />
          <AddressFormFields
            control={control}
            isDisable={isDisable}
          />
          <SocialProfilesFormFields
            control={control}
            isDisable={isDisable}
          />
          {/* <ReviewFormFields /> */}
          <InlineStack align="end">
            <Button
              variant="primary"
              loading={isLoading || isUpdateLoading}
              disabled={isDisable || !hasUnsavedChanges}
              submit
            >
              {t("Save")}
            </Button>
          </InlineStack>
        </BlockStack>
      </Form>
    </>
  );
}

//@ts-check
import { Card, BlockStack, Grid, TextField, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { Controller } from "react-hook-form";
import { organizationSchema } from "storeseo-schema/local-seo/organizationSchema";

/**
 *
 * @param {{control: import('react-hook-form').Control<import("yup").InferType<typeof organizationSchema>>, isDisable: boolean}} props
 * @returns
 */
export default function AddressFormField(props) {
  const { control, isDisable } = props;
  const { t } = useTranslation();
  return (
    <Card>
      <BlockStack gap="300">
        <Text
          as="h3"
          variant="headingMd"
        >
          {t("Address")}
        </Text>
        <BlockStack gap="300">
          <Grid columns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}>
            <Controller
              name="address.streetAddress"
              control={control}
              disabled={isDisable}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  label={t("Street Address")}
                  autoComplete="off"
                  placeholder={t("(ex. 123 Main Street)")}
                  {...field}
                  onChange={(value) => field.onChange(value)}
                  error={error?.message}
                />
              )}
            />

            <Controller
              name="address.city"
              control={control}
              disabled={isDisable}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  label={t("City")}
                  autoComplete="off"
                  placeholder={t("(ex. Milton)")}
                  {...field}
                  onChange={(value) => field.onChange(value)}
                  error={error?.message}
                />
              )}
            />

            <Controller
              name="address.state"
              control={control}
              disabled={isDisable}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  label={t("State")}
                  autoComplete="off"
                  placeholder={t("(ex. Milton)")}
                  {...field}
                  onChange={(value) => field.onChange(value)}
                  error={error?.message}
                />
              )}
            />

            <Controller
              name="address.country"
              control={control}
              disabled={isDisable}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  label={t("Country")}
                  autoComplete="off"
                  placeholder={t("(ex. US)")}
                  {...field}
                  onChange={(value) => field.onChange(value)}
                  error={error?.message}
                />
              )}
            />

            <Controller
              name="address.postalCode"
              control={control}
              disabled={isDisable}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  label={t("Postal Code")}
                  autoComplete="off"
                  placeholder={t("(ex. 19968)")}
                  {...field}
                  onChange={(value) => field.onChange(value)}
                  error={error?.message}
                />
              )}
            />

            <Controller
              name="address.telephone"
              control={control}
              disabled={isDisable}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  label={t("Telephone")}
                  autoComplete="off"
                  placeholder={t("(ex. 555-0123)")}
                  {...field}
                  onChange={(value) => field.onChange(value)}
                  error={error?.message}
                />
              )}
            />
          </Grid>
        </BlockStack>
      </BlockStack>
    </Card>
  );
}

//@ts-check

import { Card, BlockStack, InlineStack, RadioButton, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";

export default function ReviewFormFields() {
  const { t } = useTranslation();

  return (
    <Card>
      <BlockStack gap="300">
        <Text
          as="h3"
          variant="headingMd"
        >
          {t("Review")}
        </Text>
        <BlockStack gap="100">
          <Text
            as="p"
            variant="bodyMd"
          >
            {t("Do your store has reviews?")}
          </Text>
          <InlineStack gap="400">
            <RadioButton
              label={t("Yes")}
              id="disabled"
              name="accounts"
            />
            <RadioButton
              label={t("No")}
              id="optional"
              name="accounts"
            />
          </InlineStack>
        </BlockStack>
      </BlockStack>
    </Card>
  );
}

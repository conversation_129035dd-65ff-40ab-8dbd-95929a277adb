//@ts-check
import TooltipWrapper from "@/components/common/TooltipWrapper";
import { Card, BlockStack, TextField, InlineStack, Icon, Grid, Text } from "@shopify/polaris";
import { InfoIcon, SearchIcon } from "@shopify/polaris-icons";
import { Controller } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { productMerchantSchema } from "storeseo-schema/local-seo/productMerchantSchema";

/**
 *
 * @param {{control: import('react-hook-form').Control<import("yup").InferType<typeof productMerchantSchema>>, isDisable: boolean}} props
 * @returns
 */
export default function MerchantShippingInfoFormField({ control, isDisable }) {
  const { t } = useTranslation();

  return (
    <Card>
      <BlockStack gap="300">
        <Text
          as="h3"
          variant="headingMd"
        >
          {t("Shipping Information")}
        </Text>

        <BlockStack gap="300">
          <Controller
            name="shippingDestination"
            control={control}
            disabled={isDisable}
            render={({ field, fieldState: { error } }) => (
              <TextField
                label={
                  <InlineStack gap="050">
                    <Text as="p">{t("Shipping Destination")}</Text>
                    <TooltipWrapper content={t("The two-letter country code, in ISO 3166-1 alpha-2 format")}>
                      <Icon
                        source={InfoIcon}
                        tone={isDisable ? "subdued" : "base"}
                      />
                    </TooltipWrapper>
                  </InlineStack>
                }
                {...field}
                onChange={(value) => field.onChange(value)}
                prefix={
                  <Icon
                    source={SearchIcon}
                    tone={isDisable ? "subdued" : "base"}
                  />
                }
                autoComplete="off"
                placeholder={t("(Ex. US)")}
                error={error?.message}
              />
            )}
          />
          <Grid columns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 2 }}>
            <Controller
              name="shippingCurrency"
              control={control}
              disabled={isDisable}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  label={
                    <InlineStack gap="050">
                      <Text as="p">{t("Currency")}</Text>
                      <TooltipWrapper content={t("The currency of the shipping cost, in 3-letter ISO 4217 format.")}>
                        <Icon
                          source={InfoIcon}
                          tone={isDisable ? "subdued" : "base"}
                        />
                      </TooltipWrapper>
                    </InlineStack>
                  }
                  {...field}
                  onChange={(value) => field.onChange(value)}
                  prefix={
                    <Icon
                      source={SearchIcon}
                      tone={isDisable ? "subdued" : "base"}
                    />
                  }
                  autoComplete="off"
                  placeholder={t("(Ex. USD)")}
                  error={error?.message}
                />
              )}
            />

            <Controller
              name="shippingAmount"
              control={control}
              disabled={isDisable}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  type="number"
                  min={0}
                  label={t("Amount")}
                  {...field}
                  value={String(field.value)}
                  onChange={(value) => field.onChange(value)}
                  autoComplete="off"
                  error={error?.message}
                />
              )}
            />

            <Controller
              name="shippingHandingMinDays"
              control={control}
              disabled={isDisable}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  type="number"
                  min={0}
                  label={t("Handing min days")}
                  {...field}
                  value={String(field.value)}
                  onChange={(value) => field.onChange(Number(value))}
                  placeholder={t("(ex. 2)")}
                  autoComplete="off"
                  error={error?.message}
                />
              )}
            />

            <Controller
              name="shippingHandingMaxDays"
              control={control}
              disabled={isDisable}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  type="number"
                  min={0}
                  label={t("Handing max days")}
                  {...field}
                  value={String(field.value)}
                  onChange={(value) => field.onChange(Number(value))}
                  placeholder={t("(ex. 2)")}
                  autoComplete="off"
                  error={error?.message}
                />
              )}
            />

            <Controller
              name="shippingTransitMinDays"
              control={control}
              disabled={isDisable}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  type="number"
                  min={0}
                  label={t("Transit min days")}
                  {...field}
                  value={String(field.value)}
                  onChange={(value) => field.onChange(Number(value))}
                  placeholder={t("(ex. 2)")}
                  autoComplete="off"
                  error={error?.message}
                />
              )}
            />
            <Controller
              name="shippingTransitMaxDays"
              control={control}
              disabled={isDisable}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  type="number"
                  min={0}
                  label={t("Transit max days")}
                  {...field}
                  value={String(field.value)}
                  onChange={(value) => field.onChange(Number(value))}
                  placeholder={t("(ex. 2)")}
                  autoComplete="off"
                  error={error?.message}
                />
              )}
            />
          </Grid>
        </BlockStack>
      </BlockStack>
    </Card>
  );
}

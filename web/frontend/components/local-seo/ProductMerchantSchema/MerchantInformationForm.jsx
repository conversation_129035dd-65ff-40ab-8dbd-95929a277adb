//@ts-check
import { BlockStack, Button, InlineStack } from "@shopify/polaris";
import { Form, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { defaultValues } from "@/lib/form-default-value/localSEOProductMerchant";
import { useTranslation } from "react-i18next";
import { useLocalSEOProductMerchantSchema, useUpdateLocalSEOProductMerchantSchema } from "@/lib/hooks/local-seo";
import { policyOptions, returnFeesOptions, returnMethodOptions } from "@/lib/local-seo/localSEOProductMerchantConst";
import { useUnsavedChanges } from "@/hooks/useUnsavedChanges";
import MerchantShippingInfoFormField from "./MerchantShippingInfoFormField";
import MerchantReturnPolicyInfoFormField from "./MerchantReturnPolicyInfoFormField";
import { productMerchantSchema } from "storeseo-schema/local-seo/productMerchantSchema";
import { useEffect } from "react";
import ContextualSaveBar from "@/modules/components/ContextualSaveBar";

export default function MerchantInformationForm({ isDisable = false }) {
  const { t } = useTranslation();
  const { data: productMerchantData, isLoading } = useLocalSEOProductMerchantSchema();
  const { mutate, isLoading: isUpdateLoading, isError, error } = useUpdateLocalSEOProductMerchantSchema();

  const { watch, control, setError, handleSubmit, reset } = useForm({
    resolver: yupResolver(productMerchantSchema),
    defaultValues,
    values: {
      shippingDestination: productMerchantData?.settings?.shippingDestination || "",
      shippingCurrency: productMerchantData?.settings?.shippingCurrency || "",
      shippingAmount: productMerchantData?.settings?.shippingAmount ?? null,
      shippingHandingMinDays: productMerchantData?.settings?.shippingHandingMinDays ?? null,
      shippingHandingMaxDays: productMerchantData?.settings?.shippingHandingMaxDays ?? null,
      shippingTransitMinDays: productMerchantData?.settings?.shippingTransitMinDays ?? null,
      shippingTransitMaxDays: productMerchantData?.settings?.shippingTransitMaxDays ?? null,
      returnApplicableCountries: productMerchantData?.settings?.returnApplicableCountries || "",
      returnPolicyCategory: productMerchantData?.settings?.returnPolicyCategory || policyOptions[0].value,
      returnMethod: productMerchantData?.settings?.returnMethod || returnMethodOptions[0].value,
      returnDays: productMerchantData?.settings?.returnDays ?? null,
      returnFees: productMerchantData?.settings?.returnFees || returnFeesOptions[0].value,
    },
  });

  useEffect(() => {
    if (isError) {
      // @ts-ignore
      Object.keys(error.errors).map((key) =>
        // @ts-ignore
        setError(key.replace("settings.", ""), {
          // @ts-ignore
          message: error.errors[key] ?? t("Something went wrong"),
        })
      );
    }
  }, [isError, error]);

  /**
   *
   * @param {import("yup").InferType<typeof productMerchantSchema>} data
   * return {void}
   */
  const onSubmit = (data) => {
    mutate({ ...productMerchantData, settings: data });
  };

  const { DiscardChangesModal, hasUnsavedChanges, setShowDiscardChangeModal } = useUnsavedChanges({
    originalData: isDisable ? defaultValues : productMerchantData?.settings || defaultValues,
    currentData: isDisable ? defaultValues : watch(),
    onDiscardAction: reset,
  });

  return (
    <>
      <DiscardChangesModal />

      <ContextualSaveBar
        id="local-seo-product-merchant-schema"
        open={hasUnsavedChanges}
        isLoading={isUpdateLoading}
        onSave={handleSubmit(onSubmit)}
        onDiscard={() => setShowDiscardChangeModal(true)}
      />
      <Form
        control={control}
        onSubmit={({ data }) => {
          onSubmit(data);
        }}
      >
        <BlockStack gap="400">
          <MerchantShippingInfoFormField
            control={control}
            isDisable={isDisable}
          />

          <MerchantReturnPolicyInfoFormField
            control={control}
            isDisable={isDisable}
          />

          <InlineStack align="end">
            <Button
              variant="primary"
              submit
              loading={isLoading || isUpdateLoading}
              disabled={isDisable || !hasUnsavedChanges}
            >
              {t("Save")}
            </Button>
          </InlineStack>
        </BlockStack>
      </Form>
    </>
  );
}

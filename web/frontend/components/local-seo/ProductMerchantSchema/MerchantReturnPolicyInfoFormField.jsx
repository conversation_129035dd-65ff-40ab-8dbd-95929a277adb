//@ts-check
import { Card, BlockStack, TextField, InlineStack, Icon, Grid, Text, Select } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { Controller, useController } from "react-hook-form";
import TooltipWrapper from "@/components/common/TooltipWrapper";
import { InfoIcon, SearchIcon } from "@shopify/polaris-icons";
import { policyOptions, returnFeesOptions, returnMethodOptions } from "@/lib/local-seo/localSEOProductMerchantConst";
import {
  FINITE_RETURN_POLICY,
  UNLIMITED_RETURN_POLICY,
  productMerchantSchema,
} from "storeseo-schema/local-seo/productMerchantSchema";

/**
 *
 * @param {{control: import('react-hook-form').Control<import("yup").InferType<typeof productMerchantSchema>>, isDisable: boolean}} props
 * @returns
 */
export default function MerchantReturnPolicyInfoFormField({ control, isDisable }) {
  const { t } = useTranslation();
  const { field: returnPolicyCategory } = useController({
    control,
    name: "returnPolicyCategory",
  });

  return (
    <Card>
      <BlockStack gap="300">
        <Text
          as="h3"
          variant="headingMd"
        >
          {t("Return Policy")}
        </Text>
        <BlockStack gap="300">
          <Controller
            name="returnApplicableCountries"
            control={control}
            disabled={isDisable}
            render={({ field, fieldState: { error } }) => (
              <TextField
                label={
                  <InlineStack gap="050">
                    <Text as="p">{t("Applicable countries")}</Text>
                    <TooltipWrapper content={t("The two-letter country code, in ISO 3166-1 alpha-2 format")}>
                      <Icon
                        source={InfoIcon}
                        tone={isDisable ? "subdued" : "base"}
                      />
                    </TooltipWrapper>
                  </InlineStack>
                }
                {...field}
                onChange={(value) => field.onChange(value)}
                prefix={
                  <Icon
                    source={SearchIcon}
                    tone={isDisable ? "subdued" : "base"}
                  />
                }
                autoComplete="off"
                placeholder={t("(Ex. US)")}
                error={error?.message}
              />
            )}
          />
          <Grid columns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 2 }}>
            <Controller
              name="returnPolicyCategory"
              control={control}
              disabled={isDisable}
              render={({ field, fieldState: { error } }) => (
                <Select
                  label={t("Return policy category")}
                  options={policyOptions}
                  {...field}
                  onChange={(value) => field.onChange(value)}
                  error={error?.message}
                />
              )}
            />

            {(returnPolicyCategory.value === FINITE_RETURN_POLICY ||
              returnPolicyCategory.value === UNLIMITED_RETURN_POLICY) && (
              <Controller
                name="returnMethod"
                control={control}
                disabled={isDisable}
                render={({ field, fieldState: { error } }) => (
                  <Select
                    label={t("Return method")}
                    options={returnMethodOptions}
                    {...field}
                    onChange={(value) => field.onChange(value)}
                    error={error?.message}
                  />
                )}
              />
            )}

            {(returnPolicyCategory.value === FINITE_RETURN_POLICY ||
              returnPolicyCategory.value === UNLIMITED_RETURN_POLICY) && (
              <Controller
                name="returnFees"
                control={control}
                disabled={isDisable}
                render={({ field, fieldState: { error } }) => (
                  <Select
                    label={t("Return fees")}
                    options={returnFeesOptions}
                    {...field}
                    onChange={(value) => field.onChange(value)}
                    error={error?.message}
                  />
                )}
              />
            )}

            {returnPolicyCategory.value === FINITE_RETURN_POLICY && (
              <Controller
                name="returnDays"
                control={control}
                disabled={isDisable}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    type="number"
                    min={0}
                    label={
                      <InlineStack gap="050">
                        <Text as="p">{t("Return days")}</Text>
                        <TooltipWrapper content={t("Number of days allowed for returns after delivery")}>
                          <Icon
                            source={InfoIcon}
                            tone={isDisable ? "subdued" : "base"}
                          />
                        </TooltipWrapper>
                      </InlineStack>
                    }
                    {...field}
                    value={String(field.value)}
                    onChange={(value) => field.onChange(Number(value))}
                    autoComplete="off"
                    placeholder="(Ex. 2)"
                    error={error?.message}
                  />
                )}
              />
            )}
          </Grid>
        </BlockStack>
      </BlockStack>
    </Card>
  );
}

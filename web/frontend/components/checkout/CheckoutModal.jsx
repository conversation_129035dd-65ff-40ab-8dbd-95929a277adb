// @ts-check
import { STEPS } from "@/config/onboarding";
import { useUserApi } from "@/hooks/apiHooks/useUserApi";
import { useAppBridgeModal } from "@/hooks/useAppBridgeModal";
import { useAppBridgeRedirect } from "@/hooks/useAppBridgeRedirect";
import { resetCheckout } from "@/store/features/Checkout";
import { updateUser } from "@/store/features/User";
import { Modal, TitleBar } from "@shopify/app-bridge-react";
import {
  Badge,
  Banner,
  Bleed,
  BlockStack,
  Box,
  Button,
  Card,
  Divider,
  Icon,
  InlineGrid,
  InlineStack,
  Link,
  Text,
  TextField,
} from "@shopify/polaris";
import { InfoIcon, XSmallIcon } from "@shopify/polaris-icons";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import OnboardingSteps from "storeseo-enums/onboardingSteps";
import planInterval from "storeseo-enums/planInterval";
import subscriptionAddonGroup from "storeseo-enums/subscriptionAddonGroup";
import subscriptionAddonInterval from "storeseo-enums/subscriptionAddonInterval";
import toastMessages from "storeseo-enums/toastMessages";
import { MODAL_IDS, SUBSCRIPTION_ADDONS } from "../../config";
import { useAppQuery, useShopApi, useUtilityApi } from "../../hooks";
import { useSubscriptionApi } from "../../hooks/apiHooks/useSubscriptionApi";
import { useAddonIsAllowed } from "../../hooks/useAddonIsAllowed";
import { formatNumber, getNextObject, showNotification } from "../../utility/helpers";
import AddonCard from "../addon/AddonCard";
import AddonLoader from "../addon/AddonLoader";
import TrialLimits from "../subscription/TrialLimits";

const initialPlanData = {
  name: "",
  type: "",
  narration: "",
  price: 0,
  discount: 0,
  subtotal: 0,
  coupon_code: null,
  coupon_desc: null,
  hundredPercentCouponApplied: false,
};

const CheckoutModal = () => {
  const { t } = useTranslation();

  // @ts-ignore
  const { slug, coupon_code, isTrial } = useSelector((state) => state.checkout);

  const navigate = useNavigate();
  // @ts-ignore
  const user = useSelector((state) => state.user);
  const dispatch = useDispatch();
  const subscriptionApi = useSubscriptionApi();
  const shopApi = useShopApi();
  const userApi = useUserApi();
  const location = useLocation();
  const redirect = useAppBridgeRedirect();
  const { hideCheckoutModal } = useAppBridgeModal();

  const [planData, setPlanData] = useState(initialPlanData);
  const [couponCode, setCouponCode] = useState(coupon_code || "");
  const [validCouponCode, setValidCouponCode] = useState("");
  const [couponError, setCouponError] = useState("");
  const [isCouponApplied, setIsCouponApplied] = useState(false);
  const [showCouponInput, setShowCouponInput] = useState(false);
  const [showCouponQuestion, setShowCouponQuestion] = useState(true);
  const initialAddons = {
    [subscriptionAddonGroup.IMAGE_OPTIMIZER]: user?.addons?.[subscriptionAddonGroup.IMAGE_OPTIMIZER]?.id || null,
  };
  const [selectedAddons, setSelectedAddons] = useState(initialAddons);
  const [addons, setAddons] = useState([]);
  const [imageOptimizer, setImageOptimizer] = useState(
    addons.find((addon) => addon.id === selectedAddons[subscriptionAddonGroup.IMAGE_OPTIMIZER]) || null
  );
  const [aiOptimizer, setAiOptimizer] = useState(
    addons.find((addon) => addon.id === selectedAddons[subscriptionAddonGroup.AI_OPTIMIZER]) || null
  );

  const [totalPrice, setTotalPrice] = useState(0);
  const [allowOnlyAddon, setAllowOnlyAddon] = useState(false);
  const [isDowngrade, setIsDowngrade] = useState(false);

  useEffect(() => {
    if (user && planData) {
      const isDowngrade = user.planId && user.planSlug !== planData.slug && (planData.isFree || !planData.isUpgradable);
      setIsDowngrade(isDowngrade);
    }
  }, [user, planData]);

  useEffect(() => {
    if (user.planSlug === "visionary" && planData.slug === "visionary") {
      setAllowOnlyAddon(true);
    } else {
      setAllowOnlyAddon(false);
    }
  }, [user.planSlug, planData]);

  const allowAddon = useAddonIsAllowed(planData);

  const toggleCouponInput = () => {
    setShowCouponQuestion(!showCouponQuestion);
    setShowCouponInput(!showCouponInput);
  };

  const { data, refetch, isFetching, isFetched } = useAppQuery({
    queryKey: ["checkout_data", slug],
    queryFn: () => subscriptionApi.getCheckoutData(slug, couponCode),
    reactQueryOptions: {
      onError: (_err) => {
        // console.error(err);
      },
    },
  });

  useEffect(() => {
    if (data) {
      handleSetPlanData(data.planData);
      handleSetAddonData(data.combinedAddons);
      if (data?.combinedAddons?.length > 0) {
        setAddons(data?.combinedAddons || []);
      }
      if (data?.combinedAddons?.length === 0 && data?.addons?.length > 0) {
        setAddons(data?.addons || []);
      }
    }
  }, [data]);

  // useEffect(() => {
  //   setIsCurrentPlan(slug === user?.planSlug);
  // }, [slug, user]);

  useEffect(() => {
    if (!isCouponApplied) {
      refetch();
    }
  }, [isCouponApplied]);

  /**
   * @param {any} planDetails
   */
  const handleSetPlanData = (planDetails) => {
    setPlanData(planDetails);
    if (planDetails.couponCode && planDetails.discount > 0) {
      setCouponCode(planDetails.couponCode);
      setValidCouponCode(planDetails.couponCode);
      setIsCouponApplied(true);
      setShowCouponQuestion(false);
    } else {
      setCouponCode("");
      setValidCouponCode("");
      setIsCouponApplied(false);
      setShowCouponQuestion(true);
    }
  };

  /**
   * @param {any[]} addons
   */
  const handleSetAddonData = (addons) => {
    if (addons.length > 0) {
      setSelectedAddons(
        addons.reduce(
          /**
           * @param {any} obj
           * @param {any} item
           */
          (obj, item) => {
            obj[item.group] = item.id;
            return obj;
          },
          {}
        )
      );
    }
  };

  const { mutate: submitCouponValidate, isLoading: isCouponApplying } = useMutation({
    mutationFn: () => subscriptionApi.validateCoupon(slug, couponCode),
    onSuccess: (data) => {
      if (data) {
        handleSetPlanData(data);
        setShowCouponQuestion(false);
        setShowCouponInput(false);
      }
    },
    onError: (error) => {
      setCouponError(error.toString());
    },
  });

  const applyCoupon = () => {
    setCouponError("");

    if (!couponCode) {
      setCouponError("Please input coupon code");
      return;
    }

    submitCouponValidate();
  };

  const utilityApi = useUtilityApi();
  const { mutateAsync: startProductSync } = useMutation({
    mutationFn: () => utilityApi.submitDataMigrationInfo(null),
  });

  const { mutate: updateOnboardingStatus } = useMutation({
    /**
     * @param {string} currentStepName
     */
    mutationFn: (currentStepName) => shopApi.updateOnboardingStatus(currentStepName),
  });

  const { mutate: subscribeToPlan, isLoading: isSubmitting } = useMutation({
    mutationFn: async () => {
      return await subscriptionApi.handleSubscription(slug, validCouponCode, selectedAddons, isTrial);
    },
    onSuccess: async ({ confirmationUrl, isFreeSubscription }) => {
      const currentStep = STEPS.find((step) => step.pathname === location.pathname);

      if (currentStep) updateOnboardingStatus(currentStep?.name);

      if (confirmationUrl) return redirect.REMOTE(confirmationUrl);

      if (isFreeSubscription) {
        if (planData.isUpgradable) startProductSync();
        const { user: updatedUser } = await userApi.getAuthUserData();
        dispatch(updateUser({ ...updatedUser, isNewlyUpgraded: !isFreeSubscription }));
      }

      if (location.pathname.startsWith("/onboarding")) {
        const nextStep = getNextObject(STEPS, OnboardingSteps.SUBSCRIPTION, "name");
        navigate(nextStep.pathname);
      } else {
        navigate("/");
      }

      hideCheckoutModal();
    },
  });

  const removeCoupon = () => {
    handleSetPlanData({ ...planData, total: planData.subtotal });
    calculateTotalPrice();
    setCouponCode("");
    setValidCouponCode("");
    setIsCouponApplied(false);
    setShowCouponQuestion(true);
    setCouponError("");
    showNotification({ message: toastMessages.COUPON_CLEARED, type: "success" });
  };

  const resetCheckoutData = () => {
    setPlanData(initialPlanData);
    setSelectedAddons(initialAddons);
    setCouponCode("");
    setValidCouponCode("");
    setIsCouponApplied(false);
    setShowCouponQuestion(true);
    setCouponError("");
    dispatch(resetCheckout());
  };

  /**
   * @param {string} group
   */
  const resetAddon = (group) => {
    const selectedItems = {
      ...selectedAddons,
      [group]: null,
    };

    setSelectedAddons(selectedItems);

    if (group === subscriptionAddonGroup.IMAGE_OPTIMIZER) {
      setImageOptimizer(null);
      setAiOptimizer(null);
    }
  };

  /**
   * @param {any} _
   * @param {string} newValue
   */
  const handleAddonSelect = (_, newValue) => {
    const [group, id] = newValue.split(":");
    const selectedItems = {
      ...selectedAddons,
      [group]: Number(id),
    };
    setSelectedAddons(selectedItems);
  };

  const addonBadgeMarkup = <Badge tone="info">{t("Add-on")}</Badge>;

  useEffect(() => {
    calculateTotalPrice();

    if (selectedAddons[subscriptionAddonGroup.IMAGE_OPTIMIZER]) {
      setImageOptimizer(
        addons.find((addon) => addon.id === selectedAddons[subscriptionAddonGroup.IMAGE_OPTIMIZER]) || null
      );
    }

    if (selectedAddons[subscriptionAddonGroup.AI_OPTIMIZER]) {
      setAiOptimizer(addons.find((addon) => addon.id === selectedAddons[subscriptionAddonGroup.AI_OPTIMIZER]) || null);
    }
  }, [selectedAddons, planData?.total, allowOnlyAddon]);

  const calculateTotalPrice = () => {
    const total = Object.values(selectedAddons)
      .filter((val) => val)
      ?.reduce(
        (price, id) => {
          let addon = addons.find((addon) => addon.id === id);
          let interval =
            planData.interval === planInterval.ANNUALLY && addon.group === subscriptionAddonGroup.IMAGE_OPTIMIZER
              ? 12
              : 1;
          return price + addon?.subtotal * interval;
        },
        allowOnlyAddon ? 0 : planData.total
      );

    setTotalPrice(total);
  };

  const [showBanner, setShowBanner] = useState(false);

  useEffect(() => {
    setShowBanner(user?.isPremium && allowAddon && !allowOnlyAddon);
  }, [user, allowAddon, allowOnlyAddon]);

  // const isSamePlan = user?.planSlug === planData?.slug;
  const isAlowedToSubscribe = user?.planSlug === planData.slug && !user?.trialData?.isActive && user.isSubscribed;

  return (
    <Modal
      id={MODAL_IDS.CHECKOUT}
      onHide={resetCheckoutData}
    >
      <TitleBar
        title={t("Checkout: {{PLAN_NAME}}", {
          PLAN_NAME: planData?.name,
        })}
      >
        <button
          variant="primary"
          onClick={() => subscribeToPlan()}
          loading={isSubmitting ? "" : undefined}
          disabled={isFetching || isCouponApplying || isAlowedToSubscribe}
        >
          {t("Checkout")}
        </button>
      </TitleBar>

      {planData && (
        <Box padding={"400"}>
          <BlockStack gap="400">
            {showBanner && (
              <Card padding={"0"}>
                <Banner
                  tone="info"
                  title={t("Note")}
                  onDismiss={() => setShowBanner(false)}
                  icon={InfoIcon}
                >
                  <Text as="p">
                    {t(
                      "As per the Shopify Subscription system, your current subscription plan will be canceled (with charges deducted for the days you have already used), and a new subscription will be created"
                    )}
                    .
                  </Text>
                </Banner>
              </Card>
            )}
            {allowAddon && (
              <Card>
                <BlockStack
                  align="space-between"
                  gap={"400"}
                >
                  <Text
                    as="h6"
                    variant="headingMd"
                  >
                    {t("Available Addons")}
                  </Text>
                  <Bleed marginInline={"400"}>
                    <Divider />
                  </Bleed>

                  {SUBSCRIPTION_ADDONS.filter((a) => a.type === subscriptionAddonInterval.MONTHLY).map(
                    ({ title, key }) => (
                      <BlockStack
                        gap="200"
                        key={key}
                      >
                        <InlineStack align="space-between">
                          <Text
                            as="h4"
                            variant="headingSm"
                          >
                            {t(title)}
                          </Text>

                          {imageOptimizer && (
                            <Button
                              variant="plain"
                              onClick={() => resetAddon(key)}
                            >
                              {t("Skip Image Optimizer")}
                            </Button>
                          )}
                        </InlineStack>

                        {isFetching ? (
                          <AddonLoader />
                        ) : (
                          <InlineGrid
                            columns="4"
                            gap="400"
                          >
                            {addons
                              .filter((a) => a.group === key)
                              .sort((a, b) => a.order - b.order)
                              .map((ad) => (
                                <AddonCard
                                  key={ad.id}
                                  addon={ad}
                                  selectedAddons={selectedAddons}
                                  handleAddonSelect={handleAddonSelect}
                                />
                              ))}
                          </InlineGrid>
                        )}
                      </BlockStack>
                    )
                  )}
                </BlockStack>
              </Card>
            )}
            <Card>
              <BlockStack
                align="space-between"
                gap={"400"}
              >
                <Text
                  as="h6"
                  variant="headingMd"
                >
                  {t("Pricing Details")}
                </Text>
                <Bleed marginInline={"400"}>
                  <Divider />
                </Bleed>
                {!allowOnlyAddon && (
                  <BlockStack gap="400">
                    <PricingRow
                      title={t("Plan Name")}
                      value={planData?.name}
                    />
                    <PricingRow
                      title={t("Plan Price")}
                      value={`$${planData?.price?.toFixed(2)}`}
                    />
                    {!planData.isFree && (
                      <PricingRow
                        title={t("Duration")}
                        value={
                          <>
                            {planData?.duration} {planData?.intervalText}
                            {planData?.duration > 1 && "s"}
                          </>
                        }
                      />
                    )}

                    <PricingRow
                      title={t("Sub Total")}
                      value={`$${planData?.subtotal?.toFixed(2)}`}
                    />
                    {showCouponQuestion && (
                      <InlineStack>
                        <Link
                          onClick={toggleCouponInput}
                          monochrome
                        >
                          {t("Do You Have Any Coupon?")}
                        </Link>
                      </InlineStack>
                    )}
                    {showCouponInput && (
                      <TextField
                        label={t("Give your coupon code")}
                        type="text"
                        value={couponCode}
                        onChange={setCouponCode}
                        onFocus={() => setCouponError("")}
                        placeholder={t("Coupon Code")}
                        autoComplete="off"
                        clearButton={true}
                        onClearButtonClick={() => setCouponCode("")}
                        connectedRight={
                          <Button
                            onClick={applyCoupon}
                            loading={isCouponApplying}
                          >
                            {t("Apply")}
                          </Button>
                        }
                        labelAction={{
                          content: t("Close"),
                          onAction: () => {
                            setCouponCode("");
                            setCouponError("");
                            toggleCouponInput();
                          },
                        }}
                        error={couponError}
                      />
                    )}
                    {isCouponApplied && (
                      <PricingRow
                        title={
                          <InlineStack gap="025">
                            {t("Discount")} ({planData?.couponCode})
                            <Link onClick={removeCoupon}>
                              <Icon
                                source={XSmallIcon}
                                tone="critical"
                              />
                            </Link>
                          </InlineStack>
                        }
                        value={`-$${planData?.discount?.toFixed(2)}`}
                      />
                    )}
                  </BlockStack>
                )}
                <Bleed marginInline={"400"}>
                  <Divider />
                </Bleed>
                {isTrial && planData?.meta?.trialDays > 0 && (
                  <>
                    <Text
                      as="p"
                      variant="headingMd"
                    >
                      {t("With {{NUM_OF}}-day free trial you can", { NUM_OF: planData?.meta?.trialDays })}
                    </Text>
                    <TrialLimits
                      limits={planData?.meta?.trialRules}
                      duration={planData?.meta?.trialDays}
                    />
                    <Bleed marginInline={"400"}>
                      <Divider />
                    </Bleed>
                  </>
                )}
                {imageOptimizer && (
                  <PricingRow
                    title={
                      <InlineStack gap="200">
                        {t("Image Optimizer")} &nbsp;
                        {t("({{ADDON_LIMIT}} images)", {
                          ADDON_LIMIT: formatNumber(imageOptimizer?.limit),
                        })}
                        {addonBadgeMarkup}
                      </InlineStack>
                    }
                    value={
                      <>
                        ${imageOptimizer?.subtotal?.toFixed(2)}
                        {planData.interval === planInterval.ANNUALLY && (
                          <>x12 = ${(imageOptimizer?.subtotal * 12).toFixed(2)}</>
                        )}
                      </>
                    }
                  />
                )}
                {aiOptimizer && (
                  <PricingRow
                    title={
                      <InlineStack gap="200">
                        {t("AI Content Optimizer")} &nbsp;
                        {t("({{ADDON_LIMIT}} credits)", {
                          ADDON_LIMIT: formatNumber(aiOptimizer?.limit),
                        })}
                        {addonBadgeMarkup}
                      </InlineStack>
                    }
                    value={
                      <>
                        ${aiOptimizer?.subtotal?.toFixed(2)}
                        {planData.interval === planInterval.ANNUALLY && (
                          <>x12 = ${(aiOptimizer?.subtotal * 12).toFixed(2)}</>
                        )}
                      </>
                    }
                  />
                )}
                <Bleed marginInline={"400"}>
                  <Divider />
                </Bleed>

                <InlineStack
                  align="space-between"
                  blockAlign="center"
                >
                  <Text
                    as="h6"
                    variant="headingSm"
                  >
                    {t("Total")}
                  </Text>
                  <BlockStack inlineAlign="end">
                    <Text
                      as="h6"
                      variant="headingSm"
                    >
                      ${totalPrice?.toFixed(2)}
                    </Text>
                    {isTrial && planData?.meta?.trialDays > 0 && (
                      <Text
                        as="p"
                        tone="subdued"
                        variant="bodyXs"
                      >
                        {t("after the 7-day free trial")}
                      </Text>
                    )}
                  </BlockStack>
                </InlineStack>
              </BlockStack>
            </Card>

            {!isFetching && isDowngrade && (
              <Card padding={"0"}>
                <Banner
                  tone="warning"
                  title={t(
                    "You're about to downgrade your current subscription. Your store data will be deleted according to the new plan."
                  )}
                />
              </Card>
            )}

            {!isFetching && isAlowedToSubscribe && (
              <Card padding={"0"}>
                <Banner
                  tone="warning"
                  title={t("You're already subscribed to this plan. Please choose another plan to subscribe.")}
                />
              </Card>
            )}
          </BlockStack>
        </Box>
      )}
    </Modal>
  );
};

const PricingRow = ({ title, value }) => {
  return (
    <InlineStack
      align="space-between"
      blockAlign="center"
    >
      <Text
        as="h6"
        variant="headingSm"
      >
        {title}
      </Text>

      <Text
        as="span"
        variant="headingSm"
      >
        {value}
      </Text>
    </InlineStack>
  );
};

export default CheckoutModal;

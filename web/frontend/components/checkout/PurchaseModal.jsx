// @ts-check
import { MODAL_IDS } from "@/config";
import { useAppQuery } from "@/hooks";
import { useSubscriptionApi } from "@/hooks/apiHooks/useSubscriptionApi";
import { resetPurchase } from "@/store/features/Purchase";
import { Modal, TitleBar } from "@shopify/app-bridge-react";
import { BlockStack, Box, InlineGrid, Text } from "@shopify/polaris";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { useDispatch, useSelector } from "react-redux";
import { AI_OPTIMIZER } from "storeseo-enums/subscriptionAddonGroup";
import AddonCard from "../addon/AddonCard";
import AddonLoader from "../addon/AddonLoader";

const PurchaseModal = () => {
  const { t } = useTranslation();
  const subscriptionApi = useSubscriptionApi();
  const dispatch = useDispatch();

  // @ts-ignore
  const user = useSelector((state) => state.user);

  const initialAddons = {
    [AI_OPTIMIZER]: user?.addons?.[AI_OPTIMIZER]?.id || null,
  };

  const [selectedAddons, setSelectedAddons] = useState(initialAddons);

  const { data: { groups } = {}, isFetching } = useAppQuery({
    queryKey: ["CREDIT_BUNDLES"],
    queryFn: () => subscriptionApi.getCreditBundles(),
    reactQueryOptions: {
      onError: (err) => {
        // navigate("/404");
        // console.error(err);
      },
    },
  });

  const { mutate: goToCheckout, isLoading: isSubmitting } = useMutation({
    mutationFn: () => subscriptionApi.handleCreditPurchase(selectedAddons),
    onSuccess: () => {},
  });

  const handleAddonSelect = (_, newValue) => {
    const [group, id] = newValue.split(":");
    const selectedItems = {
      ...selectedAddons,
      [group]: Number(id),
    };
    setSelectedAddons(selectedItems);
  };

  const disableConfirmBtn = Object.values(selectedAddons).filter((val) => val).length === 0;

  const resetAddonSelection = () => {
    setSelectedAddons(initialAddons);
    dispatch(resetPurchase());
  };

  return (
    <Modal
      id={MODAL_IDS.PURCHASE}
      onHide={resetAddonSelection}
    >
      <TitleBar title={t("Purchase: Credit bundles")}>
        <button
          variant="primary"
          onClick={() => goToCheckout()}
          loading={isSubmitting ? "" : undefined}
          disabled={isFetching || disableConfirmBtn}
        >
          {t("Checkout")}
        </button>
      </TitleBar>

      <Box padding={"400"}>
        <BlockStack gap={"400"}>
          <BlockStack gap={"100"}>
            {/* <Text
              as="h4"
              variant="headingSm"
            >
              {t("Credit bundle")}
            </Text> */}
            <Text
              as="p"
              variant="bodySm"
            >
              {t("Purchase your preferred bundle to unlock our AI Content Optimizer")}
            </Text>
          </BlockStack>

          {isFetching ? (
            <AddonLoader items={3} />
          ) : (
            groups &&
            groups.map((group) => (
              <InlineGrid
                key={group.group}
                columns="3"
                gap="400"
              >
                {group.items
                  .sort((a, b) => a.order - b.order)
                  .map((ad) => (
                    <AddonCard
                      key={ad.id}
                      addon={ad}
                      selectedAddons={selectedAddons}
                      handleAddonSelect={handleAddonSelect}
                    />
                  ))}
              </InlineGrid>
            ))
          )}
        </BlockStack>
      </Box>
    </Modal>
  );
};

export default PurchaseModal;

import React from "react";
import { useTranslation } from "react-i18next";
import Radial<PERSON>hart from "../charts/RadialChart";
import { Link } from "react-router-dom";
import { usePublishStatus } from "../../hooks/usePublishStatus";
import { useLabelColor } from "../../hooks/useLabelColor";
import { useScoreColor } from "../../hooks/useScoreColor";

const BlogRowItem = ({ article, sl, pageNo = 1, perPage = 20 }) => {
  const { t } = useTranslation();
  const status = usePublishStatus(article);
  const labelColor = useLabelColor(article.issues);
  const color = useScoreColor(article.score);
  const serialNo = (pageNo - 1) * perPage + sl;

  return (
    <div className="table__row">
      <div className="table__cell table__check">
        <p>{serialNo}</p>
      </div>
      <div className="table__cell table__product">
        <p>{article.title}</p>
      </div>
      <div className="table__cell table__date">
        <p>{article.blog.blog_title}</p>
      </div>
      <div className="table__cell table__focuskey">
        {article.focus_keyword && <span className="text__label label__blue">{article.focus_keyword}</span>}
      </div>
      <div className="table__cell table__status">
        <span className={`text__label ${labelColor}`}>
          {article.issues} {t("Issues")}
        </span>
      </div>
      <div className="table__cell table__score">
        <RadialChart
          progress={article.score}
          color={color}
        />
      </div>
      <div className="table__cell table__status">
        <span className={`text__label ${status?.color}`}>{status?.name}</span>
      </div>
      <div className="table__cell table__action">
        <Link
          className="button button--xs radius-12 hover__highlight button__blue button__flex mr5"
          to={`/blogs/${article.blog_id}/articles/${article.id}/edit`}
        >
          <i className="ss-icon ss-edit" /> {t("Fix")}
        </Link>

        <Link
          className="button button--xs radius-12 hover__highlight button__primary button__flex"
          to={`/blogs/${article.blog_id}/articles/${article.id}`}
        >
          <i className="ss-icon ss-eye" /> {t("View")}
        </Link>
      </div>
    </div>
  );
};

export default BlogRowItem;

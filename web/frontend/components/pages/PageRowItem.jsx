import OptimizeSeoStatusBadge from "@/modules/optimize-seo/OptimizeSeoStatusBadge.jsx";
import { Badge, BlockStack, Box, Button, IndexTable, InlineStack, Link, Text, useBreakpoints } from "@shopify/polaris";
import { ComposeIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";
import { useLabelColor } from "../../hooks/useLabelColor.js";
import { usePublishStatus } from "../../hooks/usePublishStatus.js";
import { formatDate } from "../../utility/helpers.jsx";
import RadialChart from "../charts/RadialChart.jsx";
import TitleColumn from "../common/TitleColumn.jsx";
import TooltipWrapper from "../common/TooltipWrapper.jsx";
import PageRowItemSkeleton from "./PageRowItemSkeleton.jsx";

export default function PageRowItem({ item, isFetching }) {
  const { smDown: isSmallDevice } = useBreakpoints();

  if (isFetching) return <PageRowItemSkeleton />;
  return <>{isSmallDevice ? <RowsCellForSmallDevices item={item} /> : <RowsCellForLargeDevices item={item} />}</>;
}

const RowsCellForSmallDevices = ({ item }) => {
  const { title, focus_keyword, created_at, score, issues, page_id, published_at, page_type } = item;
  const { t } = useTranslation();
  const labelColor = useLabelColor(issues);
  const status = usePublishStatus(published_at);

  return (
    <Link
      monochrome
      removeUnderline
      url={`/optimize-seo/pages/${page_id}`}
      onClick={(e) => e.stopPropagation()}
    >
      <Box
        paddingBlock="300"
        paddingInline="400"
        width="100%"
      >
        <BlockStack gap="150">
          <InlineStack
            align="space-between"
            wrap={false}
            gap="200"
          >
            <Box width="100%">
              <BlockStack gap="300">
                {/* Basic info */}
                <BlockStack gap="100">
                  {page_type === "BETTERDOCS_HOMEPAGE" ? (
                    <InlineStack>
                      <Text
                        as={"h4"}
                        fontWeight="semibold"
                      >
                        {title}
                      </Text>
                      <Badge tone="info">{t("Doc Page")}</Badge>
                    </InlineStack>
                  ) : (
                    <Text
                      as={"h4"}
                      fontWeight="semibold"
                    >
                      {title}
                    </Text>
                  )}

                  <Text
                    as="span"
                    variant="bodySm"
                  >
                    {focus_keyword}
                  </Text>
                </BlockStack>
                {/* Scoring */}
                <InlineStack
                  align="space-between"
                  blockAlign="center"
                  gap="200"
                >
                  <BlockStack
                    gap="200"
                    blockAlign="center"
                  >
                    <BlockStack gap="200">
                      <InlineStack gap="200">
                        <Badge tone={status.color}>{t(status.name)}</Badge>
                        <Badge tone={labelColor}>
                          {issues} {t("Issues")}
                        </Badge>
                      </InlineStack>
                      <OptimizeSeoStatusBadge score={score} />
                    </BlockStack>

                    <Text
                      as="span"
                      variant="bodySm"
                      tone="subdued"
                    >
                      {formatDate(created_at)}
                    </Text>
                  </BlockStack>

                  <RadialChart
                    score={score}
                    dimension={40}
                  />
                </InlineStack>
              </BlockStack>
            </Box>
          </InlineStack>
        </BlockStack>
      </Box>
    </Link>
  );
};

const RowsCellForLargeDevices = ({ item }) => {
  const { title, focus_keyword, created_at, score, issues, sl, page_id, published_at, page_type } = item;
  const { t } = useTranslation();
  const labelColor = useLabelColor(issues);
  const status = usePublishStatus(published_at);

  return (
    <>
      <IndexTable.Cell className="width-24">{sl}</IndexTable.Cell>
      <IndexTable.Cell className="break_coll_content width-resource_table_title">
        <TitleColumn
          title={title}
          url={`/optimize-seo/pages/${page_id}`}
          label={page_type === "BETTERDOCS_HOMEPAGE"}
          labelText="Doc Page"
        />
      </IndexTable.Cell>
      <IndexTable.Cell className="width-150">
        <OptimizeSeoStatusBadge score={score} />
      </IndexTable.Cell>
      <IndexTable.Cell className="break_coll_content width-resource_table_title">{focus_keyword}</IndexTable.Cell>
      <IndexTable.Cell>
        <Badge tone={labelColor}>
          {issues} {t("Issues")}
        </Badge>
      </IndexTable.Cell>
      <IndexTable.Cell>
        <RadialChart
          score={score}
          dimension={40}
        />
      </IndexTable.Cell>
      <IndexTable.Cell className="width-120">{formatDate(created_at)}</IndexTable.Cell>
      <IndexTable.Cell className="width-100">
        <Badge tone={status.color}>{t(status.name)}</Badge>
      </IndexTable.Cell>
      <IndexTable.Cell className="width-80">
        <InlineStack align="center">
          <TooltipWrapper content="Fix issue">
            <Button
              icon={ComposeIcon}
              onClick={(e) => e.stopPropagation()}
              variant="tertiary"
              url={`/optimize-seo/pages/${page_id}`}
            />
          </TooltipWrapper>
        </InlineStack>
      </IndexTable.Cell>
    </>
  );
};

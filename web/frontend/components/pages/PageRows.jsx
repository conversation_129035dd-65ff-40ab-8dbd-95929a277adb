import { IndexTable } from "@shopify/polaris";
import React from "react";

import { generateTableSL } from "../../utility/helpers.jsx";

import PageRowItem from "./PageRowItem.jsx";

const PageRows = ({ pages, isFetching = false, pagination }) => {
  return (
    <>
      {pages?.map(({ id, ...page }, index) => {
        const sl = generateTableSL(pagination, index);
        const item = { ...page, sl };
        return (
          <IndexTable.Row
            id={id}
            key={sl}
            position={sl}
          >
            <PageRowItem
              item={item}
              isFetching={isFetching}
            />
          </IndexTable.Row>
        );
      })}
    </>
  );
};

export default PageRows;

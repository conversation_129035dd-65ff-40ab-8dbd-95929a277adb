import { Block<PERSON>tack, Box, InlineStack, <PERSON>, <PERSON>, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import config, { CHANGELOG_URL, STOREWARE_URL } from "../../config";
import LanguageSwitcher from "./LanguageSwitcher";
import Logo from "./Logo.jsx";

const year = new Date().getFullYear();

// const Footer = () => {
//   const { t } = useTranslation();

//   return (
//     <footer className="footer">
//       <Link url="/">
//         {/* <Logo
//           width={150}
//           height={38}
//         /> */}

//         <img
//           src="https://cdn.storeseo.com/storeseo-logos/StoreSEO-Footer.png"
//           alt="StoreSEO Logo"
//           style={{
//             maxWidth: "150px",
//             height: "38px",
//           }}
//         />
//       </Link>
//       <InlineStack
//         gap="200"
//         align="center"
//         blockAlign="center"
//       >
//         <Text
//           as={"span"}
//           tone={"subdued"}
//         >
//           {t("Copyright")} © {year}
//         </Text>
//         <Text
//           as={"strong"}
//           tone={"subdued"}
//         >
//           |
//         </Text>
//         <Link
//           removeUnderline
//           monochrome
//           url={STOREWARE_URL}
//           target={"_blank"}
//         >
//           <Text
//             as={"strong"}
//             tone={"subdued"}
//           >
//             Storeware
//           </Text>
//         </Link>
//         <Text
//           as={"strong"}
//           tone={"subdued"}
//         >
//           |
//         </Text>
//         <Text
//           as={"strong"}
//           tone={"subdued"}
//         >
//           {t("Version")}:{" "}
//           <Link
//             removeUnderline
//             monochrome
//             url={CHANGELOG_URL}
//             target={"_blank"}
//           >
//             {config.appVersion}
//           </Link>
//         </Text>
//         |
//         <LanguageSwitcher buttonVariant="monochromePlain" />
//       </InlineStack>
//       <div></div>
//     </footer>
//   );
// };

const Footer = () => {
  const { t } = useTranslation();

  return (
    <div>
      <Page>
        <Box>
          <InlineStack
            gap={"400"}
            align="space-between"
            blockAlign="center"
          >
            <Link url="/">
              <img
                src="https://cdn.storeseo.com/storeseo-logos/StoreSEO-Footer.png"
                alt="StoreSEO Logo"
                style={{
                  maxWidth: "150px",
                  height: "38px",
                }}
              />
            </Link>
            <InlineStack
              gap="200"
              align="center"
              blockAlign="center"
            >
              <Text
                as={"span"}
                tone={"subdued"}
              >
                {t("Copyright")} © {year}
              </Text>
              <Text
                as={"strong"}
                tone={"subdued"}
              >
                |
              </Text>
              <Link
                removeUnderline
                monochrome
                url={STOREWARE_URL}
                target={"_blank"}
              >
                <Text
                  as={"strong"}
                  tone={"subdued"}
                >
                  Storeware
                </Text>
              </Link>
              <Text
                as={"strong"}
                tone={"subdued"}
              >
                |
              </Text>
              <Text
                as={"strong"}
                tone={"subdued"}
              >
                {t("Version")}:{" "}
                <Link
                  removeUnderline
                  monochrome
                  url={CHANGELOG_URL}
                  target={"_blank"}
                >
                  {config.appVersion}
                </Link>
              </Text>
              |
              <LanguageSwitcher buttonVariant="monochromePlain" />
            </InlineStack>
          </InlineStack>
        </Box>
      </Page>
    </div>
  );
};

export default Footer;

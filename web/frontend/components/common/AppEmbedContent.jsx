import { <PERSON>ge, BlockStack, Button, Card, Grid, InlineGrid, InlineStack, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useAppQuery, useUtilityApi } from "../../hooks";
import { useAppEmbed } from "../../hooks/useAppEmbed";
import { setUser } from "../../store/features/User";

const TITLE = "Enable App Embed";

export default function AppEmbedContent() {
  const { t } = useTranslation();
  const { appEmbedUrl } = useAppEmbed();
  const utilityApi = useUtilityApi();
  const dispatch = useDispatch();
  const user = useSelector((state) => state.user);

  const { data: appEmbedAlreadyEnabled, isFetching } = useAppQuery({
    queryKey: "APP_EMBED_STATUS",
    queryFn: utilityApi.checkThemeAppEmbedStatus,
    reactQueryOptions: {
      staleTime: 0,
      refetchOnWindowFocus: true,
      onSuccess: (enabled) => {
        dispatch(setUser({ ...user, appEmbedEnabled: enabled }));
      },
    },
  });

  return (
    <Card>
      <InlineGrid
        columns={6}
        gap={"400"}
      >
        <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 4, lg: 4, xl: 4 }}>
          <BlockStack gap="300">
            <InlineStack
              blockAlign="center"
              gap="200"
            >
              <Text
                variant="headingSm"
                as="h3"
              >
                {t(TITLE)}
              </Text>

              <Badge tone={appEmbedAlreadyEnabled ? "success" : "warning"}>
                {t(appEmbedAlreadyEnabled ? "On" : "Off")}
              </Badge>
            </InlineStack>

            <Text>
              {t(
                "App embed block helps StoreSEO to output relevant SEO data to each page of your online store! Enable it to have correct SEO meta to your website."
              )}
            </Text>

            <Text>
              {t(
                "Note: The button below will open a new window with app embed enabled. You only need to click 'Save' in that window."
              )}
            </Text>
            <InlineStack gap={"200"}>
              <Button
                variant="primary"
                target="_blank"
                url={appEmbedUrl}
                loading={isFetching}
                disabled={appEmbedAlreadyEnabled}
              >
                {t("Enable app embed")}
              </Button>
            </InlineStack>
          </BlockStack>
        </Grid.Cell>
        <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 2, lg: 2, xl: 2 }}>
          <InlineStack align="center">
            <img
              width="100%"
              src="https://cdn.storeseo.com/enable-app-embed.gif"
              style={{
                maxWidth: "400px",
                borderWidth: "var(--p-border-width-025)",
                borderColor: "var(--p-color-border-brand)",
                borderRadius: "var(--p-border-radius-300)",
                borderStyle: "solid",
              }}
            />
          </InlineStack>
        </Grid.Cell>
      </InlineGrid>
    </Card>
  );
}

import { InlineStack, Pagination } from "@shopify/polaris";
import React from "react";
import { useTranslation } from "react-i18next";
import { useSearchParams } from "react-router-dom";

const CommonPagination = ({ pagination }) => {
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();

  const goToPage = (page) => {
    if (page !== "undefined" && page) {
      setSearchParams({
        ...Object.fromEntries(searchParams.entries()),
        page,
      });
    }
  };

  const hasMultiplePage = pagination?.pageCount > 1;

  if (hasMultiplePage) {
    const nextPage = pagination.page < pagination.pageCount ? pagination.page + 1 : null;
    const prevPage = pagination.page > 1 ? pagination.page - 1 : null;

    return (
      <InlineStack
        align={"center"}
        gap={{ xs: "200", sm: "300", md: "400", lg: "500", xl: "600" }}
      >
        <Pagination
          label={t("Page {{pageNo}} of {{totalPage}}", { pageNo: pagination.page, totalPage: pagination.pageCount })}
          hasPrevious={!!prevPage}
          previousKeys={[74]}
          previousTooltip={t("Prev (J)")}
          onPrevious={() => goToPage(prevPage)}
          hasNext={!!nextPage}
          nextKeys={[75]}
          nextTooltip={t("Next (K)")}
          onNext={() => goToPage(nextPage)}
        />
      </InlineStack>
    );
  }

  return null;
};

export default CommonPagination;

import { Modal } from "@shopify/polaris";
import "cropperjs/dist/cropper.css";
import { useState } from "react";
import { Cropper } from "react-cropper";
import { useTranslation } from "react-i18next";

/**
 * ImgCropper component for cropping images before upload.
 *
 * @typedef {Object} CropResult
 * @property {File} file - The cropped image file.
 * @property {string} dataURL - The data URL of the cropped image.
 *
 * @param {Object} props - The component props.
 * @param {boolean} props.show - Whether the modal is visible.
 * @param {string} props.imageSrc - The source URL of the image to be cropped.
 * @param {function(CropResult):void} [props.onCropComplete] - Callback function to handle the cropped image.
 * @param {function():void} [props.onCancel] - Callback function to handle the cancel action.
 * @param {number} [props.aspectRatio] - The aspect ratio for cropping.
 * @param {string} [props.imageName] - The name of the image file.
 * @param {boolean} [props.isLoading] - Whether the primary action is in a loading state.
 *
 * @returns {JSX.Element} The ImgCropper component.
 */
const ImgCropper = ({
  show,
  imageSrc,
  onCropComplete = () => {},
  onCancel = () => {},
  aspectRatio = NaN,
  imageName = "foo",
  isLoading = false,
}) => {
  const [cropper, setCropper] = useState(null);

  const { t } = useTranslation();

  const handleCrop = () => {
    if (cropper) {
      cropper.getCroppedCanvas().toBlob(
        (blob) => {
          const file = new File([blob], imageName.split(".")[0] + ".jpeg", { type: "image/jpeg" });
          onCropComplete({
            file,
            dataURL: URL.createObjectURL(file),
          });
        },
        "image/jpeg",
        0.85
      );
    }
  };

  return (
    <Modal
      open={show}
      title={t("Crop image before upload")}
      onClose={onCancel}
      primaryAction={{ content: t("Crop"), onAction: handleCrop, loading: isLoading }}
      secondaryActions={[{ content: t("Cancel"), onAction: onCancel }]}
    >
      <Cropper
        style={{ height: "calc(35%)", maxHeight: "355px", width: "100%" }}
        zoomTo={0}
        initialAspectRatio={aspectRatio}
        aspectRatio={aspectRatio}
        src={imageSrc}
        viewMode={1}
        minCropBoxHeight={10}
        minCropBoxWidth={10}
        background={false}
        responsive={true}
        autoCropArea={1}
        checkOrientation={false}
        onInitialized={(instance) => {
          setCropper(instance);
        }}
        guides={true}
      />
    </Modal>
  );
};

export default ImgCropper;

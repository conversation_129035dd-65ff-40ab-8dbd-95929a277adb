import { BlockStack, Button, Icon, InlineStack, TextField, Thumbnail } from "@shopify/polaris";
import {
  AlertCircleIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
  ImagesIcon,
  ImageWithTextOverlayIcon,
  MagicIcon,
} from "@shopify/polaris-icons";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import analysisEntityTypes from "storeseo-enums/analysisEntityTypes";
import imageOptimization from "storeseo-enums/imageOptimization";
import ResourceType from "storeseo-enums/resourceType";
import { useSingleImageOptimizer } from "../../hooks/imageOptimizer/useSingleImageOptimizer.jsx";
import { useBanner } from "../../hooks/useBanner.jsx";
import useUserAddon from "../../hooks/useUserAddon";
import { cardBodyPaddingStyle, flexBasisStyle } from "../../styles/common";
import { isSvgImage, prepareThumbnailURL } from "../../utility/helpers.jsx";
import ImageCompareModal from "../image-optimizer/ImageCompareModal.jsx";
import CollapsibleCard from "./CollapsibleCard";
import EmptyPage from "./EmptyPage.jsx";
import TooltipWrapper from "./TooltipWrapper.jsx";

const getAbsEntityType = (entityType) => {
  switch (entityType) {
    case analysisEntityTypes.PAGE:
      return "pages";
    case analysisEntityTypes.COLLECTION:
      return "collections";
    case analysisEntityTypes.ARTICLE:
      return "blog-posts";
    case analysisEntityTypes.DOC:
      return "docs";
    default:
      return "";
  }
};

const AltTextEditForm = ({
  title = "",
  images,
  onChange = (images) => {},
  onOptimize = (images) => {},
  emptyStateMsg = "No image found",
  emptyStateActionLabel = "Add image",
  onEmptyStateAction = () => {},
  imageOptimizerEnabled = true,
  imageAltTextGeneratorEnabled = true,
  isDisabled = false,
  products = [],
  multiLanugageMode = false,
  entityType = analysisEntityTypes.PRODUCT,
}) => {
  isDisabled = multiLanugageMode ? true : isDisabled;

  const { t } = useTranslation();
  const navigate = useNavigate();

  const [imageToCompare, setImageToCompare] = useState(null);

  const setImgAltText = (value, image) => {
    const updImages = images.map((img) => {
      const updatedImage = {
        ...img,
      };

      if (img.id === image.id) {
        updatedImage.altText = value;
      }

      delete updatedImage.product;
      return updatedImage;
    });

    onChange(updImages);
  };

  const { Banner, showBanner } = useBanner({
    title: t("Image optimizer max usage limit reached!"),
    noMargin: true,
    icon: AlertTriangleIcon,
  });

  const user = useSelector((s) => s.user);
  const { hasImageOptimizer, imageOptimizerUsageCount, imageOptimizerUsageLimit } = useUserAddon();

  useEffect(() => {
    if (hasImageOptimizer && imageOptimizerUsageCount >= imageOptimizerUsageLimit) showBanner(true);
  }, [user]);

  const imageOptimizerUrl =
    entityType !== analysisEntityTypes.PRODUCT
      ? `/image-optimizer/${getAbsEntityType(entityType)}`
      : "/image-optimizer";

  const optimizeAllButton = (
    <InlineStack
      blockAlign="center"
      gap="200"
    >
      {imageAltTextGeneratorEnabled && (
        <Button
          variant="plain"
          tone="magic"
          icon={MagicIcon}
          onClick={() => navigate(`/image-alt-text?search=${title}`)}
        >
          {t("Generate alt-text with AI")}
        </Button>
      )}
      {imageOptimizerEnabled && (
        <Button
          variant="plain"
          icon={ImageWithTextOverlayIcon}
          onClick={() => navigate(`${imageOptimizerUrl}?search=${title}`)}
        >
          {t("Optimize All")}
        </Button>
      )}
    </InlineStack>
  );

  return (
    <CollapsibleCard
      title="Images"
      suffix={
        multiLanugageMode ? (
          <TooltipWrapper
            show
            content={t("Please note that image alt-text is not translatable")}
          >
            <Icon
              tone="warning"
              source={AlertCircleIcon}
            />
          </TooltipWrapper>
        ) : null
      }
      action={images.length && !multiLanugageMode ? optimizeAllButton : null}
    >
      {imageToCompare && (
        <ImageCompareModal
          image={imageToCompare}
          onOptimize={(images) => {
            setImageToCompare(images[0]);
            onOptimize(images);
          }}
          onRestore={(images) => {
            setImageToCompare(null);
            onOptimize(images);
          }}
          onClose={() => setImageToCompare(null)}
        />
      )}

      <div style={cardBodyPaddingStyle}>
        {images.length === 0 && (
          <EmptyPage
            content={t(emptyStateMsg)}
            insideCard={false}
            primaryAction={<Button onClick={onEmptyStateAction}>{t(emptyStateActionLabel)}</Button>}
          />
        )}
        <BlockStack gap="400">
          <Banner />
          {images.map((img, idx) => (
            <InlineStack
              key={img.id}
              gap="400"
              wrap={false}
              blockAlign="center"
            >
              <Thumbnail
                source={entityType === analysisEntityTypes.DOC ? img.src : prepareThumbnailURL(img.src)}
                size="medium"
                alt={img.altText}
              />
              <div style={flexBasisStyle}>
                <TextField
                  label={t("Alt text")}
                  value={img.altText || ""}
                  placeholder={title}
                  onChange={(val) => setImgAltText(val, img)}
                  autoComplete="off"
                  connectedRight={
                    imageOptimizerEnabled && (
                      <ImageOptimizerButtons
                        image={{ ...img, products }}
                        onOptimize={onOptimize}
                        onRestore={onOptimize}
                        onCompare={() => setImageToCompare(img)}
                      />
                    )
                  }
                  disabled={isDisabled}
                />
              </div>
            </InlineStack>
          ))}
        </BlockStack>
      </div>
    </CollapsibleCard>
  );
};

const ImageOptimizerButtons = ({ image, onOptimize, onRestore, onCompare }) => {
  const { t } = useTranslation();
  const { imageOptimizerUsageCount, imageOptimizerUsageLimit } = useUserAddon();
  const { optimizeImage, isOptimizingImage } = useSingleImageOptimizer({
    onOptimize,
    onRestore,
    resourceType: ResourceType.PRODUCT,
  });

  return (
    <InlineStack
      wrap={false}
      gap="200"
      blockAlign="center"
    >
      {image?.isOptimized ? (
        <>
          <TooltipWrapper content="Optimized">
            <Icon
              source={CheckCircleIcon}
              tone="textSuccess"
            />
          </TooltipWrapper>

          <TooltipWrapper content="Compare">
            <Button
              variant="tertiary"
              icon={ImagesIcon}
              onClick={onCompare}
            />
          </TooltipWrapper>
        </>
      ) : (
        <>
          {image.optimization_status === imageOptimization.ALREADY_OPTIMIZED && (
            <TooltipWrapper content="Already optimized">
              <Icon
                source={CheckCircleIcon}
                tone="textSuccess"
              />
            </TooltipWrapper>
          )}
          <TooltipWrapper
            content={
              isSvgImage(image.src)
                ? "SVG image optimization is not available right now. We're working to make it available soon..."
                : "Optimize"
            }
          >
            <Button
              variant="tertiary"
              icon={ImageWithTextOverlayIcon}
              onClick={() => optimizeImage(image)}
              loading={isOptimizingImage}
              disabled={imageOptimizerUsageCount >= imageOptimizerUsageLimit || isSvgImage(image.src)}
            />
          </TooltipWrapper>
        </>
      )}
    </InlineStack>
  );
};

export default AltTextEditForm;

import { <PERSON><PERSON>, InlineStack, Link } from "@shopify/polaris";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Flex } from "./CustomComponent";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import ScheduleACall from "../modals/ScheduleACall.jsx";
import ProBadge from "../svg/ProBadge.jsx";
import Notifications from "../notifications/index.jsx";
import { useSelector } from "react-redux";
import { XIcon, MenuIcon } from "@shopify/polaris-icons";
import LanguageSwitcher from "./LanguageSwitcher";
import { isEmpty } from "lodash";

const ACTIVE_CLASS = "active";

function Header() {
  const { t } = useTranslation();
  const user = useSelector((state) => state.user);
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const [showMobileMenu, setShowMobileMenu] = useState(false);

  const menus = [
    {
      id: "dashboard",
      content: t("Dashboard"),
      url: "/",
    },
    {
      id: "products",
      content: t("Products"),
      url: "/products",
    },
    {
      id: "analytics",
      content: (
        <InlineStack gap="100">
          {t("Analytics")} {!isEmpty(user) && !user.isPremium && <ProBadge />}
        </InlineStack>
      ),
      url: "/analytics",
    },
    {
      id: "pages",
      content: t("Pages"),
      url: "/pages",
    },
    {
      id: "blog-posts",
      content: t("Blog Posts"),
      url: "/articles",
    },
    {
      id: "reports",
      content: t("Reports"),
      url: "/reports",
    },
    {
      id: "sitemaps",
      content: t("Sitemaps"),
      url: "/sitemaps",
    },
    {
      id: "image-optimizer",
      content: t("Image Optimizer"),
      url: "/image-optimizer",
    },
    {
      id: "settings",
      content: t("Settings"),
      url: "/settings",
    },
  ];

  const isActive = (path) => {
    if (location.pathname === "/" && path === "/") {
      return ACTIVE_CLASS;
    }

    if (path !== "/" && location.pathname.startsWith(path)) {
      return ACTIVE_CLASS;
    }
  };

  const handleMobileMenu = () => {
    setShowMobileMenu(!showMobileMenu);
  };

  const goToPath = (path) => {
    if (showMobileMenu) setShowMobileMenu(!showMobileMenu);
    if (isActive(path) !== ACTIVE_CLASS) return navigate(path);

    searchParams.append("refresh", "1");
    return navigate(`${path}?${searchParams.toString()}`);
  };

  return (
    <div className="ss-header">
      <Flex
        alignItems="center"
        justifyContent="space-between"
        gap="12px"
      >
        <div className="ss-nav-wrap">
          <Button
            onClick={handleMobileMenu}
            icon={!showMobileMenu ? MenuIcon : XIcon}
          />
          <ul className={`ss-nav ${showMobileMenu ? "show" : ""}`}>
            {menus &&
              menus.map((menu) => (
                <li
                  key={menu.id}
                  className={`nav-item ${isActive(menu.url)}`}
                >
                  <Link
                    monochrome
                    removeUnderline
                    onClick={() => goToPath(menu.url)}
                  >
                    {menu.content}
                  </Link>
                </li>
              ))}
          </ul>
        </div>
        <Flex
          gap="12px"
          alignItems="center"
          justifyContent="center"
        >
          <LanguageSwitcher />
          <Notifications />
          <ScheduleACall />
        </Flex>
      </Flex>
    </div>
  );
}

export default Header;

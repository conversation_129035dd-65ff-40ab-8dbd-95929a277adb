import { But<PERSON>, I<PERSON>, Tooltip } from "@shopify/polaris";
import { ResetIcon } from "@shopify/polaris-icons";
import { isEmpty } from "lodash";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import ConfirmationModal from "../modals/ConfirmationModal.jsx";

const SyncButton = ({ title, loading, callback, syncState, allItemsSynced = false, platform = "Shopify" }) => {
  const { t } = useTranslation();
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const [tooltipContent, setTooltipContent] = useState("");

  useEffect(() => {
    if (tooltipContent.length > 0) {
      setShowTooltip(true);
    } else {
      setShowTooltip(false);
    }
  }, [tooltipContent]);

  useEffect(() => {
    if (!isEmpty(syncState)) {
      setTooltipContent(syncState.ongoing ? `${t(`Syncing ${title}`)} ${syncState.total || "..."}` : "");
    }
    if (allItemsSynced) {
      setTooltipContent(`${t(`All ${title.toLowerCase()} synced`)}`);
    }
  }, [syncState, allItemsSynced]);

  const handleSync = async () => {
    setShowConfirmModal(true);
  };

  const actionTitle = t(`Sync ${title}`);

  return (
    <>
      <Tooltip
        active={showTooltip}
        content={t(tooltipContent)}
        persistOnClick
        activatorWrapper={"div"}
        zIndexOverride={99}
      >
        <Button
          icon={<Icon source={ResetIcon} />}
          onClick={handleSync}
          disabled={allItemsSynced}
          loading={loading || syncState.ongoing}
        >
          {t(actionTitle)}
        </Button>
      </Tooltip>

      <ConfirmationModal
        show={showConfirmModal}
        onClose={setShowConfirmModal}
        title={actionTitle}
        content={`Are you sure you want to sync ${title} from ${platform}?`}
        primaryAction={async () => {
          await callback();
          setShowConfirmModal(false);
        }}
        loading={loading}
      />
    </>
  );
};

export default SyncButton;

import { <PERSON><PERSON>, Banner, CalloutCard, InlineStack, Label, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useShopApi } from "../../hooks";
import { BANNER_WHATS_NEW } from "storeseo-enums/cacheKeys";
import { setHiddenBanner } from "../../store/features/HiddenBanner";

const title = "🎉 What's New";
const content =
  "Generate an HTML Sitemap page instantly on your store & help your visitors find all of your pages in one place";
const path = "/sitemaps/html-sitemap";
const actionLabel = "Check it out";

const NewFeatureCard = () => {
  const { t } = useTranslation();
  const { whatsNew } = useSelector((state) => state.hiddenBanner);
  const navigate = useNavigate();

  const dispatch = useDispatch();
  const shopApi = useShopApi();

  const handleClose = async () => {
    const { value } = await shopApi.hideBanner(BANNER_WHATS_NEW);
    dispatch(setHiddenBanner({ whatsNew: false }));
  };

  const onAction = () => navigate(path);

  if (!whatsNew) return null;

  return (
    <CalloutCard
      title={
        <InlineStack gap="200">
          {title} <Badge tone="info">Free</Badge>
        </InlineStack>
      }
      primaryAction={{
        content: actionLabel,
        onAction: onAction,
      }}
      onDismiss={handleClose}
    >
      <Text
        as="p"
        variant="subdued"
      >
        {t(content)}
      </Text>
    </CalloutCard>
    // <Banner
    //   title={title}
    //   hideIcon
    //   onDismiss={handleClose}
    //   action={{ content: actionLabel, onAction }}
    // >
    //   <Text
    //     as="p"
    //     variant="subdued"
    //   >
    //     {t(content)}
    //   </Text>
    // </Banner>
  );
};

export default NewFeatureCard;

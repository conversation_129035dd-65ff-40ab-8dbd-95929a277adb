import { BlockStack, Box, Card, InlineStack, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { EMPTYSTATE_IMAGES } from "../../config";
import TableContentFilter from "./TableContentFilter";

const EmptyPage = ({
  heading,
  content = "",
  insideCard = true,
  primaryAction = undefined,
  secondaryAction = undefined,
  withWrapper = true,
  withTableFilter = false,
  image = EMPTYSTATE_IMAGES.default,
}) => {
  const { t } = useTranslation();

  const emptyState = (
    <Box padding="1600">
      <BlockStack
        gap="400"
        inlineAlign="center"
      >
        <img
          role="presentation"
          src={image}
          alt={heading}
          width={170}
          height={170}
        />
        {heading && (
          <Text
            as={"h4"}
            variant="headingMd"
          >
            {t(heading)}
          </Text>
        )}
        {content && <Text as={"p"}>{t(content)}</Text>}

        <InlineStack gap="200">
          {secondaryAction}
          {primaryAction}
        </InlineStack>
      </BlockStack>
    </Box>
  );

  return insideCard ? (
    withTableFilter ? (
      <Card padding="0">
        <TableContentFilter />
        {emptyState}
      </Card>
    ) : withWrapper ? (
      <div style={{ maxWidth: "950px", margin: "0 auto" }}>
        <Card>{emptyState}</Card>
      </div>
    ) : (
      <Card>{emptyState}</Card>
    )
  ) : (
    emptyState
  );
};

export default EmptyPage;

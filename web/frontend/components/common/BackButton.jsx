import React from "react";
import { useNavigate } from "react-router-dom";

const BackButton = ({ additionalClass = "" }) => {
  const navigate = useNavigate();

  return (
    <a
      href="#"
      onClick={(e) => {
        e.preventDefault();
        navigate(-1);
      }}
      className={["go__back", additionalClass].join(" ")}
    >
      <i className="ss-icon ss-arrow-left" />
    </a>
  );
};

export default BackButton;

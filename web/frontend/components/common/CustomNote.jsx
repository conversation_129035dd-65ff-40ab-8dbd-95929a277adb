import { Bleed, Box, Icon, InlineStack, Text } from "@shopify/polaris";
import { InfoIcon } from "@shopify/polaris-icons";

/**
 * @typedef {Object} CustomNoteProps
 * @property {string} [content=""] - The content to display in the note.
 * @property {any} [icon] - The icon to display in the note. If null or empty string, no icon is shown. Defaults to InfoIcon.
 */

/**
 * CustomNote component displays an informational note with an optional icon.
 *
 * @param {CustomNoteProps} props - The properties for the CustomNote component.
 * @returns {JSX.Element} The rendered CustomNote component.
 */

const CustomNote = ({ content = "", icon = InfoIcon }) => {
  const showIcon = icon !== null && icon !== undefined && icon !== "";

  return (
    <Bleed
      marginInline={"400"}
      marginBlockEnd="400"
    >
      <Box
        background="bg-surface-secondary"
        padding="400"
      >
        <InlineStack
          gap="200"
          align="start"
          blockAlign="center"
        >
          {showIcon && (
            <Box as="span">
              <Icon source={icon} />
            </Box>
          )}
          <Text
            as="p"
            variant="bodySm"
            tone="subdued"
          >
            {content}
          </Text>
        </InlineStack>
      </Box>
    </Bleed>
  );
};

export default CustomNote;

import { Banner, Text } from "@shopify/polaris";
import { LockIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";

function ProFeatureBanner({
  title,
  content,
  applyForSpecificFeature = false,
  featureKey = "",
  actionLabel = "Upgrade to StoreSEO PRO",
}) {
  const { t } = useTranslation();
  const user = useSelector((state) => state.user);

  // Check for general premium access
  if (!applyForSpecificFeature && user.isPremium) return null;

  // Check for specific feature permission
  if (applyForSpecificFeature && user.permission?.[featureKey]) return null;

  return (
    <Banner
      tone="warning"
      icon={LockIcon}
      title={t(title)}
      action={{
        content: t(actionLabel),
        url: "/subscription",
      }}
    >
      <Text
        as="p"
        tone="subdued"
      >
        {t(content)}
      </Text>
    </Banner>
  );
}

export default ProFeatureBanner;

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Drop<PERSON><PERSON>, InlineStack, Text, Thumbnail } from "@shopify/polaris";
import { UploadIcon } from "@shopify/polaris-icons";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { useDispatch } from "react-redux";
import { useAppQuery, useShopApi } from "../../hooks";
import { useBanner } from "../../hooks/useBanner.jsx";
import { updateUser } from "../../store/features/User";
import { validateImageFile, validateImageFormat } from "../../utility/helpers";
import queryKeys from "../../utility/queryKeys";
import ImgCropper from "./ImgCropper.jsx";

/**
 * @typedef {Object} LogoUploaderViewerProps
 * @property {string} [currentLogoURL]
 * @property {Function} [onUploadStart]
 * @property {Function} [onUploadFinish]
 * @property {string} [title]
 * @property {boolean} [disabled]
 */

/**
 * @param {LogoUploaderViewerProps} props
 */
const LogoUploaderViewer = ({
  currentLogoURL = null,
  onUploadStart = () => {},
  onUploadFinish = (uploadPath) => uploadPath,
  title = "Add Store Logo",
  disabled = false,
}) => {
  const { t } = useTranslation();
  const shopApi = useShopApi();
  const dispatch = useDispatch();

  const [logoURL, setLogoURL] = useState(currentLogoURL);
  const [showCropper, setShowCropper] = useState(false);
  const [inputImage, setInputImage] = useState(null);

  useEffect(() => {
    setLogoURL(currentLogoURL);
  }, [currentLogoURL]);

  const { data: shop, isFetching } = useAppQuery({
    queryKey: queryKeys.SHOP_INFO,
    queryFn: shopApi.getShop,
    reactQueryOptions: {
      enabled: !currentLogoURL,
    },
  });

  const { mutate: uploadLogo, isLoading: isUploading } = useMutation({
    mutationFn: ({ file }) => shopApi.uploadShopLogo(file),
    onMutate: () => {
      onUploadStart();
    },
    onSuccess: ({ uploadPath, logoURL }) => {
      setLogoURL(logoURL);
      onUploadFinish(uploadPath, logoURL);
      dispatch(updateUser({ shopLogo: logoURL }));
      hideCropper();
    },
  });

  const { Banner, showBanner } = useBanner({
    title: t("Logo image too large!"),
    message: t("Your image size exceeds 850KB after crop. Please crop to a smaller dimension or use a small image."),
  });

  useEffect(() => {
    if (shop && !currentLogoURL) {
      setLogoURL(shop.logoURL);
    }
  }, [shop]);

  const handleLogoUpload = async ({ file, dataURL }) => {
    if (validateImageFile(file)) {
      uploadLogo({ file });
    } else {
      showBanner(true);
      hideCropper();
    }
  };

  const hideCropper = () => {
    setShowCropper(false);
    setInputImage(null);
  };

  const handleLogoImageChange = async (files) => {
    try {
      validateImageFormat(files[0]);
      setInputImage(files[0]);
      setShowCropper(true);
      showBanner(false);
    } catch (e) {}
  };

  const [openFileDialog, setOpenFileDialog] = useState(false);

  return (
    <Card>
      <Banner />
      <InlineStack
        gap="400"
        blockAlign="center"
        align="space-between"
      >
        <div style={{ width: 60, height: 60, position: "relative" }}>
          <DropZone
            type="image"
            accept="image/*"
            allowMultiple={false}
            dropOnPage={true}
            openFileDialog={openFileDialog}
            onFileDialogClose={() => {
              setOpenFileDialog(false);
            }}
            onDrop={(file) => handleLogoImageChange(file)}
            disabled={disabled}
          >
            {logoURL ? (
              <Thumbnail
                source={logoURL}
                alt={shop?.name}
                size="medium"
              />
            ) : (
              <DropZone.FileUpload />
            )}
          </DropZone>
        </div>
        <div style={{ marginRight: "auto" }}>
          <BlockStack gap="100">
            <Text
              varient="headingSm"
              as="h6"
            >
              {t(title)}
            </Text>
            <Text
              variant="bodySm"
              as="p"
              tone="subdued"
            >
              {t("HEIC, WEBP, SVG, PNG, or JPG. Recommended size: 850KB maximum.")}
            </Text>
          </BlockStack>
        </div>
        <Button
          icon={UploadIcon}
          onClick={() => setOpenFileDialog(true)}
          loading={isUploading}
          disabled={disabled}
        >
          {t("Upload")}
        </Button>
      </InlineStack>

      {showCropper && inputImage && (
        <ImgCropper
          show
          imageSrc={URL.createObjectURL(inputImage)}
          title={t("Crop logo image")}
          onCropComplete={handleLogoUpload}
          onCancel={hideCropper}
          isLoading={isUploading}
        />
      )}
    </Card>
  );
};

export default LogoUploaderViewer;

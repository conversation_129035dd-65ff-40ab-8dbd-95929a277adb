import Modal from "@/modules/components/Modal";
import { BlockStack, Box, InlineStack, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { PRO_ALERT_MODAL } from "../../config/modal";

const ProAlertPopUp = ({ show = false, onClose }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  return (
    <Modal
      type="app-bridge"
      open={show}
      setOpen={(open) => {
        if (!open) onClose();
      }}
      onClose={onClose}
    >
      <Modal.Section>
        <Box padding="400">
          <BlockStack gap="200">
            {PRO_ALERT_MODAL.items.map((item, idx) => (
              <InlineStack
                key={idx}
                gap="200"
              >
                <Text>{item.icon}</Text>
                <Text as="p">{t(item.label)}</Text>
              </InlineStack>
            ))}
          </BlockStack>
        </Box>
        <Modal.TitleBar title={t(PRO_ALERT_MODAL.title)}>
          <button
            variant="primary"
            onClick={() => navigate("/subscription")}
          >
            {t(PRO_ALERT_MODAL.primaryActionLabel)}
          </button>
        </Modal.TitleBar>
      </Modal.Section>
    </Modal>
  );
};

export default ProAlertPopUp;

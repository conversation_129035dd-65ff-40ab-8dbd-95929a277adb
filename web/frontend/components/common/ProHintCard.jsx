import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { BlockStack, Box, Button, Card, Divider, InlineStack, Text } from "@shopify/polaris";
import classNames from "classnames";
import { PRO_ALERT_MODAL, PRO_ALERT_MODAL_SM } from "../../config/modal.js";
import { useEffect } from "react";

const ProHintCard = ({ fullVersion = true, scrollIntoView = true }) => {
  const user = useSelector((state) => state.user);
  const { t } = useTranslation();
  const navigate = useNavigate();
  // const popUpRef = useRef();

  const { title, content, items, primaryActionLabel } = fullVersion ? PRO_ALERT_MODAL : PRO_ALERT_MODAL_SM;

  useEffect(() => {
    // if (fullVersion && scrollIntoView) popUpRef?.current?.scrollIntoView({ behavior: "smooth" });
  }, []);

  if (user.isPremium) return null;

  return (
    <>
      <Card padding="0">
        <BlockStack gap="400">
          <Box
            paddingInlineStart="400"
            paddingInlineEnd="400"
            paddingBlockStart="400"
          >
            <Text
              as="h3"
              variant="headingLg"
            >
              {t(title)}
            </Text>
          </Box>

          <Divider />

          <Box
            paddingInlineStart="400"
            paddingInlineEnd="400"
          >
            <BlockStack gap="200">
              {items.map((item, index) => (
                <InlineStack
                  gap="200"
                  key={index}
                >
                  <Text>{item.icon}</Text>
                  <Text as="p">{t(item.label)}</Text>
                </InlineStack>
              ))}
            </BlockStack>
          </Box>

          <Divider />

          <Box
            paddingInlineStart="400"
            paddingInlineEnd="400"
            paddingBlockEnd="400"
          >
            <InlineStack align="end">
              <Button
                onClick={() => navigate("/subscription")}
                variant="primary"
              >
                {t(primaryActionLabel)}
              </Button>
            </InlineStack>
          </Box>
        </BlockStack>
      </Card>
    </>
  );
};

export default ProHintCard;

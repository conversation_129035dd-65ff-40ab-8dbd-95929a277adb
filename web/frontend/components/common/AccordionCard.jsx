import React from "react";
import { useState } from "react";
import { useTranslation } from "react-i18next";

const AccordionCard = ({ title, children, defaultOpen = false }) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const { t } = useTranslation();

  return (
    <div className="ss__accordion__item">
      <div
        className={`accordion__head c-pointer ${isOpen && "show"}`}
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="product__analysis__issues">
          <p className="issue__name">{t(title)}</p>
        </div>
      </div>

      <div className={`accordion__body ${isOpen && "show"}`}>
        <div className="product__preview__wrap">{children}</div>
      </div>
    </div>
  );
};

export default AccordionCard;

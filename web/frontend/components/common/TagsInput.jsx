import { Autocomplete, Icon, InlineStack, Tag } from "@shopify/polaris";
import { PlusCircleIcon } from "@shopify/polaris-icons";
import { useState } from "react";
import { useTranslation } from "react-i18next";

const SelectedTagsDisplayMarkup = ({ options, onRemove = (val) => {} }) => {
  return (
    <InlineStack gap="150">
      {options.map((option, idx) => (
        <Tag
          key={`${option}-${idx}`}
          onRemove={() => onRemove(option)}
        >
          {option}
        </Tag>
      ))}
    </InlineStack>
  );
};

export default function TagsInput({ options, onChange = (optionsList) => {}, onTagRemove = null }) {
  const { t } = useTranslation();
  const [inputValue, setInputValue] = useState("");

  const autoCompleteOptions = options.map((o) => ({ label: o, value: o }));

  const handleInsert = (val) => {
    if (!options.includes(val)) onChange([val, ...options]);

    setInputValue("");
  };

  const handleRemove = (val) => {
    // Call the onTagRemove callback if provided
    if (onTagRemove) {
      onTagRemove(val);
    }

    // Update the options list by removing the tag
    onChange(options.filter((option) => val !== option));
  };

  return (
    <Autocomplete
      allowMultiple
      options={autoCompleteOptions.filter((option) => option.value.toLowerCase().startsWith(inputValue))}
      selected={options}
      onSelect={onChange}
      listTitle={t("Added tags")}
      textField={
        <Autocomplete.TextField
          label={t("Tags")}
          autoComplete="off"
          placeholder="Vintage, cotton, summer"
          value={inputValue}
          onChange={setInputValue}
          verticalContent={
            options.length ? (
              <SelectedTagsDisplayMarkup
                options={options}
                onRemove={handleRemove}
              />
            ) : null
          }
        />
      }
      actionBefore={
        inputValue
          ? {
              accessibilityLabel: `Add '${inputValue}'`,
              content: `Add '${inputValue}'`,
              prefix: <Icon source={PlusCircleIcon} />,
              onAction: () => handleInsert(inputValue),
            }
          : null
      }
    />
  );
}

import { Button, Text } from "@shopify/polaris";
import { ComposeIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";

const FixButton = ({ onClick }) => {
  const { t } = useTranslation();

  return (
    <Button
      onClick={onClick}
      // variant="plain"
      icon={ComposeIcon}
    >
      <Text
        as="span"
        variant="headingSm"
      >
        {t("Fix issues")}
      </Text>
    </Button>
  );
};

export default FixButton;

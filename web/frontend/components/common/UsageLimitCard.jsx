// @ts-check
import { useAppBridgeModal } from "@/hooks/useAppBridgeModal";
import { useSubscribeNowAction } from "@/lib/hooks/subscription";
import { showPurchase } from "@/store/features/Purchase";
import { formatNumber } from "@/utility/helpers";
import { Banner, BlockStack, Button, Card, InlineStack, ProgressBar, Text } from "@shopify/polaris";
import { AlertCircleIcon } from "@shopify/polaris-icons";
import moment from "moment";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import SubscriptionAddonGroup from "storeseo-enums/subscriptionAddonGroup";
import SubscriptionAddonInterval from "storeseo-enums/subscriptionAddonInterval";
import useUserAddon from "../../hooks/useUserAddon";
import LearnMoreButton from "../common/LearnMoreButton";

/**
 * @typedef {object} LearnMoreButtonType
 * @property {string} title
 * @property {string} url
 */

/**
 * @typedef {object} ActionType
 * @property {string} content
 * @property {string} url
 */

/**
 * @param {{
 *  title: string,
 *  group: string,
 *  hiddenBannerKey: string,
 *  learnMoreButton: LearnMoreButtonType,
 *  action: ActionType,
 *  imageOptimizeSettings: {autoOptimization: boolean}
 * }} props
 * @returns
 */

const UsageLimitCard = ({ title, group, learnMoreButton, action, imageOptimizeSettings }) => {
  const { t } = useTranslation();
  const { addons } = useUserAddon();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { showCheckoutModal, showPurchaseModal } = useAppBridgeModal();
  const subscribeNow = useSubscribeNowAction();

  // @ts-ignore
  const user = useSelector((s) => s.user);

  const totalLimit = addons.filter((a) => a.group === group).reduce((acc, a) => acc + a.usageLimit, 0);

  let monthlyAddon = addons.find((a) => a.group === group && a.interval === SubscriptionAddonInterval.MONTHLY);
  let lifetimeAddon = addons.find((a) => a.group === group && a.interval === SubscriptionAddonInterval.LIFETIME);

  let onetimeAddon = addons
    .filter((a) => a.group === group && a.interval === SubscriptionAddonInterval.CREDIT)
    .reduce(
      (acc, a) => ({
        ...a,
        usageLimit: (acc?.usageLimit || 0) + a.usageLimit,
      }),
      null
    );

  if (!monthlyAddon && lifetimeAddon) {
    monthlyAddon = lifetimeAddon;
  }

  if (!monthlyAddon && !lifetimeAddon) {
    monthlyAddon = onetimeAddon;
    onetimeAddon = null;
  }

  if (!monthlyAddon?.isActive) return null;

  const daysSinceSubscription = moment().diff(moment(user.subscribedAt), "d");
  const daysTillReset = 30 - (daysSinceSubscription % 30);
  const usagePercentage = Math.round((monthlyAddon.usageCount * 100) / totalLimit);

  const isTrialActive = !!user.trialData?.isActive;

  const handleAiContentOptimizerButtonClick = () => {
    if (isTrialActive) {
      // dispatch(setCheckout({ slug: user.planSlug, coupon_code: user.planCoupon }));
      // showCheckoutModal();
      subscribeNow.mutate();
    } else {
      dispatch(showPurchase());
      showPurchaseModal();
    }
  };

  const handleImageOptimizerButtonClick = () => {
    if (isTrialActive) {
      // dispatch(setCheckout({ slug: user.planSlug, coupon_code: user.planCoupon }));
      // showCheckoutModal();
      subscribeNow.mutate();
    } else {
      navigate("/subscription");
    }
  };

  const handleButtonClick = (group) => {
    switch (group) {
      case SubscriptionAddonGroup.AI_OPTIMIZER:
        handleAiContentOptimizerButtonClick();
        break;
      case SubscriptionAddonGroup.IMAGE_OPTIMIZER:
        handleImageOptimizerButtonClick();
        break;
      default:
        handleImageOptimizerButtonClick();
        break;
    }
  };

  return (
    <Card>
      <BlockStack gap={"300"}>
        <BlockStack>
          <InlineStack
            gap={"200"}
            align="space-between"
            blockAlign="center"
          >
            <InlineStack
              blockAlign="center"
              gap="100"
            >
              <Text
                as="h4"
                variant="headingSm"
              >
                {t(title)}
              </Text>

              {learnMoreButton && (
                <LearnMoreButton
                  title={t(learnMoreButton.title)}
                  url={learnMoreButton.url}
                />
              )}
            </InlineStack>
            {action && (
              <Button
                size="micro"
                // url={action.url}
                onClick={() => handleButtonClick(group)}
                loading={subscribeNow.isLoading}
              >
                {t(user.trialData?.isActive ? "Upgrade now" : action.content)}
              </Button>
            )}
          </InlineStack>

          <Text
            as="p"
            tone="subdued"
            variant="bodySm"
          >
            ({formatNumber(monthlyAddon.usageLimit)} {t(monthlyAddon.limitLabel)}
            {!monthlyAddon.isFree && monthlyAddon.isMonthly && t("/Month")}
            {onetimeAddon && t(` + ${formatNumber(onetimeAddon.usageLimit)} ${onetimeAddon.limitLabel}`)})
          </Text>
        </BlockStack>

        <BlockStack gap={"150"}>
          <InlineStack
            gap={"200"}
            align="space-between"
          >
            <Text
              as="p"
              tone="subdued"
            >
              {formatNumber(monthlyAddon.usageCount, 2)}/{formatNumber(totalLimit)} {t(monthlyAddon.limitLabel)}
            </Text>
            {!monthlyAddon.isFree && monthlyAddon.isMonthly && (
              <Text
                as="p"
                tone="subdued"
              >
                {t("Monthly usage resets in {{DAYS_TILL_RESET}} days", { DAYS_TILL_RESET: daysTillReset })}
              </Text>
            )}
          </InlineStack>
          <ProgressBar
            size="small"
            progress={usagePercentage}
            tone={usagePercentage < 75 ? "success" : "critical"}
          />
        </BlockStack>
        {imageOptimizeSettings?.autoOptimization && (
          <Banner
            icon={AlertCircleIcon}
            tone="warning"
          >
            <Text as="p">
              {t("Enabling this feature will automatically use the available limit on your Image Optimizer plan")}
            </Text>
          </Banner>
        )}
      </BlockStack>
    </Card>
  );
};

export default UsageLimitCard;

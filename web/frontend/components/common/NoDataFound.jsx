import React from "react";
import { useTranslation } from "react-i18next";
import classNames from "classnames";
import NoProductIcon from "../svg/NoProduct";

const NoDataFound = ({ message = "No Data Found", withBg = true, icon = <NoProductIcon /> }) => {
  const { t } = useTranslation();

  return (
    <div className={classNames("no__data", { background__white: withBg })}>
      {icon}
      <h4 className="mt50">{t(message)}</h4>
    </div>
  );
};

export default NoDataFound;

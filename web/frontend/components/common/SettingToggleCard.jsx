import { <PERSON>ge, BlockStack, <PERSON><PERSON>, <PERSON>, InlineStack, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";

const SettingToggleCard = ({ label, content, status, onButtonClick, loading = false }) => {
  const { t } = useTranslation();
  const tone = status ? "success" : "attention";
  const btnText = status ? "Turn Off" : "Turn On";
  const badgeLabel = status ? "On" : "Off";

  return (
    <Card>
      <BlockStack gap="200">
        <InlineStack
          gap="300"
          align="space-between"
          blockAlign="center"
        >
          <InlineStack gap="200">
            <Text variant="headingSm">{t(label)}</Text>
            <Badge tone={tone}>{t(badgeLabel)}</Badge>
          </InlineStack>
          <Button
            loading={loading}
            onClick={onButtonClick}
          >
            {t(btnText)}
          </Button>
        </InlineStack>
        {content && (
          <Text
            variant="bodySm"
            tone="subdued"
          >
            {t(content)}
          </Text>
        )}
      </BlockStack>
    </Card>
  );
};

export default SettingToggleCard;

import { Box, Button, Collapsible, Icon, InlineStack, Tabs, TextField, Tooltip } from "@shopify/polaris";
import { FilterIcon, SearchIcon } from "@shopify/polaris-icons";
import { debounce } from "lodash";
import moment from "moment";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import {
  noFollowStatusList,
  noIndexStatusList,
  productLimitList,
  productStatusList,
  sitemapStatusList,
} from "../../config/index.js";
import DateRangePicker from "./DateRangePicker.jsx";
import SelectInput from "./SelectInput.jsx";

const TableContentFilter = ({
  withSearch = true,
  withFilters = true,
  withDateRange = false,
  withStatus = false,
  withLimit = false,
  withNoindex = false,
  withNofollow = false,
  withSitemap = false,
  withTabs = true,
}) => {
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const location = useLocation();

  const [searchText, setSearchText] = useState("");
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");
  const [status, setStatus] = useState(null);
  const [limit, setLimit] = useState(null);
  const [noindex, setNoindex] = useState(null);
  const [nofollow, setNofollow] = useState(null);
  const [sitemap, setSitemap] = useState(null);
  const [openSearchAndFilter, setOpenSearchAndFilter] = useState(() => false);
  const [filterApplied, setFilterApplied] = useState(() => false);
  const [isFirstLoad, setIsFirstLoad] = useState(() => true);
  const user = useSelector((state) => state.user);

  const isBetterDocsInstalled = user.betterDocsInstallationStatus;

  // useEffect(() => {
  //   const { from, to, status, limit } = Object.fromEntries(searchParams.entries());
  //   setFromDate(from ? new Date(from) : "");
  //   setToDate(to ? new Date(to) : "");
  //   setStatus(productStatusList.find((sl) => sl.value === status) || null);
  //   setLimit(productLimitList.find((ll) => ll.value === limit) || null);
  // }, [searchParams]);

  const filterProduct = () => {
    const query = {
      ...Object.fromEntries(searchParams.entries()),
      // page: 1,
    };

    if (query?.page) {
      delete query.page;
    }

    if (status?.value) {
      query.status = status.value;
    }

    if (limit?.value) {
      query.limit = limit.value;
    }

    if (noindex?.value) {
      query.filterOn = "noindex";
      query.filterValue = noindex?.value;
    }

    if (nofollow?.value) {
      query.filterOn = "nofollow";
      query.filterValue = nofollow?.value;
    }

    if (sitemap?.value) {
      query.filterOn = "sitemap";
      query.filterValue = sitemap?.value;
    }

    if (fromDate) query.from = moment(fromDate).format("YYYY-MM-DD");

    if (toDate) query.to = moment(toDate).format("YYYY-MM-DD");

    setSearchParams(query);
  };

  const resetFilter = async () => {
    setSearchParams({});
    setSearchText("");
    setStatus(null);
    setLimit(null);
    setNoindex(null);
    setNofollow(null);
    setSitemap(null);
    setFromDate("");
    setToDate("");
    setFilterApplied(false);
  };

  const handleSearchChange = useCallback((value) => {
    setSearchText(value);
    onSearch(value);
  }, []);

  const onSearch = debounce((value) => {
    setSearchParams({
      ...Object.fromEntries(searchParams.entries()),
      page: 1,
      search: value.trim(),
    });
  }, 300);
  const onStatusSelect = (selectedItem) => {
    setStatus(selectedItem);
    setFilterApplied(true);
  };
  const onLimitSelect = (selectedItem) => {
    setLimit(selectedItem);
    setFilterApplied(true);
  };
  const onNoindexSelect = (selectedItem) => {
    // console.log(selectedItem);
    setNoindex(selectedItem);
    setFilterApplied(true);
  };
  const onNofollowSelect = (selectedItem) => {
    setNofollow(selectedItem);
    setFilterApplied(true);
  };
  const onSitemapSelect = (selectedItem) => {
    setSitemap(selectedItem);
    setFilterApplied(true);
  };

  const onDateRangeApply = ({ since, until }) => {
    let from = moment(since).format("YYYY-MM-DD");
    let to = moment(until).format("YYYY-MM-DD");

    setFromDate(from);
    setToDate(to);

    setFilterApplied(true);
  };

  useEffect(() => {
    if (!isFirstLoad) {
      filterProduct();
    } else {
      setIsFirstLoad(false);
    }
  }, [status, limit, fromDate, toDate, noindex, nofollow, sitemap]);

  const filterIsAllowed = openSearchAndFilter && withFilters;

  const toggleSearchAndFilter = (status = true) => {
    setOpenSearchAndFilter(status);
  };

  let tabs = [
    {
      id: "products",
      content: t("Products"),
      accessibilityLabel: "Products",
      panelID: "products",
      path: "/optimize-seo",
    },
    {
      id: "collections",
      content: t("Collections"),
      accessibilityLabel: "Collections",
      panelID: "collections",
      path: "/optimize-seo/collections",
    },
    {
      id: "pages",
      content: t("Pages"),
      accessibilityLabel: "Pages",
      panelID: "pages",
      path: "/optimize-seo/pages",
    },
    {
      id: "articles",
      content: t("Blog Posts"),
      accessibilityLabel: "Blog Posts",
      panelID: "articles",
      path: "/optimize-seo/articles",
    },
    // {
    //   id: "docs",
    //   content: t("Docs"),
    //   accessibilityLabel: "Docs",
    //   panelID: "docs",
    //   path: "/optimize-seo/docs",
    // },
  ];

  if (isBetterDocsInstalled) {
    tabs = [
      ...tabs,
      {
        id: "docs",
        content: t("Docs"),
        accessibilityLabel: "Docs",
        panelID: "docs",
        path: "/optimize-seo/docs",
      },
    ];
  }

  const [selectedTab, setSelectedTab] = useState(0);

  const onSelect = (idx) => {
    navigate(tabs[idx].path);
  };

  useEffect(() => {
    setSelectedTab(tabs.findIndex((t) => t.path === location.pathname));
  }, [location.pathname]);

  return (
    <>
      <Box
        borderBlockEndWidth="025"
        borderColor="border"
      >
        {!openSearchAndFilter && (
          <InlineStack align={"space-between"}>
            <Tabs
              tabs={withTabs ? tabs : []}
              selected={selectedTab}
              onSelect={onSelect}
            />
            <Box padding={"200"}>
              <Tooltip content={t("Search and filter")}>
                <Button
                  accessibilityLabel="Search and filter"
                  onClick={toggleSearchAndFilter}
                  size="slim"
                  icon={
                    <InlineStack>
                      <Icon source={SearchIcon} />
                      <Icon source={FilterIcon} />
                    </InlineStack>
                  }
                  focused
                />
              </Tooltip>
            </Box>
          </InlineStack>
        )}

        {openSearchAndFilter && (
          <InlineStack
            gap="400"
            blockAlign={"center"}
            align={"space-between"}
            wrap
          >
            <Box
              width="calc(100% - 100px)"
              padding={"200"}
            >
              {withSearch && (
                <TextField
                  label={t("Search")}
                  placeholder={t("Search")}
                  labelHidden
                  type="text"
                  value={searchText}
                  onChange={handleSearchChange}
                  autoComplete="off"
                  clearButton
                  onClearButtonClick={() => handleSearchChange("")}
                  prefix={<Icon source={SearchIcon} />}
                  focused={openSearchAndFilter}
                  variant="borderless"
                />
              )}
            </Box>
            <Box padding={"200"}>
              <Button
                onClick={() => {
                  toggleSearchAndFilter(false);
                  resetFilter();
                }}
                variant="tertiary"
              >
                {t("Cancel")}
              </Button>
            </Box>
          </InlineStack>
        )}
      </Box>
      <Collapsible
        open={filterIsAllowed}
        id="basic-collapsible"
        transition={{ duration: "300ms", timingFunction: "ease-in-out" }}
      >
        <Box padding="200">
          <InlineStack gap="200">
            {withDateRange && (
              <DateRangePicker
                startDate={fromDate}
                endDate={toDate}
                onApply={onDateRangeApply}
                isOutline={true}
              />
            )}
            {withStatus && (
              <SelectInput
                title="Status"
                options={productStatusList}
                selected={status}
                selectedTitle="Status is"
                onSelect={onStatusSelect}
                isOutline={true}
              />
            )}
            {withLimit && (
              <SelectInput
                title="Show Products"
                options={productLimitList}
                selected={limit}
                selectedTitle="Showing"
                onSelect={onLimitSelect}
                isOutline={true}
              />
            )}
            {withNoindex && (
              <SelectInput
                title="Noindex"
                options={noIndexStatusList}
                selected={noindex}
                onSelect={onNoindexSelect}
                isOutline={true}
              />
            )}
            {withNofollow && (
              <SelectInput
                title="Nofollow"
                options={noFollowStatusList}
                selected={nofollow}
                onSelect={onNofollowSelect}
                isOutline={true}
              />
            )}
            {withSitemap && (
              <SelectInput
                title="Sitemap"
                options={sitemapStatusList}
                selected={sitemap}
                onSelect={onSitemapSelect}
                isOutline={true}
              />
            )}
            {filterApplied && (
              <Button
                removeUnderline
                onClick={resetFilter}
                variant="monochromePlain"
              >
                {t("Clear All")}
              </Button>
            )}
          </InlineStack>
        </Box>
      </Collapsible>
    </>
  );
};

export default TableContentFilter;

import React from "react";
import { useTranslation } from "react-i18next";
import SubmitButton from "./SubmitButton";

const CanonicalUrl = ({ url = "", viewMode = false, onChange = () => {}, isValid = true, onUpdate = () => {} }) => {
  const { t } = useTranslation();

  return (
    <div className="ss-modal__wrap show">
      <div className="ss-modal--classic">
        <div className="modal__header">
          <h3>{t("Canonical URL")}</h3>
          <span className="modal__times c-pointer">
            <i className="ss-icon ss-times-circle"></i>
          </span>
        </div>
        <div
          className="modal__body"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="new__billing__card">
            <div className="form__group">
              <label htmlFor="#">{t("Canonical URL")}</label>
              <input
                type="url"
                name=""
                id=""
                className="form__control"
                value={url}
                onChange={(e) => onChange(e.target.value)}
                disabled={viewMode}
              />
              <div>
                <span className="color__danger">{!isValid && t("Please enter a valid url")}</span>
              </div>
            </div>
          </div>
        </div>
        {!viewMode && (
          <div className="modal__footer">
            <div className="d__flex justify__end gap15">
              <SubmitButton
                type="button"
                callback={() => {}}
                text={t("Cancel")}
                btnclassName="button button--md button__danger hover__highlight radius-20"
              />
              <SubmitButton
                type="button"
                callback={onUpdate}
                text={t("Update")}
                disabled={!(url && isValid)}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CanonicalUrl;

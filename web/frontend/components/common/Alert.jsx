import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";

/**
 * @typedef {Object} AlertProps
 * @property {string} [type="primary"] - The type of alert (e.g., "primary", "secondary").
 * @property {string} message - The message to display in the alert.
 * @property {string} [icon=""] - The icon class to display in the alert.
 * @property {string} [additionalClass=""] - Additional CSS classes to apply to the alert.
 * @property {string|null} [title=null] - The title of the alert (optional).
 */

/**
 * Alert component to display a message with optional icon and title.
 *
 * @param {AlertProps} props - The properties for the Alert component.
 * @returns {JSX.Element} The rendered Alert component.
 */
const Alert = ({ type = "primary", message, icon = "", additionalClass = "", title = null }) => {
  const { t } = useTranslation();
  return (
    <div className={`ss-alert ss-alert--${type} ${additionalClass} ${icon ? "d__flex" : ""}`}>
      {icon && <i className={`ss-icon ${icon} mr10 color__${type}`} />}
      <div>
        {title && <h2 className="mb10">{t(title)}</h2>}
        <p>{t(message)}</p>
      </div>
    </div>
  );
};

Alert.propTypes = {
  type: PropTypes.oneOf(["primary", "warning", "danger"]),
  icon: PropTypes.oneOf(["ss-warning-circle", "ss-times-circle", "ss-check-circle"]),
  additionalClass: PropTypes.string,
  message: PropTypes.string,
  title: PropTypes.string,
};

export default Alert;

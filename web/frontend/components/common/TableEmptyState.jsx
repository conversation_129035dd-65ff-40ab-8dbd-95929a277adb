import { BlockStack, Box, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { EMPTYSTATE_IMAGES } from "../../config";

const TableEmptyState = ({ title, description, withIllustration = true, image = EMPTYSTATE_IMAGES.default }) => {
  const { t } = useTranslation();

  return (
    <Box padding="1600">
      <BlockStack
        align="center"
        gap="200"
        inlineAlign="center"
      >
        <img
          src={image}
          alt={title}
          width={170}
          height={170}
        />
        <Text
          as="h4"
          variant="headingMd"
        >
          {t(title)}
        </Text>

        <Text
          as="p"
          tone="subdued"
        >
          {t(description)}
        </Text>
      </BlockStack>
    </Box>
  );
};

export default TableEmptyState;

import React, { useEffect, useRef, useState } from "react";

import { useTranslation } from "react-i18next";
import { useSearchParams } from "react-router-dom";

const SearchText = ({ isFetching = false }) => {
  const [search, setSearch] = useState("");
  const [isOpen, setIsOpen] = useState(false);

  const { t } = useTranslation();

  const [searchParams, setSearchParams] = useSearchParams();
  const searchText = searchParams.get("search");

  useEffect(() => {
    setSearch(searchText || "");
  }, [searchText]);

  const handleSearch = (e) => {
    if (e.which === 13) {
      e.preventDefault();

      setSearchParams({
        ...Object.fromEntries(searchParams.entries()),
        page: 1,
        search: search.trim(),
      });
    }
  };

  const handleOnToggle = (e) => {
    e.preventDefault();
    setIsOpen(!isOpen);
  };

  const clearSearch = () => {
    setSearchParams({
      ...Object.fromEntries(searchParams.entries()),
      page: 1,
      search: "",
    });
  };

  const productSearchRef = useRef();
  useEffect(() => {
    const checkIfClickedOutside = (e) => {
      if (isOpen && productSearchRef.current && !productSearchRef.current.contains(e.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", checkIfClickedOutside);

    return () => {
      document.removeEventListener("mousedown", checkIfClickedOutside);
    };
  }, [isOpen]);

  return (
    <div
      className="dash__header__left basis__auto dash__header__search d__flex"
      // ref={productSearchRef}
    >
      <a
        href="#"
        className="dash__header__search__toggler"
        onClick={(e) => handleOnToggle(e)}
      >
        {isOpen ? <i className="ss-icon ss-times" /> : <i className="ss-icon ss-search" />}
      </a>
      <form
        action="#"
        className={`dash__header__search__form ${isOpen ? "show" : ""}`}
      >
        <div className="input__group input__group--reverse">
          <input
            type="text"
            className="form__control"
            placeholder={t("Search")}
            value={search}
            onChange={(event) => setSearch(event.target.value)}
            onKeyPress={handleSearch}
          />
          <div className="input__group__prepend">
            <span className="input__group__icon">
              <i className="ss-icon ss-search" />
            </span>
          </div>
          {search.length > 0 && (
            <span
              className="search__reset color__danger c-pointer"
              onClick={clearSearch}
            >
              <i className="ss-icon ss-times" />
            </span>
          )}
        </div>
      </form>
    </div>
  );
};

export default SearchText;

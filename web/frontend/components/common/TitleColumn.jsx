import React from "react";
import { Badge, Box, InlineStack, Link, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";

const TitleColumn = ({ title, url, label = false, labelText = "" }) => {
  const { t } = useTranslation();

  if (label) {
    return (
      <InlineStack
        gap="50"
        align="start"
        blockAlign="center"
      >
        <Box width={"70%"}>
          <Link
            dataPrimaryLink
            monochrome
            removeUnderline
            url={url}
            onClick={(e) => e.stopPropagation()}
          >
            <Text
              as={"h4"}
              fontWeight="semibold"
            >
              {title}
            </Text>
          </Link>
        </Box>
        {label && (
          <Box>
            <Badge tone="info">{t(labelText)}</Badge>
          </Box>
        )}
      </InlineStack>
    );
  }

  return (
    <Link
      dataPrimaryLink
      monochrome
      removeUnderline
      url={url}
      onClick={(e) => e.stopPropagation()}
    >
      <Text
        as={"h4"}
        fontWeight="semibold"
      >
        {title}
      </Text>
    </Link>
  );
};

export default TitleColumn;

import { isEmpty } from "lodash";
import { useEffect, useRef, useState } from "react";
import { Trans, useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { setTags } from "../../store/features/AiContent";

import { prepareThumbnailURL } from "@/utility/helpers";
import { BlockStack, Box, Button, InlineStack, Tag, Text, TextField, Thumbnail } from "@shopify/polaris";
import analysisEntityTypes from "storeseo-enums/analysisEntityTypes";
import { useKeywordAnalytics } from "../../hooks/useKeywordAnalytics";
import AiContent from "../ai/AiContent";
import URLHandleInput from "../forms/URLHandleInput";
import KeywordAnalyticsModal from "../modals/KeywordAnalyticsModal";
import CollapsibleCard from "./CollapsibleCard";
import TagsInput from "./TagsInput";

const EditForm = ({
  focusKeywordSuggestions,
  featuredImage,
  formErrors = {},
  data = {},
  onChange = (key, val) => {},
  readOnly = false,
  hideTagsInput = false,
  originalFormData = {},
  urlPrefix = undefined,
  isURLChanged = false,
  createRedirectUrl = false,
  entityType = analysisEntityTypes.PRODUCT,
  disabled = false,
}) => {
  const { t } = useTranslation();
  const [showKeywordMetrics, setShowKeywordMetrics] = useState(false);
  const { keywordAnalytics } = useKeywordAnalytics(data.focusKeyword);

  // Track AI-generated tags for revert functionality
  const dispatch = useDispatch();
  const aiContent = useSelector((state) => state.aiContent);

  // Store the original AI-generated tags for reference
  const originalAiTags = useRef([]);

  // Keep track of all AI-generated tags, including those that have been added to the input
  const allAiGeneratedTags = useRef([]);

  // Update originalAiTags ref when AI content changes
  useEffect(() => {
    if (aiContent?.tags && aiContent.tags.length > 0) {
      // Store the current AI tags
      originalAiTags.current = [...aiContent.tags];

      // Add these tags to our master list of all AI-generated tags
      allAiGeneratedTags.current = [...new Set([...allAiGeneratedTags.current, ...aiContent.tags])];
    }
  }, [aiContent?.tags]);

  const handleTagKeep = (val) => {
    // If we're keeping a single tag, append it to the existing tags

    if (Array.isArray(val) && val.length === 1) {
      const newTag = val[0];

      // Add this tag to our master list of all AI-generated tags
      allAiGeneratedTags.current = [...new Set([...allAiGeneratedTags.current, newTag])];

      // Only add the tag if it doesn't already exist in the list
      if (!data.tags?.includes(newTag)) {
        onChange("tags", [...(data.tags || []), newTag]);
      }
    } else {
      // Otherwise, replace the tags (this is for the "Keep all" button)

      // Add all these tags to our master list of all AI-generated tags
      if (val && Array.isArray(val)) {
        allAiGeneratedTags.current = [...new Set([...allAiGeneratedTags.current, ...val])];
      }

      onChange("tags", allAiGeneratedTags.current);
    }
  };

  const handleTagRevert = () => {
    onChange("tags", originalFormData.tags);
    dispatch(setTags([...allAiGeneratedTags.current]));
  };

  // Function to handle tag removal and revert to AI tags if needed
  const handleTagRemove = (removedTag) => {
    // If the tag was originally AI-generated, add it back to the AI tags list
    if (allAiGeneratedTags.current.includes(removedTag)) {
      // Get the current AI tags
      const currentAiTags = aiContent?.tags || [];

      // Only add the tag back if it's not already in the AI tags list
      if (!currentAiTags.includes(removedTag)) {
        // Add the removed tag back to the AI tags list
        dispatch(setTags([...currentAiTags, removedTag]));
      }
    }
  };

  const titleLength = data?.metaTitle?.length || 0;
  const metaDescriptionLength = data?.metaDescription?.length || 0;

  const metaTitleHelpText = (
    <Text
      as="span"
      tone="subdued"
    >
      <Trans i18nKey="metaTitleInputHelpText">{{ titleLength }} out of 70 characters used</Trans>
    </Text>
  );

  const metaDescHelpText = (
    <Text
      as="span"
      tone="subdued"
    >
      <Trans i18nKey="metaDescriptionInputHelpText">{{ metaDescriptionLength }} out of 165 characters used</Trans>
    </Text>
  );

  const featureImageTitle =
    entityType == analysisEntityTypes.ARTICLE
      ? t("Article Image")
      : entityType == analysisEntityTypes.COLLECTION
      ? t("Collection Image")
      : entityType == analysisEntityTypes.PAGE
      ? t("Page Image")
      : entityType == analysisEntityTypes.HOMEPAGE
      ? t("Homepage Image")
      : entityType === analysisEntityTypes.DOC
      ? t("Doc Image")
      : t("Product Image");

  return (
    <CollapsibleCard title="General SEO">
      <Box padding="400">
        <BlockStack gap="400">
          {!isEmpty(featuredImage) && (
            <Box>
              <Box paddingBlockEnd="200">
                <Text>{featureImageTitle}</Text>
              </Box>
              <Thumbnail
                size="large"
                source={
                  entityType === analysisEntityTypes.DOC ? featuredImage?.src : prepareThumbnailURL(featuredImage?.src)
                }
                alt={featuredImage?.altText}
              />
            </Box>
          )}
          <BlockStack gap="100">
            <TextField
              label={t("Meta Title")}
              value={data.metaTitle}
              placeholder={""}
              onChange={(val) => {
                onChange("metaTitle", val);
              }}
              autoComplete="off"
              error={formErrors?.metaTitle ? t(formErrors.metaTitle) : undefined}
              disabled={readOnly}
            />
            <AiContent
              contentKey="metaTitle"
              onKeep={(val) => onChange("metaTitle", val)}
              onRevert={() => onChange("metaTitle", originalFormData.metaTitle)}
              helpText={metaTitleHelpText}
            />
          </BlockStack>
          <BlockStack gap="100">
            <TextField
              label={t("Meta Description")}
              value={data.metaDescription}
              onChange={(val) => {
                onChange("metaDescription", val);
              }}
              multiline={4}
              autoComplete="off"
              requiredIndicator
              error={formErrors?.metaDescription ? t(formErrors.metaDescription) : undefined}
              disabled={readOnly}
            />
            <AiContent
              contentKey="metaDescription"
              onKeep={(val) => onChange("metaDescription", val)}
              onRevert={() => onChange("metaDescription", originalFormData.metaDescription)}
              helpText={metaDescHelpText}
            />
          </BlockStack>

          <URLHandleInput
            handle={data?.handle}
            originalHandle={originalFormData?.handle}
            urlPrefix={urlPrefix}
            createRedirectUrl={createRedirectUrl}
            onChange={onChange}
            error={formErrors?.handle}
            disabled={readOnly || disabled}
            isURLChanged={isURLChanged}
          />

          <TextField
            label={t("Focus Keyword")}
            value={data.focusKeyword}
            requiredIndicator
            onChange={(val) => {
              onChange("focusKeyword", val);
            }}
            autoComplete="off"
            error={formErrors?.focusKeyword ? t(formErrors.focusKeyword) : undefined}
            connectedRight={<Button onClick={() => setShowKeywordMetrics(true)}>{t("Keyword Analytics")}</Button>}
          />
          <KeywordAnalyticsModal
            keyword={data.focusKeyword}
            g
            show={showKeywordMetrics}
            keywordMetrics={keywordAnalytics}
            onKeywordClick={(val) => {
              onChange("focusKeyword", val);
            }}
            onClose={() => setShowKeywordMetrics(false)}
          />
          {focusKeywordSuggestions.length > 0 && (
            <Box>
              <Box paddingBlockEnd="200">
                <Text>{t("Keyword Suggestions")}:</Text>
              </Box>
              <InlineStack
                gap="200"
                align="start"
              >
                {focusKeywordSuggestions?.map((keyword, idx) => (
                  <Tag
                    onClick={() => {
                      onChange("focusKeyword", keyword);
                    }}
                    key={idx}
                  >
                    {keyword}
                  </Tag>
                ))}
              </InlineStack>
            </Box>
          )}

          {!hideTagsInput && (
            <BlockStack gap="100">
              <TagsInput
                options={data?.tags}
                onChange={(tags) => onChange("tags", tags)}
                onTagRemove={handleTagRemove}
              />
              <AiContent
                contentKey="tags"
                onKeep={handleTagKeep}
                onRevert={handleTagRevert}
              />
            </BlockStack>
          )}
        </BlockStack>
      </Box>
    </CollapsibleCard>
  );
};

export default EditForm;

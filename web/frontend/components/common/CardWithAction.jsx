import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Card, InlineStack, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { XIcon } from "@shopify/polaris-icons";

const CardWithAction = ({ show = true, title, content, button, onClose }) => {
  if (!show) return null;
  const { t } = useTranslation();

  const { label: btnLabel, onAction: btnAction, ...btnRest } = button;

  return (
    <Card>
      <InlineStack
        align="space-between"
        blockAlign="center"
        gap="400"
      >
        <BlockStack gap="200">
          {title && (
            <Text
              variant="headingMd"
              as="h6"
            >
              {t(title)}
            </Text>
          )}
          {content && (
            <Text
              as={"p"}
              tone={"subdued"}
            >
              {t(content)}
            </Text>
          )}
        </BlockStack>

        {button && (
          <span>
            <Button
              onClick={btnAction}
              {...btnRest}
            >
              {t(btnLabel)}
            </Button>
          </span>
        )}
      </InlineStack>
      {onClose && (
        <span className="ss-close-button">
          <Button
            icon={XIcon}
            variant="plain"
            onClick={onClose}
          />
        </span>
      )}
    </Card>
  );
};

// CardWithAction.propTypes = {
//   title: PropTypes.string,
//   content: PropTypes.string,
//   button: {
//     label: PropTypes.string,
//     onAction: PropTypes.func,
//   },
// };

export default CardWithAction;

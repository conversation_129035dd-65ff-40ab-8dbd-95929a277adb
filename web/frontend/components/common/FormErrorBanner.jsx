import { <PERSON>, <PERSON><PERSON>, List } from "@shopify/polaris";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import analysisEntityTypes from "storeseo-enums/analysisEntityTypes";

export const FormErrorBanner = ({ formErrors = {}, entityType, handleEditButtonClick = () => {}, setFormErrors }) => {
  const { t } = useTranslation();
  const [errors, setErrors] = useState([]);
  const [editButtonText, setEditButtonText] = useState("");
  const [showEditButton, setShowEditButton] = useState(false);
  const [showBanner, setShowBanner] = useState(false);

  useEffect(() => {
    const errorMessages = Object.values(formErrors);
    setErrors(errorMessages.length > 0 ? errorMessages : []);
    setShowBanner(errorMessages.length > 0);

    const keys = Object.keys(formErrors);
    setShowEditButton(keys.includes("title") || keys.includes("description"));
  }, [formErrors]);

  useEffect(() => {
    switch (entityType) {
      case analysisEntityTypes.PRODUCT:
        setEditButtonText("Edit product");
        break;
      case analysisEntityTypes.PAGE:
        setEditButtonText("Edit page");
        break;
      case analysisEntityTypes.ARTICLE:
        setEditButtonText("Edit article");
        break;
      case analysisEntityTypes.COLLECTION:
        setEditButtonText("Edit collection");
        break;
      default:
        setEditButtonText("");
    }
  }, [entityType]);

  const handleOnDismiss = () => {
    setShowBanner(false);

    if (formErrors.hasOwnProperty("title")) delete formErrors["title"];
    if (formErrors.hasOwnProperty("description")) delete formErrors["description"];
    if (formErrors.hasOwnProperty("focusKeyword")) delete formErrors["focusKeyword"];

    setFormErrors(formErrors);
  };

  if (!showBanner) return null;

  const errorMessageTitle = t("There is {{ERROR_COUNT}} error in the {{entity}}", {
    ERROR_COUNT: errors.length,
    entity: entityType.toLowerCase(),
  });

  return (
    <Banner
      title={errorMessageTitle}
      tone="critical"
      onDismiss={handleOnDismiss}
    >
      <List>
        {errors.map((error, idx) => (
          <List.Item key={`error-${idx}`}>{t(error)}</List.Item>
        ))}
        {showEditButton && <Button onClick={handleEditButtonClick}>{t(editButtonText)}</Button>}
      </List>
    </Banner>
  );
};

import React from "react";
import { textReduce } from "../../utility/helpers";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";

const Breadcrumb = ({ breadcrumbs }) => {
  const { t } = useTranslation();

  if (breadcrumbs.length === 0) return null;
  return (
    <div className="ss-breadcrumb">
      <ul>
        {breadcrumbs.map((breadcrumb, index) => (
          <li key={index}>
            {breadcrumb.isActive ? (
              <span title={breadcrumb.title}>{t(textReduce(breadcrumb.title, 55, "..."))}</span>
            ) : (
              <Link to={breadcrumb.url}>{t(breadcrumb.title)}</Link>
            )}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default Breadcrumb;

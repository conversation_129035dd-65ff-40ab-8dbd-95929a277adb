import classNames from "classnames";
import Loader from "../loader/Loader";

/**
 * @typedef {Object} SubmitButtonProps
 * @property {string} [text="Submit"]
 * @property {boolean} [loading=false]
 * @property {boolean} [disabled=false]
 * @property {"submit"|"button"|"reset"} [type="submit"]
 * @property {string} [btnClass="button button--md button__primary radius-20"]
 * @property {() => void} [callback]
 * @property {string} [loaderColor="#fff"]
 */

/**
 * @param {SubmitButtonProps} props
 */
const SubmitButton = ({
  text = "Submit",
  loading = false,
  disabled = false,
  type = "submit",
  btnClass = "button button--md button__primary radius-20",
  callback,
  loaderColor = "#fff",
}) => (
  <button
    type={type}
    className={classNames(btnClass, { disabled, "d__flex loading": loading })}
    onClick={callback}
    disabled={disabled}
  >
    {loading && (
      <Loader
        show
        size={30}
        height="14px"
        color={loaderColor}
      />
    )}{" "}
    {text}
  </button>
);

export default SubmitButton;

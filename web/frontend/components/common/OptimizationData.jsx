import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Card, Divider, Icon, InlineStack, <PERSON>, Text } from "@shopify/polaris";
import { AlertTriangleIcon, CheckIcon, XIcon } from "@shopify/polaris-icons";
import { Fragment } from "react";
import { useTranslation } from "react-i18next";
import { collapseHeaderStyle } from "../../styles/common";
import RadialChart from "../charts/RadialChart";
import CollapsibleCard from "./CollapsibleCard";
import NeedHelpCard from "./NeedHelpCard";
import TooltipWrapper from "./TooltipWrapper";

const OptimizationData = ({
  score = 0,
  optimizations = [],
  showEditLink = false,
  onShopifyEditClick = () => {},
  onOptimizeClick = () => {},
  aiContentCard = null,
  resourceType = "product",
}) => {
  const { t } = useTranslation();

  const data = optimizations.map((opt) => {
    const issues = opt.values.filter((val) => !val.isOptimized).length;
    const isOpen = opt.values.filter((val) => val.percent < 100).length > 0;

    return {
      ...opt,
      isOpen,
      issues,
    };
  });

  const analysisRelatedToEditProductLink = (value) =>
    [/keyword.*(?:(?:in_title)|(?:dens.+desc))/gim, /desc_min_word/gim].some((exp) => value.key.match(exp));

  const editResourceLinkText = resourceType === "collection" ? t("Edit collection") : t("Edit product");

  return (
    <BlockStack gap="400">
      <Card padding="0">
        <div
          className="collapse-header"
          style={collapseHeaderStyle}
        >
          <Text
            as="h6"
            variant="headingSm"
          >
            {t("SEO Score")}
          </Text>
        </div>
        <Divider />
        <Box padding="400">
          <InlineStack align="center">
            <RadialChart
              score={score}
              radius={70}
              strokeWidth={18}
              dimension={160}
              chartTitle={t("SEO Score")}
              size={"large"}
              showPercent={false}
            />
          </InlineStack>
        </Box>
      </Card>
      {aiContentCard}
      {data.length &&
        data.map((optimizationData) => (
          <CollapsibleCard
            key={optimizationData.key}
            title={t(optimizationData.title)}
            badgeText={
              optimizationData.issues === 0
                ? t("No Issues")
                : `${optimizationData.issues} ${optimizationData.issues > 1 ? t("Issues") : t("Issue")}`
            }
            badgeStatus={optimizationData.issues === 0 ? "success" : "attention"}
          >
            <div className="seo-analytics-list">
              <BlockStack>
                {optimizationData.values?.map((val) => {
                  return (
                    <Fragment key={val.key}>
                      <Box padding="200">
                        <TooltipWrapper
                          show={val.percent >= 50 && val.percent < 100}
                          content={`${`${val.percent}%`} ${val.hint ? ", " : ""}${t(val.hint)}`}
                        >
                          <InlineStack
                            gap="200"
                            wrap={false}
                          >
                            <Box as="span">
                              <Icon
                                source={val.percent < 50 ? XIcon : val.percent < 75 ? AlertTriangleIcon : CheckIcon}
                                tone={val.percent < 50 ? "critical" : val.percent < 75 ? "warning" : "success"}
                              />
                            </Box>

                            <Text as="p">
                              {t(val.title)}.{" "}
                              {!val.isOptimized && showEditLink && analysisRelatedToEditProductLink(val) && (
                                /**
                                 * Todo: Replace with new Shopify App Bridge 4.0 [Navigation API](https://shopify.dev/docs/api/app-bridge-library/apis/navigation)
                                 */
                                <Link
                                  onClick={onShopifyEditClick}
                                  target={"_blank"}
                                >
                                  {editResourceLinkText}
                                </Link>
                              )}
                              {!val.isOptimized && val.key === "OPTIMIZED_ALL_IMAGES" && (
                                <Link onClick={onOptimizeClick}>{t("Optimize")}</Link>
                              )}
                              {val.percent >= 75 && val.percent < 100 && (
                                <img
                                  style={{ transform: "translateY(2px)" }}
                                  src="/img/warning.png"
                                  alt="warning"
                                />
                              )}
                            </Text>
                          </InlineStack>
                        </TooltipWrapper>
                      </Box>
                      <Divider />
                    </Fragment>
                  );
                })}
                <Divider />
              </BlockStack>
            </div>
          </CollapsibleCard>
        ))}

      <NeedHelpCard />
    </BlockStack>
  );
};

export default OptimizationData;

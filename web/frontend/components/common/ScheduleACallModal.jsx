import React from "react";
import { useTranslation } from "react-i18next";
import classNames from "classnames";

const ScheduleACallModal = ({ show, closeModal }) => {
  const { t } = useTranslation();
  const URL = "https://storeseo.com/talk-to-seo-expert";

  return (
    <div
      className={classNames("ss-modal__wrap", { show })}
      style={{ zIndex: 1000 }}
    >
      <div className="ss-modal ss-modal--lg">
        <a
          href="#"
          onClick={closeModal}
          className="modal__times"
        >
          <i className="ss-icon ss-times" />
        </a>
        <div className="modal__body">
          <div className="delete__account__dialoge">
            <h4>{t("Want to Schedule a call?")} 🎧</h4>
            <p>{t("We have some Shopify experts in the house. Want to book a one and one session with them?")}</p>
            <p>
              <strong>{t("PS")}:</strong> &nbsp;
              {t("It is completely FREE.")}
            </p>
            <div className="button__group">
              <button
                type="button"
                onClick={() => {
                  window.open(URL, "_blank");
                  closeModal();
                }}
                className={`button radius-20 mb0 button__primary`}
              >
                {t("Schedule a call")}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScheduleACallModal;

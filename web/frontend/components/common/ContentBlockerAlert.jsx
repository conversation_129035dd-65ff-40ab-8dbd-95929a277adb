import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { BlockStack, Button, InlineStack, Text } from "@shopify/polaris";
import classNames from "classnames";
import { PRO_ALERT_MODAL, PRO_ALERT_MODAL_SM } from "../../config/modal.js";
import { useEffect } from "react";

const ContentBlockerAlert = ({ fullVersion = true, scrollIntoView = true }) => {
  const user = useSelector((state) => state.user);
  const { t } = useTranslation();
  const navigate = useNavigate();
  // const popUpRef = useRef();

  const { title, content, items, primaryActionLabel } = fullVersion ? PRO_ALERT_MODAL : PRO_ALERT_MODAL_SM;

  useEffect(() => {
    // if (fullVersion && scrollIntoView) popUpRef?.current?.scrollIntoView({ behavior: "smooth" });
  }, []);

  if (user.isPremium) return null;

  return (
    <div
      className="modal-wrap"
      // ref={popUpRef}
    >
      <div className={classNames("modal", { "modal-sm": !fullVersion })}>
        <div className="modal-header">
          <h3 className="modal-title">{t(title)}</h3>
        </div>
        <div className="modal-body">
          {!fullVersion && <Text as="p">{t(content)}</Text>}
          {fullVersion && (
            <BlockStack gap="200">
              {items.map((item, index) => (
                <InlineStack
                  gap="200"
                  key={index}
                >
                  <Text>{item.icon}</Text>
                  <Text as="p">{t(item.label)}</Text>
                </InlineStack>
              ))}
            </BlockStack>
          )}
        </div>
        <div className="modal-footer">
          <Button
            onClick={() => navigate("/subscription")}
            variant="primary"
          >
            {t(primaryActionLabel)}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ContentBlockerAlert;

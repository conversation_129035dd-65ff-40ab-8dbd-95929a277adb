import React, { useEffect, useState } from "react";
import { BlockStack, Box, Button, Card, InlineStack, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useAppNavigation } from "../../hooks/useAppNavigation.js";
import { isExternalLink } from "../../utility/helpers.jsx";

const CustomCalloutCard = ({
  title,
  content,
  actionText,
  Illustration,
  url,
  background = undefined,
  padding = undefined,
}) => {
  const { t } = useTranslation();
  const { goToPath } = useAppNavigation();
  const [target, setTarget] = useState();

  useEffect(() => {
    if (isExternalLink(url)) {
      setTarget("_blank");
    }
  }, [url]);

  const onClick = () => {
    if (!isExternalLink(url)) {
      goToPath(url);
    }
  };

  return (
    <Card
      background={background}
      padding={padding}
    >
      <InlineStack
        wrap={false}
        align="space-between"
        blockAlign="center"
      >
        <BlockStack gap="300">
          <Text
            as="h4"
            variant="headingMd"
          >
            {t(title)}
          </Text>
          <Text as="p">{t(content)}</Text>

          <Box>
            <Button
              url={url || "#"}
              target={target}
              onClick={onClick}
            >
              {t(actionText)}
            </Button>
          </Box>
        </BlockStack>
        {Illustration && (
          <div className="ss-dash-illustration">
            <Illustration />
          </div>
        )}
      </InlineStack>
    </Card>
  );
};

export default CustomCalloutCard;

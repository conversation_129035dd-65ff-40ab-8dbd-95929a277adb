import { useState } from "react";
import { useTranslation } from "react-i18next";
import ScheduleACallModal from "./ScheduleACallModal.jsx";

const TalkToSeoExpertButton = () => {
  const { t } = useTranslation();
  const buttonTitle = t("Get Free SEO Consultation");

  const [show, setShow] = useState(false);

  const showModal = () => {
    setShow(!show);
  };
  const closeModal = () => {
    setShow(false);
  };

  return (
    <>
      <button
        type="button"
        className="button button__blue hover__highlight talk--to--expert"
        onClick={showModal}
      >
        <i className="ss-icon ss-call"></i>
        {buttonTitle}
      </button>

      <ScheduleACallModal
        show={show}
        closeModal={closeModal}
      />
    </>
  );
};

export default TalkToSeoExpertButton;

import { Button } from "@shopify/polaris";
import React, { useRef } from "react";
import { useTranslation } from "react-i18next";

const FileUpload = ({ title, accept = "*", onChange, loading = false, disabled = false }) => {
  const fileInputRef = useRef(null);
  const { t } = useTranslation();

  const handleButtonClick = () => {
    fileInputRef.current.click();
  };

  return (
    <>
      <input
        type="file"
        accept={accept}
        style={{ display: "none" }}
        ref={fileInputRef}
        onChange={onChange}
      />
      <Button
        onClick={handleButtonClick}
        loading={loading}
        disabled={disabled}
      >
        {t(title)}
      </Button>
    </>
  );
};

export default FileUpload;

import React from "react";
import { Link, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useAppNavigation } from "../../hooks/useAppNavigation.js";

const InlineProHint = () => {
  const { t } = useTranslation();
  const { goToPath } = useAppNavigation();

  return (
    <Text as="p">
      {t("This feature is avaiable in our")} <Link onClick={() => goToPath("/subscription")}>{t("pro plans")}</Link>.
    </Text>
  );
};

export default InlineProHint;

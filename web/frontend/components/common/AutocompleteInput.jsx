import { Autocomplete, Icon } from "@shopify/polaris";
import { SearchIcon } from "@shopify/polaris-icons";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

const AutocompleteInput = ({
  options = [],
  label,
  value = "",
  name,
  loading = false,
  withIcon = false,
  placeholder = "Search",
  onChange = (value, id) => {},
  onSelect = (value) => {},
  disabled = false,
}) => {
  const { t } = useTranslation();
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [inputValue, setInputValue] = useState(value);

  const updateText = (value, id) => {
    setInputValue(value);
    onChange(value, id);
  };

  const updateSelection = useCallback(
    (selected) => {
      const selectedText = selected.map((selectedItem) => {
        const matchedOption = options.find((option) => {
          return option.value.match(selectedItem);
        });
        return matchedOption && matchedOption.label;
      });
      setSelectedOptions(selected);
      setInputValue(selectedText[0] || "");

      onSelect(selected[0]);
    },
    [options]
  );

  useEffect(() => {
    setInputValue(value);
  }, [value]);

  const textField = (
    <Autocomplete.TextField
      onChange={updateText}
      name={name}
      label={t(label)}
      value={inputValue}
      prefix={
        withIcon && (
          <Icon
            source={SearchIcon}
            tone="base"
          />
        )
      }
      placeholder={placeholder}
      autoComplete="off"
      disabled={disabled}
    />
  );

  return (
    <Autocomplete
      options={options}
      listTitle="Powered by Google"
      selected={selectedOptions}
      onSelect={updateSelection}
      loading={loading}
      textField={textField}
    />
  );
};

export default AutocompleteInput;

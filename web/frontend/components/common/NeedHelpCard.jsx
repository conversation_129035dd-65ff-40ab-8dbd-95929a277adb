import { sendRequestToCrisp } from "@/utility/crisp";
import { BlockStack, Box, Button, Card, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";

const NeedHelpCard = () => {
  const { t } = useTranslation();

  return (
    <Card>
      <BlockStack gap="400">
        <BlockStack gap="200">
          <Text
            as="h4"
            variant="headingSm"
          >
            {t("Need help?")}
          </Text>
          <Text
            as="p"
            tone="subdued"
          >
            {t("Chat with us live to get help optimizing your product SEO score")} .
          </Text>
        </BlockStack>

        <Box>
          <Button onClick={() => sendRequestToCrisp("I would like to improve my SEO score in StoreSEO")}>
            {t("Open Live Chat")}
          </Button>
        </Box>
      </BlockStack>
    </Card>
  );
};

export default NeedHelpCard;

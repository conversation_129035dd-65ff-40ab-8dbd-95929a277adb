import { appsByStoreware } from "@/config";
import { BlockStack, Box, Button, InlineGrid, InlineStack, List, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import Carousel from "react-multi-carousel";
import { BANNER_APP_BY_STOREWARE } from "storeseo-enums/cacheKeys";
import DismissableBanner from "../common/DismissableBanner";
import CarouselCustomPagination from "./CarouselCustomPagination";

const OtherAppsByStoreware = () => {
  const { t } = useTranslation();

  return (
    <DismissableBanner
      title={t("Others apps from Storeware")}
      content={<CarouselSlider />}
      bannerKey={BANNER_APP_BY_STOREWARE}
    />
  );
};

const CarouselSlider = () => {
  return (
    <BlockStack gap="400">
      <Carousel
        arrows={false}
        containerClass="container"
        focusOnSelect={false}
        customTransition="fade-in-out 1s ease-in-out"
        className="fade-carousel"
        customButtonGroup={<CarouselCustomPagination />}
        renderButtonGroupOutside
        transitionDuration={400}
        infinite
        autoPlay
        autoPlaySpeed={10000}
        keyBoardControl
        pauseOnHover
        responsive={{
          desktop: {
            breakpoint: {
              max: 3000,
              min: 1024,
            },
            items: 1,
          },
          tablet: {
            breakpoint: {
              max: 1024,
              min: 464,
            },
            items: 1,
          },
          mobile: {
            breakpoint: {
              max: 464,
              min: 0,
            },
            items: 1,
          },
        }}
        shouldResetAutoplay
        slidesToSlide={1}
      >
        {appsByStoreware.map((item) => (
          <OtherAppsCard
            key={item.key}
            item={item}
            style={{}}
          />
        ))}
      </Carousel>
    </BlockStack>
  );
};

const OtherAppsCard = ({ item }) => {
  const { t } = useTranslation();

  return (
    <div className="fade-slide">
      <InlineGrid
        gap="600"
        columns={{ xs: 1, sm: 1, md: ["oneHalf", "oneHalf"], lg: ["oneHalf", "oneHalf"], xl: ["oneHalf", "oneHalf"] }}
      >
        <Box>
          <img
            alt={t(item.label)}
            width="100%"
            style={{
              // objectFit: "cover",
              objectPosition: "center",
              maxHeight: "100%",
              borderRadius: "var(--p-border-radius-200)",
            }}
            src={item.image}
            loading="lazy"
          />
        </Box>

        <Box
        // width="56%"
        >
          <BlockStack gap="400">
            <Text
              as="h4"
              variant="headingSm"
            >
              {item.title}
            </Text>

            <Text
              as="p"
              variant="bodyMd"
              tone="subdued"
            >
              {t(item.content)}
            </Text>

            {item.features.length > 0 && (
              <List type="bullet">
                {item.features.map((feature, idx) => (
                  <List.Item key={idx}>
                    <Text
                      as="span"
                      tone="subdued"
                    >
                      {t(feature)}
                    </Text>
                  </List.Item>
                ))}
              </List>
            )}

            <InlineStack gap="200">
              <Button
                url={item.button.url}
                target="_blank"
              >
                {t(item.button.label)}
              </Button>
            </InlineStack>
          </BlockStack>
        </Box>
      </InlineGrid>
    </div>
  );
};

export default OtherAppsByStoreware;

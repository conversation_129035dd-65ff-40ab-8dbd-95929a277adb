import { sendRequestToCrisp } from "@/utility/crisp";
import { InlineStack } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { BANNER_AI_CREDIT_GIFT } from "storeseo-enums/cacheKeys";
import DismissableBanner from "../common/DismissableBanner";
import AiCreditGift from "../svg/AiCreditGift";

const GiftAICreditCard = () => {
  const { t } = useTranslation();

  const aiCreditGift = 500;

  return (
    <DismissableBanner
      title={
        <InlineStack
          gap="100"
          blockAlign="start"
          align="start"
        >
          <AiCreditGift /> {t("Get Extra {{CREDITS}} Free AI Credits!", { CREDITS: aiCreditGift })}
        </InlineStack>
      }
      content={t(
        "Contact us now to claim {{CREDITS}} extra AI credits instantly for free. Don’t miss this opportunity to optimization more effectively.",
        { CREDITS: aiCreditGift }
      )}
      bannerKey={BANNER_AI_CREDIT_GIFT}
      primaryAction={{
        content: t("Chat with Us Live!"),
        onAction: () =>
          sendRequestToCrisp(
            t("I would like to get {{CREDITS}} free AI credits for my store", { CREDITS: aiCreditGift })
          ),
      }}
    />
  );
};

export default GiftAICreditCard;

import { whatsNewContents } from "@/config";
import { Bleed, BlockStack, Box, Divider, InlineStack, MediaCard } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import Carousel from "react-multi-carousel";
import { BANNER_WHATS_NEW } from "storeseo-enums/cacheKeys";
import DismissableBanner from "../common/DismissableBanner";
import CarouselCustomPagination from "./CarouselCustomPagination";

const WhatsNewSection = () => {
  const { t } = useTranslation();

  return (
    <DismissableBanner
      title={
        <InlineStack
          gap="200"
          blockAlign="center"
        >
          <Box as="span">🎉</Box> {t("What's New")}
        </InlineStack>
      }
      content={<CarouselSlider />}
      bannerKey={BANNER_WHATS_NEW}
    />
  );
};

const CarouselSlider = () => {
  const { t } = useTranslation();

  return (
    <Box minHeight="223.5px">
      <BlockStack gap="400">
        <Bleed marginInline="400">
          <Divider />
        </Bleed>
        <Carousel
          additionalTransfrom={0}
          arrows={false}
          autoPlay
          autoPlaySpeed={5000}
          centerMode={false}
          className=""
          containerClass="container"
          customButtonGroup={<CarouselCustomPagination />}
          dotListClass=""
          draggable
          focusOnSelect={false}
          itemClass=""
          keyBoardControl
          minimumTouchDrag={80}
          partialVisible
          pauseOnHover
          renderArrowsWhenDisabled={false}
          renderButtonGroupOutside
          renderDotsOutside={false}
          responsive={{
            desktop: {
              breakpoint: {
                max: 3000,
                min: 1024,
              },
              items: 1,
              partialVisibilityGutter: 250,
            },
            tablet: {
              breakpoint: {
                max: 1024,
                min: 464,
              },
              items: 1,
              partialVisibilityGutter: 100,
            },
            mobile: {
              breakpoint: {
                max: 464,
                min: 0,
              },
              items: 1,
              partialVisibilityGutter: 50,
            },
          }}
          rewind
          rewindWithAnimation
          rtl={false}
          shouldResetAutoplay
          showDots={false}
          sliderClass=""
          slidesToSlide={1}
          swipeable
        >
          {whatsNewContents.map((item, idx) => (
            <Box
              paddingInlineEnd={400}
              key={item.key}
              minHeight="160px"
            >
              <MediaCard
                title={t(item.label)}
                primaryAction={{
                  content: t(item.button.content),
                  url: item.button.url,
                }}
                description={t(item.content)}
                // size="small"
              >
                <img
                  alt={item.label}
                  width="100%"
                  height="100%"
                  style={{ objectFit: "cover", objectPosition: "center", minHeight: "160px" }}
                  src={item.image}
                  loading={idx === 0 ? "eager" : "lazy"}
                />
              </MediaCard>
            </Box>
          ))}
        </Carousel>
      </BlockStack>
    </Box>
  );
};

export default WhatsNewSection;

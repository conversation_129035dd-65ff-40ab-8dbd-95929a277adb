// @ts-check
import { useAi<PERSON>ontent<PERSON><PERSON>, useAppQuery, useShop<PERSON><PERSON> } from "@/hooks";
import useUserAddon from "@/hooks/useUserAddon";
import queryKeys from "@/utility/queryKeys";
import { Badge, BlockStack, Button, InlineGrid, InlineStack, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { BANNER_ENABLE_AUTO_AI_OPTIMIZER, BANNER_ENABLE_AUTO_IMAGE_OPTIMIZER } from "storeseo-enums/cacheKeys";
import settingKeys from "storeseo-enums/settingKeys";
import { AI_OPTIMIZER, IMAGE_OPTIMIZER } from "storeseo-enums/subscriptionAddonGroup";
import DismissableBanner from "../common/DismissableBanner";
import ResourceType from "storeseo-enums/resourceType";

export default function AutoOptimizerCard() {
  const { t } = useTranslation();

  const { getImageOptimizerSetting } = useShopApi();
  const { hasImageOptimizer, hasAiOptimizer } = useUserAddon();
  const { getAiAutoOptimizationSettings } = useAiContentApi();

  const { data: imageOptimiationSetting } = useAppQuery({
    queryKey: [queryKeys.IMAGE_OPTIMIZE_SETTINGS],
    queryFn: () => getImageOptimizerSetting(),
    reactQueryOptions: {
      staleTime: 0,
    },
  });

  const { data: aiOptimizationSetting } = useAppQuery({
    queryKey: [settingKeys.AI_CONTENT_SETTINGS],
    queryFn: () => getAiAutoOptimizationSettings(),
    reactQueryOptions: {
      staleTime: 0,
    },
  });

  const autoOptimizationCards = [
    {
      enabled: hasImageOptimizer && !imageOptimiationSetting?.autoOptimization,
      name: IMAGE_OPTIMIZER,
      title: "Activate auto image optimization",
      content:
        "Automatically optimize your store’s images and their alt text according to your selected image optimization settings.",
      action: {
        label: "Enable auto image optimizer",
        url: "/settings/image-optimizer",
      },
      bannerKey: BANNER_ENABLE_AUTO_IMAGE_OPTIMIZER,
    },
    {
      enabled: hasAiOptimizer && !aiOptimizationSetting?.[ResourceType.PRODUCT]?.status,
      name: AI_OPTIMIZER,
      title: "Enable AI auto optimizer",
      content:
        "Let StoreSEO automatically generate all the meta titles, descriptions, and tags using the speed and power of AI.",
      action: {
        label: "Activate AI auto optimizer",
        url: "/settings/auto-ai-optimization",
      },
      bannerKey: BANNER_ENABLE_AUTO_AI_OPTIMIZER,
    },
  ];

  const columnCount = autoOptimizationCards.filter((c) => c.enabled)?.length || 1;

  return (
    <InlineGrid
      gap="400"
      columns={{ xs: 1, sm: 1, md: columnCount, lg: columnCount, xl: columnCount }}
    >
      {autoOptimizationCards
        .filter((c) => c.enabled)
        .map((card) => (
          <DismissableBanner
            key={card.name}
            title={
              <InlineStack
                gap="200"
                blockAlign="center"
              >
                {t(card.title)} <Badge tone="attention">{t("Disabled")}</Badge>
              </InlineStack>
            }
            content={
              <BlockStack gap="200">
                <Text
                  as="p"
                  tone="subdued"
                >
                  {t(card.content)}
                </Text>

                <InlineStack
                  gap="200"
                  wrap={false}
                  align="start"
                  blockAlign="center"
                >
                  <Button url={card.action.url}>{t(card.action.label)}</Button>
                </InlineStack>
              </BlockStack>
            }
            bannerKey={BANNER_ENABLE_AUTO_IMAGE_OPTIMIZER}
          />
        ))}
    </InlineGrid>
  );
}

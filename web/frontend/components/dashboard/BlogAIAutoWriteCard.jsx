import { useTranslation } from "react-i18next";
import { BANNER_BLOG_AI_AUTOWRITE } from "storeseo-enums/cacheKeys";
import DismissableBanner from "../common/DismissableBanner";

const BlogAIAutoWriteCard = () => {
  const { t } = useTranslation();

  return (
    <DismissableBanner
      mediaCard
      title={t("Write Well Optimized Blog with AI")}
      content={t(
        "Grow your online store with AI blog generation. Easily create clear, SEO-friendly content that helps your products pop up in AI-driven search results and recommendations"
      )}
      primaryAction={{
        content: t("Write with AI"),
        url: "/ai-blog-generator",
      }}
      illustration="https://cdn.storeseo.com/whats-new/blog-ai-autowrite.png"
      bannerKey={BANNER_BLOG_AI_AUTOWRITE}
    />
  );
};

export default BlogAIAutoWriteCard;

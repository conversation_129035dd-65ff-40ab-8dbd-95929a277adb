import moment from "moment";
import { useTranslation } from "react-i18next";
import { BANNER_PAGEFLY } from "storeseo-enums/cacheKeys";
import DismissableBanner from "../common/DismissableBanner";

const exclusivePartnership = {
  name: "pagefly",
  title: "PageFly Page Builder - Trusted by 200.000+ Shopify merchants",
  content:
    "Create high-converting, SEO-friendly pages with an intuitive drag-and-drop interface—no coding required. Easily design stunning product, landing, and home pages to boost visibility and drive sales.",
  action: {
    label: "Try PageFly Free",
    url: "https://pagefly.io/?ref=RWKu3hDH&target=app-listing",
  },
  image: "https://cdn.storeseo.com/partnerships/pagefly.png",
  cacheKey: BANNER_PAGEFLY,
};

const ExclusivePartnershipCard = () => {
  const { t } = useTranslation();
  const now = +moment();
  const expiry = +moment(process.env?.FRONTEND_EXCLUSIVE_PARTNER_EXPIRE_DATE).endOf("D");

  if (expiry < now) return null;

  return (
    <DismissableBanner
      mediaCard
      title={t(exclusivePartnership.title)}
      content={t(exclusivePartnership.content)}
      primaryAction={{
        content: t(exclusivePartnership.action.label),
        url: exclusivePartnership.action.url,
        target: "_blank",
      }}
      illustration={exclusivePartnership.image}
      bannerKey={exclusivePartnership.cacheKey}
    />
  );
};

export default ExclusivePartnershipCard;

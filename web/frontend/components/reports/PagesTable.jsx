import { Card, IndexTable, Link, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import Radial<PERSON><PERSON> from "../charts/RadialChart";
import TableRowsSkeleton from "../loader/TableRowsSkeleton";

export default function PagesTable({ items = [], isLoading }) {
  const { t } = useTranslation();

  if (!isLoading && !items.length)
    return (
      <Card>
        <Text
          as="p"
          tone="subdued"
          alignment="center"
        >
          {t("You haven't synced any pages yet")}
        </Text>
      </Card>
    );

  return (
    <Card padding={"0"}>
      <IndexTable
        headings={[{ title: "#" }, { title: t("Page") }, { title: t("Score") }]}
        itemCount={items.length || 5}
        selectable={false}
      >
        {isLoading && (
          <TableRowsSkeleton
            cols={3}
            rows={5}
          />
        )}

        {!isLoading &&
          items.map((p, idx) => (
            <IndexTable.Row
              id={idx}
              key={idx}
              position={idx}
            >
              <IndexTable.Cell className="width-24">
                <Text>{idx + 1}</Text>
              </IndexTable.Cell>

              <IndexTable.Cell className="break_coll_content">
                <Link
                  url={`/optimize-seo/pages/${p.page_id}`}
                  monochrome
                  removeUnderline
                  dataPrimaryLink
                  onClick={(e) => e.stopPropagation()}
                >
                  <Text
                    as="h4"
                    variant="bodyMd"
                    fontWeight="medium"
                  >
                    {p.title}
                  </Text>
                </Link>
              </IndexTable.Cell>

              <IndexTable.Cell className={"width-60"}>
                <RadialChart
                  score={p.score}
                  dimension={40}
                />
              </IndexTable.Cell>
            </IndexTable.Row>
          ))}
      </IndexTable>
    </Card>
  );
}

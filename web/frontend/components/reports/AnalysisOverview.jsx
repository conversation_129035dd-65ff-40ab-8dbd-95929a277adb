import { BlockStack, Card, Grid, InlineStack, SkeletonBodyText, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { archiveIcon, barIcon, chartIcon, docIcon, meterIcon, meterIconDanger } from "../svg/Icons";
import IconArea from "./IconArea";

export const PRODUCT_ANALYSIS_CARDS_INFO = [
  {
    title: "Total products",
    key: "totalCount",
    value: 0,
    color: "rgba(255, 250, 235, 1)",
    icon: archiveIcon("rgba(178, 132, 0, 1)"),
  },
  {
    title: "Total optimized products",
    key: "optimizedCount",
    value: 0,
    color: "rgba(235, 249, 255, 1)",
    icon: meterIcon("rgba(0, 148, 213, 1)"),
  },
  {
    title: "Total unoptimized products",
    key: "unoptimizedCount",
    value: 0,
    color: "rgba(254, 239, 236, 1)",
    icon: meterIconDanger("rgba(239, 77, 47, 1)"),
  },
  {
    title: "Total alt-text added",
    key: "imgAltTextAdded",
    value: 0,
    color: "rgba(240, 235, 255, 1)",
    icon: docIcon("rgba(128, 81, 255, 1)"),
  },
  {
    title: "Total meta tags added",
    key: "metaTagsAdded",
    value: 0,
    color: "rgba(239, 250, 245, 1)",
    icon: archiveIcon("rgba(41, 132, 90, 1)"),
  },
  {
    title: "Total issues",
    key: "seoIssues",
    value: 0,
    color: "rgba(254, 239, 236, 1)",
    icon: chartIcon("rgba(239, 77, 47, 1)"),
  },
];

export const COLLECTION_ANALYSIS_CARDS_INFO = [
  {
    title: "Total collections",
    key: "totalCount",
    value: 0,
    color: "rgba(255, 250, 235, 1)",
    icon: archiveIcon("rgba(178, 132, 0, 1)"),
  },
  {
    title: "Total optimized collections",
    key: "optimizedCount",
    value: 0,
    color: "rgba(235, 249, 255, 1)",
    icon: meterIcon("rgba(0, 148, 213, 1)"),
  },
  {
    title: "Total unoptimized collections",
    key: "unoptimizedCount",
    value: 0,
    color: "rgba(254, 239, 236, 1)",
    icon: meterIconDanger("rgba(239, 77, 47, 1)"),
  },
  {
    title: "Total alt-text added",
    key: "imgAltTextAdded",
    value: 0,
    color: "rgba(240, 235, 255, 1)",
    icon: docIcon("rgba(128, 81, 255, 1)"),
  },
  {
    title: "Total meta tags added",
    key: "metaTagsAdded",
    value: 0,
    color: "rgba(239, 250, 245, 1)",
    icon: archiveIcon("rgba(41, 132, 90, 1)"),
  },
  {
    title: "Total issues",
    key: "seoIssues",
    value: 0,
    color: "rgba(254, 239, 236, 1)",
    icon: chartIcon("rgba(239, 77, 47, 1)"),
  },
];

export const PAGE_ANALYSIS_CARDS_INFO = [
  {
    title: "Total pages",
    key: "totalCount",
    value: 0,
    color: "rgba(255, 250, 235, 1)",
    icon: archiveIcon("rgba(178, 132, 0, 1)"),
  },
  {
    title: "Total optimized pages",
    key: "optimizedCount",
    value: 0,
    color: "rgba(235, 249, 255, 1)",
    icon: meterIcon("rgba(0, 148, 213, 1)"),
  },
  {
    title: "Total unoptimized pages",
    key: "unoptimizedCount",
    value: 0,
    color: "rgba(254, 239, 236, 1)",
    icon: meterIconDanger("rgba(239, 77, 47, 1)"),
  },
  {
    title: "Total alt-text added",
    key: "imgAltTextAdded",
    value: 0,
    color: "rgba(240, 235, 255, 1)",
    icon: docIcon("rgba(128, 81, 255, 1)"),
  },
  {
    title: "Total meta tags added",
    key: "metaTagsAdded",
    value: 0,
    color: "rgba(239, 250, 245, 1)",
    icon: archiveIcon("rgba(41, 132, 90, 1)"),
  },
  {
    title: "Total issues",
    key: "seoIssues",
    value: 0,
    color: "rgba(254, 239, 236, 1)",
    icon: chartIcon("rgba(239, 77, 47, 1)"),
  },
];

export const BLOG_POSTS_ANALYSIS_CARDS_INFO = [
  {
    title: "Total blog posts",
    key: "totalCount",
    value: 0,
    color: "rgba(255, 250, 235, 1)",
    icon: archiveIcon("rgba(178, 132, 0, 1)"),
  },
  {
    title: "Total optimized blog posts",
    key: "optimizedCount",
    value: 0,
    color: "rgba(235, 249, 255, 1)",
    icon: meterIcon("rgba(0, 148, 213, 1)"),
  },
  {
    title: "Total unoptimized blog posts",
    key: "unoptimizedCount",
    value: 0,
    color: "rgba(254, 239, 236, 1)",
    icon: meterIconDanger("rgba(239, 77, 47, 1)"),
  },
  {
    title: "Total alt-text added",
    key: "imgAltTextAdded",
    value: 0,
    color: "rgba(240, 235, 255, 1)",
    icon: docIcon("rgba(128, 81, 255, 1)"),
  },
  {
    title: "Total meta tags added",
    key: "metaTagsAdded",
    value: 0,
    color: "rgba(239, 250, 245, 1)",
    icon: archiveIcon("rgba(41, 132, 90, 1)"),
  },
  {
    title: "Total issues",
    key: "seoIssues",
    value: 0,
    color: "rgba(254, 239, 236, 1)",
    icon: chartIcon("rgba(239, 77, 47, 1)"),
  },
];

export const DOC_ANALYSIS_CARDS_INFO = [
  {
    title: "Total docs",
    key: "totalCount",
    value: 0,
    color: "rgba(255, 250, 235, 1)",
    icon: archiveIcon("rgba(178, 132, 0, 1)"),
  },
  {
    title: "Total optimized docs",
    key: "optimizedCount",
    value: 0,
    color: "rgba(235, 249, 255, 1)",
    icon: meterIcon("rgba(0, 148, 213, 1)"),
  },
  {
    title: "Total unoptimized docs",
    key: "unoptimizedCount",
    value: 0,
    color: "rgba(254, 239, 236, 1)",
    icon: meterIconDanger("rgba(239, 77, 47, 1)"),
  },
  {
    title: "Total alt-text added",
    key: "imgAltTextAdded",
    value: 0,
    color: "rgba(240, 235, 255, 1)",
    icon: docIcon("rgba(128, 81, 255, 1)"),
  },
  {
    title: "Total meta tags added",
    key: "metaTagsAdded",
    value: 0,
    color: "rgba(239, 250, 245, 1)",
    icon: archiveIcon("rgba(41, 132, 90, 1)"),
  },
  {
    title: "Total issues",
    key: "seoIssues",
    value: 0,
    color: "rgba(254, 239, 236, 1)",
    icon: chartIcon("rgba(239, 77, 47, 1)"),
  },
];

const AnalysisCard = ({ title, value, icon, color, isLoading }) => {
  const { t } = useTranslation();
  return (
    <div className="overview-item">
      <Card>
        {isLoading ? (
          <SkeletonBodyText />
        ) : (
          <InlineStack
            wrap={false}
            gap="400"
            blockAlign="center"
          >
            <IconArea color={color}>{icon}</IconArea>
            <BlockStack gap="100">
              <Text
                variant="headingLg"
                as="h3"
              >
                {value}
              </Text>
              <Text
                variant="bodyMd"
                as="p"
                tone="subdued"
              >
                {t(title)}
              </Text>
            </BlockStack>
          </InlineStack>
        )}
      </Card>
    </div>
  );
};

export default function AnalysisOverview({ data, isLoading, cardsInfo = PRODUCT_ANALYSIS_CARDS_INFO }) {
  const { t } = useTranslation();

  return (
    <BlockStack gap="200">
      <Text
        variant="headingMd"
        as="h6"
      >
        {t("Performance Breakdown")}
      </Text>
      <Grid columns={{ xs: 1, sm: 2, md: 2, lg: 3 }}>
        {cardsInfo.map((c) => (
          <Grid.Cell key={c.key}>
            <AnalysisCard
              {...c}
              isLoading={isLoading}
              value={data?.[c.key] || c.value}
            />
          </Grid.Cell>
        ))}
      </Grid>
    </BlockStack>
  );
}

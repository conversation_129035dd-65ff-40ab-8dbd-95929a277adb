import { BlockStack, Card, Grid, SkeletonBodyText, Text } from "@shopify/polaris";
import Radial<PERSON>hart from "../charts/RadialChart";
import { useTranslation } from "react-i18next";

const flexStyle = {
  display: "flex",
  justifyContent: "space-between",
  alignItems: "end",
  gap: 16,
};

const cardsInfo = [
  {
    title: "Overall",
    dataKey: "overallScore",
    percentage: 0,
    color: null,
  },
  {
    title: "Good",
    dataKey: "goodCount",
    percentage: 0,
    color: "success",
  },
  {
    title: "Fair",
    dataKey: "fairCount",
    percentage: 0,
    color: "warning",
  },
  {
    title: "Poor",
    dataKey: "poorCount",
    percentage: 0,
    color: "critical",
  },
];

const OptimizationCard = ({ title, percentage, isLoading, color = null }) => {
  const { t } = useTranslation();

  return (
    <Grid.Cell>
      <Card>
        {isLoading ? (
          <SkeletonBodyText />
        ) : (
          <div style={flexStyle}>
            <BlockStack gap="200">
              <Text
                variant="headingLg"
                as="h3"
              >
                {percentage}%
              </Text>
              <Text
                variant="headingSm"
                as="h6"
              >
                {t(title)}
              </Text>

              {/* <HorizontalStack blockAlign="center">
                  <Icon
                    source={CaretDownIcon}
                    color="critical"
                  />
                  <Text
                    variant="headingXs"
                    as="h6"
                    color="critical"
                  >
                    3.29K
                  </Text>
                </HorizontalStack> */}
            </BlockStack>
            <RadialChart
              score={percentage}
              dimension={50}
              color={color}
              showPercent
            />
          </div>
        )}
      </Card>
    </Grid.Cell>
  );
};

export default function OverallOptimizationHighlights({ data, isLoading }) {
  const { t } = useTranslation();

  const cards = cardsInfo.map((c) => {
    let percentage = 0;

    if (data) {
      const value = String(data?.[c.dataKey]);
      if (c.dataKey === "overallScore") {
        percentage = Number(value?.replace("%", "") || 0);
      } else {
        percentage = Math.round((Number(value) * 100) / data?.totalCount) || 0;
      }
    }
    return {
      ...c,
      percentage,
    };
  });

  return (
    <BlockStack gap="200">
      <Text
        variant="headingMd"
        as="h6"
      >
        {t("Performance Overview")}
      </Text>
      <Grid columns={{ xs: 1, sm: 2, md: 2, lg: 4, xl: 4 }}>
        {cards.map((c, idx) => (
          <Grid.Cell key={idx}>
            <OptimizationCard
              {...c}
              key={c.dataKey}
              isLoading={isLoading}
            />
          </Grid.Cell>
        ))}
      </Grid>
    </BlockStack>
  );
}

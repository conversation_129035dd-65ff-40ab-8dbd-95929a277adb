import { Card, IndexTable, Link, Text, Thumbnail } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useAppNavigation } from "../../hooks/useAppNavigation";
import { prepareThumbnailURL } from "../../utility/helpers";
import RadialChart from "../charts/RadialChart";
import TableRowsSkeleton from "../loader/TableRowsSkeleton";

export default function CollectionsTable({ items = [], isLoading }) {
  const { t } = useTranslation();

  const { goToCollection } = useAppNavigation();

  if (!isLoading && !items.length)
    return (
      <Card>
        <Text
          as="p"
          tone="subdued"
          alignment="center"
        >
          {t("You haven't synced any collections yet")}
        </Text>
      </Card>
    );

  return (
    <Card padding="0">
      <IndexTable
        headings={[{ title: "#" }, { title: "" }, { title: t("Collection") }, { title: t("Score") }]}
        itemCount={items.length || 5}
        selectable={false}
      >
        {isLoading && (
          <TableRowsSkeleton
            cols={4}
            rows={5}
          />
        )}

        {!isLoading &&
          items.map((p, idx) => {
            const thumbnail = prepareThumbnailURL(p.featuredImage?.src);

            return (
              <IndexTable.Row
                key={idx}
                id={idx}
                position={idx}
              >
                <IndexTable.Cell className="width-50">
                  <Text>{idx + 1}</Text>
                </IndexTable.Cell>

                <IndexTable.Cell className="width-60">
                  <Thumbnail
                    source={thumbnail}
                    alt={p.featuredImage?.altText}
                    size="small"
                  />
                </IndexTable.Cell>

                <IndexTable.Cell className={"break_coll_content"}>
                  <Link
                    onClick={() => goToCollection(p.shopifyId)}
                    monochrome
                    removeUnderline
                  >
                    <Text
                      as="h4"
                      variant="bodyMd"
                      fontWeight="medium"
                    >
                      {p.title}
                    </Text>
                  </Link>
                </IndexTable.Cell>

                <IndexTable.Cell className={"width-60"}>
                  <RadialChart
                    score={p.score}
                    dimension={40}
                    percentFontSize={12}
                  />
                </IndexTable.Cell>
              </IndexTable.Row>
            );
          })}
      </IndexTable>
    </Card>
  );
}

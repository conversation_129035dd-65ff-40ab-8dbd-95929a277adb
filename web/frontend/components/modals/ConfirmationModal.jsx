//@ts-check
import Modal from "@/modules/components/Modal";
import { Box, Text } from "@shopify/polaris";
import { useCallback } from "react";
import { useTranslation } from "react-i18next";

/**
 * @typedef {object} ConfirmationModalProps
 * @property {boolean} [show=false] - Whether the modal is open
 * @property {string} [title="Confirm!"] - The title of the modal
 * @property {string|React.ReactNode} [content="Are you sure?"] - The content to display in the modal body
 * @property {string} [primaryActionText="Confirm"] - The text for the primary action button
 * @property {() => void} [primaryAction=() => {}] - Callback function executed when primary action is clicked
 * @property {boolean} [primaryActionIsDestructive=false] - Whether the primary action is destructive
 * @property {(show: boolean) => void} [onClose=(show=false) => {}] - Callback function executed when modal is closed
 * @property {boolean} [loading=false] - Whether the primary action is in a loading state
 * @property {React.ReactNode} [children=null] - Custom content to render in the modal body
 * @property {('small' | 'base' | 'large' | 'max')} [size="small"] - Size variant of the modal
 */

/**
 * @param {ConfirmationModalProps} props
 */
const ConfirmationModal = ({
  show = false,
  title = "Confirm!",
  content = "Are you sure?",
  primaryActionText = "Confirm",
  primaryAction = () => {},
  primaryActionIsDestructive = false,
  onClose = (show = false) => {},
  loading = false,
  children = null,
  size = "small",
}) => {
  const { t } = useTranslation();

  const handleClose = useCallback(() => {
    onClose(false);
  }, [onClose]);

  const setOpen = useCallback(
    (isOpen) => {
      if (!isOpen) {
        handleClose();
      }
    },
    [handleClose]
  );

  return (
    <Modal
      type="app-bridge"
      open={show}
      setOpen={setOpen}
      onClose={handleClose}
      variant={size}
    >
      <Modal.Section>
        <Box padding="400">
          {children ? children : <Text as={"p"}>{typeof content === "string" ? t(content) : content}</Text>}
        </Box>

        <Modal.TitleBar title={t(title)}>
          <button
            variant="primary"
            tone={primaryActionIsDestructive ? "critical" : undefined}
            onClick={primaryAction}
            loading={loading ? "" : undefined}
          >
            {t(primaryActionText)}
          </button>
          <button onClick={handleClose}>{t("Cancel")}</button>
        </Modal.TitleBar>
      </Modal.Section>
    </Modal>
  );
};

export default ConfirmationModal;

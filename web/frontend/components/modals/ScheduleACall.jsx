import { BlockStack, But<PERSON>, <PERSON><PERSON>, Text } from "@shopify/polaris";
import { PhoneIcon } from "@shopify/polaris-icons";
import { useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import { SCHEDULE_A_CALL_URL } from "../../config/index.js";

const ScheduleACall = () => {
  const [active, setActive] = useState(false);
  const { t } = useTranslation();

  const handleChange = useCallback(() => setActive(!active), [active]);

  return (
    <>
      <Button
        icon={PhoneIcon}
        onClick={handleChange}
      >
        {t("Get Free SEO Consultation")}
      </Button>

      <Modal
        sectioned
        open={active}
        onClose={handleChange}
        title={t("Want to talk with our SEO experts?")}
        primaryAction={{
          content: t("Schedule a call"),
          onAction: handleChange,
          url: SCHEDULE_A_CALL_URL,
          target: "_blank",
        }}
      >
        <Modal.Section>
          <BlockStack gap="200">
            <Text as={"p"}>
              {t(
                "We have some Shopify experts in the house. Book a one-on-one session with them and get your SEO issues solved."
              )}
            </Text>
            <Text as={"p"}>
              <strong>{t("PS")}:</strong> &nbsp;
              {t("It is completely FREE.")}
            </Text>
          </BlockStack>
        </Modal.Section>
      </Modal>
    </>
  );
};

export default ScheduleACall;

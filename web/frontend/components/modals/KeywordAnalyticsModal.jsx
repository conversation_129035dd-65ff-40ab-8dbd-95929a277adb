import Modal from "@/modules/components/Modal";
import { Box, Button, Card, IndexTable, InlineGrid, SkeletonDisplayText, Text } from "@shopify/polaris";
import { PlusIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";
import TableRowsSkeleton from "../loader/TableRowsSkeleton";

const TABLE_COLUMNS = [
  { label: "Keyword" },
  { label: "Search Volume" },
  { label: "Cost Per Click" },
  { label: "Paid Difficulty" },
  { label: "" },
];

const LoadingState = () => {
  const { t } = useTranslation();

  return (
    <Modal.Section>
      <Box padding="400">
        <InlineGrid
          columns={3}
          gap="300"
        >
          <Card>
            <SkeletonDisplayText />
            <Box paddingBlockStart="200"></Box>
            <Text
              as="span"
              variant="headingMd"
            >
              {t("Search Volume")}
            </Text>
          </Card>
          <Card>
            <SkeletonDisplayText />
            <Box paddingBlockStart="200"></Box>
            <Text
              as="span"
              variant="headingMd"
            >
              {t("Cost Per Click")}
            </Text>
          </Card>
          <Card>
            <SkeletonDisplayText />

            <Box paddingBlockStart="200"></Box>
            <Text
              as="span"
              variant="headingMd"
            >
              {t("Paid Difficulty")}
            </Text>
          </Card>
        </InlineGrid>

        <Box paddingBlockStart="600">
          <Text
            as="h3"
            variant="headingMd"
          >
            {t("Related Keywords")}
          </Text>
          <Box paddingBlockStart="300"></Box>
          <Card padding="0">
            <IndexTable
              emptyState={<p>{t("No related keyword found!")}</p>}
              selectable={false}
              headings={TABLE_COLUMNS.map((c) => ({ title: t(c.label) }))}
              itemCount={5}
            >
              <TableRowsSkeleton
                rows={5}
                cols={TABLE_COLUMNS.length}
              />
            </IndexTable>
          </Card>
        </Box>
      </Box>
    </Modal.Section>
  );
};

export default function KeywordAnalyticsModal({
  show,
  keyword,
  keywordMetrics,
  onKeywordClick = () => {},
  onClose = () => {},
}) {
  const { t } = useTranslation();
  const { searchVolume = "-", cpc = "-", paidDifficulty = "-", relatedKeywords = [] } = keywordMetrics || {};

  return (
    <Modal
      type="app-bridge"
      open={show}
      setOpen={(open) => {
        if (!open) onClose();
      }}
      onClose={onClose}
      variant="large"
    >
      {!keywordMetrics && <LoadingState />}
      {keywordMetrics && (
        <Modal.Section>
          <Box padding="400">
            <InlineGrid
              columns={3}
              gap="300"
            >
              <Card>
                <Text
                  as="p"
                  variant="heading2xl"
                >
                  {searchVolume}
                </Text>
                <Box paddingBlockStart="200"></Box>
                <Text
                  as="span"
                  variant="headingMd"
                >
                  {t("Search Volume")}
                </Text>
              </Card>
              <Card>
                <Text
                  as="p"
                  variant="heading2xl"
                >
                  {cpc}
                </Text>
                <Box paddingBlockStart="200"></Box>
                <Text
                  as="span"
                  variant="headingMd"
                >
                  {t("Cost Per Click")}
                </Text>
              </Card>
              <Card>
                <Text
                  as="p"
                  variant="heading2xl"
                >
                  {paidDifficulty}
                </Text>
                <Box paddingBlockStart="200"></Box>
                <Text
                  as="span"
                  variant="headingMd"
                >
                  {t("Paid Difficulty")}
                </Text>
              </Card>
            </InlineGrid>

            <Box paddingBlockStart="600">
              <Text
                as="h3"
                variant="headingMd"
              >
                {t("Related Keywords")}
              </Text>
              <Box paddingBlockStart="300"></Box>
              <Card padding="0">
                <IndexTable
                  emptyState={<p>{t("No related keyword found!")}</p>}
                  selectable={false}
                  headings={TABLE_COLUMNS.map((c) => ({ title: t(c.label) }))}
                  itemCount={relatedKeywords.length}
                >
                  {relatedKeywords.map((keyword, idx) => (
                    <IndexTable.Row
                      key={idx}
                      position={idx}
                      id={idx}
                    >
                      <IndexTable.Cell>
                        <Text>{keyword.text}</Text>
                      </IndexTable.Cell>
                      <IndexTable.Cell>
                        <Text>{keyword.searchVolume}</Text>
                      </IndexTable.Cell>
                      <IndexTable.Cell>
                        <Text>{keyword.cpc}</Text>
                      </IndexTable.Cell>
                      <IndexTable.Cell>
                        <Text>{keyword.paidDifficulty}</Text>
                      </IndexTable.Cell>
                      <IndexTable.Cell>
                        <Button
                          size={"micro"}
                          icon={PlusIcon}
                          onClick={() => {
                            onKeywordClick(keyword.text);
                            onClose();
                          }}
                        >
                          {t("Insert")}
                        </Button>
                      </IndexTable.Cell>
                    </IndexTable.Row>
                  ))}
                </IndexTable>
              </Card>
            </Box>
          </Box>
          <Modal.TitleBar title={`${t("Current Keyword")}: ${keyword}`}>
            <button onClick={onClose}>{t("Close")}</button>
          </Modal.TitleBar>
        </Modal.Section>
      )}
    </Modal>
  );
}

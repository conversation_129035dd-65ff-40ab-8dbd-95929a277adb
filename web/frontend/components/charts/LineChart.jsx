import ReactApex<PERSON>hart from "react-apexcharts";

/**
 *
 * @param {import("react").PropsWithChildren & { series: ApexAxisChartSeries, categories: string[], height: string, width: string, color: string xaxisType: string, showTooltip: boolean, showGrid: boolean, colors: string[] }} param0
 * @returns
 */
export default function LineChart({
  series = [],
  categories = [],
  xaxisType = "datetime",
  height = "auto",
  width = "100%",
  color = "#00E396",
  colors = [],
  showTooltip = true,
  showGrid = true,
  showAxisLabel = false,
}) {
  const state = {
    series,
    options: {
      colors: [...colors, color],
      legend: {
        show: false,
      },
      grid: {
        show: showGrid,
        xaxis: {
          lines: {
            show: false,
          },
        },
        yaxis: {
          lines: {
            show: true,
          },
        },
      },
      chart: {
        height,
        width,
        offsetX: -15,
        type: "line",
        toolbar: {
          show: false,
        },
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        curve: "smooth",
        width: "2",
        dashArray: [0, 0],
      },
      xaxis: {
        type: xaxisType,
        categories,
        labels: {
          show: showAxisLabel,
        },
        lines: {
          show: false,
        },
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
        tooltip: {
          enabled: false,
        },
      },
      yaxis: {
        labels: {
          show: showAxisLabel,
        },
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
      },
      tooltip: {
        enabled: showTooltip,
        x: {
          format: "MMM, dd",
        },
      },
    },
  };

  return (
    <ReactApexChart
      height={height}
      options={state.options}
      series={state.series}
      type="line"
    />
  );
}

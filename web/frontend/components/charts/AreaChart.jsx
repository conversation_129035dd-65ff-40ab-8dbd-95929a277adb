import ReactApexChart from "react-apexcharts";

/**
 *
 * @param {import("react").PropsWithChildren & { series: ApexAxisChartSeries, categories: string[], height: string, width: string, color: string xaxisType: string, showTooltip: boolean, showGrid: boolean }} param0
 * @returns
 */
export default function AreaChart({
  series = [],
  categories = [],
  xaxisType = "datetime",
  height = "auto",
  width = "100%",
  color = "#00E396",
  showTooltip = true,
  showGrid = true,
  showAxisLabel = false,
}) {
  const state = {
    series,
    options: {
      colors: [color],
      legend: {
        show: false,
      },
      grid: {
        show: showGrid,
        xaxis: {
          lines: {
            show: false,
          },
        },
        yaxis: {
          lines: {
            show: true,
          },
        },
      },
      chart: {
        height: "auto",
        width,
        // parentHeightOffset: 0,
        type: "area",
        toolbar: {
          show: false,
        },
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        curve: "smooth",
        width: "2",
      },
      xaxis: {
        type: xaxisType,
        categories,
        labels: {
          show: showAxisLabel,
        },
        lines: {
          show: false,
        },
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
        tooltip: {
          enabled: false,
        },
      },
      yaxis: {
        labels: {
          show: showAxisLabel,
        },
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
      },
      tooltip: {
        enabled: showTooltip,
        x: {
          format: "MMM, dd",
        },
      },
    },
  };

  return (
    <ReactApexChart
      height={height}
      options={state.options}
      series={state.series}
      type="area"
    />
  );
}

import AIOptimizationStatusBadge from "@/modules/optimize-seo/AIOptimizationStatusBadge";
import OptimizeSeoStatusBadge from "@/modules/optimize-seo/OptimizeSeoStatusBadge.jsx";
import {
  Badge,
  BlockStack,
  Box,
  Button,
  Icon,
  IndexTable,
  InlineStack,
  Link,
  Text,
  Thumbnail,
  useBreakpoints,
} from "@shopify/polaris";
import { BlogIcon, ComposeIcon, ViewIcon, WandIcon } from "@shopify/polaris-icons";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import BlogAutoWriteJobStatus from "storeseo-enums/blogAutoWrite/jobStatus";
import { useAppBridgeRedirect } from "../../hooks/useAppBridgeRedirect.js";
import { useBlogAutoWriteStatus } from "../../hooks/useBlogAutoWriteStatus.js";
import { useLabelColor } from "../../hooks/useLabelColor.js";
import { usePublishStatus } from "../../hooks/usePublishStatus.js";
import { formatDate, prepareThumbnailURL } from "../../utility/helpers.jsx";
import BlogAutoWriteModal from "../blog-auto-write/BlogAutoWriteModal.jsx";
import RadialChart from "../charts/RadialChart.jsx";
import TitleColumn from "../common/TitleColumn.jsx";
import TooltipWrapper from "../common/TooltipWrapper.jsx";
import ArticleRowItemSkeleton from "./ArticleRowItemSkeleton.jsx";

// Helper function to get the appropriate icon for blog auto-write status
const getStatusIcon = (status) => {
  if (status === BlogAutoWriteJobStatus.COMPLETED) {
    return WandIcon;
  }
  return ViewIcon;
};

export default function ArticleRowItem({ item, isFetching }) {
  const { smDown: isSmallDevice } = useBreakpoints();

  if (isFetching) return <ArticleRowItemSkeleton />;

  return <>{isSmallDevice ? <RowsCellForSmallDevices item={item} /> : <RowsCellForLargeDevices item={item} />}</>;
}

const RowsCellForSmallDevices = ({ item }) => {
  const { title, focus_keyword, created_at, score, issues, article_id, published_at, blog, image } = item;
  const { t } = useTranslation();
  const labelColor = useLabelColor(issues);
  const status = usePublishStatus(published_at);
  const thumbnail = prepareThumbnailURL(image?.src);
  const autoWriteStatus = useBlogAutoWriteStatus(item);
  const [progressModalOpen, setProgressModalOpen] = useState(false);
  const { handleEditArticle } = useAppBridgeRedirect();
  return (
    <Link
      monochrome
      removeUnderline
      url={`/optimize-seo/articles/${article_id}`}
      onClick={(e) => {
        e.stopPropagation();
        if (autoWriteStatus.shouldBlockNavigation) {
          e.preventDefault();
        }
      }}
    >
      <Box
        paddingBlock="300"
        paddingInline="400"
        width="100%"
      >
        <BlockStack gap="150">
          <InlineStack
            align="space-between"
            wrap={false}
            gap="200"
          >
            {/* Thumbnail */}
            <Box>
              <Thumbnail
                source={thumbnail}
                alt={image?.alt}
                size="small"
              />
            </Box>
            <Box width="100%">
              <BlockStack gap="300">
                {/* Basic info */}
                <BlockStack gap="100">
                  <Text
                    as="p"
                    variant="bodySm"
                  >
                    {blog.blog_title}
                  </Text>

                  {autoWriteStatus.isAiGenerated ? (
                    // AI-generated articles: Vertical stack with title and status
                    <BlockStack gap="100">
                      <Text
                        as={"h4"}
                        fontWeight="semibold"
                      >
                        {title}
                      </Text>
                      <InlineStack
                        gap="200"
                        blockAlign="center"
                      >
                        <Button
                          variant="plain"
                          size="micro"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            setProgressModalOpen(true);
                          }}
                        >
                          <InlineStack
                            gap="100"
                            blockAlign="center"
                            align="start"
                            wrap={false}
                          >
                            <Icon
                              source={getStatusIcon(autoWriteStatus.status)}
                              tone={autoWriteStatus.statusTone}
                            />
                            <Text
                              variant="bodySm"
                              tone={autoWriteStatus.statusTone}
                              fontWeight="medium"
                              breakWord
                            >
                              {autoWriteStatus.displayStatus}
                            </Text>
                          </InlineStack>
                        </Button>
                      </InlineStack>
                    </BlockStack>
                  ) : (
                    // Regular articles: Original layout
                    <Text
                      as={"h4"}
                      fontWeight="semibold"
                    >
                      {title}
                    </Text>
                  )}

                  <Text
                    as="span"
                    variant="bodySm"
                  >
                    {focus_keyword}
                  </Text>
                </BlockStack>
                {/* Scoring */}
                <InlineStack
                  align="space-between"
                  blockAlign="center"
                  gap="200"
                >
                  <BlockStack
                    gap="200"
                    blockAlign="center"
                  >
                    <BlockStack gap="200">
                      <InlineStack gap="200">
                        <Badge tone={status.color}>{t(status.name)}</Badge>

                        <Badge tone={labelColor}>
                          {issues} {t("Issues")}
                        </Badge>
                      </InlineStack>
                      <OptimizeSeoStatusBadge score={score} />
                    </BlockStack>

                    <Text
                      as="span"
                      variant="bodySm"
                      tone="subdued"
                    >
                      {formatDate(created_at)}
                    </Text>
                  </BlockStack>

                  <BlockStack
                    gap="100"
                    align="center"
                  >
                    <RadialChart
                      score={score}
                      dimension={40}
                    />

                    {/* Admin link button for successful auto-write jobs */}
                    {autoWriteStatus.isAiGenerated && autoWriteStatus.status === BlogAutoWriteJobStatus.COMPLETED && (
                      <TooltipWrapper content="Edit in Shopify Admin">
                        <Button
                          icon={BlogIcon}
                          onClick={(e) => {
                            e.stopPropagation();
                            e.preventDefault();
                            handleEditArticle(article_id, { external: true });
                          }}
                          variant="tertiary"
                          size="micro"
                        />
                      </TooltipWrapper>
                    )}
                  </BlockStack>
                </InlineStack>
              </BlockStack>
            </Box>
          </InlineStack>
        </BlockStack>
      </Box>

      {/* Blog Auto-Write Progress Modal */}
      {autoWriteStatus.isAiGenerated && (
        <BlogAutoWriteModal
          isOpen={progressModalOpen}
          onClose={() => setProgressModalOpen(false)}
          initialJobId={autoWriteStatus.jobId}
        />
      )}
    </Link>
  );
};

const RowsCellForLargeDevices = ({ item }) => {
  const {
    title,
    focus_keyword,
    created_at,
    score,
    issues,
    article_id,
    published_at,
    blog,
    image,
    ai_optimization_status,
    // id, // Database ID for fetching job progress (unused for now)
  } = item;
  const { t } = useTranslation();
  const labelColor = useLabelColor(issues);
  const status = usePublishStatus(published_at);
  const thumbnail = prepareThumbnailURL(image?.src);
  const autoWriteStatus = useBlogAutoWriteStatus(item);
  const [progressModalOpen, setProgressModalOpen] = useState(false);
  const { handleEditArticle } = useAppBridgeRedirect();

  return (
    <>
      <IndexTable.Cell className="width-table_thumbnail">
        <Thumbnail
          source={thumbnail}
          alt={image?.alt}
          size="small"
        />
      </IndexTable.Cell>
      <IndexTable.Cell className="break_coll_content width-resource_table_title">
        {autoWriteStatus.isAiGenerated ? (
          // AI-generated articles: Vertical stack with title and status
          <BlockStack gap="100">
            <Link
              monochrome
              removeUnderline
              url={`/optimize-seo/articles/${article_id}`}
              onClick={(e) => {
                if (autoWriteStatus.shouldBlockNavigation) {
                  e.preventDefault();
                }
              }}
            >
              <Text
                as="span"
                fontWeight="medium"
                tone={autoWriteStatus.shouldBlockNavigation ? "subdued" : undefined}
              >
                {title}
              </Text>
            </Link>
            <InlineStack
              gap="200"
              blockAlign="center"
            >
              <Button
                variant="plain"
                size="micro"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setProgressModalOpen(true);
                }}
              >
                <InlineStack
                  gap="100"
                  blockAlign="center"
                  align="start"
                  wrap={false}
                >
                  <Icon
                    source={getStatusIcon(autoWriteStatus.status)}
                    tone={autoWriteStatus.statusTone}
                  />
                  <Text
                    variant="bodySm"
                    tone={autoWriteStatus.statusTone}
                    fontWeight="medium"
                    breakWord
                  >
                    {autoWriteStatus.displayStatus}
                  </Text>
                </InlineStack>
              </Button>
              <AIOptimizationStatusBadge
                status={ai_optimization_status}
                noBadge
              />
            </InlineStack>
          </BlockStack>
        ) : (
          // Regular articles: Original layout
          <InlineStack
            wrap={false}
            gap="200"
            blockAlign="center"
          >
            {autoWriteStatus.shouldBlockNavigation ? (
              <Text
                as={"h4"}
                fontWeight="semibold"
                tone="subdued"
              >
                {title}
              </Text>
            ) : (
              <TitleColumn
                title={title}
                url={`/optimize-seo/articles/${article_id}`}
              />
            )}
            <AIOptimizationStatusBadge
              status={ai_optimization_status}
              noBadge
            />
          </InlineStack>
        )}
      </IndexTable.Cell>
      <IndexTable.Cell className="break_coll_content">
        <Text as={"p"}>{blog.blog_title}</Text>
      </IndexTable.Cell>
      <IndexTable.Cell className="width-150">
        <OptimizeSeoStatusBadge score={score} />
      </IndexTable.Cell>
      <IndexTable.Cell className="break_coll_content width-resource_table_title">
        <Text as={"p"}>{focus_keyword}</Text>
      </IndexTable.Cell>
      <IndexTable.Cell>
        <Badge tone={labelColor}>
          {issues} {t("Issues")}
        </Badge>
      </IndexTable.Cell>
      <IndexTable.Cell>
        <RadialChart
          score={score}
          dimension={40}
        />
      </IndexTable.Cell>
      <IndexTable.Cell className="width-120">{formatDate(created_at)}</IndexTable.Cell>
      <IndexTable.Cell className="width-100">
        <Badge tone={status.color}>{t(status.name)}</Badge>
      </IndexTable.Cell>
      <IndexTable.Cell className="width-120">
        <Box paddingInlineStart="800">
          <InlineStack
            align="start"
            gap="200"
          >
            {/* Fix button - Primary action */}
            <TooltipWrapper
              content={
                autoWriteStatus.shouldBlockNavigation
                  ? autoWriteStatus.status === BlogAutoWriteJobStatus.FAILED
                    ? "Article generation failed - retry generation"
                    : "Article generation in progress"
                  : "Fix issue"
              }
            >
              <Button
                icon={ComposeIcon}
                onClick={(e) => {
                  e.stopPropagation();
                  if (autoWriteStatus.shouldBlockNavigation) {
                    e.preventDefault();
                  }
                }}
                variant="tertiary"
                url={`/optimize-seo/articles/${article_id}`}
                disabled={autoWriteStatus.shouldBlockNavigation}
              />
            </TooltipWrapper>

            {/* Admin link button for successful auto-write jobs - Secondary action */}
            {autoWriteStatus.isAiGenerated && autoWriteStatus.status === BlogAutoWriteJobStatus.COMPLETED && (
              <TooltipWrapper content="Edit in Shopify Admin">
                <Button
                  icon={BlogIcon}
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    handleEditArticle(article_id, { external: true });
                  }}
                  variant="tertiary"
                />
              </TooltipWrapper>
            )}
          </InlineStack>
        </Box>
      </IndexTable.Cell>

      {/* Blog Auto-Write Progress Modal */}
      {autoWriteStatus.isAiGenerated && (
        <BlogAutoWriteModal
          isOpen={progressModalOpen}
          onClose={() => setProgressModalOpen(false)}
          initialJobId={autoWriteStatus.jobId}
        />
      )}
    </>
  );
};

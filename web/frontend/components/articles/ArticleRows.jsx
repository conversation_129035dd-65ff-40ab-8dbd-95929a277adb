import useIsRunningBackupRestore from "@/lib/hooks/useIsRunningBackupRestore.jsx";
import { IndexTable } from "@shopify/polaris";
import AiOptimizationStatus from "storeseo-enums/aiOptimization";
import ArticleRowItem from "./ArticleRowItem.jsx";

const ArticleRows = ({ articles, isFetching = false, selectedResources }) => {
  const { isRunning: isRunningBackupOrRestore } = useIsRunningBackupRestore();
  return (
    <>
      {articles?.map(({ id, ...article }, index) => {
        const item = { ...article };
        const isDisableRow = [
          AiOptimizationStatus.PENDING,
          AiOptimizationStatus.DISPATCHED,
          AiOptimizationStatus.SAVING,
          AiOptimizationStatus.PROCESSING,
        ].includes(article.ai_optimization_status);

        return (
          <IndexTable.Row
            id={id}
            key={index}
            position={index}
            selected={selectedResources?.includes(id)}
            disabled={isDisableRow || isRunningBackupOrRestore}
          >
            <ArticleRowItem
              item={item}
              isFetching={isFetching}
            />
          </IndexTable.Row>
        );
      })}
    </>
  );
};

export default ArticleRows;

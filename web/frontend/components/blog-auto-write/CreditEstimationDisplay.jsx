import {
  BlockStack,
  Text,
  Card,
  InlineStack,
  Badge,
  Button,
  Divider,
  ProgressBar,
  Icon,
} from "@shopify/polaris";
import { CreditCardIcon, CheckIcon, AlertTriangleIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";

/**
 * Credit Estimation Display Component
 * 
 * Shows detailed credit cost breakdown and allows user to confirm
 * or go back to modify their blog generation request.
 * 
 * @param {Object} props
 * @param {Object} props.formData - Original form data
 * @param {Object} props.creditEstimate - Credit estimation response
 * @param {boolean} props.isLoading - Loading state
 * @param {Function} props.onConfirm - Confirm generation handler
 * @param {Function} props.onBack - Go back handler
 * @param {Function} props.onCancel - Cancel handler
 * @param {boolean} props.isCreatingJob - Job creation loading state
 */
const CreditEstimationDisplay = ({
  formData,
  creditEstimate,
  isLoading,
  onConfirm,
  onBack,
  onCancel,
  isCreatingJob = false,
}) => {
  const { t } = useTranslation();

  if (isLoading) {
    return (
      <Card>
        <BlockStack gap="400" align="center">
          <Text as="h2" variant="headingMd">
            {t("Calculating Credits...")}
          </Text>
          <ProgressBar progress={75} size="small" />
          <Text as="p" tone="subdued">
            {t("Analyzing your request to estimate credit usage")}
          </Text>
        </BlockStack>
      </Card>
    );
  }

  if (!creditEstimate) {
    return (
      <Card>
        <BlockStack gap="400" align="center">
          <Icon source={AlertTriangleIcon} tone="critical" />
          <Text as="h2" variant="headingMd">
            {t("Unable to Calculate Credits")}
          </Text>
          <Text as="p" tone="subdued">
            {t("Please try again or contact support if the issue persists")}
          </Text>
          <InlineStack gap="200">
            <Button onClick={onBack}>{t("Back")}</Button>
            <Button onClick={onCancel}>{t("Cancel")}</Button>
          </InlineStack>
        </BlockStack>
      </Card>
    );
  }

  const {
    estimatedCredits,
    availableCredits,
    currentUsage,
    usageLimit,
    canProceed,
    breakdown,
    estimatedCost,
  } = creditEstimate;

  const usagePercentage = (currentUsage / usageLimit) * 100;
  const afterGenerationUsage = currentUsage + estimatedCredits;
  const afterGenerationPercentage = (afterGenerationUsage / usageLimit) * 100;

  return (
    <BlockStack gap="500">
      {/* Header */}
      <Card>
        <BlockStack gap="300">
          <InlineStack align="space-between">
            <Text as="h2" variant="headingMd">
              {t("Credit Estimation")}
            </Text>
            <Badge tone={canProceed ? "success" : "critical"}>
              {canProceed ? t("Sufficient Credits") : t("Insufficient Credits")}
            </Badge>
          </InlineStack>
          
          <Text as="p" tone="subdued">
            {t("Review the estimated credit cost for your blog generation")}
          </Text>
        </BlockStack>
      </Card>

      {/* Generation Summary */}
      <Card>
        <BlockStack gap="300">
          <Text as="h3" variant="headingSm">
            {t("Generation Summary")}
          </Text>
          
          <BlockStack gap="200">
            <InlineStack align="space-between">
              <Text as="span">{t("Topic:")}</Text>
              <Text as="span" fontWeight="semibold">{formData?.topic}</Text>
            </InlineStack>
            
            {formData?.keyword && (
              <InlineStack align="space-between">
                <Text as="span">{t("Keyword:")}</Text>
                <Text as="span" fontWeight="semibold">{formData.keyword}</Text>
              </InlineStack>
            )}
            
            <InlineStack align="space-between">
              <Text as="span">{t("Type:")}</Text>
              <Badge>{formData?.blogType}</Badge>
            </InlineStack>
            
            <InlineStack align="space-between">
              <Text as="span">{t("Length:")}</Text>
              <Badge>{formData?.wordCount}</Badge>
            </InlineStack>
            
            <InlineStack align="space-between">
              <Text as="span">{t("Tone:")}</Text>
              <Badge>{formData?.tone}</Badge>
            </InlineStack>
          </BlockStack>
        </BlockStack>
      </Card>

      {/* Credit Breakdown */}
      <Card>
        <BlockStack gap="400">
          <InlineStack align="space-between">
            <Text as="h3" variant="headingSm">
              {t("Credit Breakdown")}
            </Text>
            <InlineStack gap="200" align="center">
              <Icon source={CreditCardIcon} />
              <Text as="span" variant="headingSm" fontWeight="bold">
                {estimatedCredits} {t("credits")}
              </Text>
            </InlineStack>
          </InlineStack>

          {breakdown && (
            <BlockStack gap="300">
              {breakdown.content && (
                <InlineStack align="space-between">
                  <Text as="span">{t("Content Generation")}</Text>
                  <Text as="span" fontWeight="semibold">
                    {breakdown.content.credits} {t("credits")}
                  </Text>
                </InlineStack>
              )}
              
              {breakdown.images && (
                <InlineStack align="space-between">
                  <Text as="span">{t("Featured Image")}</Text>
                  <Text as="span" fontWeight="semibold">
                    {breakdown.images.credits} {t("credits")}
                  </Text>
                </InlineStack>
              )}
              
              <Divider />
              
              <InlineStack align="space-between">
                <Text as="span" fontWeight="semibold">{t("Total Estimated")}</Text>
                <Text as="span" fontWeight="bold">
                  {estimatedCredits} {t("credits")}
                </Text>
              </InlineStack>
              
              {estimatedCost && (
                <InlineStack align="space-between">
                  <Text as="span" tone="subdued">{t("Estimated Cost")}</Text>
                  <Text as="span" tone="subdued">
                    ${estimatedCost.toFixed(3)}
                  </Text>
                </InlineStack>
              )}
            </BlockStack>
          )}
        </BlockStack>
      </Card>

      {/* Credit Usage Status */}
      <Card>
        <BlockStack gap="400">
          <Text as="h3" variant="headingSm">
            {t("Credit Usage")}
          </Text>
          
          <BlockStack gap="300">
            <InlineStack align="space-between">
              <Text as="span">{t("Available Credits")}</Text>
              <Text as="span" fontWeight="semibold" tone={canProceed ? "success" : "critical"}>
                {availableCredits.toLocaleString()} / {usageLimit.toLocaleString()}
              </Text>
            </InlineStack>
            
            <ProgressBar 
              progress={usagePercentage} 
              size="small"
              tone={usagePercentage > 90 ? "critical" : usagePercentage > 70 ? "warning" : "success"}
            />
            
            <Text as="p" tone="subdued" textAlign="center">
              {t("Current usage: {{current}} credits ({{percentage}}%)", {
                current: currentUsage.toLocaleString(),
                percentage: Math.round(usagePercentage)
              })}
            </Text>
            
            {canProceed && (
              <BlockStack gap="200">
                <Text as="p" tone="subdued" textAlign="center">
                  {t("After generation: {{after}} credits ({{percentage}}%)", {
                    after: afterGenerationUsage.toLocaleString(),
                    percentage: Math.round(afterGenerationPercentage)
                  })}
                </Text>
                
                <ProgressBar 
                  progress={afterGenerationPercentage} 
                  size="small"
                  tone={afterGenerationPercentage > 90 ? "critical" : afterGenerationPercentage > 70 ? "warning" : "success"}
                />
              </BlockStack>
            )}
          </BlockStack>
        </BlockStack>
      </Card>

      {/* Insufficient Credits Warning */}
      {!canProceed && (
        <Card background="bg-surface-critical-subdued">
          <BlockStack gap="300">
            <InlineStack gap="200" align="start">
              <Icon source={AlertTriangleIcon} tone="critical" />
              <BlockStack gap="200">
                <Text as="h3" variant="headingSm" tone="critical">
                  {t("Insufficient Credits")}
                </Text>
                <Text as="p" tone="critical">
                  {t("You need {{needed}} more credits to generate this blog post. Consider upgrading your AI Optimizer addon or reducing the scope.", {
                    needed: estimatedCredits - availableCredits
                  })}
                </Text>
              </BlockStack>
            </InlineStack>
          </BlockStack>
        </Card>
      )}

      {/* Action Buttons */}
      <Card>
        <InlineStack align="space-between">
          <InlineStack gap="200">
            <Button onClick={onBack}>{t("Back")}</Button>
            <Button onClick={onCancel}>{t("Cancel")}</Button>
          </InlineStack>
          
          <Button
            variant="primary"
            onClick={onConfirm}
            disabled={!canProceed}
            loading={isCreatingJob}
            icon={canProceed ? CheckIcon : undefined}
          >
            {isCreatingJob 
              ? t("Creating...") 
              : canProceed 
                ? t("Generate Blog Post") 
                : t("Insufficient Credits")
            }
          </Button>
        </InlineStack>
      </Card>
    </BlockStack>
  );
};

export default CreditEstimationDisplay;

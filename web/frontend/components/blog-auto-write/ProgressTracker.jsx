import { <PERSON>ge, <PERSON><PERSON><PERSON>ck, <PERSON><PERSON>, Card, Icon, InlineStack, ProgressBar, Text } from "@shopify/polaris";
import { AlertTriangleIcon, CheckIcon, ClockIcon, EditIcon, SearchIcon, UploadIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";
import BlogAutoWriteJobStatus from "storeseo-enums/blogAutoWrite/jobStatus";

/**
 * Progress Tracker Component
 * 
 * Displays real-time progress of blog generation with detailed step tracking
 * and appropriate actions based on job status.
 * 
 * @param {Object} props
 * @param {Object} props.jobStatus - Current job status object
 * @param {number} props.progress - Progress percentage (0-100)
 * @param {boolean} props.isActive - Whether job is currently active
 * @param {boolean} props.isFailed - Whether job has failed
 * @param {Function} props.onCancel - Cancel job handler (when active)
 * @param {Function} props.onClose - Close modal handler (when complete)
 */
const ProgressTracker = ({
  jobStatus,
  progress = 0,
  isActive = false,
  isFailed = false,
  onCancel,
  onClose,
}) => {
  const { t } = useTranslation();

  // Progress steps with icons and descriptions
  const progressSteps = [
    {
      status: BlogAutoWriteJobStatus.PENDING,
      icon: ClockIcon,
      title: t("Initializing"),
      description: t("Preparing your blog generation request"),
    },
    {
      status: BlogAutoWriteJobStatus.GENERATING_OUTLINE,
      icon: EditIcon,
      title: t("Creating Outline"),
      description: t("Structuring your blog post content"),
    },
    {
      status: BlogAutoWriteJobStatus.GENERATING_CONTENT,
      icon: EditIcon,
      title: t("Writing Content"),
      description: t("Generating high-quality blog content"),
    },
    {
      status: BlogAutoWriteJobStatus.OPTIMIZING_SEO,
      icon: SearchIcon,
      title: t("SEO Optimization"),
      description: t("Optimizing for search engines"),
    },
    {
      status: BlogAutoWriteJobStatus.PUBLISHING_TO_SHOPIFY,
      icon: UploadIcon,
      title: t("Publishing"),
      description: t("Publishing to your Shopify store"),
    },
    {
      status: BlogAutoWriteJobStatus.COMPLETED,
      icon: CheckIcon,
      title: t("Complete"),
      description: t("Blog post successfully generated"),
    },
  ];

  const currentStatus = jobStatus?.status || BlogAutoWriteJobStatus.PENDING;
  const currentStepIndex = progressSteps.findIndex(step => step.status === currentStatus);
  const currentStep = progressSteps[currentStepIndex] || progressSteps[0];

  // Get progress bar tone based on status
  const getProgressTone = () => {
    if (isFailed) return "critical";
    if (progress === 100) return "success";
    if (progress > 0) return "primary";
    return "subdued";
  };

  // Get status badge
  const getStatusBadge = () => {
    if (isFailed) {
      return <Badge tone="critical">{t("Failed")}</Badge>;
    }
    if (progress === 100) {
      return <Badge tone="success">{t("Completed")}</Badge>;
    }
    if (isActive) {
      return <Badge tone="info">{t("In Progress")}</Badge>;
    }
    return <Badge tone="subdued">{t("Pending")}</Badge>;
  };

  // Render failed state
  if (isFailed) {
    return (
      <BlockStack gap="500">
        <Card>
          <BlockStack gap="400" align="center">
            <Icon source={AlertTriangleIcon} tone="critical" />
            <Text as="h2" variant="headingMd" tone="critical">
              {t("Generation Failed")}
            </Text>
            <Text as="p" tone="subdued" textAlign="center">
              {jobStatus?.errorMessage || t("An error occurred during blog generation. Please try again.")}
            </Text>
            <Button variant="primary" onClick={onClose}>
              {t("Close")}
            </Button>
          </BlockStack>
        </Card>
      </BlockStack>
    );
  }

  // Render completed state
  if (progress === 100 && !isActive) {
    return (
      <BlockStack gap="500">
        <Card>
          <BlockStack gap="400" align="center">
            <Icon source={CheckIcon} tone="success" />
            <Text as="h2" variant="headingMd" tone="success">
              {t("Blog Generated Successfully!")}
            </Text>
            <Text as="p" tone="subdued" textAlign="center">
              {t("Your blog post has been created and published to your Shopify store. You can now view and edit it in your articles list.")}
            </Text>
            <Button variant="primary" onClick={onClose}>
              {t("Close")}
            </Button>
          </BlockStack>
        </Card>
      </BlockStack>
    );
  }

  // Render active progress state
  return (
    <BlockStack gap="500">
      {/* Progress Header */}
      <Card>
        <BlockStack gap="400">
          <InlineStack align="space-between">
            <Text as="h2" variant="headingMd">
              {t("Generating Your Blog Post")}
            </Text>
            {getStatusBadge()}
          </InlineStack>
          
          <BlockStack gap="300">
            <InlineStack align="space-between">
              <Text as="span" variant="bodySm" tone="subdued">
                {t("Progress")}
              </Text>
              <Text as="span" variant="bodySm" fontWeight="semibold">
                {Math.round(progress)}%
              </Text>
            </InlineStack>
            
            <ProgressBar 
              progress={progress} 
              size="medium"
              tone={getProgressTone()}
            />
          </BlockStack>
        </BlockStack>
      </Card>

      {/* Current Step */}
      <Card>
        <BlockStack gap="400">
          <InlineStack gap="300" align="start">
            <Icon source={currentStep.icon} tone={isActive ? "primary" : "subdued"} />
            <BlockStack gap="200">
              <Text as="h3" variant="headingSm">
                {currentStep.title}
              </Text>
              <Text as="p" tone="subdued">
                {currentStep.description}
              </Text>
            </BlockStack>
          </InlineStack>
        </BlockStack>
      </Card>

      {/* Step Progress */}
      <Card>
        <BlockStack gap="400">
          <Text as="h3" variant="headingSm">
            {t("Generation Steps")}
          </Text>
          
          <BlockStack gap="300">
            {progressSteps.slice(0, -1).map((step, index) => {
              const isCompleted = index < currentStepIndex;
              const isCurrent = index === currentStepIndex;
              
              return (
                <InlineStack key={step.status} gap="300" align="start">
                  <div style={{ marginTop: '2px' }}>
                    <Icon 
                      source={step.icon} 
                      tone={isCompleted ? "success" : isCurrent ? "primary" : "subdued"} 
                    />
                  </div>
                  <BlockStack gap="100">
                    <InlineStack gap="200" align="start">
                      <Text 
                        as="span" 
                        variant="bodySm" 
                        fontWeight={isCurrent ? "semibold" : "regular"}
                        tone={isCompleted ? "success" : isCurrent ? "primary" : "subdued"}
                      >
                        {step.title}
                      </Text>
                      {isCompleted && (
                        <Icon source={CheckIcon} tone="success" />
                      )}
                    </InlineStack>
                    <Text 
                      as="span" 
                      variant="bodyXs" 
                      tone="subdued"
                    >
                      {step.description}
                    </Text>
                  </BlockStack>
                </InlineStack>
              );
            })}
          </BlockStack>
        </BlockStack>
      </Card>

      {/* Estimated Time */}
      {isActive && jobStatus?.estimatedTimeRemaining && (
        <Card background="bg-surface-secondary">
          <InlineStack gap="200" align="center">
            <Icon source={ClockIcon} tone="subdued" />
            <Text as="p" variant="bodySm" tone="subdued">
              {t("Estimated time remaining: {{time}} seconds", {
                time: jobStatus.estimatedTimeRemaining
              })}
            </Text>
          </InlineStack>
        </Card>
      )}

      {/* Action Buttons */}
      <Card>
        <InlineStack align="end">
          {isActive && onCancel && (
            <Button tone="critical" onClick={onCancel}>
              {t("Cancel Generation")}
            </Button>
          )}
          {!isActive && onClose && (
            <Button variant="primary" onClick={onClose}>
              {t("Close")}
            </Button>
          )}
        </InlineStack>
      </Card>
    </BlockStack>
  );
};

export default ProgressTracker;

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { QueryClient, QueryClientProvider } from 'react-query';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../../i18n/i18n';
import BlogSelector from '../BlogSelector';
import * as hooks from '../../../hooks';

// Mock the hooks module
vi.mock('../../../hooks', () => ({
  useSyncedBlogs: vi.fn(),
}));

// Mock data
const mockBlogs = [
  {
    id: 1,
    title: 'News Blog',
    handle: 'news',
    is_synced: true,
    article_count: 5,
    template_suffix: 'custom',
    tags: ['news', 'updates'],
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-12-01T00:00:00Z',
  },
  {
    id: 2,
    title: 'Product Updates',
    handle: 'product-updates',
    is_synced: true,
    article_count: 0,
    template_suffix: null,
    tags: [],
    created_at: '2023-06-01T00:00:00Z',
    updated_at: '2023-06-01T00:00:00Z',
  },
  {
    id: 3,
    title: 'Unsynced Blog',
    handle: 'unsynced',
    is_synced: false,
    article_count: 2,
    template_suffix: null,
    tags: ['test'],
    created_at: '2023-03-01T00:00:00Z',
    updated_at: '2023-03-01T00:00:00Z',
  },
];

// Test wrapper component
const TestWrapper = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <I18nextProvider i18n={i18n}>
        {children}
      </I18nextProvider>
    </QueryClientProvider>
  );
};

describe('BlogSelector', () => {
  const mockOnBlogChange = vi.fn();
  const mockOnRetry = vi.fn();
  const mockRefetch = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Loading State', () => {
    it('should display loading state when fetching blogs', () => {
      hooks.useSyncedBlogs.mockReturnValue({
        data: [],
        isLoading: true,
        error: null,
        refetch: mockRefetch,
        isError: false,
      });

      render(
        <TestWrapper>
          <BlogSelector onBlogChange={mockOnBlogChange} />
        </TestWrapper>
      );

      expect(screen.getByText('Loading blogs...')).toBeInTheDocument();
      expect(screen.getByRole('status')).toBeInTheDocument(); // Spinner
    });
  });

  describe('Error State', () => {
    it('should display error state when fetch fails', () => {
      hooks.useSyncedBlogs.mockReturnValue({
        data: [],
        isLoading: false,
        error: { message: 'Network error' },
        refetch: mockRefetch,
        isError: true,
      });

      render(
        <TestWrapper>
          <BlogSelector onBlogChange={mockOnBlogChange} onRetry={mockOnRetry} />
        </TestWrapper>
      );

      expect(screen.getByText('Failed to load blogs')).toBeInTheDocument();
      expect(screen.getByText('Network error')).toBeInTheDocument();
      expect(screen.getByText('Retry')).toBeInTheDocument();
    });

    it('should call refetch and onRetry when retry button is clicked', async () => {
      hooks.useSyncedBlogs.mockReturnValue({
        data: [],
        isLoading: false,
        error: { message: 'Network error' },
        refetch: mockRefetch,
        isError: true,
      });

      render(
        <TestWrapper>
          <BlogSelector onBlogChange={mockOnBlogChange} onRetry={mockOnRetry} />
        </TestWrapper>
      );

      const retryButton = screen.getByText('Retry');
      fireEvent.click(retryButton);

      expect(mockRefetch).toHaveBeenCalledTimes(1);
      expect(mockOnRetry).toHaveBeenCalledTimes(1);
    });

    it('should not show retry button when showRetry is false', () => {
      hooks.useSyncedBlogs.mockReturnValue({
        data: [],
        isLoading: false,
        error: { message: 'Network error' },
        refetch: mockRefetch,
        isError: true,
      });

      render(
        <TestWrapper>
          <BlogSelector 
            onBlogChange={mockOnBlogChange} 
            showRetry={false}
          />
        </TestWrapper>
      );

      expect(screen.queryByText('Retry')).not.toBeInTheDocument();
    });
  });

  describe('Empty State', () => {
    it('should display empty state when no blogs are available', () => {
      hooks.useSyncedBlogs.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
        refetch: mockRefetch,
        isError: false,
      });

      render(
        <TestWrapper>
          <BlogSelector onBlogChange={mockOnBlogChange} />
        </TestWrapper>
      );

      expect(screen.getByText('No blogs available')).toBeInTheDocument();
      expect(screen.getByText(/No synced blogs found/)).toBeInTheDocument();
    });
  });

  describe('Success State', () => {
    beforeEach(() => {
      hooks.useSyncedBlogs.mockReturnValue({
        data: mockBlogs.filter(blog => blog.is_synced),
        isLoading: false,
        error: null,
        refetch: mockRefetch,
        isError: false,
      });
    });

    it('should render blog selector with options', () => {
      render(
        <TestWrapper>
          <BlogSelector onBlogChange={mockOnBlogChange} />
        </TestWrapper>
      );

      const select = screen.getByLabelText('Target Blog');
      expect(select).toBeInTheDocument();
      expect(select).not.toBeDisabled();
    });

    it('should call onBlogChange when a blog is selected', async () => {
      render(
        <TestWrapper>
          <BlogSelector onBlogChange={mockOnBlogChange} />
        </TestWrapper>
      );

      const select = screen.getByLabelText('Target Blog');
      fireEvent.change(select, { target: { value: '1' } });

      expect(mockOnBlogChange).toHaveBeenCalledWith('1');
    });

    it('should display blog metadata when a blog is selected', () => {
      render(
        <TestWrapper>
          <BlogSelector 
            selectedBlog="1" 
            onBlogChange={mockOnBlogChange} 
          />
        </TestWrapper>
      );

      expect(screen.getByText('Blog Information')).toBeInTheDocument();
      expect(screen.getByText('Synced')).toBeInTheDocument();
      expect(screen.getByText('5 articles')).toBeInTheDocument();
      expect(screen.getByText('Template: custom')).toBeInTheDocument();
      expect(screen.getByText('/news')).toBeInTheDocument();
      expect(screen.getByText('news, updates')).toBeInTheDocument();
    });

    it('should show helpful message for empty blogs', () => {
      render(
        <TestWrapper>
          <BlogSelector 
            selectedBlog="2" 
            onBlogChange={mockOnBlogChange} 
          />
        </TestWrapper>
      );

      expect(screen.getByText(/This blog doesn't have any articles yet/)).toBeInTheDocument();
    });

    it('should disable selector when disabled prop is true', () => {
      render(
        <TestWrapper>
          <BlogSelector 
            onBlogChange={mockOnBlogChange} 
            disabled={true}
          />
        </TestWrapper>
      );

      const select = screen.getByLabelText('Target Blog');
      expect(select).toBeDisabled();
    });

    it('should use custom label and helpText', () => {
      render(
        <TestWrapper>
          <BlogSelector 
            onBlogChange={mockOnBlogChange}
            label="Choose Blog"
            helpText="Select your target blog"
          />
        </TestWrapper>
      );

      expect(screen.getByLabelText('Choose Blog')).toBeInTheDocument();
      expect(screen.getByText('Select your target blog')).toBeInTheDocument();
    });

    it('should use custom placeholder', () => {
      render(
        <TestWrapper>
          <BlogSelector 
            onBlogChange={mockOnBlogChange}
            placeholder="Pick a blog"
          />
        </TestWrapper>
      );

      const select = screen.getByLabelText('Target Blog');
      const firstOption = select.querySelector('option[value=""]');
      expect(firstOption).toHaveTextContent('Pick a blog');
    });
  });

  describe('Date Formatting', () => {
    beforeEach(() => {
      hooks.useSyncedBlogs.mockReturnValue({
        data: [mockBlogs[0]], // Blog with different created/updated dates
        isLoading: false,
        error: null,
        refetch: mockRefetch,
        isError: false,
      });
    });

    it('should format dates correctly', () => {
      render(
        <TestWrapper>
          <BlogSelector 
            selectedBlog="1" 
            onBlogChange={mockOnBlogChange} 
          />
        </TestWrapper>
      );

      expect(screen.getByText(/Created.*Jan 1, 2023/)).toBeInTheDocument();
      expect(screen.getByText(/Last updated.*Dec 1, 2023/)).toBeInTheDocument();
    });

    it('should not show last updated if same as created date', () => {
      hooks.useSyncedBlogs.mockReturnValue({
        data: [mockBlogs[1]], // Blog with same created/updated dates
        isLoading: false,
        error: null,
        refetch: mockRefetch,
        isError: false,
      });

      render(
        <TestWrapper>
          <BlogSelector 
            selectedBlog="2" 
            onBlogChange={mockOnBlogChange} 
          />
        </TestWrapper>
      );

      expect(screen.getByText(/Created.*Jun 1, 2023/)).toBeInTheDocument();
      expect(screen.queryByText(/Last updated/)).not.toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      hooks.useSyncedBlogs.mockReturnValue({
        data: mockBlogs.filter(blog => blog.is_synced),
        isLoading: false,
        error: null,
        refetch: mockRefetch,
        isError: false,
      });
    });

    it('should have proper ARIA labels', () => {
      render(
        <TestWrapper>
          <BlogSelector onBlogChange={mockOnBlogChange} />
        </TestWrapper>
      );

      const select = screen.getByLabelText('Target Blog');
      expect(select).toHaveAttribute('aria-label', 'Target Blog');
    });

    it('should have proper heading hierarchy', () => {
      render(
        <TestWrapper>
          <BlogSelector 
            selectedBlog="1" 
            onBlogChange={mockOnBlogChange} 
          />
        </TestWrapper>
      );

      const heading = screen.getByRole('heading', { level: 4 });
      expect(heading).toHaveTextContent('Blog Information');
    });
  });
});

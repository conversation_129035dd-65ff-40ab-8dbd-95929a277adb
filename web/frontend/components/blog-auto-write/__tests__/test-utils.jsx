import { render } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from 'react-query';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../../i18n/i18n';

/**
 * Test wrapper component that provides necessary context providers
 */
export const TestWrapper = ({ children, queryClientOptions = {} }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { 
        retry: false,
        cacheTime: 0,
        staleTime: 0,
        ...queryClientOptions.queries 
      },
      mutations: { 
        retry: false,
        ...queryClientOptions.mutations 
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <I18nextProvider i18n={i18n}>
        {children}
      </I18nextProvider>
    </QueryClientProvider>
  );
};

/**
 * Custom render function that includes providers
 */
export const renderWithProviders = (ui, options = {}) => {
  const { queryClientOptions, ...renderOptions } = options;
  
  return render(ui, {
    wrapper: ({ children }) => (
      <TestWrapper queryClientOptions={queryClientOptions}>
        {children}
      </TestWrapper>
    ),
    ...renderOptions,
  });
};

/**
 * Mock blog data for testing
 */
export const mockBlogs = [
  {
    id: 1,
    title: 'News Blog',
    handle: 'news',
    is_synced: true,
    article_count: 5,
    template_suffix: 'custom',
    tags: ['news', 'updates'],
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-12-01T00:00:00Z',
  },
  {
    id: 2,
    title: 'Product Updates',
    handle: 'product-updates',
    is_synced: true,
    article_count: 0,
    template_suffix: null,
    tags: [],
    created_at: '2023-06-01T00:00:00Z',
    updated_at: '2023-06-01T00:00:00Z',
  },
  {
    id: 3,
    title: 'Unsynced Blog',
    handle: 'unsynced',
    is_synced: false,
    article_count: 2,
    template_suffix: null,
    tags: ['test'],
    created_at: '2023-03-01T00:00:00Z',
    updated_at: '2023-03-01T00:00:00Z',
  },
];

/**
 * Mock hook return values for different states
 */
export const mockHookStates = {
  loading: {
    data: [],
    isLoading: true,
    error: null,
    refetch: vi.fn(),
    isError: false,
  },
  error: {
    data: [],
    isLoading: false,
    error: { message: 'Network error' },
    refetch: vi.fn(),
    isError: true,
  },
  empty: {
    data: [],
    isLoading: false,
    error: null,
    refetch: vi.fn(),
    isError: false,
  },
  success: {
    data: mockBlogs.filter(blog => blog.is_synced),
    isLoading: false,
    error: null,
    refetch: vi.fn(),
    isError: false,
  },
};

/**
 * Helper to create mock hook with custom data
 */
export const createMockHook = (overrides = {}) => ({
  ...mockHookStates.success,
  ...overrides,
});

// Re-export testing library utilities
export * from '@testing-library/react';
export { default as userEvent } from '@testing-library/user-event';

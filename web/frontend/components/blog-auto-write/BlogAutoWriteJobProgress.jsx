import { <PERSON><PERSON>ta<PERSON>, <PERSON>, Card, EmptyState, Icon, InlineStack, <PERSON><PERSON><PERSON>, Spinner, Text } from "@shopify/polaris";
import { BlankIcon, CheckIcon, ClockIcon, MinusIcon } from "@shopify/polaris-icons";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import BlogAutoWriteJobStatus from "storeseo-enums/blogAutoWrite/jobStatus";
import BlogAutoWriteJobSteps from "storeseo-enums/blogAutoWrite/jobSteps";
import { useBlogApi } from "../../hooks/apiHooks/useBlogApi";
import { useAppQuery } from "../../hooks/useAppQuery";
import { formatDate } from "../../utility/helpers";

// Helper function to get proper status display text using enum labels
const getStatusDisplayText = (status) => {
  if (!status) return "";
  // Use the official enum labels for proper capitalization and user-friendly text
  return BlogAutoWriteJobStatus.labels[status] || status;
};

// Examples of status display:
// "completed" -> "Completed"
// "pending" -> "Pending"
// "generating_content" -> "Generating Content"
// "creating_draft" -> "Creating Draft in Shopify"

// Blog generation statuses that indicate auto-write is in progress
const IN_PROGRESS_STATUSES = [
  BlogAutoWriteJobStatus.PENDING,
  BlogAutoWriteJobStatus.GENERATING_CONTENT,
  BlogAutoWriteJobStatus.CREATING_DRAFT,
  BlogAutoWriteJobStatus.LINKING_ARTICLE,
  BlogAutoWriteJobStatus.ANALYZING_SEO,
  BlogAutoWriteJobStatus.GENERATING_IMAGE,
  BlogAutoWriteJobStatus.UPLOADING_IMAGE,
  BlogAutoWriteJobStatus.UPDATING_ARTICLE,
  BlogAutoWriteJobStatus.FINALIZING_SEO,
  BlogAutoWriteJobStatus.PUBLISHING,
];

/**
 * Reusable Blog Auto-Write Job Progress Component
 *
 * Displays real-time progress of blog generation with detailed step tracking.
 * Can be used in modals, popovers, or any other UI context.
 *
 * @param {Object} props
 * @param {string} props.jobId - Job ID to track
 * @param {number} props.refreshInterval - Refresh interval in milliseconds (default: 15000)
 * @param {boolean} props.autoRefresh - Whether to auto-refresh (default: true)
 * @param {Function} props.onJobComplete - Callback when job completes
 * @param {Function} props.onJobFailed - Callback when job fails
 * @param {Function} props.onProgressUpdate - Callback when job progress updates
 * @param {boolean} props.showHeader - Whether to show the header (default: true)
 * @param {boolean} props.showCreditInfo - Whether to show credit information (default: true)
 * @param {string} props.size - Size variant: 'compact' | 'normal' (default: 'normal')
 */
const BlogAutoWriteJobProgress = ({
  jobId,
  refreshInterval = 3500, // 15 seconds as requested
  autoRefresh = true,
  onJobComplete,
  onJobFailed,
  onProgressUpdate,
  showHeader = true,
  showCreditInfo = true,
  size = "normal",
}) => {
  const { t } = useTranslation();
  const { getBlogAutoWriteJob } = useBlogApi();

  // Helper function to check if job is in progress
  const isJobInProgress = (status) => {
    return IN_PROGRESS_STATUSES.includes(status);
  };

  // Use useAppQuery for data fetching with auto-refresh
  const {
    data: queryData,
    isLoading: loading,
    error: queryError,
    refetch,
  } = useAppQuery({
    queryKey: ["blog-auto-write-job-progress", jobId],
    queryFn: () => getBlogAutoWriteJob(jobId),
    reactQueryOptions: {
      enabled: !!jobId,
      refetchInterval: (data) => {
        // If auto-refresh is disabled, don't poll
        if (!autoRefresh) return false;

        // Stop polling if job is completed or failed
        const status = data?.data?.status;
        if (status && !isJobInProgress(status)) {
          return false;
        }

        // Continue polling with the specified interval for in-progress jobs
        return refreshInterval;
      },

      onSuccess: (data) => {
        if (!data?.success) return;

        const currentStatus = data.data?.status;
        const currentProgress = data.data?.progress || 0;

        // Call progress update callback
        onProgressUpdate?.(currentProgress, data.data);

        // Call callbacks on status changes
        if (currentStatus === "completed") {
          onJobComplete?.(data.data);
        } else if (currentStatus === "failed") {
          onJobFailed?.(data.data);
        }
      },
      onError: (err) => {
        console.error("Error fetching job progress:", err);
      },
      // Keep previous data while refetching to prevent UI flicker
      keepPreviousData: true,
      // Retry failed requests
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
  });

  const jobProgress = queryData?.data || { progress: 0, status: "" };
  const error = queryError
    ? `Failed to fetch progress: ${queryError.message || queryError}`
    : queryData && !queryData.success
    ? queryData.message || queryData.error || "Failed to fetch progress"
    : null;

  // Call onProgressUpdate when job progress changes
  useEffect(() => {
    if (jobProgress && onProgressUpdate) {
      onProgressUpdate(jobProgress.progress || 0, jobProgress);
    }
  }, [jobProgress, onProgressUpdate]);

  const renderStepIcon = (step) => {
    if (step.result?.skipped) {
      return (
        <Icon
          source={MinusIcon}
          tone="base"
        />
      );
    }
    if (step.completed) {
      return (
        <Icon
          source={CheckIcon}
          tone="success"
        />
      );
    }

    // Check if this is the current step (started but not completed)
    const isCurrentStep = step.startedAt && !step.completed;
    if (isCurrentStep) {
      return <Spinner size="small" />;
    }

    return (
      <Icon
        source={ClockIcon}
        tone="subdued"
      />
    );
  };

  const getStepTone = (step) => {
    if (step.result?.skipped) return "subdued";
    if (step.completed) return "success";
    if (step.startedAt && !step.completed) return "primary";
    return "subdued";
  };

  const getStepFontWeight = (step) => {
    if (step.completed) return "medium";
    if (step.startedAt && !step.completed) return "medium";
    return "regular";
  };

  if (loading) {
    return (
      <Card>
        <InlineStack
          align="center"
          gap="200"
        >
          <Spinner size="small" />
          <Text>{t("Loading progress...")}</Text>
        </InlineStack>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <EmptyState
          heading={t("Unable to load progress")}
          text={error}
          action={{
            content: t("Retry"),
            onAction: refetch,
          }}
        />
      </Card>
    );
  }

  if (!jobProgress) {
    return (
      <Card>
        <EmptyState
          heading={t("No generation data")}
          text={t("Unable to find job progress information.")}
        />
      </Card>
    );
  }

  const { status, progress, steps, estimatedCreditUsage, creditUsage, createdAt } = jobProgress;
  const isCompact = size === "compact";

  return (
    <BlockStack gap={isCompact ? "300" : "400"}>
      {/* Header */}
      {showHeader && (
        <BlockStack gap="200">
          {/* <Text variant={isCompact ? "headingSm" : "headingMd"}>{t("Blog Auto-Write Progress")}</Text> */}
          <Text
            variant="bodySm"
            tone="subdued"
          >
            {t("Started")} {formatDate(createdAt)}
          </Text>
        </BlockStack>
      )}

      {/* Progress Bar */}
      <Card>
        <BlockStack gap="200">
          <InlineStack align="space-between">
            <Text
              variant="bodyMd"
              fontWeight="medium"
            >
              {progress}% {t("Complete")}
            </Text>
            <Text
              variant="bodySm"
              tone="subdued"
            >
              {getStatusDisplayText(status)}
            </Text>
          </InlineStack>
          <ProgressBar
            progress={progress}
            size="small"
            tone={progress === 100 ? "success" : "primary"}
          />
        </BlockStack>
      </Card>

      {/* Steps */}
      {steps && steps.length > 0 && (
        <Card>
          <BlockStack gap={isCompact ? "200" : "300"}>
            <Text
              variant="bodyMd"
              fontWeight="medium"
            >
              {t("Generation Steps")}
            </Text>
            <BlockStack gap={isCompact ? "150" : "200"}>
              {steps.map((step, index) => (
                <InlineStack
                  key={step.step || index}
                  align="start"
                  gap="300"
                >
                  <Box minWidth="20px">{renderStepIcon(step)}</Box>
                  <Box style={{ flex: 1 }}>
                    <BlockStack gap="100">
                      <Text
                        variant={isCompact ? "bodyXs" : "bodySm"}
                        tone={getStepTone(step)}
                        fontWeight={getStepFontWeight(step)}
                      >
                        {BlogAutoWriteJobSteps.labels[step.step] || step.step}
                      </Text>
                      {step.completedAt && (
                        <Text
                          variant="caption"
                          tone="subdued"
                        >
                          {step.result?.skipped ? t("Skipped") : `${t("Completed")} ${formatDate(step.completedAt)}`}
                        </Text>
                      )}
                      {step.error && (
                        <Text
                          variant="caption"
                          tone="critical"
                        >
                          {t("Error")}: {step.error}
                        </Text>
                      )}
                    </BlockStack>
                  </Box>
                </InlineStack>
              ))}
            </BlockStack>
          </BlockStack>
        </Card>
      )}

      {/* Credit Information */}
      {showCreditInfo && (estimatedCreditUsage || creditUsage) && (
        <Card>
          <BlockStack gap="200">
            <Text
              variant="bodyMd"
              fontWeight="medium"
            >
              {t("Credit Usage")}
            </Text>
            <InlineStack align="space-between">
              <Text
                variant="bodySm"
                tone="subdued"
              >
                {t("Estimated")}
              </Text>
              <Text variant="bodySm">
                {estimatedCreditUsage?.totalCredits || 0} {t("credits")}
              </Text>
            </InlineStack>
            {creditUsage?.totalCredits > 0 && (
              <InlineStack align="space-between">
                <Text
                  variant="bodySm"
                  tone="subdued"
                >
                  {t("Actual")}
                </Text>
                <Text
                  variant="bodySm"
                  fontWeight="medium"
                >
                  {creditUsage.totalCredits} {t("credits")}
                </Text>
              </InlineStack>
            )}
          </BlockStack>
        </Card>
      )}
    </BlockStack>
  );
};

export default BlogAutoWriteJobProgress;

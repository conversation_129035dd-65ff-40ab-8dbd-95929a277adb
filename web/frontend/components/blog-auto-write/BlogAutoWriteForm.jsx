import { <PERSON>Stack, Box, InlineStack, RadioButton, Select, Text, TextField } from "@shopify/polaris";
import { Controller } from "react-hook-form";
import { useTranslation } from "react-i18next";

// Import enums
import BlogType from "storeseo-enums/blogAutoWrite/blogType";
import ToneType from "storeseo-enums/blogAutoWrite/toneType";
import WordCountRange from "storeseo-enums/blogAutoWrite/wordCountRange";

// Import components
import { useFeatureFlags } from "../../hooks/useFeatureFlags";
import BlogSelector from "./BlogSelector";

/**
 * Blog Auto-Write Form Component
 *
 * Pure UI component that receives all state and handlers as props.
 * No internal state or logic - everything is lifted to the parent.
 *
 * @param {Object} props
 * @param {Object} props.control - React Hook Form control object
 * @param {Object} props.errors - Form validation errors
 * @param {Object} props.watchedValues - Current form values for dynamic UI
 * @param {Error} props.error - Error from form submission
 */
const BlogAutoWriteForm = ({ control, errors, watchedValues, error = null }) => {
  const { t } = useTranslation();

  // Get feature flags from API
  const featureFlags = useFeatureFlags();
  const isImageGenerationEnabled = featureFlags.blogs.autoWrite.isImageGenerationEnabled();
  const flagsLoading = featureFlags.loading;

  // Log feature flag status for debugging (development only)
  if (process.env.NODE_ENV === "development") {
    // console.log("[BlogAutoWriteForm] Image generation enabled:", isImageGenerationEnabled);
  }

  // Note: Form validation automatically handles hidden fields
  // When image generation is disabled, the validation schema will ignore
  // generateFeaturedImage and featuredImageDescription fields

  // Convert enums to select options
  const blogTypeOptions = Object.values(BlogType)
    .filter((value) => BlogType.labels[value])
    .map((value) => ({
      label: BlogType.labels[value],
      value,
      helpText: BlogType.descriptions[value],
    }));

  const toneOptions = Object.values(ToneType)
    .filter((value) => ToneType.labels[value])
    .map((value) => ({
      label: ToneType.labels[value],
      value,
      helpText: ToneType.descriptions[value],
    }));

  const wordCountOptions = Object.values(WordCountRange)
    .filter((value) => WordCountRange.labels[value])
    .map((value) => ({
      label: WordCountRange.labels[value],
      value,
      helpText: `${WordCountRange.descriptions[value]} • ${WordCountRange.estimatedReadingTime[value]} read`,
    }));

  return (
    <Box padding="500">
      <BlockStack gap="500">
        <form id="blog-auto-write-form">
          <BlockStack gap="400">
            {/* Blog Topic */}
            <Controller
              name="topic"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label={t("Blog Topic *")}
                  placeholder={t("e.g., Best SEO practices for e-commerce stores")}
                  error={errors.topic?.message}
                  helpText={t("The main subject or theme of your blog post")}
                  autoComplete="off"
                />
              )}
            />

            {/* Keyword */}
            <Controller
              name="keyword"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label={t("Target Keyword")}
                  placeholder={t("e.g., SEO optimization")}
                  error={errors.keyword?.message}
                  helpText={t(
                    "Optional: Primary keyword to focus on for SEO and content generation. Keyword will be auto-generated if not provided by you."
                  )}
                  autoComplete="off"
                />
              )}
            />

            {/* Target Blog Selection */}
            <Controller
              name="targetBlog"
              control={control}
              render={({ field }) => (
                <div>
                  <BlogSelector
                    selectedBlog={field.value}
                    onBlogChange={field.onChange}
                    disabled={false}
                    label={t("Target Blog *")}
                    helpText={t("Choose which blog to publish the article to")}
                    placeholder={t("Select a blog")}
                  />
                  {errors.targetBlog && (
                    <Text
                      as="p"
                      tone="critical"
                      variant="bodyMd"
                    >
                      {t(errors.targetBlog.message)}
                    </Text>
                  )}
                </div>
              )}
            />

            {/* Blog Type and Word Count - Side by side */}
            <InlineStack
              gap="400"
              align="start"
            >
              <div style={{ flex: 1 }}>
                <Controller
                  name="blogType"
                  control={control}
                  render={({ field }) => (
                    <Select
                      {...field}
                      label={t("Blog Type *")}
                      options={blogTypeOptions}
                      error={errors.blogType?.message}
                      helpText={t("Choose the format that best fits your content")}
                    />
                  )}
                />
              </div>

              <div style={{ flex: 1 }}>
                <Controller
                  name="wordCount"
                  control={control}
                  render={({ field }) => (
                    <Select
                      {...field}
                      label={t("Word Count *")}
                      options={wordCountOptions}
                      error={errors.wordCount?.message}
                    />
                  )}
                />
              </div>
            </InlineStack>

            {/* Tone Type - Single row */}
            <Controller
              name="tone"
              control={control}
              render={({ field }) => (
                <Select
                  {...field}
                  label={t("Tone Type *")}
                  options={toneOptions}
                  error={errors.tone?.message}
                />
              )}
            />

            {/* Custom Instructions */}
            <Controller
              name="customInstructions"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label={t("Custom Instructions")}
                  placeholder={t("e.g., Include actionable tips and real examples")}
                  multiline={2}
                  error={errors.customInstructions?.message}
                  helpText={t("Optional: Additional instructions for content generation")}
                  autoComplete="off"
                />
              )}
            />

            {/* Featured Image Generation - Controlled by FEATURE_BLOG_AUTO_WRITE_IMAGE_GENERATION */}
            {/* Only show image generation fields when feature flag is enabled and not loading */}
            {!flagsLoading && isImageGenerationEnabled && (
              <>
                <Controller
                  name="generateFeaturedImage"
                  control={control}
                  render={({ field }) => (
                    <BlockStack gap="200">
                      <Text
                        as="label"
                        variant="bodyMd"
                      >
                        {t("Featured Image *")}
                      </Text>
                      <InlineStack gap="400">
                        <RadioButton
                          label={t("Yes")}
                          checked={field.value === true}
                          id="generateFeaturedImage-yes"
                          name="generateFeaturedImage"
                          onChange={() => field.onChange(true)}
                        />
                        <RadioButton
                          label={t("No")}
                          checked={field.value === false}
                          id="generateFeaturedImage-no"
                          name="generateFeaturedImage"
                          onChange={() => field.onChange(false)}
                        />
                      </InlineStack>
                      {errors.generateFeaturedImage?.message && (
                        <Text
                          as="p"
                          tone="critical"
                        >
                          {errors.generateFeaturedImage.message}
                        </Text>
                      )}
                    </BlockStack>
                  )}
                />

                {/* Featured Image Description - Conditional */}
                {watchedValues.generateFeaturedImage && (
                  <Controller
                    name="featuredImageDescription"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={t("Featured Image Description")}
                        placeholder={t("e.g., Modern e-commerce dashboard showing SEO metrics")}
                        error={errors.featuredImageDescription?.message}
                        helpText={t("Optional: Description for AI-generated featured image")}
                        autoComplete="off"
                      />
                    )}
                  />
                )}
              </>
            )}

            {/* Error Display */}
            {error && (
              <Text
                as="p"
                tone="critical"
              >
                {t("Error: {{message}}", { message: error.message })}
              </Text>
            )}
          </BlockStack>
        </form>
      </BlockStack>
    </Box>
  );
};

export default BlogAutoWriteForm;

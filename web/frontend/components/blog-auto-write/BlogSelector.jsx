//@ts-check
import { <PERSON><PERSON><PERSON>s, emitter } from "@/config";
import { Banner, BlockStack, Box, Icon, InlineStack, Link, Select, Spinner, Text } from "@shopify/polaris";
import { AlertTriangleIcon } from "@shopify/polaris-icons";
import PropTypes from "prop-types";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useSyncedBlogs } from "../../hooks";

/**
 * @typedef {Object} Blog
 * @property {number} id - Blog database ID
 * @property {string} title - Blog title
 * @property {string} handle - Blog URL handle
 * @property {boolean} is_synced - Whether blog is synced
 * @property {number} [article_count] - Number of articles in blog
 * @property {string} [template_suffix] - Theme template suffix
 * @property {string[]} [tags] - Blog tags
 * @property {string} [created_at] - Creation timestamp
 * @property {string} [updated_at] - Last update timestamp
 */

/**
 * @typedef {Object} BlogSelectorProps
 * @property {string} [selectedBlog] - Currently selected blog ID
 * @property {(value: string) => void} onBlogChange - Callback when blog selection changes
 * @property {boolean} [disabled] - Whether the selector is disabled
 * @property {string} [label] - Label for the select input
 * @property {string} [helpText] - Help text to display below the select
 * @property {string} [placeholder] - Placeholder text when no blog is selected
 * @property {boolean} [showRetry] - Whether to show retry button on error
 * @property {() => void} [onRetry] - Callback for retry action
 */

/**
 * BlogSelector Component
 *
 * A reusable dropdown component for selecting blogs with metadata display.
 * Shows blog title, sync status, and article count information.
 *
 * @param {BlogSelectorProps} props
 * @returns {React.ReactElement}
 */
const BlogSelector = ({
  selectedBlog = "",
  onBlogChange = () => {},
  disabled = false,
  label = "Target Blog",
  helpText = "Choose which blog to publish the article to",
  placeholder = "Select a blog",
  showRetry = true,
  onRetry = () => {},
}) => {
  const { t } = useTranslation();

  // Fetch blogs using the custom hook
  const {
    data: blogs = [],
    isLoading,
    error,
    refetch,
    isError,
  } = useSyncedBlogs({
    retry: 2,
    retryDelay: 1000,
    onError: (err) => {
      console.error("BlogSelector: Error fetching blogs:", err);
    },
    onSuccess: () => {
      // Blogs fetched successfully
    },
  });

  // Transform blogs into select options
  const options = [
    {
      label: t(placeholder),
      value: "",
      disabled: true,
    },
    ...blogs.map((blog) => {
      const isDisabled = !blog.is_synced;
      // Add sync status to label for better UX
      const label = isDisabled ? `${blog.title} (${t("Not Synced")})` : blog.title;

      return {
        label,
        value: blog.id.toString(),
        disabled: isDisabled,
      };
    }),
  ];

  // // Find selected blog for metadata display
  // const selectedBlogData = blogs.find((blog) => blog.id.toString() === selectedBlog);

  // // Debug: Track selectedBlog prop changes
  // useEffect(() => {
  //   console.log("BlogSelector selectedBlog prop changed to:", selectedBlog);
  // }, [selectedBlog]);

  // Smart default blog selection logic
  useEffect(() => {
    // Ensure blogs is an array
    const blogsArray = Array.isArray(blogs) ? blogs : [];

    // Skip if loading, error, already selected, or no blogs
    if (isLoading || isError || selectedBlog || blogsArray.length === 0) {
      return;
    }

    const syncedBlogs = blogsArray.filter((blog) => blog?.is_synced === true);
    if (syncedBlogs.length === 0) {
      return; // No synced blogs available
    }

    let defaultBlog = null;

    // Strategy 1: Single blog auto-selection
    if (syncedBlogs.length === 1) {
      defaultBlog = syncedBlogs[0];
    } else {
      // Strategy 2: Blog with most articles (helps users pick their main blog)
      defaultBlog = syncedBlogs.reduce((prev, current) => {
        const prevCount = prev.article_count || 0;
        const currentCount = current.article_count || 0;
        return currentCount > prevCount ? current : prev;
      });
    }

    if (defaultBlog && defaultBlog.id) {
      // Add a small delay to ensure form reset has completed first
      setTimeout(() => {
        onBlogChange(defaultBlog.id.toString());
      }, 50);
    }
  }, [isLoading, isError, blogs, selectedBlog, onBlogChange]);

  // Validate selected blog
  const getValidationError = () => {
    if (!selectedBlog) return null;

    const selectedBlogData = blogs.find((blog) => blog.id.toString() === selectedBlog);

    if (!selectedBlogData) {
      return t("Selected blog is no longer available. Please choose another blog.");
    }

    if (!selectedBlogData.is_synced) {
      return t("Selected blog is not synced. Please choose a synced blog or sync your blogs first.");
    }

    return null;
  };

  const validationError = getValidationError();

  // Blog metadata functions removed for now

  // Handle loading state
  if (isLoading) {
    return (
      <Box>
        <Text
          variant="bodyMd"
          fontWeight="medium"
          as="p"
        >
          {t(label)}
        </Text>
        <Box paddingBlockStart="200">
          <InlineStack
            gap="200"
            align="center"
          >
            <Spinner size="small" />
            <Text
              variant="bodyMd"
              tone="subdued"
              as="span"
            >
              {t("Loading blogs...")}
            </Text>
          </InlineStack>
        </Box>
      </Box>
    );
  }

  // Handle error state
  if (isError && error) {
    return (
      <Box>
        <Text
          variant="bodyMd"
          fontWeight="medium"
          as="p"
        >
          {t(label)}
        </Text>
        <Box paddingBlockStart="200">
          <Banner
            title={t("Failed to load blogs")}
            tone="critical"
            action={
              showRetry
                ? {
                    content: t("Retry"),
                    onAction: () => {
                      refetch();
                      onRetry();
                    },
                  }
                : undefined
            }
          >
            <Text
              variant="bodyMd"
              as="p"
            >
              {error && typeof error === "object" && "message" in error
                ? String(error.message)
                : t("There was an error loading your blogs. Please try again.")}
            </Text>
          </Banner>
        </Box>
      </Box>
    );
  }

  return (
    <BlockStack gap="300">
      <Select
        label={t(label)}
        options={options}
        value={selectedBlog}
        onChange={onBlogChange}
        disabled={disabled || blogs.length === 0}
        error={validationError}
      />

      {/* Blog metadata display is hidden for now */}

      {/* Show message when no blogs are available */}
      {!isLoading && blogs.length === 0 && !isError && (
        <InlineStack
          gap="100"
          wrap={false}
        >
          <Box as="span">
            <Icon source={AlertTriangleIcon} />
          </Box>
          <Text
            variant="bodyMd"
            as="p"
          >
            {t("No synced blogs found.")}
          </Text>
          <Link onClick={() => emitter.emit(browserEvents.TRIGGER_BLOG_SYNC)}>{t("Please sync your blogs")}</Link>
        </InlineStack>
      )}

      {/* Show helpful message when there are unsynced blogs */}
      {!isLoading && blogs.length > 0 && blogs.every((blog) => !blog.is_synced) && (
        <InlineStack
          gap="100"
          wrap={false}
        >
          <Box as="span">
            <Icon source={AlertTriangleIcon} />
          </Box>
          <Text
            variant="bodyMd"
            as="p"
          >
            {t("Some blogs are not synced. Please wait till current sync operation is finished (if any) or ")}
          </Text>
          <Link onClick={() => emitter.emit(browserEvents.TRIGGER_BLOG_SYNC)}>{t("sync blogs again")}</Link>
        </InlineStack>
      )}
    </BlockStack>
  );
};

// PropTypes for runtime validation
BlogSelector.propTypes = {
  selectedBlog: PropTypes.string,
  onBlogChange: PropTypes.func,
  disabled: PropTypes.bool,
  label: PropTypes.string,
  helpText: PropTypes.string,
  placeholder: PropTypes.string,
  showRetry: PropTypes.bool,
  onRetry: PropTypes.func,
};

export default BlogSelector;

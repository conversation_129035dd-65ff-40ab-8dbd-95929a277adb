import BlogAutoWriteForm from "@/components/blog-auto-write/BlogAutoWriteForm";
import BlogAutoWriteJobProgress from "@/components/blog-auto-write/BlogAutoWriteJobProgress";
import { useAppQuery } from "@/hooks";
import { useBlogApi } from "@/hooks/apiHooks/useBlogApi";
import { useAppBridgeRedirect } from "@/hooks/useAppBridgeRedirect";
import { useBlogAutoWrite } from "@/hooks/useBlogAutoWrite";
import { useBlogAutoWriteForm } from "@/hooks/useBlogAutoWriteForm";
import Modal from "@/modules/components/Modal";
import { Banner, Box, Card, InlineStack, Spinner, Text } from "@shopify/polaris";
import { BlogIcon } from "@shopify/polaris-icons";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";
import BlogAutoWriteJobStatus from "storeseo-enums/blogAutoWrite/jobStatus";
import queryKeys from "../../utility/queryKeys";

// Status constants for regeneration eligibility
const REGENERATION_ELIGIBLE_STATUSES = [
  BlogAutoWriteJobStatus.COMPLETED,
  BlogAutoWriteJobStatus.FAILED,
  BlogAutoWriteJobStatus.CANCELLED,
];

/**
 * Blog Auto-Write Modal Component
 *
 * Enhanced modal that shows job creation form and then displays real-time
 * progress tracking after job creation. Allows users to monitor generation
 * progress without closing the modal.
 *
 * Can also open directly in progress view for existing jobs (from article rows).
 * Supports regeneration mode with form prepopulation.
 *
 * @param {Object} props
 * @param {boolean} props.isOpen - Modal open state
 * @param {Function} props.onClose - Close handler
 * @param {string} props.initialView - Initial view: 'form' | 'progress' (default: auto-detect)
 * @param {string} props.initialJobId - Job ID to track when opening in progress view
 * @param {string} props.regenerationJobId - Job ID for regeneration mode (opens form with original data)
 * @param {boolean} props.isExplicitRegeneration - Whether modal was opened explicitly for regeneration (default: false)
 */
const BlogAutoWriteModal = ({
  isOpen,
  onClose,
  initialView = null,
  initialJobId = null,
  regenerationJobId = null,
  isExplicitRegeneration = false,
}) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const blogApi = useBlogApi();
  const { handleEditArticle } = useAppBridgeRedirect();

  // Detect regeneration mode
  const isRegenerationMode = !!regenerationJobId;

  // Auto-detect the correct initial view
  const getInitialView = useCallback(() => {
    // If initialView is explicitly provided, use it
    if (initialView) return initialView;

    // If initialJobId is provided, open in progress view
    if (initialJobId) return "progress";

    // Default to form view (for Generate button clicks or regeneration)
    return "form";
  }, [initialView, initialJobId]);

  // Modal state management
  const [currentView, setCurrentView] = useState(getInitialView()); // 'form' | 'progress'
  const [createdJobId, setCreatedJobId] = useState(initialJobId);

  // Fetch original job data for regeneration
  const {
    data: originalJobData,
    isLoading: isLoadingJobData,
    error: jobDataError,
  } = useAppQuery({
    queryKey: ["blog-auto-write-job", regenerationJobId],
    queryFn: () => blogApi.getBlogAutoWriteJob(regenerationJobId),
    reactQueryOptions: {
      enabled: isRegenerationMode && isOpen,
      keepPreviousData: true,
    },
  });

  // Business logic from main hook
  const { createJob, isCreatingJob, createJobError, regenerateJob, isRegeneratingJob } = useBlogAutoWrite();

  // UI-only form state from form hook
  const {
    control,
    formState: { errors },
    watch,
    reset,
    handleSubmit,
  } = useBlogAutoWriteForm();

  // Reset modal state when props change (for reuse between form and progress views)
  useEffect(() => {
    if (isOpen) {
      setCurrentView(getInitialView());
      setCreatedJobId(initialJobId);

      // Reset form to default values when opening modal for new job
      if (!initialJobId && !isRegenerationMode) {
        reset();
      }
    }
  }, [isOpen, getInitialView, initialJobId, reset, isRegenerationMode]);

  // Prepopulate form with original job data for regeneration
  useEffect(() => {
    if (isRegenerationMode && originalJobData?.data.inputData && isOpen) {
      // Prepopulate form with original job input data
      reset(originalJobData.data.inputData);
      // Set initial job status and data for button logic
      if (originalJobData.data.status) {
        setCurrentJobStatus(originalJobData.data.status);
        setCurrentJobData(originalJobData.data);
      }
    }
  }, [isRegenerationMode, originalJobData, reset, isOpen]);

  // Watch form values for dynamic UI
  const watchedValues = watch();

  // State for manual banner dismissal and error message
  const [isBannerDismissed, setIsBannerDismissed] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);

  // Local loading state for re-generate button to handle UI transitions
  const [isRegeneratingLocal, setIsRegeneratingLocal] = useState(false);

  // Reset form and banner state when modal opens
  useEffect(() => {
    if (isOpen) {
      reset();
      setIsBannerDismissed(false); // Reset banner dismissal state
      setErrorMessage(null); // Reset error message
      setIsRegeneratingLocal(false); // Reset local loading state
    }
  }, [isOpen, reset]);

  // Reset local loading state when job ID changes (successful regeneration)
  useEffect(() => {
    if (createdJobId) {
      setIsRegeneratingLocal(false);
    }
  }, [createdJobId]);

  // Get field-specific guidance for validation errors - matches backend validation exactly
  const getFieldGuidance = useCallback((field, code) => {
    const guidanceMap = {
      topic: {
        FIELD_REQUIRED: "Enter a blog topic",
        FIELD_TOO_SHORT: "Use at least 2 characters", // Updated to match backend
        FIELD_TOO_LONG: "Keep under 200 characters",
        INVALID_FORMAT: "Use only letters, numbers, and basic punctuation",
      },
      keyword: {
        FIELD_TOO_SHORT: "Use at least 2 characters",
        FIELD_TOO_LONG: "Keep under 50 characters", // Updated to match backend
        INVALID_FORMAT: "Use simple keywords without special characters",
      },
      blogType: {
        INVALID_ENUM_VALUE: "Choose from: Product-based, Guide, Listicle, Informational, News, Seasonal",
      },
      wordCount: {
        INVALID_ENUM_VALUE: "Choose from: Up to 500, 500-800, 800-1200, 1200+",
      },
      tone: {
        INVALID_ENUM_VALUE: "Choose from: Formal, Informal, Conversational, Persuasive, Friendly, Encouraging",
      },
      customInstructions: {
        FIELD_TOO_LONG: "Keep under 500 characters",
        INVALID_FORMAT: "Avoid special characters and HTML",
      },
      generateFeaturedImage: {
        FIELD_REQUIRED: "Choose whether to generate a featured image",
      },
      featuredImageDescription: {
        FIELD_TOO_LONG: "Keep under 500 characters",
        INVALID_FORMAT: "Describe the image clearly and simply",
      },
    };

    return guidanceMap[field]?.[code] || null;
  }, []);

  // Format error message for better user experience based on actual API response
  const getErrorMessage = useCallback(
    async (error) => {
      if (!error) return null;

      console.log("=== ERROR DEBUG ===");
      console.log("Full error object:", error);
      console.log("Error constructor:", error.constructor.name);

      let errorData = null;

      // Handle Response object (fetch API)
      if (error instanceof Response) {
        try {
          console.log("Error is Response object, extracting JSON...");
          errorData = await error.json();
          console.log("Extracted error data:", errorData);
        } catch (jsonError) {
          console.log("Failed to parse JSON from Response:", jsonError);
          return `HTTP ${error.status}: ${error.statusText}`;
        }
      }
      // Handle axios-style error
      else if (error.response?.data) {
        errorData = error.response.data;
      }
      // Handle direct error data
      else if (error.data) {
        errorData = error.data;
      }
      // Handle error with errors array directly
      else if (error.errors) {
        errorData = error;
      }

      if (errorData) {
        console.log("Using errorData:", errorData);

        // Handle validation errors array with field-specific guidance
        if (errorData.errors && Array.isArray(errorData.errors)) {
          const validationErrors = errorData.errors
            .map((err) => {
              const fieldGuidance = getFieldGuidance(err.field, err.code);
              return `${err.field}: ${err.message}${fieldGuidance ? ` (${fieldGuidance})` : ""}`;
            })
            .join(", ");
          console.log("Found validation errors:", validationErrors);
          return `Please fix the following: ${validationErrors}`;
        }

        // Handle single error with code and enhanced credit info
        if (errorData.error) {
          const { code, message, details, ...otherDetails } = errorData.error;

          switch (code) {
            case "JOB_CREATION_FAILED":
              // Show job creation error details as-is (e.g., blog sync message)
              return details || message || "Failed to create blog generation job";

            case "INSUFFICIENT_CREDITS":
              // Enhanced credit error with creditInfo if available
              const creditInfo = errorData.creditInfo;
              let creditMessage = `Insufficient credits: Required ${otherDetails?.required || "N/A"}, Available ${
                otherDetails?.available || "N/A"
              }.`;

              if (creditInfo) {
                creditMessage += ` Current usage: ${creditInfo.currentUsage}/${creditInfo.usageLimit}.`;
                if (creditInfo.upgradeUrl) {
                  creditMessage += ` Please upgrade your plan to continue.`;
                } else {
                  creditMessage += ` Please upgrade your AI Optimizer addon.`;
                }
              } else {
                creditMessage += ` Please upgrade your plan or wait for your credits to reset.`;
              }

              return creditMessage;

            case "RATE_LIMIT_EXCEEDED":
              return `Rate limit exceeded: ${message}. Please wait a moment and try again.`;

            case "OPENAI_API_ERROR":
              return `AI service temporarily unavailable: ${message}. Please try again in a few minutes.`;

            case "SHOPIFY_API_ERROR":
              return `Shopify connection error: ${message}. Please check your store connection and try again.`;

            default:
              return `Error (${code}): ${message}`;
          }
        }

        // Simple message format (fallback)
        if (errorData.message) {
          return errorData.message;
        }
      }

      // Enhanced fallback handling with better user guidance
      let fallbackMessage = "";

      // Try to extract meaningful info from error
      if (error instanceof Response) {
        fallbackMessage = `Server error (${error.status}): ${error.statusText}. `;

        // Provide specific guidance based on status code
        switch (error.status) {
          case 400:
            fallbackMessage += "Please check your form inputs and try again.";
            break;
          case 401:
            fallbackMessage += "Please refresh the page and try again.";
            break;
          case 403:
            fallbackMessage += "You may not have permission for this action.";
            break;
          case 429:
            fallbackMessage += "Too many requests. Please wait a moment and try again.";
            break;
          case 500:
          case 502:
          case 503:
            fallbackMessage += "Our servers are experiencing issues. Please try again in a few minutes.";
            break;
          default:
            fallbackMessage += "Please try again or contact support if the problem persists.";
        }
      } else if (error.message) {
        // Handle other error types with message
        const message = error.message;
        if (message.includes("network") || message.includes("fetch")) {
          fallbackMessage = "Network connection error. Please check your internet connection and try again.";
        } else if (message.includes("timeout")) {
          fallbackMessage = "Request timed out. Please try again.";
        } else {
          fallbackMessage = `${message}. Please try again or contact support if the problem persists.`;
        }
      } else {
        // Last resort fallback
        fallbackMessage =
          "An unexpected error occurred while creating the blog generation job. Please try again or contact support if the problem persists.";
      }

      console.log("Using enhanced fallback message:", fallbackMessage);
      return fallbackMessage;
    },
    [t, getFieldGuidance]
  );

  // Reset error state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setErrorMessage(null);
      setIsBannerDismissed(false);
    }
  }, [isOpen]);

  // Process error when it changes (after getErrorMessage is defined)
  useEffect(() => {
    if (createJobError) {
      getErrorMessage(createJobError).then(setErrorMessage);
    } else {
      setErrorMessage(null);
    }
  }, [createJobError, getErrorMessage]);

  // Handle generate/regenerate blog button - validate form and create/regenerate job
  const handleGenerateBlog = useCallback(
    async (e) => {
      if (e) {
        e.stopPropagation(); // Prevent event bubbling
        e.preventDefault();
      }
      try {
        setIsBannerDismissed(false); // Auto-close banner on new submission

        // Use handleSubmit to validate and get form data
        await handleSubmit(async (formData) => {
          // Clean and prepare data (all blogs saved as drafts for current release)
          const cleanData = {
            topic: formData.topic.trim(),
            keyword: formData.keyword?.trim() || undefined,
            targetBlog: formData.targetBlog,
            blogType: formData.blogType,
            wordCount: formData.wordCount,
            tone: formData.tone,
            customInstructions: formData.customInstructions?.trim() || undefined,
            generateFeaturedImage: formData.generateFeaturedImage,
            featuredImageDescription: formData.featuredImageDescription?.trim() || undefined,
            autoPublish: false, // Always false for current release - UI will be added later
          };

          let result;
          if (isRegenerationMode) {
            // Use regeneration API with input overrides
            result = await regenerateJob(regenerationJobId, cleanData);
          } else {
            // Use creation API (existing logic)
            result = await createJob(cleanData);
          }

          // Switch to progress view instead of closing modal
          if (result?.data?.jobId) {
            setCreatedJobId(result.data.jobId);
            setCurrentView("progress");
          }
        })();
      } catch (error) {
        console.error(isRegenerationMode ? "Regenerate blog failed:" : "Generate blog failed:", error);
        // Keep modal open on error so user can retry
      }
    },
    [handleSubmit, createJob]
  );

  // Handle job completion
  const handleJobComplete = useCallback(
    (jobData) => {
      console.log("Job completed:", jobData);
      // Invalidate articles list to show updated job status
      queryClient.invalidateQueries([queryKeys.ARTICLES_LIST]);
      // Could show success message or auto-close after delay
    },
    [queryClient]
  );

  // Handle job failure
  const handleJobFailed = useCallback(
    (jobData) => {
      console.error("Job failed:", jobData);
      // Invalidate articles list to show updated job status
      queryClient.invalidateQueries([queryKeys.ARTICLES_LIST]);
      // Could show error message or allow retry
    },
    [queryClient]
  );

  // Handle job progress updates
  const handleJobProgressUpdate = useCallback((_progress, jobData) => {
    // Update current job status and data for button logic
    if (jobData?.status) {
      setCurrentJobStatus(jobData.status);
      setCurrentJobData(jobData);
    }
  }, []);

  // Handle close button - close modal
  const handleClose = useCallback(
    (e) => {
      if (e) {
        e.stopPropagation(); // Prevent event bubbling
        e.preventDefault();
      }
      // Invalidate articles list to refresh the table with new/updated articles
      queryClient.invalidateQueries([queryKeys.ARTICLES_LIST]);
      onClose();
    },
    [onClose, queryClient]
  );

  // Render form view
  const renderFormView = () => (
    <>
      {/* Error Banner with proper modal padding */}
      {createJobError && !isBannerDismissed && errorMessage && (
        <Box
          paddingInlineStart="500"
          paddingInlineEnd="500"
          paddingBlockStart="400"
        >
          <Banner
            title={isRegenerationMode ? t("Blog Regeneration Failed") : t("Blog Generation Failed")}
            tone="critical"
            onDismiss={() => {
              setIsBannerDismissed(true); // Allow manual dismissal
            }}
          >
            <p>{errorMessage}</p>
          </Banner>
        </Box>
      )}

      {/* Job data loading state for regeneration */}
      {isRegenerationMode && isLoadingJobData && (
        <Box padding="500">
          <Card>
            <InlineStack
              align="center"
              gap="200"
            >
              <Spinner size="small" />
              <Text>{t("Loading job input data...")}</Text>
            </InlineStack>
          </Card>
        </Box>
      )}

      {/* Job data error state for regeneration */}
      {isRegenerationMode && jobDataError && (
        <Box
          paddingInlineStart="500"
          paddingInlineEnd="500"
          paddingBlockStart="400"
        >
          <Banner
            title={t("Failed to load blog data")}
            tone="critical"
          >
            <p>{jobDataError.message || t("Unable to load original blog data. Please try again.")}</p>
          </Banner>
        </Box>
      )}

      {/* Show form when not loading or when not in regeneration mode */}
      {(!isRegenerationMode || (!isLoadingJobData && !jobDataError)) && (
        <BlogAutoWriteForm
          control={control}
          errors={errors}
          watchedValues={watchedValues}
        />
      )}
    </>
  );

  // Get modal title based on current view and mode
  const getModalTitle = () => {
    if (currentView === "progress") {
      return t("AI Blog Generation Progress");
    }
    return isRegenerationMode ? t("Regenerate Blog") : t("Write with AI");
  };

  // Handle re-generate button click
  const handleRegenerate = async (e) => {
    e.stopPropagation(); // Prevent event bubbling to table row
    e.preventDefault();

    try {
      setIsRegeneratingLocal(true); // Set local loading state
      const result = await regenerateJob(createdJobId, {}); // No overrides for now

      // Update the job ID to the new regenerated job
      if (result?.data?.jobId) {
        setCreatedJobId(result.data.jobId);
      }
    } catch (error) {
      console.error("Failed to regenerate job:", error);
      // Error handling is done by the mutation hook
    } finally {
      setIsRegeneratingLocal(false); // Clear local loading state
    }
  };

  // Helper function to determine if re-generate button should be shown
  const shouldShowRegenerateButton = (jobStatus) => {
    // Only show if explicitly opened for regeneration
    if (!isExplicitRegeneration) {
      return false;
    }

    // Only show for completed, failed, or cancelled jobs
    return REGENERATION_ELIGIBLE_STATUSES.includes(jobStatus);
  };

  // Helper function to determine if re-generate button should be enabled
  const isRegenerateButtonEnabled = (jobStatus) => {
    return shouldShowRegenerateButton(jobStatus) && !isRegeneratingJob && !isRegeneratingLocal;
  };

  // State to track current job status and data for button logic
  const [currentJobStatus, setCurrentJobStatus] = useState(null);
  const [currentJobData, setCurrentJobData] = useState(null);

  // Helper function to determine if Edit Blog button should be shown
  const shouldShowEditBlogButton = (jobStatus, jobData) => {
    // Only show for completed jobs that have a linked article
    return jobStatus === BlogAutoWriteJobStatus.COMPLETED && jobData?.article?.article_id;
  };

  // Handle Edit Blog button click
  const handleEditBlog = useCallback(() => {
    if (currentJobData?.article?.article_id) {
      handleEditArticle(currentJobData.article.article_id, { external: true });
    }
  }, [currentJobData, handleEditArticle]);

  // Get modal buttons based on current view
  const getModalButtons = () => {
    if (currentView === "progress") {
      const buttons = [];

      // Add Edit Blog button for completed jobs with linked articles
      if (shouldShowEditBlogButton(currentJobStatus, currentJobData)) {
        buttons.push(
          <button
            key="edit-blog"
            variant="primary"
            onClick={handleEditBlog}
            icon={BlogIcon}
          >
            {t("Edit Blog Post")}
          </button>
        );
      }
      // Add re-generate button if conditions are met (only for explicit regeneration)
      else if (shouldShowRegenerateButton(currentJobStatus)) {
        buttons.push(
          <button
            key="regenerate"
            variant="primary"
            onClick={handleRegenerate}
            loading={isRegeneratingJob || isRegeneratingLocal}
            disabled={!isRegenerateButtonEnabled(currentJobStatus)}
          >
            {t("Re-generate")}
          </button>
        );
      }

      buttons.push(
        <button
          key="close"
          onClick={handleClose}
        >
          {t("Close")}
        </button>
      );

      return buttons;
    }

    const isSubmitting = isRegenerationMode ? isRegeneratingJob : isCreatingJob;
    const isFormDisabled = isSubmitting || (isRegenerationMode && isLoadingJobData);

    return [
      <button
        key="generate"
        variant="primary"
        onClick={handleGenerateBlog}
        loading={isSubmitting ? "" : undefined}
        disabled={isFormDisabled}
      >
        {isRegenerationMode ? t("Re-generate") : t("Generate Blog")}
      </button>,
      <button
        key="close"
        onClick={handleClose}
        disabled={isSubmitting}
      >
        {t("Close")}
      </button>,
    ];
  };

  return (
    <Modal
      type="app-bridge"
      open={isOpen}
      setOpen={onClose}
      variant="medium"
      id="blog-auto-write-modal"
      onClose={handleClose}
    >
      <Modal.Section>
        {currentView === "form" && renderFormView()}
        {currentView === "progress" && (createdJobId || initialJobId) && (
          <Box padding="500">
            <BlogAutoWriteJobProgress
              key={createdJobId || initialJobId} // Use key to ensure component identity is stable
              jobId={createdJobId || initialJobId}
              refreshInterval={3500}
              onJobComplete={handleJobComplete}
              onJobFailed={handleJobFailed}
              onProgressUpdate={handleJobProgressUpdate}
              showHeader={true}
              showCreditInfo={true}
              size="normal"
              autoRefresh={true} // Always auto-refresh when visible
            />
          </Box>
        )}

        <Modal.TitleBar title={getModalTitle()}>{getModalButtons()}</Modal.TitleBar>
      </Modal.Section>
    </Modal>
  );
};

export default BlogAutoWriteModal;

import { Page, Text, View, Document, StyleSheet, Svg, Defs, ClipPath, Rect, G, Path, Font } from "@react-pdf/renderer";
import { formatDate, generateTransactionID, getPaymentStatus, PAYMENT_STATUS } from "../../utility/payment";
import { STORE_SEO_ADDRESS, STORE_SEO_SUPPORT_EMAIL, STORE_SEO_TITLE } from "../../config";

const DATE_FORMAT_STR = "MMMM DD, YYYY";

const PAGE_CONTENT_WIDTH = "495.28px";
const PAGE_MARGIN_HORIZONTAL = "50px";

const COLOR_LIGHT_GREY = "#f4f7fc";
const COLOR_LIGHT_BLACK = "#555";
const COLOR_BLACK = "#2a282e";
const COLOR_GREEN = "#00cc76";
const COLOR_RED = "#FE504F";
const COLOR_WHITE = "#FFFFFF";

Font.register({
  family: "Lato",
  fonts: [
    { src: "/fonts/Lato-Light.ttf", fontWeight: "light" },
    { src: "/fonts/Lato-Regular.ttf", fontWeight: "normal" },
    { src: "/fonts/Lato-Bold.ttf", fontWeight: "bold" },
  ],
});

const styles = StyleSheet.create({
  page: {
    marginHorizontal: PAGE_MARGIN_HORIZONTAL,
    fontFamily: "Lato",
    color: COLOR_BLACK,
    letterSpacing: "0.35px",
  },
  header: {
    width: PAGE_CONTENT_WIDTH,
    height: "auto",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    paddingTop: "35px",
    paddingBottom: "15px",
  },
  headerInfoBox: { textAlign: "right", width: "65%" },
  invoiceNumber: {
    fontSize: "15px",
    fontWeight: "bold",
    marginBottom: "3.85px",
  },
  invoiceDate: {
    fontWeight: "normal",
    fontSize: "10.85px",
    color: COLOR_LIGHT_BLACK,
  },
  horizontalLine: {
    width: "595.28px",
    marginLeft: "-50px",
    borderBottom: "3.5px",
    borderBottomColor: COLOR_LIGHT_GREY,
  },
  mailingAddressBox: {
    width: PAGE_CONTENT_WIDTH,
    display: "flex",
    flexDirection: "row",
    paddingTop: "30px",
    paddingBottom: "40px",
  },
  mailingAddressDetails: {
    width: "43.5%",
    maxWidth: "auto",
    marginRight: "5%",
    borderLeft: "3px",
    borderLeftColor: COLOR_LIGHT_GREY,
    paddingLeft: "4.1%",
  },
  mailingTextSecondary: {
    fontSize: "10.75px",
    color: COLOR_LIGHT_BLACK,
    lineHeight: "1.55",
  },
  mailingTextPrimary: {
    fontSize: "16px",
    fontWeight: "bold",
    color: COLOR_BLACK,
    marginTop: "1px",
    marginBottom: "5px",
  },
  colorGreen: { color: COLOR_GREEN },
  invoiceItemsHeader: {
    fontSize: "15px",
    fontWeight: "bold",
    color: COLOR_BLACK,
    marginBottom: "18px",
  },
  invoiceItemRow: {
    width: PAGE_CONTENT_WIDTH,
    paddingVertical: "12px",
    paddingHorizontal: "13px",
    border: "1.35px",
    borderColor: COLOR_LIGHT_GREY,
    display: "flex",
    flexDirection: "row",
    fontSize: "11px",
  },
  textRight: {
    textAlign: "right",
  },
  width30: { width: "30%" },
  width45: { width: "45%" },
  width25: { width: "25%" },
  colorBlack: { color: COLOR_BLACK },
  colorWhite: { color: COLOR_WHITE },
  fontBold: { fontWeight: "bold" },
  bgLightGrey: { backgroundColor: COLOR_LIGHT_GREY },
  bgGreen: { backgroundColor: COLOR_GREEN },
  billingSection: {
    width: PAGE_CONTENT_WIDTH,
    display: "flex",
    flexDirection: "row",
    marginTop: "25px",
  },
  additionalBillingInfo: {
    width: "60%",
  },
  billBreakdown: {
    width: "40%",
  },
  billRow: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: "12px",
    paddingHorizontal: "13px",
    fontSize: "11px",
    marginBottom: "10.75px",
    borderRadius: "2px",
  },
  paymentStatus: {
    width: "55px",
    textAlign: "center",
    textTransform: "uppercase",
    fontWeight: "bold",
    fontSize: "9.5px",
    border: "2px",
    borderRadius: "5px",
    padding: "0px",
    paddingTop: "3px",
    paddingLeft: "3px",
    marginTop: "8.75px",
  },
  labelSuccess: {
    borderColor: COLOR_GREEN,
    color: COLOR_GREEN,
  },
  labelDanger: {
    borderColor: COLOR_RED,
    color: COLOR_RED,
  },
});

const Invoice = ({ transactionDetails, shop }) => {
  const { id, name, price, discount, subtotal: totalPrice, created_at: transactionDate } = transactionDetails;

  const planName = name
    ?.split(" ")
    .map((s) => s[0].toUpperCase() + s.substring(1))
    .join(" ");

  const invoiceDate = formatDate(transactionDate, DATE_FORMAT_STR);
  const transactionID = generateTransactionID(id);
  const paymentStatus = getPaymentStatus(transactionDetails);

  const renderStoreSeoLogo = () => (
    <Svg
      style={{ width: "110px", height: "32px" }}
      viewBox="0 0 148 43"
    >
      <Defs>
        <ClipPath id="clipPath">
          <Rect
            width="148"
            height="43"
          />
        </ClipPath>
      </Defs>
      <G clipPath="url(#clipPath)">
        <Path
          d="M56.2866 31.4098C54.4591 31.4098 52.8927 30.9335 52.0572 30.5366V28.0227C53.2582 28.5784 54.5896 28.896 55.6861 28.896C57.5658 28.896 58.4273 28.1021 58.4273 26.3821C58.4273 24.5827 57.7485 24.08 55.5817 22.8363C53.2582 21.4867 51.8484 20.4812 51.8484 17.544C51.8484 14.2098 53.8847 12.4369 57.6963 12.4369C59.0017 12.4369 60.3331 12.6486 61.4818 13.019L61.1424 15.3477C60.3592 15.083 59.2889 14.9243 58.2968 14.9243C56.9392 14.9243 55.5556 15.1889 55.5556 17.0941C55.5556 18.3114 56.0516 18.7347 58.2185 20.0843C60.8031 21.672 62.1345 22.651 62.1345 25.8264C62.1606 29.6104 60.2548 31.4098 56.2866 31.4098Z"
          fill="#26263F"
        />
        <Path
          d="M69.549 31.4099C66.86 31.4099 65.6591 30.1662 65.6591 27.3877V19.6874H63.8838V17.2794H65.7635L65.9984 13.8923H69.1313V17.2794H72.1597L72.0552 19.6874H69.1313V27.2554C69.1313 28.552 69.4446 29.24 71.0632 29.24C71.3765 29.24 71.6898 29.1871 71.9508 29.0813L71.8464 31.0129C71.272 31.2776 70.4366 31.4099 69.549 31.4099Z"
          fill="#26263F"
        />
        <Path
          d="M79.8088 31.4097C74.9268 31.4097 74.248 28.6313 74.248 25.8793V22.4393C74.248 18.7347 76.0755 16.9088 79.8088 16.9088C83.5682 16.9088 85.3957 18.7082 85.3957 22.4393V25.8793C85.3957 29.7691 83.7249 31.4097 79.8088 31.4097ZM79.8088 19.1051C77.9291 19.1051 77.7203 20.5605 77.7203 21.6454V26.7525C77.7203 27.811 77.9291 29.2399 79.8088 29.2399C81.6885 29.2399 81.8974 27.811 81.8974 26.7525V21.6454C81.9235 19.9254 81.2447 19.1051 79.8088 19.1051Z"
          fill="#26263F"
        />
        <Path
          d="M87.8237 31.0923V17.2793H91.0088V19.7403C91.6876 17.7556 93.1234 16.9618 94.8465 16.9618C94.8987 16.9618 94.9509 16.9618 94.977 16.9618V20.1636C94.9248 20.1636 94.8987 20.1636 94.8465 20.1636C93.515 20.1636 92.1314 20.6664 91.3482 21.4603L91.296 21.5132V31.1187H87.8237V31.0923Z"
          fill="#26263F"
        />
        <Path
          d="M102.992 31.4098C99.128 31.4098 96.9351 29.3987 96.9351 25.9058V22.4129C96.9351 18.7876 98.7364 16.9618 102.261 16.9618C105.838 16.9618 107.508 18.5759 107.508 22.0689V24.8209H100.355V26.0381C100.355 28.0492 101.687 29.187 103.984 29.187C104.924 29.187 105.916 29.0018 107.117 28.5519L107.012 30.7218C106.072 31.1452 104.48 31.4098 102.992 31.4098ZM102.287 18.9993C100.59 18.9993 100.355 20.243 100.355 21.4867V23.1538H104.193V21.4867C104.193 20.243 103.958 18.9993 102.287 18.9993Z"
          fill="#26263F"
        />
        <Path
          d="M114.165 31.4098C112.338 31.4098 110.772 30.9335 109.936 30.5366V28.0227C111.137 28.5784 112.469 28.896 113.565 28.896C115.445 28.896 116.306 28.1021 116.306 26.3821C116.306 24.5827 115.627 24.08 113.461 22.8363C111.137 21.4867 109.727 20.4812 109.727 17.544C109.727 14.2098 111.764 12.4369 115.575 12.4369C116.881 12.4369 118.212 12.6486 119.361 13.019L119.021 15.3477C118.238 15.083 117.168 14.9243 116.176 14.9243C114.818 14.9243 113.434 15.1889 113.434 17.0941C113.434 18.3114 113.931 18.7347 116.097 20.0843C118.682 21.672 120.013 22.651 120.013 25.8264C120.04 29.6104 118.108 31.4098 114.165 31.4098Z"
          fill="#26263F"
        />
        <Path
          d="M122.572 31.0923V12.8339H132.467V15.2419H126.279V20.6929H131.448V23.1009H126.279V28.6314H132.701V31.0923H122.572Z"
          fill="#26263F"
        />
        <Path
          d="M141.317 31.4098C137.009 31.4098 134.633 28.9225 134.633 24.424V19.4492C134.633 15.0037 137.061 12.4634 141.317 12.4634C145.703 12.4634 148 14.8714 148 19.4492V24.4769C148 28.896 145.546 31.4098 141.317 31.4098ZM141.317 14.9508C139.333 14.9508 138.497 16.0886 138.497 18.7612V25.2972C138.497 27.7846 139.359 28.9225 141.317 28.9225C143.823 28.9225 144.136 27.1231 144.136 25.3766V18.7612C144.136 16.0886 143.301 14.9508 141.317 14.9508Z"
          fill="#26263F"
        />
        <Path
          d="M45.1911 2.43443C45.0084 1.61413 44.4862 0.899665 43.7813 0.449818C43.2853 0.13228 42.7109 -0.0264893 42.1366 -0.0264893C41.0401 -0.0264893 40.0219 0.529203 39.4476 1.48182L22.4259 29.875C21.5121 31.3834 21.9821 33.3415 23.4702 34.2412C23.9662 34.5587 24.5405 34.7175 25.1149 34.7175C26.2114 34.7175 27.2296 34.1618 27.8039 33.2092L44.8517 4.8689C45.2694 4.12797 45.4 3.2812 45.1911 2.43443Z"
          fill="#00CC76"
        />
        <Path
          d="M27.4384 7.75316C27.2556 6.93285 26.7335 6.21839 26.0286 5.76854C25.5326 5.45101 24.9582 5.29224 24.3839 5.29224C23.2874 5.29224 22.2692 5.84793 21.6948 6.80054L8.53697 28.3932C7.62323 29.9015 8.09316 31.8596 9.58125 32.7593C10.0773 33.0769 10.6516 33.2356 11.226 33.2356C12.3225 33.2356 13.3406 32.6799 13.915 31.7273L27.0729 10.1347C27.5167 9.42024 27.6472 8.57347 27.4384 7.75316Z"
          fill="#00CC76"
        />
        <Path
          d="M12.6358 9.73778C12.4531 8.91747 11.9309 8.20301 11.226 7.75316C10.73 7.43562 10.1556 7.27686 9.5813 7.27686C8.48481 7.27686 7.46664 7.83255 6.89229 8.78516L0.46999 18.8935C0.0261725 19.6079 -0.104362 20.4812 0.104493 21.3015C0.287242 22.1218 0.783273 22.8362 1.48816 23.2861C1.98419 23.6036 2.55854 23.7624 3.13289 23.7624C4.22938 23.7624 5.24755 23.2067 5.8219 22.2541L12.2703 12.1458C12.7141 11.4313 12.8447 10.5845 12.6358 9.73778Z"
          fill="#00CC76"
        />
        <Path
          d="M9.16347 39.3747C8.95461 37.8135 7.62316 36.6492 6.08285 36.6492C5.92621 36.6492 5.79568 36.6492 5.63903 36.6756C3.94209 36.9138 2.74117 38.5279 3.00224 40.2744C3.21109 41.8356 4.54254 42.9999 6.08285 42.9999C6.23949 42.9999 6.37003 42.9999 6.52667 42.9735C8.19751 42.7353 9.39843 41.1212 9.16347 39.3747Z"
          fill="#00CC76"
        />
        <Path
          d="M23.1046 39.3747C22.8958 37.8135 21.5643 36.6492 20.024 36.6492C19.8674 36.6492 19.7368 36.6492 19.5802 36.6756C17.8832 36.9138 16.6823 38.5279 16.9434 40.2744C17.1523 41.8356 18.4837 42.9999 20.024 42.9999C20.1807 42.9999 20.3112 42.9999 20.4678 42.9735C21.3033 42.8412 22.0342 42.4178 22.5303 41.7298C23.0263 41.0682 23.2352 40.2215 23.1046 39.3747Z"
          fill="#00CC76"
        />
        <Path
          d="M32.503 13.2043C31.5631 14.5538 29.7618 14.9772 28.352 14.104C26.9422 13.2308 26.4462 11.3785 27.1772 9.89661L26.8639 10.3994L16.8127 26.9114C16.8389 26.8849 16.8389 26.8849 16.865 26.8585C16.8911 26.832 16.9172 26.7791 16.9433 26.7526C16.9694 26.7261 16.9955 26.6997 17.0216 26.6732C17.5959 26.0381 18.4314 25.6677 19.319 25.6677C21.0682 25.6677 22.504 27.0966 22.504 28.896C22.504 29.3194 22.4257 29.7428 22.2691 30.1132L28.2476 20.2166L32.503 13.2043Z"
          fill="#00AA95"
        />
        <Path
          d="M16.865 26.8849C16.8911 26.8584 16.9172 26.8055 16.9433 26.7791C16.9433 26.8055 16.8911 26.832 16.865 26.8849Z"
          fill="#E54141"
        />
      </G>
    </Svg>
  );

  const renderInvoiceNumberAndDate = () => (
    <View style={styles.headerInfoBox}>
      <Text style={styles.invoiceNumber}>Invoice No: {transactionID}</Text>
      <Text style={styles.invoiceDate}>Date: {invoiceDate}</Text>
    </View>
  );

  const renderInvoiceHeader = () => (
    <>
      <View style={styles.header}>
        {renderStoreSeoLogo()}
        {renderInvoiceNumberAndDate()}
      </View>
      <View style={styles.horizontalLine}></View>
    </>
  );

  const renderMailingAddress = (name, email, addressDetails, sender = false) => (
    <View style={styles.mailingAddressDetails}>
      <Text style={styles.mailingTextSecondary}>{sender ? "From" : "To"}</Text>
      <Text style={styles.mailingTextPrimary}>{name}</Text>
      <Text style={{ ...styles.mailingTextSecondary, ...styles.colorGreen }}>{email}</Text>
      <Text style={styles.mailingTextSecondary}>{addressDetails.address2}</Text>
      <View style={{ width: "100%" }}></View>
      <Text style={styles.mailingTextSecondary}>{`${addressDetails.address1 || ""}, ${addressDetails.city || ""}, ${
        addressDetails.zip || ""
      }`}</Text>
      <View style={{ width: "100%" }}></View>
      <Text style={styles.mailingTextSecondary}>{addressDetails.country}</Text>
    </View>
  );

  const renderMailingDetails = () => (
    <View style={styles.mailingAddressBox}>
      {renderMailingAddress(STORE_SEO_TITLE, STORE_SEO_SUPPORT_EMAIL, STORE_SEO_ADDRESS, true)}
      {renderMailingAddress(shop.name, shop.email, shop.billing_address)}
    </View>
  );

  const renderInvoiceItems = () => (
    <View>
      <Text style={styles.invoiceItemsHeader}>Invoice Items</Text>
      <View style={{ ...styles.invoiceItemRow, ...styles.bgLightGrey }}>
        <Text style={{ ...styles.width45, ...styles.colorBlack, ...styles.fontBold }}>Plan Name</Text>
        <Text style={{ ...styles.width30, ...styles.colorBlack, ...styles.fontBold }}>Quantity</Text>
        <Text style={{ ...styles.width25, ...styles.colorBlack, ...styles.fontBold, ...styles.textRight }}>Price</Text>
      </View>
      <View style={{ ...styles.invoiceItemRow, ...{ borderTop: "none" } }}>
        <Text style={{ ...styles.width45, ...styles.colorBlack }}>{planName}</Text>
        <Text style={{ ...styles.width30, ...styles.colorBlack }}>1</Text>
        <Text style={{ ...styles.width25, ...styles.colorBlack, ...styles.textRight }}>{`$${price}`}</Text>
      </View>
    </View>
  );

  const renderAdditionalBillingInfo = () => (
    <View style={styles.additionalBillingInfo}>
      <Text style={styles.invoiceItemsHeader}>Additional Information</Text>
      <Text style={styles.invoiceDate}>Transaction Date: {invoiceDate}</Text>
      <Text
        style={{
          ...styles.paymentStatus,
          ...(paymentStatus === PAYMENT_STATUS.PAID ? styles.labelSuccess : styles.labelDanger),
        }}
      >
        {paymentStatus}
      </Text>
    </View>
  );

  const renderBillBreakDown = () => (
    <View style={{ ...styles.billBreakdown, ...styles.fontBold }}>
      <View style={{ ...styles.billRow, ...styles.colorBlack, ...styles.bgLightGrey }}>
        <Text>Subtotal</Text>
        <Text>{`$${price}`}</Text>
      </View>
      <View style={{ ...styles.billRow, ...styles.colorBlack, ...styles.bgLightGrey }}>
        <Text>Discount</Text>
        <Text>{`-$${discount}`}</Text>
      </View>
      <View style={{ ...styles.billRow, ...styles.colorWhite, ...styles.bgGreen }}>
        <Text>Total</Text>
        <Text>{`$${totalPrice}`}</Text>
      </View>
    </View>
  );

  const renderTotalBill = () => (
    <View style={styles.billingSection}>
      {renderAdditionalBillingInfo()}
      {renderBillBreakDown()}
    </View>
  );

  return (
    <Document title={transactionID}>
      <Page
        size="A4"
        style={styles.page}
      >
        {renderInvoiceHeader()}
        {renderMailingDetails()}
        {renderInvoiceItems()}
        {renderTotalBill()}
      </Page>
    </Document>
  );
};

export default Invoice;

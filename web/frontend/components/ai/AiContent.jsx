// @ts-check
import { Box, Button, Icon, InlineStack, Text } from "@shopify/polaris";
import { MagicIcon } from "@shopify/polaris-icons";
import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { setIsKept, setTags } from "../../store/features/AiContent";
import AiTag from "./AiTag";

/**
 * AiContent component for displaying and managing AI-generated content
 * @component
 * @param {Object} props - Component props
 * @param {('metaTitle'|'metaDescription'|'focusKeyword'|'tags')} props.contentKey - Key to identify the type of content being displayed
 * @param {React.ReactNode} [props.helpText] - Optional help text to display when no AI content is available
 * @param {Function} [props.onKeep] - Callback function when content is kept
 * @param {Function} [props.onRevert] - Callback function when content is reverted
 * @returns {React.ReactElement | React.ReactNode} Rendered component
 */

const AiContent = ({ contentKey, helpText = null, onKeep = () => {}, onRevert = () => {} }) => {
  const { t } = useTranslation();

  // @ts-ignore
  const aiContent = useSelector((state) => state.aiContent);
  const dispatch = useDispatch();

  const handleOnKeep = () => {
    dispatch(setIsKept({ [contentKey]: true }));
    onKeep(aiContent[contentKey]);
  };

  const handleOnRevert = () => {
    dispatch(setIsKept({ [contentKey]: false }));
    onRevert();
  };

  // Handle keeping a single tag
  const handleKeepSingleTag = (tag) => {
    if (contentKey === "tags") {
      // Call the parent's onKeep function with the single tag
      // The parent component will handle adding it to the existing tags
      onKeep([tag]);

      // Remove the tag from the AI generated tags list
      const updatedTags = aiContent[contentKey].filter((t) => t !== tag);

      dispatch(setTags(updatedTags));

      updatedTags.length === 0 && dispatch(setIsKept({ [contentKey]: true }));
    }
  };

  const keepText = useMemo(() => {
    return contentKey === "tags" ? "Keep & replace all" : "Keep it";
  }, [contentKey]);

  const revertText = "Revert";

  if (!aiContent[contentKey] && !aiContent.creating) return helpText;

  return (
    <InlineStack
      gap="200"
      wrap={false}
      align="space-between"
      blockAlign="start"
    >
      <InlineStack
        gap="200"
        wrap={false}
        align="start"
        blockAlign="start"
      >
        {!aiContent.isKept[contentKey] ? (
          <>
            <Box>
              <Icon
                source={MagicIcon}
                tone="magic"
              />
            </Box>
            {aiContent.creating && (
              <Text
                as="p"
                tone="magic"
              >
                {t("Generating")}...
              </Text>
            )}
            {contentKey !== "tags" ? (
              <Text
                as="p"
                tone="magic"
              >
                {aiContent[contentKey]}
              </Text>
            ) : (
              <InlineStack gap="100">
                {aiContent[contentKey]?.map((tag, idx) => (
                  <AiTag
                    key={`${tag}-${idx}`}
                    content={tag}
                    onKeep={handleKeepSingleTag}
                  />
                ))}
              </InlineStack>
            )}
          </>
        ) : (
          helpText
        )}
      </InlineStack>
      {!aiContent.creating && (
        <Box minWidth={contentKey === "tags" ? "150px" : "70px"}>
          <InlineStack
            align="end"
            blockAlign="start"
          >
            {aiContent.isKept[contentKey] ? (
              <Button onClick={handleOnRevert}>{t(revertText)}</Button>
            ) : (
              <Button onClick={handleOnKeep}>{t(keepText)}</Button>
            )}
          </InlineStack>
        </Box>
      )}
    </InlineStack>
  );
};

export default AiContent;

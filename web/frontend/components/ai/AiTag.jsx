import { Box, Text, Tooltip } from "@shopify/polaris";
import { useTranslation } from "react-i18next";

/**
 * AiTag component for displaying and managing individual AI-generated tags
 * @component
 * @param {Object} props - Component props
 * @param {string} props.content - The tag content to display
 * @param {Function} [props.onKeep] - Callback function when tag is kept
 * @returns {JSX.Element} Rendered component
 */

const AiTag = ({ content, onKeep }) => {
  const { t } = useTranslation();

  const handleClick = () => {
    if (onKeep) {
      onKeep(content);
    }
  };

  return (
    <Tooltip content={t("Keep tag")}>
      <span
        style={{ cursor: "pointer" }}
        onClick={handleClick}
      >
        <Box
          background="bg-fill-magic-secondary"
          paddingInlineStart="200"
          paddingInlineEnd="200"
          borderRadius="200"
        >
          <Text
            as="span"
            tone="magic"
          >
            {content}
          </Text>
        </Box>
      </span>
    </Tooltip>
  );
};

export default AiTag;

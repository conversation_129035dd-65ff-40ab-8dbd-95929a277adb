import { useMultiLanguageSetting } from "@/hooks/useMultiLanguageSetting";
import {
  Banner,
  BlockStack,
  Box,
  Button,
  ButtonGroup,
  Card,
  InlineStack,
  Link,
  Text,
  TextField,
} from "@shopify/polaris";
import { LockIcon, MagicIcon } from "@shopify/polaris-icons";
import { isEmpty, pick } from "lodash";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { useDispatch, useSelector } from "react-redux";
import { PRODUCT } from "storeseo-enums/analysisEntityTypes";
import { useAiContentApi } from "../../hooks";
import useUserAddon from "../../hooks/useUserAddon";
import { resetAiContent } from "../../store/features/AiContent";
import AiThumbnail from "../svg/AiThumbnail";

// const generatorOptions = [
//   { key: "meta_title", label: "Meta Title" },
//   { key: "meta_desc", label: "Meta Description" },
//   { key: "tags", label: "Tags" },
// ];

const AiContentGeraratorCard = ({
  title,
  description,
  focusKeyword,
  setErrors = () => {},
  handleKeepAll = () => {},
  handleRevertAll = () => {},
  isSaved = false,
  setIsSaved = () => {},
  resourceType = PRODUCT,
}) => {
  const { t } = useTranslation();
  const aiContentApi = useAiContentApi();
  const { selectedLanguageName, selectedLanguage } = useMultiLanguageSetting();
  const dispatch = useDispatch();
  const aiContent = useSelector((state) => state.aiContent);
  const [generated, setGenerated] = useState(() => false);

  useEffect(() => {
    setGenerated(Object.values(pick(aiContent, ["metaTitle", "metaDescription", "tags"])).some((val) => val));
  }, [aiContent]);

  useEffect(() => {
    dispatch(resetAiContent({ creating: false }));
  }, [selectedLanguage]);

  const {
    hasAiOptimizer,
    aiOptimizerUsageLimitExceeded,
    aiOptimizerIsEnabled,
    aiOptimizerUsageRemaining,
    updateAiOptimizerUsage,
  } = useUserAddon();

  const checkoutPath = "/credit-bundles";

  const isAllKept = Object.keys(aiContent.isKept).every((key) =>
    key === "tags" && !aiContent?.tags ? true : aiContent.isKept[key]
  );

  const showKeepAllBtn = generated && !isSaved;

  const {
    mutate: generate,
    isLoading: isGenerating,
    isError,
    error,
  } = useMutation({
    mutationFn: () =>
      aiContentApi.generateAiContent({
        title,
        description,
        focusKeyword,
        language: selectedLanguageName,
        type: resourceType,
      }),
    onMutate: () => {
      dispatch(resetAiContent({ creating: true }));
      setErrors({});
    },
    onSuccess: ({ creditUsage }) => {
      updateAiOptimizerUsage(creditUsage);
      setGenerated(true);
      setIsSaved(false);
    },
    onError: ({ errors }) => {
      !isEmpty(errors) && setErrors(errors);
      dispatch(resetAiContent({ creating: false }));
    },
  });

  return (
    <Card>
      <BlockStack gap="400">
        <InlineStack
          gap="400"
          wrap={false}
        >
          <Box minWidth="40px">
            <AiThumbnail />
          </Box>
          <BlockStack gap="100">
            <Text
              as="h4"
              variant="headingSm"
            >
              {t("AI Content Optimizer")}
            </Text>
            <Text
              as="p"
              tone="subdued"
            >
              {t("Optimize your meta titles, meta descriptions, and tags with the power of AI")}
            </Text>
          </BlockStack>
        </InlineStack>

        {isError && error?.isIrrelevantInput && <Banner tone="critical">{t(error.message)}</Banner>}

        {aiOptimizerUsageLimitExceeded && (
          <Banner
            title={t("No credits available")}
            tone="warning"
          >
            {t("No credits available for generating SEO content")} <Link url={checkoutPath}>{t("Buy credits")}</Link>
          </Banner>
        )}

        {!hasAiOptimizer && (
          <Banner
            title={t("AI Content Optimizer is not enabled")}
            tone="warning"
            action={{ content: t("Choose your plan"), url: checkoutPath }}
            icon={LockIcon}
          >
            {t("Please add AI Content Optimizer to your subscription plan to unlock this feature")}.
          </Banner>
        )}

        <TextField
          disabled
          label={t("Focus Keyword")}
          value={focusKeyword}
          requiredIndicator
        />
        {/* 
        <TextField
          label="Prompt"
          multiline={3}
          disabled
        />*/}

        {/* {aiOptimizerIsEnabled && generatorOptions.length > 0 && (
          <BlockStack gap="050">
            <Text as="p">{t("Generate for")}</Text>
            {generatorOptions.map((item) => (
              <Checkbox
                key={item.key}
                label={item.label}
                onChange={(val) => {
                  setGenerateFor({ ...generateFor, [item.key]: val });
                }}
                checked={generateFor[item.key]}
              />
            ))}
          </BlockStack>
        )} */}

        <ButtonGroup fullWidth>
          {showKeepAllBtn && !isGenerating && (
            <Button
              variant={!isAllKept ? "primary" : "secondary"}
              onClick={isAllKept ? handleRevertAll : handleKeepAll}
            >
              {t(isAllKept ? "Revert all" : "Keep all")}
            </Button>
          )}

          <Button
            variant={!generated && !isGenerating ? "primary" : "secondary"}
            icon={MagicIcon}
            disabled={!aiOptimizerIsEnabled}
            loading={isGenerating}
            onClick={generate}
          >
            {t("{{PREFIX}}Generate", {
              PREFIX: generated ? t("Re-") : "",
            })}
          </Button>
        </ButtonGroup>

        {hasAiOptimizer && (
          <InlineStack
            gap="200"
            align="space-between"
            wrap={false}
          >
            <Text
              as="p"
              tone="subdued"
            >
              {t("{{REMAINING_LIMIT}} Credits available", {
                REMAINING_LIMIT: aiOptimizerUsageRemaining > 0 ? aiOptimizerUsageRemaining?.toFixed(1) : 0,
              })}
            </Text>

            <Link
              url={checkoutPath}
              removeUnderline
            >
              {t("Buy Credits")}
            </Link>
          </InlineStack>
        )}
      </BlockStack>
    </Card>
  );
};

export default AiContentGeraratorCard;

import React, { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import {
  readAllNotifications,
  readNotification,
  setAllNotifications,
  setInitialNotifications,
} from "../../store/features/Notifications";
import socketEvents from "storeseo-enums/socketEvents";
import { usePusher } from "../../providers/PusherProvider";
import { useNotificationApi, useUtilityApi } from "../../hooks/apiHooks";
import {
  BlockStack,
  Button,
  Icon,
  InlineStack,
  Popover,
  ResourceItem,
  ResourceList,
  Text,
  Tooltip,
} from "@shopify/polaris";
import { CheckCircleIcon, NotificationIcon, CheckSmallIcon } from "@shopify/polaris-icons";
import { formatDate } from "../../utility/helpers.jsx";
import translationKeys from "storeseo-enums/interpolatedTranslationKeys";

export default function Notifications({}) {
  const [showDropdown, setShowDropdown] = useState(false);
  const [showAll, setShowAll] = useState(false);
  const [hasNotification, setHasNotification] = useState(false);
  const [loading, setLoading] = useState(false);

  const { t } = useTranslation();
  const { pusherChannel, socketId } = usePusher();

  const user = useSelector((state) => state.user);
  const notifications = useSelector((state) => state.notifications);
  const dispatch = useDispatch();
  const notificationApi = useNotificationApi();
  const utilityApi = useUtilityApi();

  const markAllAsReadViaSocket = async () => {
    setLoading(true);
    dispatch(readAllNotifications());

    await utilityApi.triggerEvent(socketEvents.NOTIFICATION_ALL_READ, {
      room: user.shop,
      shopId: user.shopId,
      socketId,
    });
    setLoading(false);
  };

  const markAsReadViaSocket = async (id, isRead) => {
    if (isRead) return;

    setLoading(true);
    const res = await utilityApi.triggerEvent(socketEvents.NOTIFICATION_READ, {
      id,
      room: user.shop,
      shopId: user.shopId,
      socketId,
    });

    if (res?.success) {
      dispatch(readNotification(id));
    }
    setLoading(false);
  };

  const getTranslatedNotificationMessage = (msg) => {
    let translationKey = msg;
    let interpolationOptions = {};
    const words = msg.split(" ");

    if (msg.includes("auto-fix")) {
      translationKey = translationKeys.OPTIMIZATION_TASK_COMPLETE_NOTIFICATION_MSG;
      // interpolationOptions = {
      //   "?": words[1],
      //   "??": words[9],
      // };
    } else {
      translationKey = translationKeys.SYNC_COMPLETE_NOTIFICATION_MSG;
      interpolationOptions = {
        "?": words[1],
        "??": words.includes("pages") ? "pages" : words.includes("products") ? "products" : "blog posts",
      };
    }

    return t(translationKey, interpolationOptions);
  };

  const loadLatestNotifications = async () => {
    setLoading(true);
    const { notifications: notificationsArr } = await notificationApi.fetchNotifications({ limit: 5 });
    dispatch(setInitialNotifications(notificationsArr));
    setLoading(false);
  };

  const loadAllNotifications = async () => {
    setLoading(true);
    const { notifications: notificationsArr } = await notificationApi.fetchNotifications({ limit: 250 });
    dispatch(setAllNotifications(notificationsArr));
    setLoading(false);
  };

  useEffect(() => {
    if (!notifications.latestNotificationsLoaded) loadLatestNotifications();
  }, []);

  useEffect(() => {
    setHasNotification(notifications?.list?.length > 0);
  }, [notifications.list]);

  useEffect(() => {
    if (showAll) {
      loadAllNotifications();
    } else {
      loadLatestNotifications();
    }
  }, [showAll]);

  const togglePopoverActive = useCallback(() => setShowDropdown((status) => !status), []);
  const handleResourceListItemClick = useCallback(
    (id) => {
      const { is_read = false, id: nId = null } = notifications?.list?.find((n) => n.id === id);
      markAsReadViaSocket(id, is_read);
    },
    [notifications]
  );

  const toggleShowAll = () => {
    setShowAll(!showAll);
  };

  useEffect(() => {
    if (!showDropdown) {
      setShowAll(false);
    }
  }, [showDropdown]);

  const notificationCountBadge = (
    <Text
      as="span"
      tone="success"
      variant="headingSm"
    >
      ({notifications.numOfUnreadNotifications})
    </Text>
  );

  const activator = (
    <div style={{ position: "relative", lineHeight: 0 }}>
      <Tooltip
        content={hasNotification ? t("Show notifications") : t("No notifications")}
        zIndexOverride={999}
      >
        <Button
          onClick={togglePopoverActive}
          icon={NotificationIcon}
          variant="monochromePlain"
        />
      </Tooltip>
      {notifications.numOfUnreadNotifications > 0 && (
        <span
          style={{
            width: "10px",
            height: "10px",
            background: "var(--p-color-bg-fill-brand)",
            borderRadius: "var(--p-border-radius-full)",
            position: "absolute",
            top: "0",
            right: "0",
          }}
        />
      )}
    </div>
  );

  const headerContent = (
    <InlineStack gap="200">
      <Text
        as={"p"}
        variant={"headingMd"}
      >
        {t("Notifications")}
      </Text>
      {notifications.numOfUnreadNotifications > 0 && <span>({notifications.numOfUnreadNotifications})</span>}
    </InlineStack>
  );
  const renderItem = ({ id, title, message, is_read, created_at }) => {
    return (
      <ResourceItem
        id={id}
        onClick={handleResourceListItemClick}
      >
        <BlockStack gap="200">
          <InlineStack align={"space-between"}>
            <Text
              as="p"
              variant="headingSm"
            >
              {t(title)}
            </Text>
            {is_read && (
              <span>
                <Icon
                  source={CheckSmallIcon}
                  tone="base"
                />
              </span>
            )}
          </InlineStack>
          <Text
            as="p"
            tone="subdued"
            variant="bodySm"
          >
            {getTranslatedNotificationMessage(message)}
          </Text>
          <Text
            as="i"
            tone="subdued"
            variant="bodySm"
            alignment="end"
          >
            {formatDate(created_at, "D MMM YYYY h:mm a")}
          </Text>
        </BlockStack>
      </ResourceItem>
    );
  };

  return (
    <Popover
      sectioned
      active={showDropdown}
      activator={activator}
      onClose={togglePopoverActive}
      ariaHaspopup={false}
      zIndexOverride={101}
      fixed
    >
      <Popover.Pane>
        <ResourceList
          background="bg-surface"
          items={notifications.list}
          renderItem={renderItem}
          showHeader={true}
          headerContent={headerContent}
          alternateTool={
            <Tooltip
              content={t("Read All")}
              zIndexOverride={999}
              preferredPosition={"below"}
            >
              <Button
                onClick={markAllAsReadViaSocket}
                icon={CheckCircleIcon}
                variant="monochromePlain"
                loading={loading}
              />
            </Tooltip>
          }
          // loading={loading}
        />
      </Popover.Pane>
      {hasNotification && (
        <Popover.Section>
          <div style={{ textAlign: "center" }}>
            <Button
              variant="monochromePlain"
              onClick={toggleShowAll}
            >
              {t(showAll ? "Show Less" : "View all notifications")}
            </Button>
          </div>
        </Popover.Section>
      )}
    </Popover>
  );
}

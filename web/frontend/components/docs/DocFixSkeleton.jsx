import {
  <PERSON><PERSON>,
  <PERSON>,
  BlockStack,
  Di<PERSON>r,
  Text,
  Card,
  Box,
  InlineStack,
  Icon,
  TextField,
  Button,
  SkeletonThumbnail,
  SkeletonDisplayText,
  SkeletonBodyText,
} from "@shopify/polaris";
import CollapsibleCard from "../common/CollapsibleCard";

import { cardBodyPaddingStyle, collapseHeaderStyle } from "../../styles/common";

import { XIcon } from "@shopify/polaris-icons";
import SkeletonLoader from "../loader/SkeletonLoader";
import { useTranslation } from "react-i18next";
import React from "react";

const basicSeoAnalysis = [
  "Focus keyword is unique",
  "Focus keyword is used in the meta title",
  "Meta description is within 80-165 characters",
  "Collection Meta title is within 30-70 characters",
];

const detailedSeoAnalysis = [
  "Focus keyword is used in the Doc title",
  "Focus keyword found in meta description",
  "Focus keyword is used in the URL",
];

export default function DocFixSkeleton() {
  const { t } = useTranslation();
  return (
    <Page
      backAction={{
        content: "Docs",
      }}
      title={<SkeletonDisplayText size="medium" />}
      compactTitle
      pagination={{
        hasPrevious: false,
        hasNext: false,
      }}
    >
      <Grid>
        <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 4, lg: 8, xl: 8 }}>
          <BlockStack gap="400">
            <CollapsibleCard title="General SEO">
              <div style={cardBodyPaddingStyle}>
                <BlockStack gap="400">
                  <Box>
                    <Box paddingBlockEnd="200"></Box>
                    <SkeletonThumbnail size="large" />
                  </Box>
                  <TextField
                    label={t("Meta Title")}
                    showCharacterCount
                    disabled
                    maxLength={70}
                  />
                  <TextField
                    label={t("Meta Description")}
                    multiline={4}
                    autoComplete="off"
                    showCharacterCount
                    requiredIndicator
                    maxLength={165}
                    disabled
                  />
                  <TextField
                    label={t("Focus Keyword")}
                    requiredIndicator
                    autoComplete="off"
                    disabled
                    connectedRight={
                      <Button
                        variant="primary"
                        disabled
                      >
                        Keyword Analytics
                      </Button>
                    }
                  />
                  <div>
                    <div style={{ marginBottom: 8 }}>
                      <Text tone="text-inverse">{t("Keyword Suggestions")}:</Text>
                    </div>
                    <InlineStack
                      gap="200"
                      align="start"
                    >
                      <SkeletonLoader style={{ height: 20, width: 80, borderRadius: 8 }} />
                      <SkeletonLoader style={{ height: 20, width: 100, borderRadius: 8 }} />
                      <SkeletonLoader style={{ height: 20, width: 70, borderRadius: 8 }} />
                    </InlineStack>
                  </div>
                </BlockStack>
              </div>
            </CollapsibleCard>
          </BlockStack>
        </Grid.Cell>
        <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 2, lg: 4, xl: 4 }}>
          <BlockStack gap="400">
            <Card padding="0">
              <div
                className="collapse-header"
                style={collapseHeaderStyle}
              >
                <Text
                  as="h6"
                  variant="headingSm"
                >
                  {t("SEO Score")}
                </Text>
              </div>
              <Divider />
              <div style={{ ...cardBodyPaddingStyle, height: 232 }}>
                <SkeletonBodyText lines={4} />
              </div>
            </Card>
            {/* Basic SEO analysis */}
            <AnalysisItemList
              items={basicSeoAnalysis}
              title="Basic SEO Analysis"
              badge={{
                badgeTitle: `5 ${t("Issues")}`,
                badgeStatus: "attention",
              }}
            />
            {/* Detailed SEO analysis */}
            <AnalysisItemList
              items={detailedSeoAnalysis}
              title="Detailed SEO Analysis"
              badge={{
                badgeTitle: `5 ${t("Issues")}`,
                badgeStatus: "attention",
              }}
            />
          </BlockStack>
        </Grid.Cell>
      </Grid>
    </Page>
  );
}

const AnalysisItemList = ({ title, items, badge: { badgeTitle, badgeStatus } }) => {
  const { t } = useTranslation();
  return (
    <CollapsibleCard
      title={title}
      badgeText={badgeTitle}
      badgeStatus={badgeStatus}
    >
      <div className="seo-analytics-list">
        <BlockStack>
          {items.map((item, index) => (
            <React.Fragment key={index}>
              <AnalysisItem title={t(item)} />
              {index !== items.length - 1 && <Divider />}
            </React.Fragment>
          ))}
        </BlockStack>
      </div>
    </CollapsibleCard>
  );
};

const AnalysisItem = ({ title }) => {
  return (
    <Box padding="200">
      <InlineStack
        gap="200"
        wrap={false}
      >
        <span>
          <Icon
            source={XIcon}
            tone={"critical"}
          />
        </span>
        <Text as="p">{title}.</Text>
      </InlineStack>
    </Box>
  );
};

import { generateTableSL } from "@/utility/helpers";
import { IndexTable } from "@shopify/polaris";
import DocRowsItem from "./DocRowsItem";

const DocRows = ({ docs, isFetching = false, pagination }) => {
  return (
    <>
      {docs?.map(({ id, ...doc }, index) => {
        const sl = generateTableSL(pagination, index);
        const item = { ...doc, sl };
        return (
          <IndexTable.Row
            id={id}
            key={index}
            position={index}
          >
            <DocRowsItem
              item={item}
              isFetching={isFetching}
            />
          </IndexTable.Row>
        );
      })}
    </>
  );
};

export default DocRows;

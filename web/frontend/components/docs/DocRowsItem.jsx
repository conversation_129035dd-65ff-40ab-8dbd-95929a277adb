import { formatDate, prepareThumbnailURL } from "@/utility/helpers";
import {
  Badge,
  BlockStack,
  Box,
  Button,
  IndexTable,
  InlineStack,
  Link,
  Text,
  Thumbnail,
  useBreakpoints,
} from "@shopify/polaris";
import Radial<PERSON>hart from "../charts/RadialChart";
import { t } from "i18next";
import OptimizeSeoStatusBadge from "@/modules/optimize-seo/OptimizeSeoStatusBadge";
import TooltipWrapper from "../common/TooltipWrapper";
import TitleColumn from "../common/TitleColumn";
import { ComposeIcon, ImageIcon } from "@shopify/polaris-icons";
import { useLabelColor } from "@/hooks/useLabelColor";
import DocRowsItemSkeleton from "./DocRowsItemSkeleton";

const DocRowsItem = ({ item, isFetching }) => {
  const { smDown: isSmallDevice } = useBreakpoints();

  if (isFetching) return <DocRowsItemSkeleton />;

  return <>{isSmallDevice ? <RowsCellForSmallDevices item={item} /> : <RowsCellForLargeDevices item={item} />}</>;
};

const RowsCellForSmallDevices = ({ item }) => {
  const { title, categories, score, focus_keyword, issues, created_at, img, doc_id } = item;
  const labelColor = useLabelColor(issues);
  // const thumbnail = prepareThumbnailURL(featured_image?.url);
  return (
    <Link
      monochrome
      removeUnderline
      url={`/optimize-seo/docs/${doc_id}`}
      onClick={(e) => e.stopPropagation()}
    >
      <Box
        paddingBlock="300"
        paddingInline="400"
        width="100%"
      >
        <BlockStack gap="150">
          <InlineStack
            align="space-between"
            wrap={false}
            gap="200"
          >
            {/* Thumbnail */}
            <Box>
              <Thumbnail
                source={img?.src || ImageIcon}
                alt={img?.alt_text}
                size="small"
              />
            </Box>
            <Box width="100%">
              <BlockStack gap="300">
                {/* Basic info */}
                <BlockStack gap="100">
                  <Text
                    as={"h4"}
                    fontWeight="semibold"
                  >
                    {title}
                  </Text>

                  <Text
                    as="span"
                    variant="bodySm"
                  >
                    {focus_keyword}
                  </Text>
                </BlockStack>
                {/* Scoring */}
                <InlineStack
                  align="space-between"
                  blockAlign="center"
                  gap="200"
                >
                  <BlockStack
                    gap="200"
                    blockAlign="center"
                  >
                    <InlineStack gap="200">
                      <Badge tone={labelColor}>
                        {issues} {t("Issues")}
                      </Badge>

                      <OptimizeSeoStatusBadge score={score} />
                    </InlineStack>

                    <Text
                      as="span"
                      variant="bodySm"
                      tone="subdued"
                    >
                      {formatDate(created_at)}
                    </Text>
                  </BlockStack>

                  <RadialChart
                    score={score}
                    dimension={40}
                  />
                </InlineStack>
              </BlockStack>
            </Box>
          </InlineStack>
        </BlockStack>
      </Box>
    </Link>
  );
};

const RowsCellForLargeDevices = ({ item }) => {
  const { title, categories, score, focus_keyword, issues, created_at, sl, img, doc_id } = item;

  const labelColor = useLabelColor(issues);
  // const thumbnail = prepareThumbnailURL(img?.src);
  return (
    <>
      <IndexTable.Cell className="width-24">{sl}</IndexTable.Cell>
      <IndexTable.Cell className="width-table_thumbnail">
        <Thumbnail
          source={img?.src || ImageIcon}
          alt={img?.alt_text}
          size="small"
        />
      </IndexTable.Cell>

      <IndexTable.Cell className="break_coll_content width-resource_table_title">
        <TitleColumn
          title={title}
          url={`/optimize-seo/docs/${doc_id}`}
        />
      </IndexTable.Cell>

      <IndexTable.Cell className="break_coll_content width-resource_table_title">
        {categories?.map((cat) => cat.name).join(",")}
      </IndexTable.Cell>

      <IndexTable.Cell className="width-150">
        <OptimizeSeoStatusBadge score={score} />
      </IndexTable.Cell>

      <IndexTable.Cell className="break_coll_content width-resource_table_title">{focus_keyword}</IndexTable.Cell>
      <IndexTable.Cell>
        <Badge tone={labelColor}>
          {issues} {t("Issues")}
        </Badge>
      </IndexTable.Cell>
      <IndexTable.Cell>
        <RadialChart
          score={score}
          dimension={40}
        />
      </IndexTable.Cell>
      <IndexTable.Cell className="width-120">{formatDate(created_at)}</IndexTable.Cell>
      <IndexTable.Cell className="width-80">
        <InlineStack align="center">
          <TooltipWrapper content="Fix issue">
            <Button
              icon={ComposeIcon}
              onClick={(e) => e.stopPropagation()}
              variant="tertiary"
              url={`/optimize-seo/docs/${doc_id}`}
            />
          </TooltipWrapper>
        </InlineStack>
      </IndexTable.Cell>
    </>
  );
};

export default DocRowsItem;

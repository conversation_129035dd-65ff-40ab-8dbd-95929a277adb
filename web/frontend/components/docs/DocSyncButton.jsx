import { <PERSON><PERSON>, <PERSON><PERSON>, Toolt<PERSON> } from "@shopify/polaris";
import { ResetIcon } from "@shopify/polaris-icons";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import ConfirmationModal from "../modals/ConfirmationModal";
import { useDocCount, useSyncDocs } from "@/hooks/docs";
import { setDocSyncStatus } from "../../store/features/DocSync";
import { isEmpty } from "lodash";

const DocSyncButton = ({ onSyncErr = () => {} }) => {
  const docSync = useSelector((state) => state.docSync);
  const user = useSelector((state) => state.user);
  const docCount = useSelector((state) => state.docCount);

  const dispatch = useDispatch();
  const { t } = useTranslation();

  const [isTooltipActive, setIsTooltipActive] = useState(false);
  const [tooltipContent, setTooltipContent] = useState("");
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const handleSyncDoc = async () => {
    setShowConfirmModal(true);
  };

  let { mutate: startSyncOperation, isLoading } = useSyncDocs({
    onSuccess: (status) => {
      dispatch(setDocSyncStatus(status));
    },
    onError: () => {
      onSyncErr();
    },
    onSettled: () => setShowConfirmModal(false),
  });

  const { data } = useDocCount();

  const allDocsSynced = docCount >= data?.totalDocsCount;

  const disableSyncBtn = !user?.isSubscribed || allDocsSynced || isTooltipActive;

  useEffect(() => {
    if (!isEmpty(docSync) && docSync.ongoing) {
      const message = t("Syncing docs") + (docSync.total > 0 ? ` ${docSync.synced}/${docSync.total}` : "...");
      setTooltipContent(message);
      setIsTooltipActive(true);
    } else if (allDocsSynced && docSync.ongoing === false) {
      setTooltipContent(t("All docs are synced"));
      setIsTooltipActive(true);
    } else {
      setTooltipContent("");
      setIsTooltipActive(false);
    }
  }, [user, docSync, allDocsSynced]);

  return (
    <>
      <Tooltip
        active={isTooltipActive}
        content={t(tooltipContent)}
        persistOnClick
        activatorWrapper={"div"}
        zIndexOverride={99}
      >
        <Button
          icon={<Icon source={ResetIcon} />}
          onClick={handleSyncDoc}
          disabled={disableSyncBtn}
          loading={isLoading || docSync?.ongoing}
        >
          {t("Sync docs")}
        </Button>
      </Tooltip>

      <ConfirmationModal
        show={showConfirmModal}
        onClose={setShowConfirmModal}
        title="Sync Docs"
        content="Are you sure you want to sync docs from BetterDocs?"
        primaryAction={startSyncOperation}
        loading={isLoading}
      />
    </>
  );
};

export default DocSyncButton;

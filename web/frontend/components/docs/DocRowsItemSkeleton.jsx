import {
  BlockStack,
  Box,
  Grid,
  IndexTable,
  InlineStack,
  SkeletonBodyText,
  SkeletonDisplayText,
  SkeletonThumbnail,
  useBreakpoints,
} from "@shopify/polaris";

export default function DocRowsItemSkeleton() {
  const { smDown: isSmallDevice } = useBreakpoints();

  return <>{isSmallDevice ? <RowsCellForSmallDevices /> : <RowsCellForLargeDevices />}</>;
}

const RowsCellForSmallDevices = () => {
  return (
    <Box
      paddingBlock="300"
      paddingInline="400"
      width="100%"
    >
      <BlockStack gap="150">
        <InlineStack
          align="space-between"
          wrap={false}
          gap="200"
        >
          {/* Thumbnail */}
          <Box>
            <SkeletonThumbnail size="small" />
          </Box>
          <Box width="100%">
            <BlockStack gap="300">
              {/* Basic info */}
              <BlockStack gap="300">
                <SkeletonBodyText lines={1} />

                <SkeletonBodyText lines={1} />
              </BlockStack>
              {/* Scoring */}
              <Grid gap={{ xs: "200" }}>
                <Grid.Cell columnSpan={{ xs: 3 }}>
                  <BlockStack
                    gap="400"
                    blockAlign="center"
                  >
                    <Grid>
                      <Grid.Cell columnSpan={{ xs: 2 }}>
                        <SkeletonBodyText lines={1} />
                      </Grid.Cell>
                      <Grid.Cell
                        column={{ xs: 4 }}
                        columnSpan={{ xs: 3 }}
                      >
                        <SkeletonBodyText lines={1} />
                      </Grid.Cell>
                    </Grid>

                    <SkeletonBodyText lines={1} />
                  </BlockStack>
                </Grid.Cell>
                <Grid.Cell
                  column={{ xs: 6 }}
                  columnSpan={{ xs: 1 }}
                >
                  <SkeletonThumbnail size={"small"} />
                </Grid.Cell>
              </Grid>
            </BlockStack>
          </Box>
        </InlineStack>
      </BlockStack>
    </Box>
  );
};

const RowsCellForLargeDevices = () => {
  return (
    <>
      <IndexTable.Cell className="width-24">
        {" "}
        <SkeletonBodyText lines={1} />{" "}
      </IndexTable.Cell>
      <IndexTable.Cell className="width-table_thumbnail">
        <SkeletonThumbnail size="small" />
      </IndexTable.Cell>
      <IndexTable.Cell className="break_coll_content width-resource_table_title">
        <SkeletonBodyText lines={1} />
      </IndexTable.Cell>
      <IndexTable.Cell className="break_coll_content width-resource_table_title">
        <SkeletonBodyText lines={1} />
      </IndexTable.Cell>
      <IndexTable.Cell>
        <SkeletonBodyText lines={1} />
      </IndexTable.Cell>
      <IndexTable.Cell>
        <SkeletonThumbnail size={"small"} />
      </IndexTable.Cell>
      <IndexTable.Cell className="width-120">
        <SkeletonBodyText lines={1} />
      </IndexTable.Cell>
      <IndexTable.Cell className="width-150">
        <SkeletonBodyText lines={1} />
      </IndexTable.Cell>
      <IndexTable.Cell className="width-80">
        <SkeletonDisplayText size={"small"} />
      </IndexTable.Cell>
    </>
  );
};

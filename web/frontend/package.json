{"name": "frontend", "version": "1.0.0", "private": true, "license": "UNLICENSED", "scripts": {"build": "vite build", "dev": "vite", "coverage": "vitest run --coverage"}, "type": "module", "engines": {"node": ">= 20.19.0"}, "stylelint": {"extends": "@shopify/stylelint-polaris"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@microsoft/clarity": "^1.0.0", "@microsoft/fetch-event-source": "^2.0.1", "@react-pdf/renderer": "^3.4.5", "@reduxjs/toolkit": "^2.6.1", "@sentry/vite-plugin": "^2.23.0", "@shopify/app-bridge": "3.7.10", "@shopify/app-bridge-react": "4.1.5", "@shopify/polaris": "13.9.5", "@shopify/polaris-icons": "9.3.1", "@uidotdev/usehooks": "^2.4.1", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-react-swc": "^3.8.1", "apexcharts": "^4.5.0", "classnames": "^2.5.1", "crisp-sdk-web": "^1.0.25", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^7.2.2", "i18next-http-backend": "^2.7.3", "mitt": "^3.0.1", "moment": "^2.30.1", "prop-types": "^15.8.1", "pusher-js": "^8.4.0", "react": "^18.3.1", "react-apexcharts": "^1.7.0", "react-colorful": "^5.6.1", "react-compare-slider": "^3.1.0", "react-confirm-alert": "^2.8.0", "react-cropper": "^2.3.3", "react-datepicker": "^4.25.0", "react-dom": "^18.3.1", "react-hook-form": "^7.56.0", "react-i18next": "^13.5.0", "react-multi-carousel": "^2.8.5", "react-query": "^3.39.3", "react-redux": "^9.2.0", "react-router-dom": "^6.30.0", "react-select": "^5.10.1", "react-tagsinput": "^3.20.3", "redux": "^5.0.1", "storeseo-enums": "workspace:storeseo-enums", "storeseo-schema": "workspace:storeseo-schema", "vite": "^6.2.5", "web-vitals": "^3.5.2", "yup": "1.6.1"}, "devDependencies": {"@shopify/app-bridge-types": "^0.0.18", "@shopify/stylelint-polaris": "^16.0.7", "history": "^5.3.0", "jsdom": "^19.0.0", "prettier": "^2.8.8", "stylelint": "^16.18.0"}}
// import { sentryVitePlugin } from "@sentry/vite-plugin";
import react from "@vitejs/plugin-react-swc";
import fs from "fs/promises";
import path, { dirname } from "path";
import { fileURLToPath } from "url";
import { defineConfig, loadEnv } from "vite";

const _dirname = typeof __dirname !== "undefined" ? __dirname : dirname(fileURLToPath(import.meta.url));

if (process.env.npm_lifecycle_event === "build" && !process.env.CI && !process.env.SHOPIFY_API_KEY) {
  console.warn(
    "\nBuilding the frontend app without an API key. The frontend build will not run without an API key. Set the SHOPIFY_API_KEY environment variable when running the build command.\n"
  );
}

const proxyOptions = {
  target: `http://127.0.0.1:${process.env.BACKEND_PORT}`,
  changeOrigin: false,
  secure: true,
  ws: false,
};

const host = process.env.HOST ? process.env.HOST.replace(/https?:\/\//, "") : "localhost";

let hmrConfig;
if (host === "localhost") {
  hmrConfig = {
    protocol: "ws",
    host: "localhost",
    port: 64999,
    clientPort: 64999,
  };
} else {
  hmrConfig = {
    protocol: "wss",
    host: host,
    port: process.env.FRONTEND_PORT,
    clientPort: 443,
  };
}

/**
 * Example return value: `^/(?:(?:pages)|(?:products))?(\\?.*)?$`
 * @returns {Promise<string>}
 *
 */
const topLevelPageRoutesRegex = async () => {
  const routeFileNames = await fs.readdir(`${import.meta.dirname}/pages`);

  const individualRoutesGroupRegexs = routeFileNames.map((fileName) => {
    const routePath = fileName.split(".")[0];
    return `(?:${routePath})`;
  });

  return `^/(?:${individualRoutesGroupRegexs.join("|")})?(?!/dev_embed.js)[/a-z\\d-]*(\\?.*)?$`;
};

export default defineConfig(async ({ command, mode }) => {
  const env = loadEnv(mode, process.cwd() + "/../", ["FRONTEND_", "NODE_", "SENTRY_"]);

  const pageRoutesRegex = await topLevelPageRoutesRegex();

  // console.log("pageRoutesRegex: " + pageRoutesRegex);

  return {
    root: dirname(fileURLToPath(import.meta.url)),
    plugins: [
      react(),

      // Put the Sentry vite plugin after all other plugins
      // sentryVitePlugin({
      //   org: env.SENTRY_ORG,
      //   project: env.SENTRY_PROJECT,
      //   authToken: env.SENTRY_AUTH_TOKEN,
      //   telemetry: false,
      // }),
    ],
    define: {
      "process.env": { ...env },
    },
    resolve: {
      preserveSymlinks: true,
      alias: {
        "@": path.resolve(_dirname, "./"),
        react: path.resolve(_dirname, "./node_modules/react"),
        "react-dom": path.resolve(_dirname, "./node_modules/react-dom"),
      },
    },
    server: {
      host: "localhost",
      port: process.env.FRONTEND_PORT,
      hmr: hmrConfig,
      proxy: {
        [pageRoutesRegex]: proxyOptions,
        "^/app-proxy.*": proxyOptions,
        "^/image-service.*": proxyOptions,
        "^/api(/|(\\?.*)?$)": proxyOptions,
        "^/api/v1(/|(\\?.*)?$)": proxyOptions,
        "^/webhooks.*": proxyOptions, // webhooks path
        "^/better-docs/webhooks.*": proxyOptions, // BetterDocs webhooks path
        "^/(?:(?:img)|(?:fonts)|(?:locales)).*": proxyOptions, // static assets in public folder
        "^/(?:(?:install)|(?:oauth2callback)).*": proxyOptions, // root app routes for install/third-party redirects
      },
    },
    publicDir: "./assets/css/icon",
    build: {
      sourcemap: true, // Source map generation must be turned on
      emptyOutDir: true,
      assetsInlineLimit: "7168",
      chunkSizeWarningLimit: 512,
      rollupOptions: {
        output: {
          entryFileNames: "[name].[hash:16].js",
          chunkFileNames: "chunks/[name].[hash:16].js",
          assetFileNames: "assets/[name].[hash:16][extname]",
          sanitizeFileName: true,
          manualChunks: (id, { getModuleIds, getModuleInfo }) => {
            if (id.match(/node_modules\/react-dom/gim)) {
              return "react-dom";
            }

            if (id.match(/node_modules\/(?:(?:react-router-dom)|(?:react-router)|(?:@remix-run))\//gim)) {
              return "react-dom";
            }

            if (id.match(/node_modules\/react\//gim)) {
              return "react-dom";
            }

            if (id.match(/node_modules\/prop-types/gim)) return "react-dom";

            if (
              id.match(
                /node_modules\/(?:(?:react-redux)|(?:@reduxjs)|(?:redux)|(?:redux-thunk)|(?:storeseo-enums))\//gim
              )
            ) {
              return "redux-react-query";
            }

            if (id.match(/frontend\/store\//gim)) {
              return "redux-react-query";
            }

            if (id.match(/node_modules\/react-query/gim)) return "redux-react-query";

            if (id.match(/frontend\/i18n/gim)) return "i18n";
            if (
              id.match(
                /node_modules\/(?:(?:i18next)|(?:react-i18next)|(?:i18next-http-backend)|(?:i18next-browser-languagedetector))/gim
              )
            )
              return "i18n";

            if (id.match(/node_modules\/lodash/gim)) return "lodash";

            if (id.match(/node_modules\/moment\//gim)) return "date-time";
            if (id.match(/node_modules\/dayjs\//gim)) return "date-time";

            if (id.match(/frontend\/pages\/index/gim)) return "base-app";

            if (id.match(/frontend[\W\w]*(?:(?:DefaultLayout))/gim)) return "base-app";

            if (id.match(/frontend[\W\w]*DashboardLoader/gim)) return "base-app";

            if (id.match(/frontend[\W\w]*DummyProductFix/gim)) return "base-app";

            if (id.match(/node_modules\/@shopify\/app-bridge-core/gim)) return "app-bridge";

            if (id.match(/node_modules\/@shopify\/polaris\/build\/esm\/components/gim)) return "polaris";

            if (id.match(/node_modules\/@shopify\/polaris-icons/gim)) return "polaris";

            if (id.match(/node_modules\/@shopify/gim) && !id.match(/@shopify\/polaris/gim)) return "shopify";

            // // Onboarding modules to a single chunk
            if (id.match(/frontend\/(?:pages\/onboarding\/|.*OnboardingLayout.*)\.(jsx?|js)$/gim)) return "onboarding";

            if (id.match(/frontend\/config/gim)) return "config";
          },
        },
      },
    },
  };
});

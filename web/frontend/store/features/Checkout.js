import { createSlice } from "@reduxjs/toolkit";

export const checkoutSlice = createSlice({
  name: "checkout",
  initialState: {
    slug: "",
    coupon_code: "",
    isTrial: false,
  },
  reducers: {
    setPlanSlug: (state, action) => {
      state.slug = action.payload;
    },
    setCouponCode: (state, action) => {
      state.coupon_code = action.payload;
    },
    setCheckout: (state, action) => {
      state.slug = action.payload.slug;
      state.coupon_code = action.payload?.coupon_code || "";
      state.isTrial = action.payload?.isTrial || false;
    },
    resetCheckout: (state) => {
      state.slug = "";
      state.coupon_code = "";
      state.isTrial = false;
    },
  },
});

// Action creators are generated for each case reducer function
export const { setPlanSlug, setCouponCode, setCheckout, resetCheckout } = checkoutSlice.actions;

export default checkoutSlice.reducer;

import { createSlice } from "@reduxjs/toolkit";

export const purchaseSlice = createSlice({
  name: "purchase",
  initialState: {
    show: false,
  },
  reducers: {
    showPurchase: (state) => {
      state.show = true;
    },
    resetPurchase: (state) => {
      state.show = false;
    },
  },
});

// Action creators are generated for each case reducer function
export const { showPurchase, resetPurchase } = purchaseSlice.actions;

export default purchaseSlice.reducer;

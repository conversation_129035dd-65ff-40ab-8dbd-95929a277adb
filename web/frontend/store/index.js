import { configureStore } from "@reduxjs/toolkit";
import AiContentReducer from "./features/AiContent";
import blogSyncReducer from "./features/BlogSync";
import campaignReducer from "./features/Campaign";
import CheckoutReducer from "./features/Checkout.js";
import collectionCountReducer from "./features/CollectionCount.js";
import CollectionSyncReducer from "./features/CollectionSync.js";
import docCountReducer from "./features/DocCount.js";
import DocSyncReducer from "./features/DocSync.js";
import HiddenBannerReducer from "./features/HiddenBanner";
import notificationsReducer from "./features/Notifications";
import OnboardStepReducer from "./features/OnboardStep";
import OptimizationTaskReducer from "./features/OptimizationTask";
import pageSyncReducer from "./features/PageSync";
import productCountReducer from "./features/ProductCount";
import productSyncReducer from "./features/ProductSync";
import PurchaseReducer from "./features/Purchase.js";
import userReducer from "./features/User";

export default configureStore({
  reducer: {
    user: userReducer,
    onboardStep: OnboardStepReducer,
    productCount: productCountReducer,
    campaign: campaignReducer,
    productSync: productSyncReducer,
    blogSync: blogSyncReducer,
    pageSync: pageSyncReducer,
    notifications: notificationsReducer,
    optimizationTask: OptimizationTaskReducer,
    hiddenBanner: HiddenBannerReducer,
    collectionCount: collectionCountReducer,
    collectionSync: CollectionSyncReducer,
    aiContent: AiContentReducer,
    docCount: docCountReducer,
    docSync: DocSyncReducer,
    checkout: CheckoutReducer,
    purchase: PurchaseReducer,
  },
});

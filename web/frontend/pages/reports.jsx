import { Block<PERSON>ta<PERSON>, Page, Tabs, Text } from "@shopify/polaris";
import { useEffect, useState } from "react";

import DocsTable from "@/components/reports/DocsTable";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { useSearchParams } from "react-router-dom";
import AnalysisOverview, {
  BLOG_POSTS_ANALYSIS_CARDS_INFO,
  COLLECTION_ANALYSIS_CARDS_INFO,
  DOC_ANALYSIS_CARDS_INFO,
  PAGE_ANALYSIS_CARDS_INFO,
  PRODUCT_ANALYSIS_CARDS_INFO,
} from "../components/reports/AnalysisOverview";
import BlogsTable from "../components/reports/BlogsTable";
import CollectionsTable from "../components/reports/CollectionsTable.jsx";
import OverallOptimizationHighlights from "../components/reports/OverallOptimizationHighlights";
import PagesTable from "../components/reports/PagesTable";
import ProductsTable from "../components/reports/ProductsTable";
import { useAppQuery, useShopApi } from "../hooks";
import { useAppNavigation } from "../hooks/useAppNavigation.js";
import { useStoreSeo } from "../providers/StoreSeoProvider";
import queryKeys from "../utility/queryKeys";

const Report = () => {
  const { t } = useTranslation();
  const shopApi = useShopApi();
  const { doManualRefresh } = useStoreSeo();
  const [searchParams, setSearchParams] = useSearchParams();
  const navigation = useAppNavigation();
  const user = useSelector((state) => state.user);

  const [selectedTabIndex, setSelectedTabIndex] = useState(0);

  const isBetterDocsInstalled = user?.betterDocsInstallationStatus;

  let tabs = [
    {
      id: "products",
      content: t("Products"),
      accessibilityLabel: "Products",
      panelID: "Products",
      cardsInfo: PRODUCT_ANALYSIS_CARDS_INFO,
      dataKey: "productStats",
      Table: ProductsTable,
      tableTitle1: "Top scored products",
      tableTitle2: "Less scored products",
    },
    {
      id: "collections",
      content: t("Collections"),
      accessibilityLabel: "Collections",
      panelID: "Collections",
      cardsInfo: COLLECTION_ANALYSIS_CARDS_INFO,
      dataKey: "collectionStats",
      Table: CollectionsTable,
      tableTitle1: "Top scored collections",
      tableTitle2: "Less scored collections",
    },
    {
      id: "pages",
      content: t("Pages"),
      accessibilityLabel: "Pages",
      panelID: "Pages",
      cardsInfo: PAGE_ANALYSIS_CARDS_INFO,
      dataKey: "pageStats",
      Table: PagesTable,
      tableTitle1: "Top scored pages",
      tableTitle2: "Less scored pages",
    },
    {
      id: "blogPosts",
      content: t("Blog Posts"),
      accessibilityLabel: "Blog Posts",
      panelID: "Blog Posts",
      cardsInfo: BLOG_POSTS_ANALYSIS_CARDS_INFO,
      dataKey: "blogArticleStats",
      Table: BlogsTable,
      tableTitle1: "Top scored blog posts",
      tableTitle2: "Less scored blog posts",
    },
    {
      id: "docs",
      content: t("Docs"),
      accessibilityLabel: "Docs",
      panelID: "Docs",
      cardsInfo: DOC_ANALYSIS_CARDS_INFO,
      dataKey: "docStats",
      Table: DocsTable,
      tableTitle1: "Top scored docs",
      tableTitle2: "Less scored docs",
    },
  ].filter((tab) => (isBetterDocsInstalled ? true : tab.id !== "docs"));

  const {
    data = {},
    isFetching,
    isFetched,
    refetch,
  } = useAppQuery({
    queryKey: queryKeys.STATISTICS_REPORT,
    queryFn: shopApi.getStatisticsReport,
  });

  if (doManualRefresh) refetch();

  useEffect(() => {
    const tabId = searchParams.get("tab") || tabs[0].id;
    const tabIndex = tabs.findIndex((t) => t.id === tabId);
    setSelectedTabIndex(tabIndex === -1 ? 0 : tabIndex);
  }, [searchParams, tabs]);

  const handleTabChange = (idx) => {
    const tabId = tabs[idx]?.id;
    if (tabId) {
      setSearchParams({ ...Object.fromEntries(searchParams), tab: tabId });
    }
  };

  // const handleOptionChange = (selected) => {
  //   setSearchParams({
  //     ...Object.fromEntries(searchParams),
  //     tab: selected.value,
  //   });
  // };

  const selectedTab = tabs[selectedTabIndex] || tabs[0];
  const tabData = data?.[selectedTab?.dataKey];

  // const options = tabs.map((t) => ({ label: t.content, value: t.id }));
  // const selectedOption = options.find((op) => op.value === selectedTab?.id);

  // const tabName = selectedTab?.content.toLowerCase();

  // const emptyImage = EMPTYSTATE_IMAGES[selectedTab?.id] || EMPTYSTATE_IMAGES.default;

  // useEffect(() => {}, [selectedTab]);

  return (
    <Page
      title={t("Reports")}
      backAction={navigation.backAction}
    >
      <div className="ss-reports-tab">
        <Tabs
          tabs={tabs}
          selected={selectedTabIndex}
          onSelect={handleTabChange}
        />

        {/* <ButtonGroup variant="segmented">
          {tabs.map((tab, idx) => (
            <Button
              variant={selectedTabIndex === idx && "primary"}
              onClick={() => handleTabChange(idx)}
            >
              {tab.content}
            </Button>
          ))}
        </ButtonGroup> */}
      </div>

      <BlockStack gap="600">
        <OverallOptimizationHighlights
          data={tabData}
          isLoading={isFetching}
        />
        <AnalysisOverview
          data={tabData}
          cardsInfo={selectedTab.cardsInfo}
          isLoading={isFetching}
        />

        <BlockStack gap="200">
          <Text
            variant="headingMd"
            as="h6"
          >
            {t(selectedTab.tableTitle1)}
          </Text>
          <selectedTab.Table
            items={tabData?.topScoredItems}
            isLoading={isFetching}
            isFetched={isFetched}
          />
        </BlockStack>
        <BlockStack gap="200">
          <Text
            variant="headingMd"
            as="h6"
          >
            {t(selectedTab.tableTitle2)}
          </Text>
          <selectedTab.Table
            items={tabData?.leastScoredItems}
            isLoading={isFetching}
          />
        </BlockStack>
      </BlockStack>
    </Page>
  );
};

export default Report;

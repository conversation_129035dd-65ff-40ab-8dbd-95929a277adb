import { useAppQ<PERSON>y, useNotification<PERSON>pi, useUtility<PERSON>pi } from "@/hooks";
import { useAppNavigation } from "@/hooks/useAppNavigation";
import useIndexTablePagination from "@/lib/hooks/useIndexTablePagination";
import { usePusher } from "@/providers/PusherProvider";
import { readAllNotifications, readNotification, setAllNotifications } from "@/store/features/Notifications";
import { formatDate, showNotification } from "@/utility/helpers";
import queryKeys from "@/utility/queryKeys";
import {
  BlockStack,
  Box,
  Card,
  Collapsible,
  Icon,
  InlineStack,
  Page,
  Pagination,
  SkeletonBodyText,
  Text,
} from "@shopify/polaris";
import { ChevronDownIcon, ChevronUpIcon, NotificationFilledIcon, NotificationIcon } from "@shopify/polaris-icons";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useSearchParams } from "react-router-dom";
import socketEvents from "storeseo-enums/socketEvents";

export default function Notifications() {
  const dispatch = useDispatch();
  const user = useSelector((state) => state.user);
  const notificationsList = useSelector((state) => state.notifications);
  const notificationApi = useNotificationApi();
  const [searchParams] = useSearchParams();
  const navigation = useAppNavigation();
  const [isReadingAll, setIsReadingAll] = useState(false);

  const page = searchParams.get("page") || 1;

  const { socketId } = usePusher();
  const utilityApi = useUtilityApi();
  const { t } = useTranslation();

  const { data, isLoading, refetch } = useAppQuery({
    queryKey: [queryKeys.NOTIFICATIONS_LIST, { page }, "all-notifications"],
    queryFn: async () => await notificationApi.fetchNotifications({ page }),
  });

  const allNotifications = notificationsList?.list?.notifications;

  useEffect(() => {
    dispatch(setAllNotifications(data));
    refetch();
  }, [data]);

  const markAsReadViaSocket = async (id, isRead) => {
    if (isRead) return;

    const res = await utilityApi.triggerEvent(socketEvents.NOTIFICATION_READ, {
      id,
      room: user.shop,
      shopId: user.shopId,
      socketId,
    });

    if (res?.success) {
      dispatch(readNotification(id));
    }
  };

  const handleResourceListItemClick = useCallback(
    (id) => {
      const { is_read = false } = notificationsList?.list?.notifications?.find((n) => n.id === id);
      markAsReadViaSocket(id, is_read);
    },
    [notificationsList]
  );

  const markAllAsReadViaSocket = async () => {
    setIsReadingAll(true);
    dispatch(readAllNotifications());

    await utilityApi.triggerEvent(socketEvents.NOTIFICATION_ALL_READ, {
      room: user.shop,
      shopId: user.shopId,
      socketId,
    });
    setIsReadingAll(false);
    showNotification({ message: "Notification read" });
  };

  const { paginationConfigs } = useIndexTablePagination(notificationsList?.list?.pagination);

  // console.info("paginationConfigs =", paginationConfigs);

  return (
    <Page
      backAction={navigation.backAction}
      title={t("All Notifications")}
      secondaryActions={[
        ...(allNotifications?.length > 0
          ? [
              {
                content: t("Read All"),
                onAction: markAllAsReadViaSocket,
                loading: isReadingAll,
              },
            ]
          : []),
      ]}
    >
      <BlockStack gap="400">
        {isLoading ? (
          Array.from({ length: 10 }).map((_, idx) => <NotificationLoader key={idx} />)
        ) : allNotifications?.length === 0 ? (
          <Text
            variant="bodyLg"
            as="p"
          >
            {t("No Notifications found.")}
          </Text>
        ) : (
          <>
            {allNotifications?.map((notification, idx) => (
              <NotificationCard
                key={idx}
                notification={notification}
                onRead={(id) => handleResourceListItemClick(id)}
              />
            ))}
            {paginationConfigs && (
              <BlockStack
                gap="400"
                inlineAlign="center"
              >
                <Pagination {...paginationConfigs} />
              </BlockStack>
            )}
          </>
        )}
      </BlockStack>
    </Page>
  );
}

const NotificationCard = ({ notification, onRead = (id) => {} }) => {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(null);

  // const isOpen = openCardId === notification?.id;
  const isRead = notification?.is_read;

  const handleToggle = () => setIsOpen(!isOpen);

  return (
    <Card key={notification?.id}>
      <div
        onClick={() => {
          handleToggle(notification?.id);
          onRead(notification?.id);
        }}
        style={{ cursor: "pointer" }}
      >
        <BlockStack>
          <InlineStack
            align="space-between"
            blockAlign="start"
          >
            <InlineStack
              align="start"
              gap="300"
              blockAlign="start"
            >
              <BlockStack>
                <div style={{ paddingTop: "3px" }}>
                  {isRead ? (
                    <Icon source={NotificationIcon} />
                  ) : (
                    <Icon
                      source={NotificationFilledIcon}
                      tone="success"
                    />
                  )}
                </div>
              </BlockStack>
              <BlockStack>
                <BlockStack gap="100">
                  <Text
                    variant="headingMd"
                    tone={isRead ? "subdued" : "base"}
                  >
                    {t(notification?.title)}
                  </Text>

                  <Text
                    variant="bodyMd"
                    tone={isRead ? "subdued" : "base"}
                  >
                    {formatDate(notification?.created_at, "D MMM YYYY h:mm a")}
                  </Text>

                  <div style={{ marginTop: "3px" }}>
                    <Collapsible
                      open={isOpen}
                      id="basic-collapsible"
                      transition={{ duration: "300ms", timingFunction: "ease-in-out" }}
                      expandOnPrint
                    >
                      <Text tone={isRead ? "subdued" : "base"}>{t(notification?.message)}</Text>
                    </Collapsible>
                  </div>
                </BlockStack>
              </BlockStack>
            </InlineStack>

            <BlockStack>
              <Icon source={isOpen ? ChevronUpIcon : ChevronDownIcon} />
            </BlockStack>
          </InlineStack>
        </BlockStack>
      </div>
    </Card>
  );
};

const NotificationLoader = () => {
  return (
    <Card>
      <div style={{ cursor: "pointer" }}>
        <BlockStack>
          <InlineStack
            align="space-between"
            blockAlign="start"
          >
            <InlineStack
              align="start"
              gap="300"
              blockAlign="start"
            >
              <BlockStack>
                <div style={{ paddingTop: "3px" }}>
                  <Icon source={NotificationIcon} />
                </div>
              </BlockStack>
              <BlockStack
                gap={400}
                align="start"
              >
                <Box width="400px">
                  <SkeletonBodyText lines={1} />
                </Box>
                <Box width="500px">
                  <SkeletonBodyText lines={1} />
                </Box>
              </BlockStack>
            </InlineStack>

            <BlockStack>
              <Icon source={ChevronDownIcon} />
            </BlockStack>
          </InlineStack>
        </BlockStack>
      </div>
    </Card>
  );
};

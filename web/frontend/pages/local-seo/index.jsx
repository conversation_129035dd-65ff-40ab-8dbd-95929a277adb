import SchemeSettings from "../../components/local-seo/SchemeSettings";
import { useLocalSEOBreadCrumbSchema, useUpdateLocalSEOBreadCrumbSchema } from "@/lib/hooks/local-seo";

const BreadcrumbSchema = () => {
  const { data, isLoading } = useLocalSEOBreadCrumbSchema();
  const isOn = data?.status;

  const { mutate, isLoading: isUpdateLoading } = useUpdateLocalSEOBreadCrumbSchema();

  const toggleBreadcrumbSchema = async () => {
    mutate({ status: !isOn });
  };

  return (
    <SchemeSettings
      title="Breadcrumb Schema"
      description="Breadcrumb schema is used for search engines to understand website structure for better indexing."
      onHandle={toggleBreadcrumbSchema}
      isOn={isOn}
      action="https://developers.google.com/search/docs/appearance/structured-data/breadcrumb"
      isLoading={isLoading || isUpdateLoading}
    />
  );
};

export default BreadcrumbSchema;

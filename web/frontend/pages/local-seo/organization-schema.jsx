//@ts-check

import { BlockStack } from "@shopify/polaris";
import SchemeSettings from "@/components/local-seo/SchemeSettings";
import LogoUploaderViewer from "@/components/common/LogoUploaderViewer";
import { useSelector } from "react-redux";
import { useLocalSEOOrganizationSchema, useUpdateLocalSEOOrganizationSchema } from "@/lib/hooks/local-seo";
import OrganizationInformationForm from "@/components/local-seo/OrganizationSchema/OrganizationInformationForm";

const OrganizationSchema = () => {
  // @ts-ignore
  const user = useSelector((state) => state.user);
  const { data, isLoading } = useLocalSEOOrganizationSchema();
  const isOn = data?.status;

  const { mutate, isLoading: isUpdateLoading } = useUpdateLocalSEOOrganizationSchema();

  const toggleOrganizationSchema = async () => {
    mutate({ ...data, status: !isOn });
  };

  return (
    <BlockStack gap="400">
      <SchemeSettings
        title="Organization Schema"
        description="Schema organization helps in improving the design and structure of a database schema to enhance performance, efficiency, and maintainability."
        onHandle={toggleOrganizationSchema}
        isOn={isOn}
        isLoading={isLoading || isUpdateLoading}
        action="https://developers.google.com/search/docs/appearance/structured-data/organization"
      />

      <LogoUploaderViewer
        currentLogoURL={user.shopLogo}
        disabled={!user.isPremium}
      />

      <OrganizationInformationForm isDisable={!isOn || !user.isPremium} />
    </BlockStack>
  );
};

export default OrganizationSchema;

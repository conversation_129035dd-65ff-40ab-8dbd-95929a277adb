import SchemeSettings from "@/components/local-seo/SchemeSettings";
import { useLocalSEOCollectionSchema, useUpdateLocalSEOCollectionSchema } from "@/lib/hooks/local-seo";

const CollectionSchema = () => {
  const { data, isLoading } = useLocalSEOCollectionSchema();
  const isOn = data?.status;

  const { mutate, isLoading: isUpdateLoading } = useUpdateLocalSEOCollectionSchema();

  const toggleCollectionSchema = async () => {
    mutate({ status: !isOn });
  };

  return (
    <SchemeSettings
      title="Collection Schema"
      description="The Collection Schema is needed to organize and display a group of related items in a structured format, enhancing search results."
      onHandle={toggleCollectionSchema}
      isOn={isOn}
      action="https://developers.google.com/search/docs/appearance/structured-data/carousel"
      isLoading={isLoading || isUpdateLoading}
    />
  );
};

export default CollectionSchema;

//@ts-check
import { BlockStack } from "@shopify/polaris";
import SchemeSettings from "@/components/local-seo/SchemeSettings";
import LogoUploaderViewer from "@/components/common/LogoUploaderViewer";
import { useLocalSEOLocalBusinessSchema, useUpdateLocalSEOLocalBusinessSchema } from "@/lib/hooks/local-seo";
import { useSelector } from "react-redux";
import LocalBusinessInformationForm from "@/components/local-seo/LocalBusinessSchema/LocalBusinessInformationForm";

const LocalBusinessSchema = () => {
  // @ts-ignore
  const user = useSelector((state) => state.user);
  const { data, isLoading } = useLocalSEOLocalBusinessSchema();
  const isOn = data?.status;

  const { mutate, isLoading: isUpdateLoading } = useUpdateLocalSEOLocalBusinessSchema();

  const toggleLocalBusinessSchema = async () => {
    mutate({ ...data, status: !isOn });
  };

  return (
    <BlockStack gap="400">
      <SchemeSettings
        title="Local Business Schema"
        description="Local Business Schema is necessary because it helps search engines better understand and display detailed information about local businesses."
        onHandle={toggleLocalBusinessSchema}
        isOn={isOn}
        action="https://developers.google.com/search/docs/appearance/structured-data/local-business"
        isLoading={isLoading || isUpdateLoading}
      />

      <LogoUploaderViewer
        currentLogoURL={user.shopLogo}
        disabled={!user.isPremium}
      />

      <LocalBusinessInformationForm isDisable={!isOn || !user.isPremium} />
    </BlockStack>
  );
};

export default LocalBusinessSchema;

//@ts-check
import { BlockStack } from "@shopify/polaris";
import SchemeSettings from "@/components/local-seo/SchemeSettings";
import {
  useLocalSEOProductMerchantSchema,
  useLocalSEOProductSchema,
  useUpdateLocalSEOProductMerchantSchema,
} from "@/lib/hooks/local-seo";
import MerchantInformationForm from "@/components/local-seo/ProductMerchantSchema/MerchantInformationForm";
import { useSelector } from "react-redux";

const ProductMerchantSchema = () => {
  // @ts-ignore
  const user = useSelector((state) => state.user);

  const { data: productSchema } = useLocalSEOProductSchema();
  const isProductSchemaOn = productSchema?.status;

  const { data, isLoading } = useLocalSEOProductMerchantSchema();
  const isOn = data?.status;

  const { mutate, isLoading: isUpdateLoading } = useUpdateLocalSEOProductMerchantSchema();

  const toggleProductMerchantSchema = async () => {
    mutate({ ...data, status: !isOn });
  };

  return (
    <BlockStack gap="400">
      <SchemeSettings
        title="Product Merchant Schema"
        description="Product Merchant Schema is needed to enhance the visibility and attractiveness of product listings in Google search results by providing detailed and structured information."
        onHandle={toggleProductMerchantSchema}
        isOn={isOn}
        action="https://developers.google.com/search/docs/appearance/structured-data/merchant-listing"
        isDisable={!isProductSchemaOn}
        disableMessage="Enable Product Schema to enable this feature"
        isLoading={isLoading || isUpdateLoading}
      />

      <MerchantInformationForm isDisable={!isOn || !user.isPremium} />
    </BlockStack>
  );
};

export default ProductMerchantSchema;

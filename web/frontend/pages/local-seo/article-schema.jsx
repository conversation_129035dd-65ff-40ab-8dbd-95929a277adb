//@ts-check
import { BlockStack } from "@shopify/polaris";
import SchemeSettings from "@/components/local-seo/SchemeSettings";
import { useLocalSEOArticleSchema, useUpdateLocalSEOArticleSchema } from "@/lib/hooks/local-seo";
import LogoUploaderViewer from "@/components/common/LogoUploaderViewer";
import { useSelector } from "react-redux";

const ArticleSchema = () => {
  // @ts-ignore
  const user = useSelector((state) => state.user);
  const { data, isLoading } = useLocalSEOArticleSchema();
  const isOn = data?.status;

  const { mutate, isLoading: isUpdateLoading } = useUpdateLocalSEOArticleSchema();

  const toggleArticleSchema = async () => {
    mutate({ ...data, status: !isOn });
  };

  return (
    <BlockStack gap="400">
      <SchemeSettings
        title="Article Schema"
        description="Article Schema is needed to help search engines understand and present article content more effectively, improving visibility."
        onHandle={toggleArticleSchema}
        isOn={isOn}
        action="https://developers.google.com/search/docs/appearance/structured-data/article"
        isLoading={isLoading || isUpdateLoading}
      />

      <LogoUploaderViewer
        currentLogoURL={user.shopLogo}
        disabled={!user.isPremium}
      />
    </BlockStack>
  );
};

export default ArticleSchema;

import SchemeSettings from "@/components/local-seo/SchemeSettings";
import { useLocalSEOBlogSchema, useUpdateLocalSEOBlogSchema } from "@/lib/hooks/local-seo";

const BlogSchema = () => {
  const { data, isLoading } = useLocalSEOBlogSchema();
  const isOn = data?.status;

  const { mutate, isLoading: isUpdateLoading } = useUpdateLocalSEOBlogSchema();

  const toggleBlogSchema = async () => {
    mutate({ status: !isOn });
  };
  return (
    <SchemeSettings
      title="Blog Schema"
      description="Blog schema is needed to enhance search visibility, improve click-through rates, and enrich the user's search experience."
      onHandle={toggleBlogSchema}
      isOn={isOn}
      action="https://developers.google.com/search/docs/appearance/structured-data/carousel"
      isLoading={isLoading || isUpdateLoading}
    />
  );
};

export default BlogSchema;

import SchemeSettings from "@/components/local-seo/SchemeSettings";
import { useLocalSEOProductSchema, useUpdateLocalSEOProductSchema } from "@/lib/hooks/local-seo";
import { BlockStack, <PERSON>ton, Card, InlineStack, Select, Text } from "@shopify/polaris";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import reviewApps from "storeseo-enums/reviewApps";

const ProductSchema = () => {
  const { t } = useTranslation();

  const { data, isLoading } = useLocalSEOProductSchema();
  const isOn = data?.status;

  const [reviewApp, setReviewApp] = useState(reviewApps.LAI_PRODUCT_REVIEWS);

  useEffect(() => {
    setReviewApp(data?.settings?.reviewApp || reviewApps.LAI_PRODUCT_REVIEWS);
  }, [data]);

  const { mutate, isLoading: isTogglingProductSchema } = useUpdateLocalSEOProductSchema();

  const toggleProductSchema = async () => {
    mutate({ status: !isOn });
  };

  const { mutate: updateSchemaSettings, isLoading: isUpdatingSchemaSettings } = useUpdateLocalSEOProductSchema();
  const handleSave = () => {
    updateSchemaSettings({ ...data, settings: { reviewApp } });
  };

  return (
    <BlockStack gap="400">
      <SchemeSettings
        title="Product Schema"
        description="Product schema is needed to help search engines understand product details on a website, enabling enhanced search results like rich snippets."
        onHandle={toggleProductSchema}
        isOn={isOn}
        action="https://developers.google.com/search/docs/appearance/structured-data/product"
        isLoading={isLoading || isTogglingProductSchema}
      />
      <Card>
        <BlockStack gap="300">
          <Text
            as="h3"
            variant="headingMd"
          >
            {t("Product Schema")}
          </Text>
          <Select
            label="Review From"
            options={reviewApps.options}
            disabled={!isOn}
            onChange={(value) => setReviewApp(value)}
            value={reviewApp}
            // helpText="If review app is not listed, then choose other to add it."
          />
        </BlockStack>
      </Card>
      <InlineStack align="end">
        <Button
          variant="primary"
          disabled={!isOn}
          onClick={handleSave}
          loading={isUpdatingSchemaSettings}
        >
          {t("Save")}
        </Button>
      </InlineStack>
    </BlockStack>
  );
};

export default ProductSchema;

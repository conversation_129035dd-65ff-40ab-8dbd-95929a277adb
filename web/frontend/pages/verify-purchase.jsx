import { BlockStack, <PERSON>, Page, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import toastMessages from "storeseo-enums/toastMessages";
import { useUserApi } from "../hooks/apiHooks/useUserApi.js";
import { useAppQuery } from "../hooks/index.js";
import { updateUser } from "../store/features/User.js";
import { showNotification } from "../utility/helpers.jsx";

function VerifyPurchase() {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const userApi = useUserApi();

  const chargeId = new URLSearchParams(location.search).get("charge_id");

  if (chargeId) {
    useAppQuery({
      queryKey: "PURCHASE_SUCCESS",
      queryFn: () => userApi.getAuthUserData(),
      reactQueryOptions: {
        staleTime: 0,
        refetchInterval: false,
        onSuccess: ({ user }) => {
          dispatch(updateUser({ ...user, isNewlyUpgraded: false }));
          showNotification({
            type: "success",
            message: toastMessages.PURCHASE_SUCCESSFUL,
          });

          setTimeout(() => navigate("/"), 3000);
        },
      },
    });
  }

  return (
    <Page>
      <Card padding="3200">
        <BlockStack
          align="center"
          inlineAlign="center"
          gap={400}
        >
          <img
            src="https://cdn.storeseo.com/verify/Verifying-Purchase.gif"
            alt="Subscription Verify"
            width="172"
            height="168"
          />
          <Text
            as={"h4"}
            variant={"headingLg"}
          >
            {t("Verifying purchase. Please wait")}...
          </Text>
        </BlockStack>
      </Card>
    </Page>
  );
}

export default VerifyPurchase;

// //@ts-check
import AiContentGeraratorCard from "@/components/ai/AiContentGeraratorCard";
import { FormErrorBanner } from "@/components/common/FormErrorBanner";
import { useMultiLanguageSetting } from "@/hooks/useMultiLanguageSetting";
import useAiContentOptimizerHelper from "@/lib/hooks/optimize-seo/useAiContentOptimizerHelper";
import ContextualSaveBar from "@/modules/components/ContextualSaveBar";
import AIOptimizationStatusBadge from "@/modules/optimize-seo/AIOptimizationStatusBadge";

import { resetAiContent } from "@/store/features/AiContent";
import { BlockStack, Grid, Page, PageActions } from "@shopify/polaris";
import { isEmpty } from "lodash";
import React, { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";
import { useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import analysisEntityTypes from "storeseo-enums/analysisEntityTypes";
import CollectionFixSkeleton from "../../../../components/collections/CollectionFixSkeleton";
import AppEmbedWarningBanner from "../../../../components/common/AppEmbedWarningBanner";
import EditForm from "../../../../components/common/EditForm";
import OptimizationData from "../../../../components/common/OptimizationData";
import SocialMediaPreview from "../../../../components/previewers/SocialMediaPreview";
import NoFollowToggler from "../../../../components/togglers/NoFollowToggler";
import NoIndexToggler from "../../../../components/togglers/NoIndexToggler";
import { PAGE_TEMPLATES } from "../../../../config";
import {
  useGetCollectionsWithOptimizationData,
  useUpdateCollection,
  useUpdateCollectionFollowStatus,
  useUpdateCollectionIndexStatus,
} from "../../../../hooks/collections";
import { useCollectionReanalysis } from "../../../../hooks/collections/useCollectionReanalysis";
import { useAppBridgeRedirect } from "../../../../hooks/useAppBridgeRedirect";
import { useAppNavigation } from "../../../../hooks/useAppNavigation";
import { useFeaturedImage } from "../../../../hooks/useFeaturedImage";
import { useGeneralSeoErrors } from "../../../../hooks/useGeneralSeoErrors";
import useShop from "../../../../hooks/useShop";
import { useUnsavedChanges } from "../../../../hooks/useUnsavedChanges";
import { getMetaTitleAndDescription, getNoIndexNoFollowFromMeta, scrollToTop } from "../../../../utility/helpers";
import queryKeys from "../../../../utility/queryKeys";

export default function CollectionDetails() {
  const { t } = useTranslation();
  const { id } = useParams();
  const { goToCollection, goToCollectionList, backAction } = useAppNavigation();
  const { handleEditCollection } = useAppBridgeRedirect();
  const queryClient = useQueryClient();
  const { usingMultiLanguageMode } = useMultiLanguageSetting();
  const dispatch = useDispatch();
  // Return to collection list if no id is provided
  if (!id) return goToCollectionList();

  const collectionQueryKey = [queryKeys.COLLECTION_DATA, id];
  // Get collection data with the given id
  const {
    data: collectionData,
    isLoading: isCollectionLoading,
    isError,
  } = useGetCollectionsWithOptimizationData({
    id,
    collectionQueryKey,
  });
  const { collection = {}, pagination } = collectionData || {};

  useEffect(() => {
    if (isError) goToCollectionList();
  }, [isError]);

  const { collectionUrlPrefix } = useShop();
  const featuredImage = useFeaturedImage(collection, analysisEntityTypes.COLLECTION);
  const [formData, setFormData] = React.useState({});
  const [originalFormData, setOriginalFormData] = useState({});

  useEffect(() => {
    if (collectionData && !collectionData.socialMediaDataChanged) {
      const { metaTitle, metaDescription } = getMetaTitleAndDescription(collection);

      const data = {
        metaTitle,
        metaDescription,
        focusKeyword: collection.focus_keyword,
        image: collection.image,
        handle: collection.handle,
      };

      setFormData(data);
      setOriginalFormData(data);
    }
  }, [collectionData]);
  const { score, optimizationData, focusKeywordSuggestions } = useCollectionReanalysis(id, formData, collectionData);

  const { errors: formErrors, setErrors } = useGeneralSeoErrors(formData);
  const [createRedirectUrl, setCreateRedirectUrl] = useState(false);
  const { hasUnsavedChanges, DiscardChangesModal, isURLChanged, setShowDiscardChangeModal } = useUnsavedChanges({
    originalData: originalFormData,
    currentData: formData,
    onDiscardAction: () => {
      setFormData({ ...originalFormData });
      dispatch(resetAiContent());
    },
  });

  useEffect(() => {
    setCreateRedirectUrl(isURLChanged);
  }, [isURLChanged]);

  const handleChange = (key, val) => {
    if (key === "createRedirectUrl") {
      setCreateRedirectUrl(val);
    } else {
      setFormData({ ...formData, [key]: val });
    }
  };

  const {
    mutate: updateCollectionData,
    isLoading: isUpdatingCollection,
    isError: isUpdateCollectionError,
  } = useUpdateCollection({
    onMutate: () => {
      dispatch(resetAiContent());
    },
    onSuccess: (updateCollection) => {
      const { pagination } = queryClient.getQueryData(collectionQueryKey);
      queryClient.setQueryData(collectionQueryKey, {
        collection: { ...updateCollection, score },
        optimizationData,
        focusKeywordSuggestions,
        pagination,
      });
      setErrors({});
    },
  });

  useEffect(() => {
    if (isUpdateCollectionError) scrollToTop();
  }, [isUpdateCollectionError]);

  const handleCollectionUpdate = useCallback(
    () => updateCollectionData({ id, data: { ...formData, createRedirectUrl }, setErrors }),
    [formData, createRedirectUrl]
  );

  // No index and follow toggler logics
  const [noIndexStatus, setNoIndexStatus] = useState(false);
  const [noFollowStatus, setNoFollowStatus] = useState(false);

  useEffect(
    function setNoIndexNoFollowStatusFromMeta() {
      if (!isEmpty(collection)) {
        const { noIndex, noFollow } = getNoIndexNoFollowFromMeta(collection.meta || []);
        setNoIndexStatus(noIndex);
        setNoFollowStatus(noFollow);
      }
    },
    [collection]
  );

  const { mutate: toggleNoIndexStatus, isLoading: isUpdatingNoIndex } = useUpdateCollectionIndexStatus({
    onSuccess: () => setNoIndexStatus(!noIndexStatus),
  });

  const { mutate: toggleNoFollowStatus, isLoading: isUpdatingNoFollow } = useUpdateCollectionFollowStatus({
    onSuccess: () => setNoFollowStatus(!noFollowStatus),
  });

  useEffect(
    function clearErrorStates() {
      setErrors({});
      dispatch(resetAiContent());
      return () => {
        dispatch(resetAiContent());
      };
    },
    [id]
  );

  const { handleAiKeepAll, handleAiRevertAll, isSaved, setIsSaved } = useAiContentOptimizerHelper({
    formData,
    setFormData,
    originalFormData,
  });

  return (
    <>
      {isCollectionLoading && <CollectionFixSkeleton />}
      {!isCollectionLoading && !isEmpty(formData) && (
        <Page
          backAction={backAction}
          title={collection.title}
          titleMetadata={
            !usingMultiLanguageMode ? <AIOptimizationStatusBadge status={collection?.ai_optimization_status} /> : null
          }
          compactTitle
          pagination={{
            hasPrevious: !!pagination?.prev,
            onPrevious: () => goToCollection(pagination?.prev),
            hasNext: !!pagination?.next,
            onNext: () => goToCollection(pagination?.next),
          }}
        >
          <DiscardChangesModal />

          <ContextualSaveBar
            id="optimize-seo-collections-details"
            open={hasUnsavedChanges}
            isLoading={isUpdatingCollection}
            onSave={handleCollectionUpdate}
            onDiscard={() => setShowDiscardChangeModal(true)}
          />
          <BlockStack gap="400">
            <FormErrorBanner
              entityType={analysisEntityTypes.COLLECTION}
              formErrors={formErrors}
              setFormErrors={setErrors}
              handleEditButtonClick={() => handleEditCollection(id, { external: true })}
            />
            <Grid>
              <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 4, lg: 8, xl: 8 }}>
                <BlockStack gap="400">
                  <EditForm
                    featuredImage={featuredImage}
                    focusKeywordSuggestions={focusKeywordSuggestions}
                    data={formData}
                    formErrors={formErrors}
                    onChange={handleChange}
                    originalFormData={originalFormData}
                    isURLChanged={isURLChanged}
                    createRedirectUrl={createRedirectUrl}
                    hideTagsInput={true}
                    entityType={analysisEntityTypes.COLLECTION}
                    urlPrefix={collectionUrlPrefix}
                  />
                  <SocialMediaPreview
                    item={collection}
                    type={analysisEntityTypes.COLLECTION}
                  />
                </BlockStack>
              </Grid.Cell>

              <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 2, lg: 4, xl: 4 }}>
                <BlockStack gap="400">
                  <OptimizationData
                    score={score}
                    optimizations={optimizationData}
                    showEditLink={true}
                    resourceType="collection"
                    onShopifyEditClick={() => handleEditCollection(id, { external: true })} // TODO: Refactor this component as we are currently using false name for this pro(id, {})p
                    aiContentCard={
                      <AiContentGeraratorCard
                        title={collection.title}
                        description={collection.description}
                        focusKeyword={formData?.focusKeyword}
                        setErrors={setErrors}
                        handleKeepAll={handleAiKeepAll}
                        handleRevertAll={handleAiRevertAll}
                        isSaved={isSaved}
                        setIsSaved={setIsSaved}
                        resourceType={analysisEntityTypes.COLLECTION}
                      />
                    }
                  />
                  <AppEmbedWarningBanner template={PAGE_TEMPLATES.FIX} />
                  <NoIndexToggler
                    status={noIndexStatus}
                    onToggle={() => toggleNoIndexStatus(id)}
                    loading={isUpdatingNoIndex}
                  />
                  <NoFollowToggler
                    status={noFollowStatus}
                    onToggle={() => toggleNoFollowStatus(id)}
                    loading={isUpdatingNoFollow}
                  />
                </BlockStack>
              </Grid.Cell>
            </Grid>
          </BlockStack>
          <PageActions
            primaryAction={{
              content: t("Save"),
              onAction: handleCollectionUpdate,
              loading: isUpdatingCollection,
              disabled: !hasUnsavedChanges,
            }}
          />
        </Page>
      )}
    </>
  );
}

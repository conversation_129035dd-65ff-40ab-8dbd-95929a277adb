// // @ts-check
import UsageLimitCard from "@/components/common/UsageLimitCard";
import useUserAddon from "@/hooks/useUserAddon";
import useCollectionAiContentGeneration from "@/lib/hooks/optimize-seo/collections/useCollectionAiContentGeneration";
import useCollectionAiContentRestore from "@/lib/hooks/optimize-seo/collections/useCollectionAiContentRestore";
import { useGetAutoAiOptimizationSettings } from "@/lib/hooks/settings/auto-ai-optimization";
import { default as useIndexTablePagination } from "@/lib/hooks/useIndexTablePagination";
import useIsRunningBackupRestore from "@/lib/hooks/useIsRunningBackupRestore";
import BackupRestoreRunningBanner from "@/modules/components/BackupRestoreRunningBanner";
import BulkResourceAiOptimizationModal from "@/modules/optimize-seo/BulkResourceAiOptimizationModal";
import { default as CollectionsIndexTableFilters } from "@/modules/optimize-seo/collections/CollectionsIndexTableFilters";
import queryKeys from "@/utility/queryKeys";
import {
  Badge,
  BlockStack,
  Button,
  Card,
  IndexTable,
  Page,
  useBreakpoints,
  useIndexResourceState,
} from "@shopify/polaris";
import { omit } from "lodash";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useSearchParams } from "react-router-dom";
import ProductsAiOptimizationStatus from "storeseo-enums/aiOptimization";
import analysisEntityTypes from "storeseo-enums/analysisEntityTypes";
import ResourceType from "storeseo-enums/resourceType";

import AutoAiOptimizationSettingsModal from "@/modules/optimize-seo/AutoAiOptimizationSettingsModal.jsx";
import socketEvents from "storeseo-enums/socketEvents";
import SubscriptionAddonGroup from "storeseo-enums/subscriptionAddonGroup";
import { autoAiOptimizationSchema } from "storeseo-schema/settings/autoAiOptimization";
import CollectionListEmptyState from "../../../components/collections/CollectionListEmptyState";
import CollectionRows from "../../../components/collections/CollectionRows";
import CollectionSyncButton from "../../../components/collections/CollectionSyncButton";
import EmptyPage from "../../../components/common/EmptyPage";
import { HELP_URLS, SORT_DIR } from "../../../config";
import { COLLECTIONS_TABLE_COLUMNS, defaultRowItem, resourceName } from "../../../config/collectionsConfig";
import { useGetCollections } from "../../../hooks/collections";
import { useAppBridgeRedirect } from "../../../hooks/useAppBridgeRedirect";
import { useAppNavigation } from "../../../hooks/useAppNavigation";
import { useBanner } from "../../../hooks/useBanner";
import { usePusher } from "../../../providers/PusherProvider";
import { useStoreSeo } from "../../../providers/StoreSeoProvider";
import { setCollectionCount } from "../../../store/features/CollectionCount";
import { getQueryFromUrlSearchParam, toggleSortDir } from "../../../utility/helpers";

const countSelectableCollections = (collections) => {
  const filteredCollections =
    collections?.filter(
      (item) =>
        item.ai_optimization_status !== ProductsAiOptimizationStatus.PENDING &&
        item.ai_optimization_status !== ProductsAiOptimizationStatus.DISPATCHED &&
        item.ai_optimization_status !== ProductsAiOptimizationStatus.SAVING &&
        item.ai_optimization_status !== ProductsAiOptimizationStatus.PROCESSING
    ) || [];

  return filteredCollections.length;
};

export default function CollectionsOptimizeSeo() {
  const { smDown: isSmallDevice } = useBreakpoints();
  const [sortDir, setSortDir] = useState(SORT_DIR.DESC);
  const [sortIndex, setSortIndex] = useState(6);
  const [isBulkAiContentGenerationModalOpen, setIsBulkAiContentGenerationModalOpen] = useState(false);
  const { isRunning: isRunningBackupOrRestore } = useIsRunningBackupRestore();
  const [isAutoAiOptimizationSettingsModalOpen, setIsAutoAiOptimizationSettingsModalOpen] = useState(false);

  const dispatch = useDispatch();
  const { t } = useTranslation();
  const navigation = useAppNavigation();
  const [searchParams, setSearchParams] = useSearchParams();
  const { doManualRefresh } = useStoreSeo();
  const { pusherChannel } = usePusher();
  const { handleAddCollections } = useAppBridgeRedirect();
  const totalCollectionCount = useSelector((state) => state.collectionCount);
  const { aiOptimizerUsageLimit, aiOptimizerUsageCount } = useUserAddon();
  const query = getQueryFromUrlSearchParam(searchParams);
  const { data: autoAiOptimizeSettings } = useGetAutoAiOptimizationSettings();

  const queryKey = [queryKeys.COLLECTIONS_LIST, query];

  const { data, isLoading, isFetching, refetch, isFetched } = useGetCollections({
    staleTime: 0,
  });

  const { collections = new Array(10).fill(defaultRowItem), pagination = {} } = data || {};
  const emptyCollections = new Array(10).fill(defaultRowItem);
  const hasEmptyContent = isFetched && totalCollectionCount === 0;

  const handleSort = useCallback(
    (index, direction) => {
      setSortIndex(index);
      setSortDir(direction);
      let key = Object.keys(defaultRowItem)[index - 1];
      let newDir = toggleSortDir(direction);
      setSearchParams({
        ...Object.fromEntries(searchParams.entries()),
        sortBy: key,
        sortOrder: newDir === SORT_DIR.ASC ? "ASC" : "DESC",
        page: 1,
      });
    },
    [collections]
  );

  const { Banner, showBanner } = useBanner({
    title: t("Collection sync start failed"),
    message: t(
      "The sync process failed to start. Please try again. If the error persists please contact with our support team."
    ),
  });

  if (doManualRefresh) {
    refetch();
  }
  // refetch on pusher event
  useEffect(() => {
    if (!pusherChannel) return;

    pusherChannel.bind(socketEvents.COLLECTION_SYNC_COMPLETE, refetch);

    return () => {
      pusherChannel.unbind(socketEvents.COLLECTION_SYNC_COMPLETE, refetch);
    };
  }, [pusherChannel]);

  // set collection count to redux store
  useEffect(() => {
    if (data && data.collectionCount !== undefined) dispatch(setCollectionCount(data.collectionCount || 0));
  }, [data]);

  const { paginationConfigs } = useIndexTablePagination(pagination);
  const { selectedResources, allResourcesSelected, handleSelectionChange, clearSelection, removeSelectedResources } =
    useIndexResourceState(collections);
  const selectableCollectionsCount = countSelectableCollections(collections);
  const limitExceeded = aiOptimizerUsageCount + selectedResources.length * 6 > aiOptimizerUsageLimit;

  const {
    mutate: generateAiContent,
    isLoading: isBulkAIContentGenerationLoading,
    isError: isBulkAiContentGenerationError,
  } = useCollectionAiContentGeneration({
    clearSelection,
    queryKey,
    selectedResources,
    setModalOpen: setIsBulkAiContentGenerationModalOpen,
  });

  const { mutate: restoreAIContent, isLoading: isRestoringAIContent } = useCollectionAiContentRestore({
    clearSelection,
    queryKey,
    selectedResources,
  });

  // Remove selected resources if they are in pending or dispatched status
  useEffect(() => {
    const collectionHash = collections?.reduce((hash, item) => ({ ...hash, [item.id]: item }), {});
    const filteredResources = isRunningBackupOrRestore
      ? selectedResources.map((id) => id)
      : selectedResources.filter(
          (id) =>
            collectionHash[id]?.ai_optimization_status === ProductsAiOptimizationStatus.PENDING ||
            collectionHash[id]?.ai_optimization_status === ProductsAiOptimizationStatus.DISPATCHED ||
            collectionHash[id]?.ai_optimization_status === ProductsAiOptimizationStatus.SAVING ||
            collectionHash[id]?.ai_optimization_status === ProductsAiOptimizationStatus.PROCESSING
        );
    if (filteredResources.length) removeSelectedResources(filteredResources);
  }, [selectedResources]);

  const handleListRestore = () => {
    const list = [];
    const resourcesHash = collections.reduce((hash, item) => ({ ...hash, [item.id]: item }), {});
    for (let id of selectedResources) {
      if (resourcesHash[id]?.ai_optimization_status === ProductsAiOptimizationStatus.OPTIMIZED) {
        list.push({
          id,
        });
      }
    }

    restoreAIContent({
      resourceList: list,
      resourceType: analysisEntityTypes.COLLECTION,
    });
  };

  /**
   * @param {import("yup").InferType<typeof autoAiOptimizationSchema>} settings
   */
  const handleBulkAiContentGeneration = (settings) => {
    const list = [];
    const collectionHash = collections?.reduce((hash, item) => ({ ...hash, [item.id]: item }), {});
    for (let id of selectedResources) {
      if (collectionHash[id]) {
        const collection = collectionHash[id];

        list.push({
          id,
          gql_id: collection.collection_id,
        });
      }
    }
    const aiContentSettings = omit(settings, ["imageAltText", "tags"]);
    generateAiContent({
      resourceList: list,
      settings: aiContentSettings,
      resourceType: analysisEntityTypes.COLLECTION,
    });
  };

  return (
    <Page
      fullWidth
      title={t("Optimize SEO - Collections")}
      titleMetadata={
        <Badge tone={autoAiOptimizeSettings?.[ResourceType.COLLECTION]?.status ? "success" : "warning"}>
          {t(
            `Auto AI Optimization: ${
              autoAiOptimizeSettings?.[ResourceType.COLLECTION]?.status ? "Enabled" : "Disabled"
            }`
          )}
        </Badge>
      }
      primaryAction={
        <CollectionSyncButton
          onSyncErr={() => showBanner(true)}
          isDisable={isRunningBackupOrRestore}
        />
      }
      backAction={navigation.backAction}
      secondaryActions={[
        { content: t("Manage Settings"), onAction: () => setIsAutoAiOptimizationSettingsModalOpen(true) },
      ]}
    >
      <BlockStack gap="400">
        <BackupRestoreRunningBanner />
        <Banner />
        {!hasEmptyContent ? (
          <>
            <UsageLimitCard
              title="AI Content Optimizer"
              group={SubscriptionAddonGroup.AI_OPTIMIZER}
              learnMoreButton={{
                title: "What do i do if i need more credits for AI Content Optimizer?",
                url: HELP_URLS.AI_OPTIMIZER,
              }}
              action={{ content: "Increase limit" }}
            />
            <BlockStack gap="400">
              <Card padding="0">
                <CollectionsIndexTableFilters isLoading={isFetching} />

                <IndexTable
                  condensed={isSmallDevice}
                  resourceName={resourceName}
                  itemCount={selectableCollectionsCount || collections?.length || 0}
                  headings={COLLECTIONS_TABLE_COLUMNS.map((item) => ({ ...item, title: t(item.title) }))}
                  sortable={[false, false, true, true, true, true, true, true, false]}
                  onSort={handleSort}
                  sortColumnIndex={sortIndex}
                  sortDirection={sortDir}
                  defaultSortDirection={SORT_DIR.DESC}
                  emptyState={<CollectionListEmptyState />}
                  pagination={paginationConfigs}
                  selectedItemsCount={allResourcesSelected ? t("All") : selectedResources.length}
                  onSelectionChange={handleSelectionChange}
                  promotedBulkActions={[
                    {
                      content: `${t("Restore")} (${selectedResources.length})`,
                      onAction: handleListRestore,
                      disabled: isRestoringAIContent || isRunningBackupOrRestore,
                    },
                    {
                      content: `${t("Optimize with AI")} (${selectedResources.length})`,
                      onAction: () => setIsBulkAiContentGenerationModalOpen(true),
                      disabled: isBulkAIContentGenerationLoading || limitExceeded || isRunningBackupOrRestore,
                    },
                  ]}
                >
                  <CollectionRows
                    collections={isLoading ? emptyCollections : collections}
                    isFetching={isLoading}
                    selectedResources={selectedResources}
                  />
                </IndexTable>
              </Card>

              <BulkResourceAiOptimizationModal
                isOpen={isBulkAiContentGenerationModalOpen}
                setIsOpen={setIsBulkAiContentGenerationModalOpen}
                resourceType={analysisEntityTypes.COLLECTION}
                onAction={(settings) => {
                  handleBulkAiContentGeneration(settings);
                }}
                isError={isBulkAiContentGenerationError}
              />
            </BlockStack>
          </>
        ) : (
          <EmptyPage
            heading="Add or sync your collections with StoreSEO"
            content="Before optimizing, you need to add collections or sync them first"
            withTableFilter
            secondaryAction={<CollectionSyncButton onSyncErr={() => showBanner(true)} />}
            primaryAction={
              <Button
                variant="primary"
                onClick={handleAddCollections}
              >
                {t("Add collections")}
              </Button>
            }
          />
        )}
      </BlockStack>
      <AutoAiOptimizationSettingsModal
        resourceType="COLLECTION"
        isOpen={isAutoAiOptimizationSettingsModalOpen}
        setIsOpen={setIsAutoAiOptimizationSettingsModalOpen}
      />
    </Page>
  );
}

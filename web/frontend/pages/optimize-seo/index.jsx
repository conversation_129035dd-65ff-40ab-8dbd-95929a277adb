import UsageLimitCard from "@/components/common/UsageLimitCard.jsx";
import ProductRows from "@/components/products/ProductRows.jsx";
import { useMultiLanguageSetting } from "@/hooks/useMultiLanguageSetting";
import useUserAddon from "@/hooks/useUserAddon.js";
import useProductAiContentGeneration from "@/lib/hooks/optimize-seo/products/useProductAiContentGeneration.jsx";
import useProductAiContentRestore from "@/lib/hooks/optimize-seo/products/useProductAiContentRestore.jsx";
import { useGetAutoAiOptimizationSettings } from "@/lib/hooks/settings/auto-ai-optimization/index.jsx";
import useIndexTablePagination from "@/lib/hooks/useIndexTablePagination";
import useIsRunningBackupRestore from "@/lib/hooks/useIsRunningBackupRestore.jsx";
import BackupRestoreRunningBanner from "@/modules/components/BackupRestoreRunningBanner.jsx";
import AutoAiOptimizationSettingsModal from "@/modules/optimize-seo/AutoAiOptimizationSettingsModal.jsx";
import BulkResourceAiOptimizationModal from "@/modules/optimize-seo/BulkResourceAiOptimizationModal.jsx";
import ProductsIndexTableFilters from "@/modules/optimize-seo/products/ProductsIndexTableFilters.jsx";
import { usePusher } from "@/providers/PusherProvider.jsx";
import { getQueryFromUrlSearchParam } from "@/utility/helpers";
import {
  Badge,
  BlockStack,
  Button,
  Card,
  IndexTable,
  Page,
  useBreakpoints,
  useIndexResourceState,
} from "@shopify/polaris";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useSearchParams } from "react-router-dom";
import ProductsAiOptimizationStatus from "storeseo-enums/aiOptimization";
import analysisEntityTypes from "storeseo-enums/analysisEntityTypes";
import ResourceType from "storeseo-enums/resourceType";
import SocketEvents from "storeseo-enums/socketEvents";
import { AI_OPTIMIZER } from "storeseo-enums/subscriptionAddonGroup";
import { autoAiOptimizationSchema } from "storeseo-schema/settings/autoAiOptimization";
import EmptyPage from "../../components/common/EmptyPage.jsx";
import TableEmptyState from "../../components/common/TableEmptyState.jsx";
import ProductSyncButton from "../../components/products/ProductSyncButton";
import { HELP_URLS, SORT_DIR } from "../../config/index.js";
import { useAppQuery, useProductApi } from "../../hooks/index";
import { useAppBridgeRedirect } from "../../hooks/useAppBridgeRedirect.js";
import { useAppNavigation } from "../../hooks/useAppNavigation.js";
import { useBanner } from "../../hooks/useBanner.jsx";
import { useStoreSeo } from "../../providers/StoreSeoProvider";
import { setProductCount } from "../../store/features/ProductCount";
import queryKeys from "../../utility/queryKeys";

const countSelectableProducts = (products) => {
  const filteredProducts =
    products?.filter(
      (item) =>
        item.ai_optimization_status !== ProductsAiOptimizationStatus.PENDING &&
        item.ai_optimization_status !== ProductsAiOptimizationStatus.DISPATCHED &&
        item.ai_optimization_status !== ProductsAiOptimizationStatus.SAVING &&
        item.ai_optimization_status !== ProductsAiOptimizationStatus.PROCESSING
    ) || [];

  return filteredProducts.length;
};

function Products() {
  const { smDown: isSmallDevice } = useBreakpoints();
  const { t } = useTranslation();
  const { isRunning: isRunningBackupOrRestore } = useIsRunningBackupRestore();
  const dispatch = useDispatch();
  const productApi = useProductApi();
  const { doManualRefresh } = useStoreSeo();
  const [searchParams, setSearchParams] = useSearchParams();
  const query = getQueryFromUrlSearchParam(searchParams);
  const [sortDir, setSortDir] = useState(() => SORT_DIR.DESC);
  const [sortIndex, setSortIndex] = useState(() => 6);
  const navigation = useAppNavigation();
  const { aiOptimizerUsageLimit, aiOptimizerUsageCount } = useUserAddon();
  const [isBulkAiContentGenerationModalOpen, setIsBulkAiContentGenerationModalOpen] = useState(false);
  const { pusherChannel } = usePusher();
  const [isAutoAiOptimizationSettingsModalOpen, setIsAutoAiOptimizationSettingsModalOpen] = useState(false);

  const queryKey = [queryKeys.PRODUCTS_LIST, query];
  const totalProductCount = useSelector((state) => state.productCount);

  const { data, isFetching, refetch, isFetched, isLoading } = useAppQuery({
    queryKey: queryKey,
    queryFn: async () => await productApi.getPaginatedProductsList(query),
    reactQueryOptions: {
      onSuccess: (data) => {
        dispatch(setProductCount(data.productCount || 0));
      },
      refetchInterval: false,
    },
  });

  useEffect(() => {
    if (doManualRefresh) {
      refetch();
    }
  }, [doManualRefresh]);

  // refetch on pusher event
  useEffect(() => {
    if (!pusherChannel) return;

    pusherChannel.bind(SocketEvents.PRODUCT_SYNC_COMPLETE, refetch);

    return () => {
      pusherChannel.unbind(SocketEvents.PRODUCT_SYNC_COMPLETE, refetch);
    };
  }, [pusherChannel]);

  const defaultRowItem = {
    // id: null,
    title: null,
    optimize_status: null,
    focus_keyword: null,
    issues: null,
    score: null,
    created_at: null,
  };
  const { products = new Array(10).fill(defaultRowItem), pagination = {} } = data || {};

  const handleSort = useCallback(
    (index, direction) => {
      setSortIndex(index);
      setSortDir(direction);
      let key = Object.keys(defaultRowItem)[index - 1];
      let newDir = toggleSortDir(direction);

      setSearchParams({
        ...Object.fromEntries(searchParams.entries()),
        sortBy: key,
        sortOrder: newDir === SORT_DIR.ASC ? "ASC" : "DESC",
        page: 1,
      });
    },
    [products]
  );

  const toggleSortDir = (direction) => {
    switch (direction) {
      case SORT_DIR.ASC:
        return SORT_DIR.DESC;
      case SORT_DIR.DESC:
        return SORT_DIR.ASC;
      default:
        return SORT_DIR.DESC;
    }
  };

  const { Banner, showBanner } = useBanner({
    title: t("Product sync start failed"),
    message: t(
      "The sync process failed to start. Please try again. If the error persists please contact with our support team."
    ),
  });

  const resourceName = {
    singular: "product",
    plural: "products",
  };

  const hasEmptyContent = isFetched && totalProductCount === 0;
  const emptyProducts = new Array(10).fill(defaultRowItem);

  // if (isFetching) return <DummyIndexPageSkeleton />;

  const { handleAddProducts } = useAppBridgeRedirect();

  const { selectedResources, allResourcesSelected, handleSelectionChange, clearSelection, removeSelectedResources } =
    useIndexResourceState(products);
  const selectableProductsCount = countSelectableProducts(products);
  const limitExceeded = aiOptimizerUsageCount + selectedResources.length * 6 > aiOptimizerUsageLimit;

  const { mutate: restoreAIContent, isLoading: isRestoringAIContent } = useProductAiContentRestore({
    clearSelection,
    queryKey,
    selectedResources,
  });

  const handleListRestore = () => {
    const list = [];
    const resourcesHash = products.reduce((hash, item) => ({ ...hash, [item.id]: item }), {});
    for (let id of selectedResources) {
      if (resourcesHash[id]?.ai_optimization_status === ProductsAiOptimizationStatus.OPTIMIZED) {
        list.push({
          id,
        });
      }
    }

    restoreAIContent({
      resourceList: list,
      resourceType: ResourceType.PRODUCT,
    });
  };

  const {
    mutate: generateAiContent,
    isLoading: isBulkAIContentGenerationLoading,
    isError: isBulkAiContentGenerationError,
  } = useProductAiContentGeneration({
    clearSelection,
    queryKey,
    selectedResources,
    setModalOpen: setIsBulkAiContentGenerationModalOpen,
  });

  /**
   *
   * @param {import("yup").InferType<typeof autoAiOptimizationSchema>} settings
   */
  const handleBulkAiContentGeneration = (settings) => {
    /**
     * @type {{id: number, media_id: string}[]}
     */
    const list = [];
    const productHash = products?.reduce((hash, item) => ({ ...hash, [item.id]: item }), {});
    for (let id of selectedResources) {
      if (productHash[id]) {
        const product = productHash[id];

        list.push({
          id,
          gql_id: product.product_id,
        });
      }
    }

    generateAiContent({ resourceList: list, settings, resourceType: ResourceType.PRODUCT });
  };

  // Remove selected resources if they are in pending or dispatched status
  useEffect(() => {
    const productHash = products?.reduce((hash, item) => ({ ...hash, [item.id]: item }), {});
    const filteredResources = isRunningBackupOrRestore
      ? selectedResources.map((id) => id)
      : selectedResources.filter(
          (id) =>
            productHash[id]?.ai_optimization_status === ProductsAiOptimizationStatus.PENDING ||
            productHash[id]?.ai_optimization_status === ProductsAiOptimizationStatus.DISPATCHED ||
            productHash[id]?.ai_optimization_status === ProductsAiOptimizationStatus.SAVING ||
            productHash[id]?.ai_optimization_status === ProductsAiOptimizationStatus.PROCESSING
        );
    if (filteredResources.length) removeSelectedResources(filteredResources);
  }, [selectedResources]);

  const emptyStateMarkup = (
    <TableEmptyState
      title="No products found"
      description="Try changing the filters or search term"
      withIllustration
    />
  );
  const { paginationConfigs } = useIndexTablePagination(pagination);
  const { data: autoAiOptimizeSettings } = useGetAutoAiOptimizationSettings();

  const { languageActions, usingMultiLanguageMode } = useMultiLanguageSetting();

  return (
    <Page
      fullWidth
      title={t("Optimize SEO - Products")}
      titleMetadata={
        <Badge tone={autoAiOptimizeSettings?.[ResourceType.PRODUCT]?.status ? "success" : "warning"}>
          {t(
            `Auto AI Optimization: ${autoAiOptimizeSettings?.[ResourceType.PRODUCT]?.status ? "Enabled" : "Disabled"}`
          )}
        </Badge>
      }
      backAction={navigation.backAction}
      primaryAction={
        <ProductSyncButton
          onSyncErr={() => showBanner(true)}
          isDisable={isRunningBackupOrRestore}
        />
      }
      actionGroups={languageActions}
      secondaryActions={[
        { content: t("Manage Settings"), onAction: () => setIsAutoAiOptimizationSettingsModalOpen(true) },
      ]}
    >
      <BlockStack gap="400">
        <BackupRestoreRunningBanner />

        <Banner />

        {!hasEmptyContent ? (
          <>
            <UsageLimitCard
              title="AI Content Optimizer"
              group={AI_OPTIMIZER}
              learnMoreButton={{
                title: "What do i do if i need more credits for AI Content Optimizer?",
                url: HELP_URLS.AI_OPTIMIZER,
              }}
              action={{ content: "Increase limit" }}
            />
            <BlockStack gap="400">
              <Card padding="0">
                <ProductsIndexTableFilters isLoading={isFetching} />

                <IndexTable
                  condensed={isSmallDevice}
                  resourceName={resourceName}
                  itemCount={selectableProductsCount || products?.length || 0}
                  headings={[
                    { title: "" },
                    { title: t("Product") },
                    { title: t("Optimize Status") },
                    { title: t("Focus Keyword") },
                    { title: t("Issues") },
                    { title: t("Score") },
                    { title: t("Date") },
                    { title: t("Action"), alignment: "center" },
                  ]}
                  selectable={!usingMultiLanguageMode}
                  selectedItemsCount={allResourcesSelected ? "All" : selectedResources.length}
                  onSelectionChange={handleSelectionChange}
                  sortable={[false, true, true, true, true, true, true, false]}
                  onSort={handleSort}
                  sortColumnIndex={sortIndex}
                  sortDirection={sortDir}
                  defaultSortDirection={SORT_DIR.DESC}
                  emptyState={emptyStateMarkup}
                  pagination={paginationConfigs}
                  promotedBulkActions={[
                    {
                      content: `${t("Restore")} (${selectedResources.length})`,
                      onAction: handleListRestore,
                      disabled: isRestoringAIContent || isRunningBackupOrRestore,
                    },
                    {
                      content: `${t("Optimize with AI")} (${selectedResources.length})`,
                      onAction: () => setIsBulkAiContentGenerationModalOpen(true),
                      disabled: isBulkAIContentGenerationLoading || limitExceeded || isRunningBackupOrRestore,
                    },
                  ]}
                >
                  <ProductRows
                    products={isLoading ? emptyProducts : products}
                    isFetching={isLoading}
                    selectedResources={selectedResources}
                  />
                </IndexTable>
              </Card>

              <BulkResourceAiOptimizationModal
                isOpen={isBulkAiContentGenerationModalOpen}
                setIsOpen={setIsBulkAiContentGenerationModalOpen}
                resourceType={analysisEntityTypes.PRODUCT}
                onAction={(settings) => {
                  handleBulkAiContentGeneration(settings);
                }}
                isError={isBulkAiContentGenerationError}
              />
            </BlockStack>
          </>
        ) : (
          <EmptyPage
            heading="Add or sync your products with StoreSEO"
            content="Before optimizing, you need to add products or sync them first"
            withTableFilter
            secondaryAction={<ProductSyncButton />}
            primaryAction={
              <Button
                variant="primary"
                onClick={handleAddProducts}
              >
                {t("Add products")}
              </Button>
            }
          />
        )}
      </BlockStack>
      <AutoAiOptimizationSettingsModal
        resourceType="PRODUCT"
        isOpen={isAutoAiOptimizationSettingsModalOpen}
        setIsOpen={setIsAutoAiOptimizationSettingsModalOpen}
      />
    </Page>
  );
}

export default Products;

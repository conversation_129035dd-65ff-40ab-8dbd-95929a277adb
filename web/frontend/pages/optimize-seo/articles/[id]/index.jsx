import AiContentGeraratorCard from "@/components/ai/AiContentGeraratorCard";
import { FormErrorBanner } from "@/components/common/FormErrorBanner";
import { useMultiLanguageSetting } from "@/hooks/useMultiLanguageSetting";
import useAiContentOptimizerHelper from "@/lib/hooks/optimize-seo/useAiContentOptimizerHelper";
import ContextualSaveBar from "@/modules/components/ContextualSaveBar";
import AIOptimizationStatusBadge from "@/modules/optimize-seo/AIOptimizationStatusBadge";
import { resetAiContent } from "@/store/features/AiContent";
import { BlockStack, Grid, Page, PageActions } from "@shopify/polaris";
import { isEmpty } from "lodash";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation, useQueryClient } from "react-query";
import { useDispatch } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { default as analysisEntityTypes, default as types } from "storeseo-enums/analysisEntityTypes";
import AltTextEditForm from "../../../../components/common/AltTextEditForm";
import AppEmbedWarningBanner from "../../../../components/common/AppEmbedWarningBanner";
import EditForm from "../../../../components/common/EditForm";
import OptimizationData from "../../../../components/common/OptimizationData";
import SyncButton from "../../../../components/common/SyncButton";
import DummyProductFix from "../../../../components/loader/DummyProductFix";
import SocialMediaPreview from "../../../../components/previewers/SocialMediaPreview";
import NoFollowToggler from "../../../../components/togglers/NoFollowToggler";
import NoIndexToggler from "../../../../components/togglers/NoIndexToggler";
import { PAGE_TEMPLATES, previewImgType } from "../../../../config";
import { useAppQuery } from "../../../../hooks";
import { useBlogApi } from "../../../../hooks/apiHooks/useBlogApi";
import { useAppBridgeRedirect } from "../../../../hooks/useAppBridgeRedirect";
import { useAppNavigation } from "../../../../hooks/useAppNavigation";
import { useArticleReanalysis } from "../../../../hooks/useArticleReanalysis";
import { useBanner } from "../../../../hooks/useBanner";
import { useFeaturedImage } from "../../../../hooks/useFeaturedImage";
import { useGeneralSeoErrors } from "../../../../hooks/useGeneralSeoErrors";
import { useUnsavedChanges } from "../../../../hooks/useUnsavedChanges";
import { getMetaTitleAndDescription, getNoIndexNoFollowFromMeta, scrollToTop } from "../../../../utility/helpers";
import queryKeys from "../../../../utility/queryKeys";

export default function Article() {
  const { id } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const blogApi = useBlogApi();
  const queryClient = useQueryClient();
  const { goToArticle, goToArticleList, backAction } = useAppNavigation();
  const { handleEditArticle } = useAppBridgeRedirect();
  const dispatch = useDispatch();
  const { usingMultiLanguageMode } = useMultiLanguageSetting();

  const articleQueryKey = [queryKeys.ARTICLE_DATA, { id }];
  const {
    data: apiData,
    isLoading: isLoadingArticle,
    refetch,
  } = useAppQuery({
    queryKey: articleQueryKey,
    queryFn: () => blogApi.getArticleWithOptimizationData(id),
    reactQueryOptions: {
      staleTime: 0,
      refetchInterval: false,
      onError: (err) => {
        goToArticleList();
      },
    },
  });

  const { article = {}, pagination } = apiData || {};

  const [originalFormData, setOriginalFormData] = useState({});
  const [formData, setFormData] = useState({});
  const [noIndexStatus, setNoIndexStatus] = useState(false);
  const [noFollowStatus, setNoFollowStatus] = useState(false);
  const [createRedirectUrl, setCreateRedirectUrl] = useState(false);
  const [urlPrefix, setUrlPrefix] = useState("");
  const featuredImage = useFeaturedImage(article, analysisEntityTypes.ARTICLE);
  const { score, optimizationData, focusKeywordSuggestions } = useArticleReanalysis(id, formData, apiData);
  const { hasUnsavedChanges, DiscardChangesModal, isURLChanged, setShowDiscardChangeModal } = useUnsavedChanges({
    originalData: originalFormData,
    currentData: formData,
    onDiscardAction: () => {
      setFormData({ ...originalFormData });
      dispatch(resetAiContent());
    },
  });

  const { errors: formErrors, setErrors } = useGeneralSeoErrors(formData);

  const { mutate: toggleNoIndexStatus, isLoading: isUpdatingNoIndex } = useMutation({
    mutationFn: () => blogApi.toggleNoIndexStatus(id),
    onSuccess: () => {
      setNoIndexStatus(!noIndexStatus);
    },
  });

  const { mutate: toggleNoFollowStatus, isLoading: isUpdatingNoFollow } = useMutation({
    mutationFn: () => blogApi.toggleNoFollowStatus(id),
    onSuccess: () => {
      setNoFollowStatus(!noFollowStatus);
    },
  });

  const { mutate: updateArticleData, isLoading: isUpdatingArticle } = useMutation({
    onMutate: () => {
      dispatch(resetAiContent());
    },
    mutationFn: () => blogApi.updateArticle(id, { ...formData, createRedirectUrl }, setErrors),
    onSuccess: (updatedArticle) => {
      const { pagination } = queryClient.getQueryData(articleQueryKey);

      queryClient.setQueryData(articleQueryKey, {
        article: { ...updatedArticle, score },
        optimizationData,
        focusKeywordSuggestions,
        pagination,
      });
      setErrors({});
    },
    onError: (err) => {
      scrollToTop();
    },
  });

  const { mutate: syncArticleFromShopify, isLoading: isSyncing } = useMutation({
    mutationFn: () => blogApi.syncArticleFromShopify(id),
    onSuccess: refetch,
    onError: (error) => {
      if (error.status === 404) {
        return navigate("/optimize-seo/articles/notFound");
      }
      showBanner(true);
    },
  });

  useEffect(() => {
    if (apiData && !apiData.socialMediaDataChanged) {
      const { metaTitle, metaDescription } = getMetaTitleAndDescription(apiData.article);

      const data = {
        metaTitle,
        metaDescription,
        focusKeyword: apiData.article.focus_keyword,
        tags: apiData.article.tags,
        image: apiData.article.image,
        handle: apiData.article.handle,
      };

      setUrlPrefix(apiData.article?.prefix);
      setFormData(data);
      setOriginalFormData(data);
    }
  }, [apiData]);

  useEffect(() => {
    setCreateRedirectUrl(isURLChanged);
  }, [isURLChanged]);

  useEffect(
    function setNoIndexNoFollowStatusFromMeta() {
      if (!isEmpty(article)) {
        const { noIndex, noFollow } = getNoIndexNoFollowFromMeta(article.meta || []);
        setNoIndexStatus(noIndex);
        setNoFollowStatus(noFollow);
      }
    },
    [article]
  );

  useEffect(
    function clearErrorStates() {
      setErrors({});
      showBanner(false);
      dispatch(resetAiContent());
      return () => {
        dispatch(resetAiContent());
      };
    },
    [id]
  );

  const onUploadStart = ({ dataURL, file, imageInputFor }) => {
    const { article, ...rest } = queryClient.getQueryData(articleQueryKey);
    queryClient.setQueryData(articleQueryKey, {
      article: {
        ...article,
        facebookPreviewImage: imageInputFor === previewImgType.FACEBOOK ? dataURL : article.facebookPreviewImage,
        twitterPreviewImage: imageInputFor === previewImgType.TWITTER ? dataURL : article.twitterPreviewImage,
      },
      ...rest,
      socialMediaDataChanged: true,
    });
  };

  const handleChange = (key, val) => {
    if (key === "createRedirectUrl") {
      setCreateRedirectUrl(val);
    } else {
      setFormData({ ...formData, [key]: val });
    }
  };

  const { Banner, showBanner } = useBanner({
    title: t("Article sync failed"),
    message: t(
      "The sync process failed to complete. Please try again. If the error persists please contact with our support team."
    ),
  });

  const { handleAiKeepAll, handleAiRevertAll, isSaved, setIsSaved } = useAiContentOptimizerHelper({
    formData,
    setFormData,
    originalFormData,
  });

  return (
    <>
      {isLoadingArticle && <DummyProductFix />}
      {!isLoadingArticle && !isEmpty(formData) && (
        <Page
          backAction={backAction}
          title={article.title}
          compactTitle
          titleMetadata={
            !usingMultiLanguageMode ? <AIOptimizationStatusBadge status={article?.ai_optimization_status} /> : null
          }
          secondaryActions={
            <>
              <SyncButton
                syncState={{ ongoing: isSyncing }}
                title={t("blog post")}
                callback={syncArticleFromShopify}
              />
              <span style={{ marginRight: "8px" }}></span>
            </>
          }
          pagination={{
            hasPrevious: !!pagination?.prev,
            onPrevious: () => goToArticle(pagination?.prev),
            hasNext: !!pagination?.next,
            onNext: () => goToArticle(pagination?.next),
          }}
        >
          <DiscardChangesModal />

          <ContextualSaveBar
            id="optimize-seo-articles-details"
            open={hasUnsavedChanges}
            isLoading={isUpdatingArticle}
            onSave={updateArticleData}
            onDiscard={() => setShowDiscardChangeModal(true)}
          />

          <Banner />
          <BlockStack gap="400">
            <FormErrorBanner
              entityType={analysisEntityTypes.ARTICLE}
              formErrors={formErrors}
              setFormErrors={setErrors}
              handleEditButtonClick={() => handleEditArticle(id, { external: true })}
            />
            <Grid>
              <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 4, lg: 8, xl: 8 }}>
                <BlockStack gap="400">
                  <EditForm
                    title={article.title}
                    featuredImage={featuredImage}
                    focusKeywordSuggestions={focusKeywordSuggestions}
                    data={formData}
                    formErrors={formErrors}
                    onChange={handleChange}
                    urlPrefix={urlPrefix}
                    originalFormData={originalFormData}
                    isURLChanged={isURLChanged}
                    createRedirectUrl={createRedirectUrl}
                    entityType={analysisEntityTypes.ARTICLE}
                  />
                  <AltTextEditForm
                    title={article.title}
                    images={formData.image ? [{ ...formData.image, altText: formData.image.alt }] : []}
                    onChange={(images) => {
                      setFormData({ ...formData, image: { ...images[0], alt: images[0].altText } });
                    }}
                    emptyStateMsg={t("Add featured image to your blog post first")}
                    emptyStateActionLabel={t("Add featured image")}
                    onEmptyStateAction={() => handleEditArticle(id, { external: true })}
                    imageOptimizerEnabled={true}
                    imageAltTextGeneratorEnabled={false}
                    entityType={analysisEntityTypes.ARTICLE}
                  />
                  <SocialMediaPreview
                    item={article}
                    type={types.ARTICLE}
                  />
                </BlockStack>
              </Grid.Cell>

              <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 2, lg: 4, xl: 4 }}>
                <BlockStack gap="400">
                  <OptimizationData
                    score={score}
                    optimizations={optimizationData}
                    aiContentCard={
                      <AiContentGeraratorCard
                        title={article.title}
                        description={article.body_html}
                        focusKeyword={formData?.focusKeyword}
                        setErrors={setErrors}
                        handleKeepAll={handleAiKeepAll}
                        handleRevertAll={handleAiRevertAll}
                        isSaved={isSaved}
                        setIsSaved={setIsSaved}
                        resourceType={analysisEntityTypes.ARTICLE}
                      />
                    }
                  />

                  <AppEmbedWarningBanner template={PAGE_TEMPLATES.FIX} />
                  <NoIndexToggler
                    status={noIndexStatus}
                    onToggle={toggleNoIndexStatus}
                    loading={isUpdatingNoIndex}
                  />
                  <NoFollowToggler
                    status={noFollowStatus}
                    onToggle={toggleNoFollowStatus}
                    loading={isUpdatingNoFollow}
                  />
                </BlockStack>
              </Grid.Cell>
            </Grid>
          </BlockStack>
          <PageActions
            primaryAction={{
              content: t("Save"),
              onAction: updateArticleData,
              loading: isUpdatingArticle,
              disabled: !hasUnsavedChanges,
            }}
          />
        </Page>
      )}
    </>
  );
}

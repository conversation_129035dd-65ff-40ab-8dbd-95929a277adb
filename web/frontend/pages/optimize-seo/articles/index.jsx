import UsageLimitCard from "@/components/common/UsageLimitCard";
import { HELP_URLS } from "@/config";
import { useSyncBlogPosts } from "@/hooks/blogposts/index.jsx";
import useUserAddon from "@/hooks/useUserAddon";
import useArticleAiContentGeneration from "@/lib/hooks/optimize-seo/articles/useArticleAiContentGeneration";
import useArticleAiContentRestore from "@/lib/hooks/optimize-seo/articles/useArticleAiContentRestore";
import { useGetAutoAiOptimizationSettings } from "@/lib/hooks/settings/auto-ai-optimization";
import useIndexTablePagination from "@/lib/hooks/useIndexTablePagination.jsx";
import useIsRunningBackupRestore from "@/lib/hooks/useIsRunningBackupRestore";
import BackupRestoreRunningBanner from "@/modules/components/BackupRestoreRunningBanner";
import ArticlesIndexTableFilters from "@/modules/optimize-seo/articles/ArticlesIndexTableFilters.jsx";
import AutoAiOptimizationSettingsModal from "@/modules/optimize-seo/AutoAiOptimizationSettingsModal.jsx";
import BulkResourceAiOptimizationModal from "@/modules/optimize-seo/BulkResourceAiOptimizationModal";
import {
  Badge,
  BlockStack,
  Button,
  Card,
  Icon,
  IndexTable,
  Page,
  useBreakpoints,
  useIndexResourceState,
} from "@shopify/polaris";
import { MagicIcon, ResetIcon } from "@shopify/polaris-icons";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";
import { useSelector } from "react-redux";
import { useSearchParams } from "react-router-dom";
import ProductsAiOptimizationStatus from "storeseo-enums/aiOptimization";
import analysisEntityTypes from "storeseo-enums/analysisEntityTypes";
import ResourceType from "storeseo-enums/resourceType";
import socketEvents from "storeseo-enums/socketEvents";
import { AI_OPTIMIZER } from "storeseo-enums/subscriptionAddonGroup";
import { autoAiOptimizationSchema } from "storeseo-schema/settings/autoAiOptimization";
import ArticleRows from "../../../components/articles/ArticleRows.jsx";
import BlogAutoWriteModal from "../../../components/blog-auto-write/BlogAutoWriteModal.jsx";
import EmptyPage from "../../../components/common/EmptyPage.jsx";
import SyncButton from "../../../components/common/SyncButton";
import TableEmptyState from "../../../components/common/TableEmptyState.jsx";
import ConfirmationModal from "../../../components/modals/ConfirmationModal.jsx";
import { SORT_DIR, browserEvents, emitter } from "../../../config";
import { useAppQuery } from "../../../hooks";
import { useBlogApi } from "../../../hooks/apiHooks/useBlogApi";
import { useAppBridgeRedirect } from "../../../hooks/useAppBridgeRedirect.js";
import { useAppNavigation } from "../../../hooks/useAppNavigation.js";
import { useBanner } from "../../../hooks/useBanner.jsx";
import { usePusher } from "../../../providers/PusherProvider";
import { useStoreSeo } from "../../../providers/StoreSeoProvider";
import { getQueryFromUrlSearchParam } from "../../../utility/helpers";
import queryKeys from "../../../utility/queryKeys";

const ARTICLES_TABLE_COLUMNS = [
  { title: "" },
  { title: "Post" },
  { title: "Blog" },
  { title: "Optimize Status" },
  { title: "Focus Keyword" },
  { title: "Issues" },
  { title: "Score" },
  { title: "Date" },
  { title: "Status" },
  { title: "Action", alignment: "center" },
];
const countSelectableArticles = (articles) => {
  const filteredArticles =
    articles?.filter(
      (item) =>
        item.ai_optimization_status !== ProductsAiOptimizationStatus.PENDING &&
        item.ai_optimization_status !== ProductsAiOptimizationStatus.DISPATCHED &&
        item.ai_optimization_status !== ProductsAiOptimizationStatus.SAVING &&
        item.ai_optimization_status !== ProductsAiOptimizationStatus.PROCESSING
    ) || [];
  return filteredArticles.length;
};

export default function Blogs() {
  const { smDown: isSmallDevice } = useBreakpoints();
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const blogSync = useSelector((state) => state.blogSync);
  const blogApi = useBlogApi();
  const [searchParams, setSearchParams] = useSearchParams();
  const query = getQueryFromUrlSearchParam(searchParams);
  const [sortDir, setSortDir] = useState(() => SORT_DIR.DESC);
  const [sortIndex, setSortIndex] = useState(() => 6);
  const { doManualRefresh } = useStoreSeo();
  const { pusherChannel } = usePusher();
  const navigation = useAppNavigation();
  const [isBulkAiContentGenerationModalOpen, setIsBulkAiContentGenerationModalOpen] = useState(false);
  const [isBlogAutoWriteModalOpen, setIsBlogAutoWriteModalOpen] = useState(false);
  const [syncTooltipContent, setSyncTooltipContent] = useState("");
  const [showSyncConfirmModal, setShowSyncConfirmModal] = useState(false);
  const { isRunning: isRunningBackupOrRestore } = useIsRunningBackupRestore();
  const { aiOptimizerUsageLimit, aiOptimizerUsageCount } = useUserAddon();
  const { data: autoAiOptimizeSettings } = useGetAutoAiOptimizationSettings();
  const [isAutoAiOptimizationSettingsModalOpen, setIsAutoAiOptimizationSettingsModalOpen] = useState(false);

  const queryKey = [queryKeys.ARTICLES_LIST, query];

  const { data, isLoading, isFetching, refetch, isFetched } = useAppQuery({
    queryKey,
    queryFn: () => blogApi.getArticlesWithPagination(query),
    reactQueryOptions: {
      staleTime: 0,
      onSettled: () => {
        emitter.emit(browserEvents.PAGE_DATA_LOADED);
      },
    },
  });

  const defaultRowItem = {
    id: null,
    title: null,
    blog_title: null,
    optimize_status: null,
    focus_keyword: null,
    issues: null,
    score: null,
    created_at: null,
    status: null,
  };
  const { articles = new Array(10).fill(defaultRowItem), pagination = {}, articleCount = 0 } = data || {};

  const { mutate: syncBlogs, isLoading: isStartingSync } = useSyncBlogPosts({
    onError: () => showBanner(true),
  });

  useEffect(() => {
    if (!pusherChannel) return;

    pusherChannel.bind(socketEvents.BLOG_SYNC_COMPLETE, refetch);
  }, [pusherChannel]);

  const handleBlogSyncTriggerViaEvent = useCallback(() => {
    setIsBlogAutoWriteModalOpen(false);
    setShowSyncConfirmModal(true);
  }, [setShowSyncConfirmModal, setIsBlogAutoWriteModalOpen]);

  useEffect(function startBlogSyncOnEventTrigger() {
    emitter.on(browserEvents.TRIGGER_BLOG_SYNC, handleBlogSyncTriggerViaEvent);

    return () => {
      emitter.off(browserEvents.TRIGGER_BLOG_SYNC, handleBlogSyncTriggerViaEvent);
    };
  }, []);

  // Update sync tooltip content based on sync state
  useEffect(() => {
    if (blogSync?.ongoing && blogSync?.total) {
      setSyncTooltipContent(
        t("Syncing blog posts {{current}}/{{total}}", {
          current: blogSync.current || "...",
          total: blogSync.total,
        })
      );
    } else if (blogSync?.ongoing) {
      setSyncTooltipContent(t("Syncing blog posts..."));
    } else {
      setSyncTooltipContent("");
    }
  }, [blogSync, t]);

  if (doManualRefresh) queryClient.invalidateQueries(queryKeys.ARTICLES_LIST);

  const handleSort = useCallback(
    (index, direction) => {
      setSortIndex(index);
      setSortDir(direction);
      let key = Object.keys(defaultRowItem)[index];
      let newDir = toggleSortDir(direction);

      setSearchParams({
        ...Object.fromEntries(searchParams.entries()),
        sortBy: key,
        sortOrder: newDir === SORT_DIR.ASC ? "ASC" : "DESC",
        page: 1,
      });
    },
    [articles]
  );

  const { Banner, showBanner } = useBanner({
    title: t("Blog posts sync start failed"),
    message: t(
      "The sync process failed to start. Please try again. If the error persists please contact with our support team."
    ),
  });

  const toggleSortDir = (direction) => {
    switch (direction) {
      case SORT_DIR.ASC:
        return SORT_DIR.DESC;
      case SORT_DIR.DESC:
        return SORT_DIR.ASC;
      default:
        return SORT_DIR.DESC;
    }
  };

  const resourceName = {
    singular: "blog post",
    plural: "blog posts",
  };

  const hasEmptyContent = isFetched && articleCount === 0;
  const emptyArticles = new Array(10).fill(defaultRowItem);

  const { handleAddArticles } = useAppBridgeRedirect();

  const handleOpenBlogAutoWrite = useCallback(() => {
    setIsBlogAutoWriteModalOpen(true);
  }, []);

  const handleSyncBlogPosts = useCallback(() => {
    setShowSyncConfirmModal(true);
  }, []);

  const handleConfirmSync = useCallback(async () => {
    await syncBlogs();
    setShowSyncConfirmModal(false);
  }, [syncBlogs]);

  const emptyStateMarkup = (
    <TableEmptyState
      title="No blog posts found"
      description="Try changing the filters or search term"
      withIllustration
    />
  );

  const { paginationConfigs } = useIndexTablePagination(pagination);

  const { selectedResources, allResourcesSelected, handleSelectionChange, clearSelection, removeSelectedResources } =
    useIndexResourceState(articles);

  const selectableArticlesCount = countSelectableArticles(articles);

  const limitExceeded = aiOptimizerUsageCount + selectedResources.length * 6 > aiOptimizerUsageLimit;

  const {
    mutate: generateAiContent,
    isLoading: isBulkAIContentGenerationLoading,
    isError: isBulkAiContentGenerationError,
  } = useArticleAiContentGeneration({
    clearSelection,
    queryKey,
    selectedResources,
    setModalOpen: setIsBulkAiContentGenerationModalOpen,
  });

  const { mutate: restoreAIContent, isLoading: isRestoringAIContent } = useArticleAiContentRestore({
    clearSelection,
    queryKey,
    selectedResources,
  });

  // Remove selected resources if they are in pending or dispatched status
  useEffect(() => {
    const articleHash = articles?.reduce((hash, item) => ({ ...hash, [item.id]: item }), {});
    const filteredResources = isRunningBackupOrRestore
      ? selectedResources.map((id) => id)
      : selectedResources.filter(
          (id) =>
            articleHash[id]?.ai_optimization_status === ProductsAiOptimizationStatus.PENDING ||
            articleHash[id]?.ai_optimization_status === ProductsAiOptimizationStatus.DISPATCHED ||
            articleHash[id]?.ai_optimization_status === ProductsAiOptimizationStatus.SAVING ||
            articleHash[id]?.ai_optimization_status === ProductsAiOptimizationStatus.PROCESSING
        );
    if (filteredResources.length) removeSelectedResources(filteredResources);
  }, [selectedResources]);

  const handleListRestore = () => {
    const list = [];
    const resourcesHash = articles.reduce((hash, item) => ({ ...hash, [item.id]: item }), {});
    for (let id of selectedResources) {
      if (resourcesHash[id]?.ai_optimization_status === ProductsAiOptimizationStatus.OPTIMIZED) {
        list.push({
          id,
        });
      }
    }

    restoreAIContent({
      resourceList: list,
      resourceType: analysisEntityTypes.ARTICLE,
    });
  };

  /**
   * @param {import("yup").InferType<typeof autoAiOptimizationSchema>} settings
   */
  const handleBulkAiContentGeneration = (settings) => {
    const list = [];
    const articleHash = articles?.reduce((hash, item) => ({ ...hash, [item.id]: item }), {});
    for (let id of selectedResources) {
      if (articleHash[id]) {
        const article = articleHash[id];

        list.push({
          id,
          gql_id: article.shopify_gql_id,
        });
      }
    }
    generateAiContent({
      resourceList: list,
      settings,
      resourceType: analysisEntityTypes.ARTICLE,
    });
  };

  return (
    <Page
      fullWidth
      title={t("Optimize SEO - Blog posts")}
      titleMetadata={
        <Badge tone={autoAiOptimizeSettings?.[ResourceType.ARTICLE]?.status ? "success" : "warning"}>
          {t(
            `Auto AI Optimization: ${autoAiOptimizeSettings?.[ResourceType.ARTICLE]?.status ? "Enabled" : "Disabled"}`
          )}
        </Badge>
      }
      primaryAction={{
        content: t("Generate with AI"),
        icon: (
          <Icon
            source={MagicIcon}
            tone="magic"
          />
        ),
        onAction: handleOpenBlogAutoWrite,
      }}
      backAction={navigation.backAction}
      secondaryActions={[
        { content: t("Manage Settings"), onAction: () => setIsAutoAiOptimizationSettingsModalOpen(true) },
        {
          content: t("Sync blog posts"),
          icon: <Icon source={ResetIcon} />,
          loading: isStartingSync || blogSync?.ongoing,
          onAction: handleSyncBlogPosts,
          disabled: isStartingSync || blogSync?.ongoing,
          helpText: syncTooltipContent || undefined,
        },
      ]}
    >
      <BlockStack gap="400">
        <BackupRestoreRunningBanner />
        <Banner />

        {!hasEmptyContent ? (
          <>
            <UsageLimitCard
              title="AI Content Optimizer"
              group={AI_OPTIMIZER}
              learnMoreButton={{
                title: "What do i do if i need more credits for AI Content Optimizer?",
                url: HELP_URLS.AI_OPTIMIZER,
              }}
              action={{ content: "Increase limit" }}
            />
            <BlockStack gap="400">
              <Card padding="0">
                <ArticlesIndexTableFilters isLoading={isFetching} />

                <IndexTable
                  condensed={isSmallDevice}
                  resourceName={resourceName}
                  itemCount={selectableArticlesCount || articles?.length || 0}
                  headings={ARTICLES_TABLE_COLUMNS.map((p) => ({ ...p, title: t(p.title) }))}
                  selectable={true}
                  selectedItemsCount={allResourcesSelected ? t("All") : selectedResources.length}
                  onSelectionChange={handleSelectionChange}
                  sortable={[false, true, true, true, false, true, true, true, true, false]}
                  onSort={handleSort}
                  sortColumnIndex={sortIndex}
                  sortDirection={sortDir}
                  defaultSortDirection={SORT_DIR.DESC}
                  emptyState={emptyStateMarkup}
                  pagination={paginationConfigs}
                  promotedBulkActions={[
                    {
                      content: `${t("Restore")} (${selectedResources.length})`,
                      onAction: handleListRestore,
                      disabled: isRestoringAIContent || isRunningBackupOrRestore,
                    },
                    {
                      content: `${t("Optimize with AI")} (${selectedResources.length})`,
                      onAction: () => setIsBulkAiContentGenerationModalOpen(true),
                      disabled: isBulkAIContentGenerationLoading || limitExceeded || isRunningBackupOrRestore,
                    },
                  ]}
                >
                  <ArticleRows
                    articles={isLoading ? emptyArticles : articles}
                    isFetching={isLoading}
                    selectedResources={selectedResources}
                  />
                </IndexTable>
              </Card>

              <BulkResourceAiOptimizationModal
                isOpen={isBulkAiContentGenerationModalOpen}
                setIsOpen={setIsBulkAiContentGenerationModalOpen}
                resourceType={analysisEntityTypes.ARTICLE}
                onAction={(settings) => {
                  handleBulkAiContentGeneration(settings);
                }}
                isError={isBulkAiContentGenerationError}
              />
            </BlockStack>
          </>
        ) : (
          <EmptyPage
            heading="Add or sync your blog posts with StoreSEO"
            content="Before optimizing, you need to add or sync your blog posts first"
            withTableFilter
            secondaryAction={
              <SyncButton
                title="blog posts"
                loading={isStartingSync}
                callback={syncBlogs}
                syncState={blogSync}
              />
            }
            primaryAction={
              <Button
                variant="primary"
                onClick={handleAddArticles}
              >
                {t("Add blog posts")}
              </Button>
            }
          />
        )}
      </BlockStack>
      <AutoAiOptimizationSettingsModal
        resourceType="ARTICLE"
        isOpen={isAutoAiOptimizationSettingsModalOpen}
        setIsOpen={setIsAutoAiOptimizationSettingsModalOpen}
      />

      {/* Blog Auto-Write Modal */}
      <BlogAutoWriteModal
        isOpen={isBlogAutoWriteModalOpen}
        onClose={() => setIsBlogAutoWriteModalOpen(false)}
      />

      {/* Blog Sync Confirmation Modal */}
      <ConfirmationModal
        show={showSyncConfirmModal}
        onClose={setShowSyncConfirmModal}
        title={t("Sync blog posts")}
        content={t("Are you sure you want to sync blog posts from Shopify?")}
        primaryAction={handleConfirmSync}
        loading={isStartingSync}
      />
    </Page>
  );
}

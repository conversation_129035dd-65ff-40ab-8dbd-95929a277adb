import { EmptyState, Text } from "@shopify/polaris";
import React from "react";

export default function ArticleNotFound() {
  return (
    <div
      style={{
        paddingTop: "20vh",
        height: "80vh",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
      }}
    >
      <EmptyState
        heading="The Blog post couldn't be found on your store"
        action={{ content: "Go to blog posts", url: "/optimize-seo/articles?refresh=1" }}
        image="https://cdn.shopify.com/shopifycloud/web/assets/v1/f9352e9573adbafe.svg"
      >
        <Text as="p">Please make sure the blog post exists on your store and sync blog posts again.</Text>
      </EmptyState>
    </div>
  );
}

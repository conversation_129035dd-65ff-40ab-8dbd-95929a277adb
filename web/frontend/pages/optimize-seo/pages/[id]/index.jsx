import AiContentGeraratorCard from "@/components/ai/AiContentGeraratorCard.jsx";
import { FormErrorBanner } from "@/components/common/FormErrorBanner.jsx";
import useAiContentOptimizerHelper from "@/lib/hooks/optimize-seo/useAiContentOptimizerHelper.jsx";
import ContextualSaveBar from "@/modules/components/ContextualSaveBar.jsx";
import { resetAiContent } from "@/store/features/AiContent.js";
import { Banner, BlockStack, Box, Grid, Page, PageActions, Text } from "@shopify/polaris";
import { isEmpty } from "lodash";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation, useQueryClient } from "react-query";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import types from "storeseo-enums/analysisEntityTypes";
import { BANNER_HOMEPAGE_HINT } from "storeseo-enums/cacheKeys";
import pageType from "storeseo-enums/pageType";
import AppEmbedWarningBanner from "../../../../components/common/AppEmbedWarningBanner.jsx";
import EditForm from "../../../../components/common/EditForm";
import OptimizationData from "../../../../components/common/OptimizationData";
import SyncButton from "../../../../components/common/SyncButton";
import DummyProductFix from "../../../../components/loader/DummyProductFix.jsx";
import SocialMediaPreview from "../../../../components/previewers/SocialMediaPreview";
import NoFollowToggler from "../../../../components/togglers/NoFollowToggler";
import NoIndexToggler from "../../../../components/togglers/NoIndexToggler";
import { PAGE_TEMPLATES, previewImgType } from "../../../../config";
import { useAppQuery, useShopApi } from "../../../../hooks";
import { usePageApi } from "../../../../hooks/apiHooks/usePageApi";
import { useAppBridgeRedirect } from "../../../../hooks/useAppBridgeRedirect";
import { useAppNavigation } from "../../../../hooks/useAppNavigation";
import { useBanner } from "../../../../hooks/useBanner.jsx";
import { useGeneralSeoErrors } from "../../../../hooks/useGeneralSeoErrors.js";
import { usePageReanalysis } from "../../../../hooks/usePageReanalysis";
import useShop from "../../../../hooks/useShop.js";
import { useUnsavedChanges } from "../../../../hooks/useUnsavedChanges.jsx";
import { setHiddenBanner } from "../../../../store/features/HiddenBanner.js";
import { getMetaTitleAndDescription, getNoIndexNoFollowFromMeta, scrollToTop } from "../../../../utility/helpers";
import queryKeys from "../../../../utility/queryKeys";

export default function PageDetails() {
  const { id } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const pageApi = usePageApi();
  const queryClient = useQueryClient();
  const { handleEditPage, ...redirect } = useAppBridgeRedirect();
  const { goToPage, goToPageList, backAction } = useAppNavigation();
  const { pageURLPrefix } = useShop();
  const shopApi = useShopApi();
  const dispatch = useDispatch();
  const hiddenBanner = useSelector((state) => state.hiddenBanner);

  const pageQueryKey = [queryKeys.PAGE_DATA, { id }];

  const {
    data: apiData,
    refetch,
    isLoading: isLoadingPage,
  } = useAppQuery({
    queryKey: pageQueryKey,
    queryFn: () => pageApi.getPageWithOptimizationData(id),
    reactQueryOptions: {
      staleTime: 0,
      refetchInterval: false,
      onError: (err) => {
        goToPageList();
      },
    },
  });

  const { page = {}, pagination } = apiData || {};

  const [originalFormData, setOriginalFormData] = useState({});
  const [formData, setFormData] = useState({});
  const [noIndexStatus, setNoIndexStatus] = useState(false);
  const [noFollowStatus, setNoFollowStatus] = useState(false);
  const [createRedirectUrl, setCreateRedirectUrl] = useState(false);

  const { score, optimizationData, focusKeywordSuggestions } = usePageReanalysis(id, formData, apiData);
  const { hasUnsavedChanges, DiscardChangesModal, isURLChanged, setShowDiscardChangeModal } = useUnsavedChanges({
    originalData: originalFormData,
    currentData: formData,
    onDiscardAction: () => {
      setFormData({ ...originalFormData });
      dispatch(resetAiContent());
    },
  });

  const { errors: formErrors, setErrors } = useGeneralSeoErrors(formData);

  const { mutate: updatePage, isLoading: isUpdatingPage } = useMutation({
    onMutate: () => {
      dispatch(resetAiContent());
    },
    mutationFn: () => pageApi.updatePageMetaData(page.id, { ...formData, createRedirectUrl }, setErrors),
    onSuccess: (updatedPage) => {
      const { pagination } = queryClient.getQueryData(pageQueryKey);
      queryClient.setQueryData(pageQueryKey, {
        page: { ...updatedPage, score },
        optimizationData,
        focusKeywordSuggestions,
        pagination,
      });
      setErrors({});
    },
    onError: (err) => {
      scrollToTop();
    },
  });

  const { mutate: syncPage, isLoading: isSyncing } = useMutation({
    mutationFn: () => pageApi.syncPageFromShopify(page.id),
    onSuccess: refetch,
    onError: (err) => {
      if (err.status === 404) {
        return navigate("/optimize-seo/pages/notFound");
      }

      showBanner(true);
    },
  });

  const { mutate: toggleNoIndexStatus, isLoading: isUpdatingNoIndex } = useMutation({
    mutationFn: () => pageApi.toggleNoIndexStatus(id),
    onSuccess: () => {
      setNoIndexStatus(!noIndexStatus);
    },
  });

  const { mutate: toggleNoFollowStatus, isLoading: isUpdatingNoFollow } = useMutation({
    mutationFn: () => pageApi.toggleNoFollowStatus(id),
    onSuccess: () => {
      setNoFollowStatus(!noFollowStatus);
    },
  });

  useEffect(() => {
    if (apiData && !apiData.socialMediaDataChanged) {
      const { metaTitle, metaDescription } = getMetaTitleAndDescription(apiData.page, {
        keepExtrachars: true,
      });

      const data = {
        metaTitle,
        metaDescription,
        focusKeyword: apiData.page.focus_keyword,
        tags: apiData.page.tags || [],
        images: apiData.page.images || [],
        handle: apiData.page.handle,
      };

      setFormData(data);
      setOriginalFormData(data);
    }
  }, [apiData]);

  useEffect(() => {
    setCreateRedirectUrl(isURLChanged);
  }, [isURLChanged]);

  useEffect(
    function setNoIndexNoFollowStatusFromPageMeta() {
      if (!isEmpty(page)) {
        const { noIndex, noFollow } = getNoIndexNoFollowFromMeta(page.meta || []);
        setNoIndexStatus(noIndex);
        setNoFollowStatus(noFollow);
      }
    },
    [page]
  );

  const handleHomepageMetaEdit = () => {
    redirect.ADMIN_PATH(`/online_store/preferences`, { external: true });
  };

  const onUploadStart = ({ dataURL, file, imageInputFor }) => {
    const { page } = queryClient.getQueryData(pageQueryKey);
    queryClient.setQueryData(pageQueryKey, {
      page: {
        ...page,
        score,
        facebookPreviewImage: imageInputFor === previewImgType.FACEBOOK ? dataURL : page.facebookPreviewImage,
        twitterPreviewImage: imageInputFor === previewImgType.TWITTER ? dataURL : page.twitterPreviewImage,
      },
      optimizationData,
      focusKeywordSuggestions,
      socialMediaDataChanged: true,
    });
  };

  const handleChange = (key, val) => {
    if (key === "createRedirectUrl") {
      setCreateRedirectUrl(val);
    } else {
      setFormData({ ...formData, [key]: val });
    }
  };

  const { Banner: SyncErrBanner, showBanner } = useBanner({
    title: t("Page sync failed"),
    message: t(
      "The sync process failed to complete. Please try again. If the error persists please contact with our support team."
    ),
  });

  const isHomepage = page?.page_type === pageType.HOMEPAGE;
  const isBetterDocsHomepage = page?.page_type === pageType.BETTERDOCS_HOMEPAGE;

  const handleClose = async () => {
    await shopApi.hideBanner(BANNER_HOMEPAGE_HINT);
    dispatch(setHiddenBanner({ bannerKey: BANNER_HOMEPAGE_HINT, status: false }));
  };

  const showHint = hiddenBanner[BANNER_HOMEPAGE_HINT] && isHomepage;

  useEffect(
    function clearErrorStates() {
      setErrors({});
      showBanner(false);
      dispatch(resetAiContent());
      return () => {
        dispatch(resetAiContent());
      };
    },
    [id]
  );

  const { handleAiKeepAll, handleAiRevertAll, isSaved, setIsSaved } = useAiContentOptimizerHelper({
    formData,
    setFormData,
    originalFormData,
  });

  return (
    <>
      {isEmpty(page) && <DummyProductFix />}
      {!isLoadingPage && !isEmpty(formData) && (
        <Page
          backAction={backAction}
          title={page.title}
          compactTitle
          secondaryActions={
            <>
              <SyncButton
                syncState={{ ongoing: isSyncing }}
                title={t("page")}
                callback={syncPage}
                platform={isBetterDocsHomepage ? "BetterDocs" : "Shopify"}
              />
              <span style={{ marginRight: "8px" }}></span>
            </>
          }
          pagination={{
            hasPrevious: pagination?.prev ? true : false,
            onPrevious: () => goToPage(pagination?.prev),
            hasNext: pagination?.next ? true : false,
            onNext: () => goToPage(pagination?.next),
          }}
        >
          {showHint && (
            <Box paddingBlockEnd="400">
              <Banner
                tone="info"
                title={t("Note")}
                onDismiss={handleClose}
              >
                <Text
                  as="p"
                  tone="subdued"
                >
                  {t("After editing the Meta Title and Meta Description from")}{" "}
                  <span
                    className="Polaris-Link"
                    onClick={handleHomepageMetaEdit}
                  >
                    {t("Shop Preferences,")}
                  </span>{" "}
                  {t('click "SYNC PAGE" button to update here and check your score.')}
                </Text>
              </Banner>
            </Box>
          )}

          <DiscardChangesModal />

          <ContextualSaveBar
            id="optimize-seo-pages-details"
            open={hasUnsavedChanges}
            isLoading={isUpdatingPage}
            onSave={updatePage}
            onDiscard={() => setShowDiscardChangeModal(true)}
          />

          <SyncErrBanner />
          <BlockStack gap="400">
            <FormErrorBanner
              entityType={types.PAGE}
              formErrors={formErrors}
              setFormErrors={setErrors}
              handleEditButtonClick={() => handleEditPage(id, { external: true })}
            />
            <Grid>
              <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 4, lg: 8, xl: 8 }}>
                <BlockStack gap="400">
                  <EditForm
                    featuredImage={null}
                    data={formData}
                    onChange={handleChange}
                    focusKeywordSuggestions={focusKeywordSuggestions}
                    formErrors={formErrors}
                    readOnly={isHomepage}
                    hideTagsInput
                    urlPrefix={pageURLPrefix}
                    originalFormData={originalFormData}
                    isURLChanged={isURLChanged}
                    createRedirectUrl={createRedirectUrl}
                    disabled={isBetterDocsHomepage}
                  />
                  <SocialMediaPreview
                    item={page}
                    type={types.PAGE}
                  />
                </BlockStack>
              </Grid.Cell>

              <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 2, lg: 4, xl: 4 }}>
                <BlockStack gap="400">
                  <OptimizationData
                    score={score}
                    optimizations={optimizationData}
                    aiContentCard={
                      !isHomepage &&
                      !isBetterDocsHomepage && (
                        <AiContentGeraratorCard
                          title={page.title}
                          description={page.body_html}
                          focusKeyword={formData?.focusKeyword}
                          setErrors={setErrors}
                          handleKeepAll={handleAiKeepAll}
                          handleRevertAll={handleAiRevertAll}
                          isSaved={isSaved}
                          setIsSaved={setIsSaved}
                          resourceType={types.PAGE}
                        />
                      )
                    }
                  />
                  {!isHomepage && !isBetterDocsHomepage && (
                    <>
                      <AppEmbedWarningBanner template={PAGE_TEMPLATES.FIX} />
                      <NoIndexToggler
                        status={noIndexStatus}
                        onToggle={toggleNoIndexStatus}
                        loading={isUpdatingNoIndex}
                      />
                      <NoFollowToggler
                        status={noFollowStatus}
                        onToggle={toggleNoFollowStatus}
                        loading={isUpdatingNoFollow}
                      />
                    </>
                  )}
                </BlockStack>
              </Grid.Cell>
            </Grid>
          </BlockStack>

          <PageActions
            primaryAction={{
              content: t("Save"),
              onAction: updatePage,
              loading: isUpdatingPage,
              disabled: !hasUnsavedChanges,
            }}
          />
        </Page>
      )}
    </>
  );
}

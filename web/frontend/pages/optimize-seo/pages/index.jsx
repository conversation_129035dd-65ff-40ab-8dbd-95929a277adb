import { <PERSON><PERSON>tack, <PERSON><PERSON>, Card, IndexTable, Page, useBreakpoints } from "@shopify/polaris";
import React, { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";
import { useSelector } from "react-redux";
import { useSearchParams } from "react-router-dom";
import socketEvents from "storeseo-enums/socketEvents";
import EmptyPage from "../../../components/common/EmptyPage.jsx";
import SyncButton from "../../../components/common/SyncButton.jsx";
import PageRows from "../../../components/pages/PageRows.jsx";
import { SORT_DIR } from "../../../config/index.js";
import { useAppQuery } from "../../../hooks/index.js";
import { usePageApi } from "../../../hooks/apiHooks/usePageApi.js";
import { usePusher } from "../../../providers/PusherProvider.jsx";
import { useStoreSeo } from "../../../providers/StoreSeoProvider.jsx";
import { getQueryFromUrlSearchParam } from "../../../utility/helpers.jsx";
import queryKeys from "../../../utility/queryKeys.js";
import { useBanner } from "../../../hooks/useBanner.jsx";
import { useAppNavigation } from "../../../hooks/useAppNavigation.js";
// import DummyIndexPageSkeleton from "../../components/loader/DummyIndexPageSkeleton.jsx";
import { useAppBridgeRedirect } from "../../../hooks/useAppBridgeRedirect.js";
import TableEmptyState from "../../../components/common/TableEmptyState.jsx";
import { useSyncPages } from "@/hooks/pages/index.jsx";
import useIndexTablePagination from "@/lib/hooks/useIndexTablePagination.jsx";
import PagesIndexTableFilters from "@/modules/optimize-seo/pages/PagesIndexTableFilters.jsx";

const PAGES_TABLE_COLUMNS = [
  { title: "#" },
  { title: "Page Title" },
  { title: "Optimize Status" },
  { title: "Focus Keyword" },
  { title: "Issues" },
  { title: "Score" },
  { title: "Date" },
  { title: "Status" },
  { title: "Action", alignment: "center" },
];

export default function Pages() {
  const { smDown: isSmallDevice } = useBreakpoints();
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const pageSync = useSelector((state) => state.pageSync);
  const pageApi = usePageApi();
  // const { setSort, sortArrowClassname } = useSort();
  const [searchParams, setSearchParams] = useSearchParams();
  const query = getQueryFromUrlSearchParam(searchParams);
  const [sortDir, setSortDir] = useState(() => SORT_DIR.DESC);
  const [sortIndex, setSortIndex] = useState(null);
  const { doManualRefresh } = useStoreSeo();
  const { pusherChannel } = usePusher();
  const navigation = useAppNavigation();
  const [isLoading, setIsLoading] = useState(true);

  const { data, isFetching, refetch, isFetched } = useAppQuery({
    queryKey: [queryKeys.PAGES_LIST, query],
    queryFn: () => pageApi.getPagesWithPagination(query),
    reactQueryOptions: {
      keepPreviousData: true,
    },
  });

  // const { data: shopifyPageCount } = useAppQuery({
  //   queryKey: queryKeys.TOTAL_SHOPIFY_PAGES_COUNT,
  //   queryFn: pageApi.getTotalShopifyPagesCount,
  // });

  const defaultRowItem = {
    id: null,
    title: null,
    optimize_status: null,
    focus_keyword: null,
    issues: null,
    score: null,
    created_at: null,
    status: null,
  };
  const { pages = new Array(10).fill(defaultRowItem), pagination = {}, pageCount = 0 } = data || {};
  // const syncedAllPages = pageCount === shopifyPageCount + 1; // 1 extra count for homepage

  const { mutate: startPageSync, isLoading: isStartingSync } = useSyncPages({
    onError: () => showBanner(true),
  });

  useEffect(() => {
    if (!pusherChannel) return;

    pusherChannel.bind(socketEvents.PAGE_SYNC_COMPLETE, refetch);
  }, [pusherChannel]);

  if (doManualRefresh) queryClient.invalidateQueries(queryKeys.PAGES_LIST);

  const handleSort = useCallback(
    (index, direction) => {
      setSortIndex(index);
      setSortDir(direction);
      let key = Object.keys(defaultRowItem)[index];
      let newDir = toggleSortDir(direction);

      setSearchParams({
        ...Object.fromEntries(searchParams.entries()),
        sortBy: key,
        sortOrder: newDir === SORT_DIR.ASC ? "ASC" : "DESC",
        page: 1,
      });
    },
    [pages]
  );

  const toggleSortDir = (direction) => {
    switch (direction) {
      case SORT_DIR.ASC:
        return SORT_DIR.DESC;
      case SORT_DIR.DESC:
        return SORT_DIR.ASC;
      default:
        return SORT_DIR.DESC;
    }
  };

  const { Banner, showBanner } = useBanner({
    title: t("Page sync start failed"),
    message: t(
      "The sync process failed to start. Please try again. If the error persists please contact with our support team."
    ),
  });

  useEffect(() => {
    setTimeout(() => setIsLoading(false), 500);
  });

  const resourceName = {
    singular: "page",
    plural: "pages",
  };

  const hasEmptyContent = isFetched && pageCount === 0;
  const emptyPages = new Array(10).fill(defaultRowItem);

  const title = t("Optimize SEO - Pages");
  const primaryActionMarkup = (
    <SyncButton
      title="pages"
      loading={isStartingSync}
      callback={startPageSync}
      syncState={pageSync}
    />
  );

  // if (isFetching) return <DummyIndexPageSkeleton />;

  const { handleAddPages } = useAppBridgeRedirect();

  const emptyStateMarkup = (
    <TableEmptyState
      title="No pages found"
      description="Try changing the filters or search term"
      withIllustration
    />
  );

  const { paginationConfigs } = useIndexTablePagination(pagination);

  return (
    <Page
      fullWidth
      title={title}
      primaryAction={primaryActionMarkup}
      backAction={navigation.backAction}
    >
      <Banner />

      {!hasEmptyContent ? (
        <BlockStack gap="400">
          <Card padding="0">
            <PagesIndexTableFilters isLoading={isFetching} />

            <IndexTable
              condensed={isSmallDevice}
              resourceName={resourceName}
              itemCount={pages.length}
              headings={PAGES_TABLE_COLUMNS.map((p) => ({ ...p, title: t(p.title) }))}
              selectable={false}
              sortable={[false, true, true, false, true, true, true, true, false]}
              onSort={handleSort}
              sortColumnIndex={sortIndex}
              sortDirection={sortDir}
              defaultSortDirection={SORT_DIR.DESC}
              emptyState={emptyStateMarkup}
              pagination={paginationConfigs}
            >
              <PageRows
                pages={isFetching || isLoading ? emptyPages : pages}
                isFetching={isFetching || isLoading}
                pagination={pagination}
              />
            </IndexTable>
          </Card>
        </BlockStack>
      ) : (
        <EmptyPage
          heading="Add or sync your pages with StoreSEO"
          content="To start optimizing pages, you need to add or sync them first"
          withTableFilter
          secondaryAction={primaryActionMarkup}
          primaryAction={
            <Button
              variant="primary"
              onClick={handleAddPages}
            >
              {t("Add pages")}
            </Button>
          }
        />
      )}
    </Page>
  );
}

import { EmptyState, Text } from "@shopify/polaris";
import React from "react";

export default function PageNotFound() {
  return (
    <div
      style={{
        paddingTop: "20vh",
        height: "80vh",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
      }}
    >
      <EmptyState
        heading="The Page couldn't be found on your store"
        action={{ content: "Go to pages", url: "/optimize-seo/pages?refresh=1" }}
        image="https://cdn.shopify.com/shopifycloud/web/assets/v1/f9352e9573adbafe.svg"
      >
        <Text as="p">Please make sure the page exists on your store and sync pages again.</Text>
      </EmptyState>
    </div>
  );
}

import EmptyPage from "@/components/common/EmptyPage";
import DocListEmptyState from "@/components/docs/DocListEmptyState";
import DocRows from "@/components/docs/DocRows";
import DocSyncButton from "@/components/docs/DocSyncButton";
import socketEvents from "storeseo-enums/socketEvents";
import { SORT_DIR } from "@/config";
import { defaultRowItem, DOCS_TABLE_COLUMNS, resourceName } from "@/config/docsConfig";
import { useAppNavigation } from "@/hooks/useAppNavigation";
import { useBanner } from "@/hooks/useBanner";
import useIndexTablePagination from "@/lib/hooks/useIndexTablePagination";
import DocsIndexTableFilters from "@/modules/optimize-seo/docs/DocsIndexTableFilters";
import { usePusher } from "@/providers/PusherProvider";
import { useStoreSeo } from "@/providers/StoreSeoProvider";
import { setDocCount } from "@/store/features/DocCount";
import { toggleSortDir } from "@/utility/helpers";
import { BlockStack, Button, Card, IndexTable, Page, useBreakpoints } from "@shopify/polaris";
import { t } from "i18next";
import { useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useSearchParams } from "react-router-dom";
import { useGetDocs } from "@/hooks/docs";

export default function Docs() {
  const { smDown: isSmallDevice } = useBreakpoints();
  const [searchParams, setSearchParams] = useSearchParams();
  const navigation = useAppNavigation();
  const dispatch = useDispatch();
  const { doManualRefresh } = useStoreSeo();
  const { pusherChannel } = usePusher();
  const totalDocCount = useSelector((state) => state.docCount);
  const user = useSelector((state) => state.user);

  const { data, isLoading, isFetching, refetch, isFetched } = useGetDocs();

  const { docs = new Array(10).fill(defaultRowItem), pagination = {} } = data || {};

  const { paginationConfigs } = useIndexTablePagination(pagination);

  const [sortDir, setSortDir] = useState(SORT_DIR.DESC);
  const [sortIndex, setSortIndex] = useState(6);
  const hasEmptyContent = isFetched && totalDocCount === 0;
  const emptyDocs = new Array(10).fill(defaultRowItem);

  const handleSort = useCallback(
    (index, direction) => {
      setSortIndex(index);
      setSortDir(direction);
      let key = Object.keys(defaultRowItem)[index - 1];
      let newDir = toggleSortDir(direction);
      setSearchParams({
        ...Object.fromEntries(searchParams.entries()),
        sortBy: key,
        sortOrder: newDir === SORT_DIR.ASC ? "ASC" : "DESC",
        page: 1,
      });
    },
    [docs]
  );

  const { Banner, showBanner } = useBanner({
    title: t("Doc sync start failed"),
    message: t(
      "The sync process failed to start. Please try again. If the error persists please contact with our support team."
    ),
  });

  const handleAddDoc = () => {
    window.open(`${process.env.FRONTEND_BETTERDOCS_BASE_URL}/doc/add?shop=${user?.shop}`, "_blank");
  };

  if (doManualRefresh) {
    refetch();
  }

  // refetch on pusher event
  useEffect(() => {
    if (!pusherChannel) return;

    pusherChannel.bind(socketEvents.DOC_SYNC_COMPLETE, refetch);
  }, [pusherChannel]);

  // set docs count to redux store
  useEffect(() => {
    if (data && data.docCount !== undefined) dispatch(setDocCount(data?.docCount));
  }, [data]);

  return (
    <Page
      fullWidth
      title={t("Optimize SEO - Docs")}
      primaryAction={<DocSyncButton onSyncErr={() => showBanner(true)} />}
      backAction={navigation.backAction}
    >
      <Banner />
      {!hasEmptyContent ? (
        <BlockStack gap="400">
          <Card padding="0">
            <DocsIndexTableFilters isLoading={isFetching} />
            <IndexTable
              condensed={isSmallDevice}
              resourceName={resourceName}
              itemCount={docs.length}
              headings={DOCS_TABLE_COLUMNS.map((item) => ({
                ...item,
                title: t(item.title),
              }))}
              selectable={false}
              onSort={handleSort}
              sortable={[false, false, true, false, true, true, true, true, true, false]}
              sortColumnIndex={sortIndex}
              sortDirection={sortDir}
              defaultSortDirection={SORT_DIR.DESC}
              emptyState={<DocListEmptyState />}
              pagination={paginationConfigs}
            >
              <DocRows
                docs={isFetching || isLoading ? emptyDocs : docs}
                isFetching={isFetching || isLoading}
                pagination={pagination}
              />
            </IndexTable>
          </Card>
        </BlockStack>
      ) : (
        <EmptyPage
          heading="Add or sync your docs with StoreSEO"
          content="Before optimizing, you need to add docs or sync them first"
          withTableFilter
          secondaryAction={<DocSyncButton onSyncErr={() => showBanner(true)} />}
          primaryAction={
            <Button
              variant="primary"
              onClick={handleAddDoc}
            >
              {t("Add Docs")}
            </Button>
          }
        />
      )}
    </Page>
  );
}

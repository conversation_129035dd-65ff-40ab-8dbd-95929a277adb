import AltTextEditForm from "@/components/common/AltTextEditForm";
import EditForm from "@/components/common/EditForm";
import { FormErrorBanner } from "@/components/common/FormErrorBanner";
import OptimizationData from "@/components/common/OptimizationData";
import SyncButton from "@/components/common/SyncButton";
import DocFixSkeleton from "@/components/docs/DocFixSkeleton";
import SocialMediaPreview from "@/components/previewers/SocialMediaPreview";
import NoFollowToggler from "@/components/togglers/NoFollowToggler";
import NoIndexToggler from "@/components/togglers/NoIndexToggler";
import {
  useGetDocWithOptimizationData,
  useSyncDoc,
  useUpdateDoc,
  useUpdateNoFollowStatus,
  useUpdateNoIndexStatus,
} from "@/hooks/docs";
import { useDocReanalysis } from "@/hooks/docs/useDocReanalysis";
import { useAppBridgeRedirect } from "@/hooks/useAppBridgeRedirect";
import { useAppNavigation } from "@/hooks/useAppNavigation";
import { useFeaturedImage } from "@/hooks/useFeaturedImage";
import { useGeneralSeoErrors } from "@/hooks/useGeneralSeoErrors";
import useShop from "@/hooks/useShop";
import { useUnsavedChanges } from "@/hooks/useUnsavedChanges";
import ContextualSaveBar from "@/modules/components/ContextualSaveBar";
import { resetAiContent } from "@/store/features/AiContent";
import { getMetaTitleAndDescription, getNoIndexNoFollowFromDocsMeta, scrollToTop } from "@/utility/helpers";
import queryKeys from "@/utility/queryKeys";
import { BlockStack, Grid, InlineStack, Page, PageActions } from "@shopify/polaris";
import { isEmpty } from "lodash";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import analysisEntityTypes from "storeseo-enums/analysisEntityTypes";

export default function DocDetails() {
  const { t } = useTranslation();
  const { id } = useParams();
  const dispatch = useDispatch();
  const { goToDoc, gotToDocList, backAction } = useAppNavigation();
  const queryClient = useQueryClient();
  const { handleEditDoc } = useAppBridgeRedirect();
  const user = useSelector((state) => state.user);

  // Return to doc list if no id is provided
  if (!id) return gotToDocList();

  const docQueryKey = [queryKeys.DOC_DATA, id];

  // Get doc data with the given id
  const {
    data: docData,
    isLoading: isDocLoading,
    isError,
    refetch,
  } = useGetDocWithOptimizationData({
    id,
    docQueryKey,
  });

  const { doc = {}, pagination } = docData || {};

  useEffect(() => {
    if (isError) gotToDocList();
  }, [isError]);

  const { docURLPrefix } = useShop();
  const featuredImage = useFeaturedImage(doc, analysisEntityTypes.DOC);
  const [formData, setFormData] = useState({});
  const [originalFormData, setOriginalFormData] = useState({});

  const tags = doc?.tags?.map((item) => item.tag);

  useEffect(() => {
    if (docData) {
      const { metaTitle, metaDescription } = getMetaTitleAndDescription(doc);

      const data = {
        metaTitle,
        metaDescription,
        focusKeyword: doc.focus_keyword,
        image: doc.image,
        handle: doc.handle,
        tags,
      };

      setFormData(data);
      setOriginalFormData(data);
    }
  }, [docData]);

  const { score, optimizationData, focusKeywordSuggestions } = useDocReanalysis(id, formData, docData);

  const { errors: formErrors, setErrors } = useGeneralSeoErrors(formData);
  const [createRedirectUrl, setCreateRedirectUrl] = useState(false);
  const { hasUnsavedChanges, DiscardChangesModal, setShowDiscardChangeModal } = useUnsavedChanges({
    originalData: originalFormData,
    currentData: formData,
    onDiscardAction: () => {
      setFormData({ ...originalFormData });
      dispatch(resetAiContent());
    },
  });

  const handleChange = (key, val) => {
    if (key === "createRedirectUrl") {
      setCreateRedirectUrl(val);
    } else {
      setFormData({ ...formData, [key]: val });
    }
  };

  const handleUploadFeaturedImage = () => {
    window.open(`${process.env.FRONTEND_BETTERDOCS_BASE_URL}/doc/edit/${doc.doc_id}?shop=${user?.shop}`, "_blank");
  };

  // update DOC
  const {
    mutate: updateDocData,
    isLoading: isUpdatingDoc,
    isError: isUpdateDocError,
  } = useUpdateDoc({
    onMutate: () => {
      dispatch(resetAiContent());
    },
    onSuccess: (updateDoc) => {
      const { pagination } = queryClient.getQueryData(docQueryKey);
      queryClient.setQueryData(docQueryKey, {
        doc: { ...updateDoc, score },
        optimizationData,
        focusKeywordSuggestions,
        pagination,
      });
      setErrors({});
    },
  });

  // sync DOC
  const { mutate: startSync, isLoading: isSyncingDoc } = useSyncDoc(id, {
    onSuccess: refetch,
  });

  useEffect(() => {
    if (isUpdateDocError) scrollToTop();
  }, [isUpdateDocError]);

  const handlePageAction = useCallback(
    () => updateDocData({ id, data: { ...formData, createRedirectUrl }, setErrors }),
    [formData, createRedirectUrl]
  );

  // No index and follow toggler logics
  const [noIndexStatus, setNoIndexStatus] = useState(false);
  const [noFollowStatus, setNoFollowStatus] = useState(false);

  let noIndexStatusFlag = noIndexStatus ? false : true;
  let noFollowStatusFlag = noFollowStatus ? false : true;

  useEffect(
    function setNoIndexNoFollowStatusFromMeta() {
      if (!isEmpty(doc)) {
        const { noIndex, noFollow } = getNoIndexNoFollowFromDocsMeta(doc?.metafields || []);
        setNoIndexStatus(noIndex);
        setNoFollowStatus(noFollow);
      }
    },
    [doc]
  );

  const { mutate: toggleNoIndexStatus, isLoading: isUpdatingNoIndex } = useUpdateNoIndexStatus({
    onSuccess: () => setNoIndexStatus(!noIndexStatus),
  });

  const { mutate: toggleNoFollowStatus, isLoading: isUpdatingNoFollow } = useUpdateNoFollowStatus({
    onSuccess: () => setNoFollowStatus(!noFollowStatus),
  });

  return (
    <>
      {isDocLoading && <DocFixSkeleton />}
      {!isDocLoading && !isEmpty(formData) && (
        <Page
          backAction={backAction}
          title={doc.title}
          compactTitle
          secondaryActions={
            <>
              <InlineStack gap="200">
                <SyncButton
                  syncState={{ ongoing: isSyncingDoc }}
                  title={t("doc")}
                  callback={startSync}
                  platform="BetterDocs"
                />
              </InlineStack>
            </>
          }
          pagination={{
            hasPrevious: !!pagination?.prev,
            onPrevious: () => goToDoc(pagination?.prev),
            hasNext: !!pagination?.next,
            onNext: () => goToDoc(pagination?.next),
          }}
        >
          <DiscardChangesModal />
          <ContextualSaveBar
            id="optimize-seo-docs-details"
            open={hasUnsavedChanges}
            isLoading={isUpdatingDoc}
            onSave={handlePageAction}
            onDiscard={() => setShowDiscardChangeModal(true)}
          />

          <BlockStack gap="400">
            <FormErrorBanner
              entityType={analysisEntityTypes.DOC}
              formErrors={formErrors}
              setFormErrors={setErrors}
              handleEditButtonClick={() => handleEditDoc(id, { external: true })}
            />
            <Grid>
              <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 4, lg: 8, xl: 8 }}>
                <BlockStack gap="400">
                  <EditForm
                    featuredImage={featuredImage}
                    focusKeywordSuggestions={focusKeywordSuggestions}
                    data={formData}
                    formErrors={formErrors}
                    onChange={handleChange}
                    originalFormData={originalFormData}
                    // isURLChanged={isURLChanged}
                    createRedirectUrl={createRedirectUrl}
                    hideTagsInput={false}
                    entityType={analysisEntityTypes.DOC}
                    urlPrefix={docURLPrefix}
                  />

                  <AltTextEditForm
                    title={doc?.image?.alt_text}
                    images={formData.image ? [{ ...formData.image, altText: formData.image.alt }] : []}
                    onChange={(images) => {
                      setFormData({ ...formData, image: { ...images[0], alt: images[0].altText } });
                    }}
                    emptyStateMsg={t("Add featured image to your doc first")}
                    emptyStateActionLabel={t("Add featured image")}
                    onEmptyStateAction={handleUploadFeaturedImage}
                    imageOptimizerEnabled={false}
                    imageAltTextGeneratorEnabled={false}
                    entityType={analysisEntityTypes.DOC}
                  />
                  <SocialMediaPreview
                    item={doc}
                    type={analysisEntityTypes.DOC}
                  />
                </BlockStack>
              </Grid.Cell>

              <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 2, lg: 4, xl: 4 }}>
                <BlockStack gap="400">
                  <OptimizationData
                    score={score}
                    optimizations={optimizationData}
                    showEditLink={true}
                    resourceType="docs"
                    onShopifyEditClick={() => handleEditDoc(id, { external: true })}
                  />

                  <NoIndexToggler
                    status={noIndexStatus}
                    onToggle={() => toggleNoIndexStatus({ id, noindex: noIndexStatusFlag })}
                    loading={isUpdatingNoIndex}
                  />
                  <NoFollowToggler
                    status={noFollowStatus}
                    onToggle={() => toggleNoFollowStatus({ id, nofollow: noFollowStatusFlag })}
                    loading={isUpdatingNoFollow}
                  />
                </BlockStack>
              </Grid.Cell>
            </Grid>
          </BlockStack>
          <PageActions
            primaryAction={{
              content: t("Save"),
              onAction: handlePageAction,
              loading: isUpdatingDoc,
              disabled: !hasUnsavedChanges,
            }}
          />
        </Page>
      )}
    </>
  );
}

import { Block<PERSON>tack, <PERSON>rid, Page, PageActions } from "@shopify/polaris";
import { isEmpty } from "lodash";
import { Suspense, lazy, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation, useQueryClient } from "react-query";
import { useNavigate, useParams } from "react-router-dom";
import EditForm from "../../../../components/common/EditForm";
import { PAGE_TEMPLATES, browserEvents, emitter, previewImgType } from "../../../../config";
import { useAppQuery, useProductApi, useProductReanalysis } from "../../../../hooks";
import { useAppBridgeRedirect } from "../../../../hooks/useAppBridgeRedirect";
import { getMetaTitleAndDescription, getNoIndexNoFollowFromMeta, scrollToTop } from "../../../../utility/helpers";

import types from "storeseo-enums/analysisEntityTypes";

import OptimizationData from "../../../../components/common/OptimizationData";
import EmptyContentCardSkeletion from "../../../../components/loader/content/EmptyContentCardSkeleton";

import SyncButton from "@/components/common/SyncButton";
import { useBanner } from "@/hooks/useBanner";
import { useMultiLanguageSetting } from "@/hooks/useMultiLanguageSetting";
import ContextualSaveBar from "@/modules/components/ContextualSaveBar";
import AIOptimizationStatusBadge from "@/modules/optimize-seo/AIOptimizationStatusBadge";
import { useDispatch, useSelector } from "react-redux";
import analysisEntityTypes from "storeseo-enums/analysisEntityTypes";
import imageOptimization from "storeseo-enums/imageOptimization";
import AiContentGeraratorCard from "../../../../components/ai/AiContentGeraratorCard";
import AltTextEditForm from "../../../../components/common/AltTextEditForm";
import AppEmbedWarningBanner from "../../../../components/common/AppEmbedWarningBanner";
import { FormErrorBanner } from "../../../../components/common/FormErrorBanner";
import DummyProductFix from "../../../../components/loader/DummyProductFix";
import SocialMediaPreview from "../../../../components/previewers/SocialMediaPreview";
import { useAppNavigation } from "../../../../hooks/useAppNavigation";
import { useFeaturedImage } from "../../../../hooks/useFeaturedImage";
import { useGeneralSeoErrors } from "../../../../hooks/useGeneralSeoErrors";
import useShop from "../../../../hooks/useShop";
import { useUnsavedChanges } from "../../../../hooks/useUnsavedChanges";
import useUserAddon from "../../../../hooks/useUserAddon";
import { useStoreSeo } from "../../../../providers/StoreSeoProvider";
import { resetAiContent, setIsKept } from "../../../../store/features/AiContent";
import queryKeys from "../../../../utility/queryKeys";

const NoIndexToggler = lazy(() => import("../../../../components/togglers/NoIndexToggler"));
const NoFollowToggler = lazy(() => import("../../../../components/togglers/NoFollowToggler"));

function ProductDetails() {
  const { id } = useParams();
  const { t } = useTranslation();
  const productApi = useProductApi();
  const queryClient = useQueryClient();
  const redirect = useAppBridgeRedirect();
  const { loadLowPriorityContent } = useStoreSeo();
  const { goToProduct, goToProductList, backAction } = useAppNavigation();
  const { handleEditProduct } = useAppBridgeRedirect();
  const navigate = useNavigate();
  const { hasImageOptimizer } = useUserAddon();
  const { productURLPrefix } = useShop();
  const dispatch = useDispatch();
  const aiContent = useSelector((state) => state.aiContent);
  const { usingMultiLanguageMode, languageActions, selectedLanguage, primaryLanguage } = useMultiLanguageSetting();

  const productQueryKey = [queryKeys.PRODUCT_DATA, { id, languageCode: selectedLanguage }];
  const {
    data: apiData,
    isLoading: isLoadingProduct,
    refetch,
  } = useAppQuery({
    queryKey: productQueryKey,
    queryFn: () => {
      const languageCode = selectedLanguage === primaryLanguage ? null : selectedLanguage;
      return productApi.getProductWithOptimizationData(id, languageCode);
    },
    reactQueryOptions: {
      staleTime: 0,
      refetchInterval: false,
      onSettled: () => {
        emitter.emit(browserEvents.PAGE_DATA_LOADED);
      },
      onError: (err) => {
        goToProductList();
      },
    },
  });

  const { product = {}, pagination } = apiData || {};
  const [originalFormData, setOriginalFormData] = useState({});
  const [formData, setFormData] = useState({});
  const [noIndexStatus, setNoIndexStatus] = useState(false);
  const [noFollowStatus, setNoFollowStatus] = useState(false);
  const [createRedirectUrl, setCreateRedirectUrl] = useState(false);
  const [isSaved, setIsSaved] = useState(false);

  const featuredImage = useFeaturedImage(product, analysisEntityTypes.PRODUCT);
  const { score, optimizationData, focusKeywordSuggestions } = useProductReanalysis(id, formData, apiData);

  const { hasUnsavedChanges, DiscardChangesModal, isURLChanged, setShowDiscardChangeModal } = useUnsavedChanges({
    originalData: originalFormData,
    currentData: formData,
    onDiscardAction: () => {
      setFormData({ ...originalFormData });
      dispatch(resetAiContent());
    },
  });

  const { errors: formErrors, setErrors } = useGeneralSeoErrors(formData);

  useEffect(() => {
    if (apiData && !apiData.socialMediaDataChanged) {
      const { metaTitle, metaDescription } = getMetaTitleAndDescription(apiData.product);
      const data = {
        metaTitle,
        metaDescription,
        focusKeyword: apiData.product.focus_keyword,
        tags: apiData.product.tags,
        images: apiData.product.images,
        handle: apiData.product.handle,
      };

      setFormData(data);
      setOriginalFormData(data);
    }
  }, [apiData]);

  useEffect(() => {
    setCreateRedirectUrl(isURLChanged);
  }, [isURLChanged]);

  const { mutate: updateProduct, isLoading: isUpdatingProduct } = useMutation({
    mutationKey: ["PRODUCT_UPDATE", id],
    mutationFn: () => productApi.updateProduct(id, { ...formData, createRedirectUrl }, setErrors),
    onMutate: () => {
      dispatch(resetAiContent());
    },
    onSuccess: (updatedProduct) => {
      const { pagination } = queryClient.getQueryData(productQueryKey);
      queryClient.setQueryData(productQueryKey, {
        product: { ...updatedProduct, score },
        optimizationData,
        focusKeywordSuggestions,
        pagination,
      });
      setErrors({});
      setIsSaved(true);
    },
    onError: (err) => {
      scrollToTop();
    },
  });

  const { mutate: updateProductTranslation, isLoading: isUpdatingTranslation } = useMutation({
    mutationKey: ["PRODUCT_UPDATE", id, selectedLanguage],
    mutationFn: () => productApi.updateTranslation(id, selectedLanguage, { ...formData, createRedirectUrl }, setErrors),
    onMutate: () => {
      dispatch(resetAiContent());
    },
    onSuccess: (updatedProduct) => {
      const { pagination } = queryClient.getQueryData(productQueryKey);
      queryClient.setQueryData(productQueryKey, {
        product: { ...updatedProduct, score },
        optimizationData,
        focusKeywordSuggestions,
        pagination,
      });
      setErrors({});
      setIsSaved(true);
    },
    onError: (err) => {
      scrollToTop();
    },
  });

  const { mutate: syncProduct, isLoading: isSyncingProduct } = useMutation({
    mutationFn: () => productApi.syncProductFromShopify(id),
    onSuccess: refetch,
    onError: () => {
      showBanner(true);
    },
  });

  const { mutate: syncTranslation, isLoading: isSyncingTranslation } = useMutation({
    mutationFn: () => productApi.syncTranslationFromShopify(id, selectedLanguage),
    onSuccess: refetch,
    onError: () => {
      showBanner(true);
    },
  });

  const { mutate: toggleNoIndexStatus, isLoading: isUpdatingNoIndex } = useMutation({
    mutationFn: () => productApi.toggleNoIndexStatus(id),
    onSuccess: () => {
      setNoIndexStatus(!noIndexStatus);
    },
  });

  const { mutate: toggleNoFollowStatus, isLoading: isUpdatingNoFollow } = useMutation({
    mutationFn: () => productApi.toggleNoFollowStatus(id),
    onSuccess: () => {
      setNoFollowStatus(!noFollowStatus);
    },
  });

  useEffect(
    function setNoIndexNoFollowStatusFromPageMeta() {
      if (!isEmpty(product)) {
        const { noIndex, noFollow } = getNoIndexNoFollowFromMeta(product.meta || []);
        setNoIndexStatus(noIndex);
        setNoFollowStatus(noFollow);
      }
    },
    [product]
  );

  useEffect(
    function clearErrorStates() {
      setErrors({});
      dispatch(resetAiContent());

      return () => {
        dispatch(resetAiContent());
      };
    },
    [id]
  );

  const onUploadStart = ({ dataURL, file, imageInputFor }) => {
    const { product, ...rest } = queryClient.getQueryData(productQueryKey);
    queryClient.setQueryData(productQueryKey, {
      product: {
        ...product,
        facebookPreviewImage: imageInputFor === previewImgType.FACEBOOK ? dataURL : product.facebookPreviewImage,
        twitterPreviewImage: imageInputFor === previewImgType.TWITTER ? dataURL : product.twitterPreviewImage,
      },
      ...rest,
      socialMediaDataChanged: true,
    });
  };

  const handleShopifyEditClick = () => {
    if (usingMultiLanguageMode)
      redirect.ADMIN_PATH(`/apps/translate-and-adapt/localize/product?shopLocale=${selectedLanguage}&id=${id}`, {
        external: true,
      });
    else redirect.ADMIN_PATH(`/products/${id}`, { external: true });
  };

  const handleOptimizeClick = () => navigate(`/image-optimizer?search=${product.title}`);

  const handleChange = (key, val) => {
    if (key === "createRedirectUrl") {
      setCreateRedirectUrl(val);
    } else {
      if (key === "handle" && val.length === 0) val = product.handle;
      setFormData({ ...formData, [key]: val });
    }
  };

  const handleAiKeepAll = () => {
    setFormData({
      ...formData,
      metaTitle: aiContent.metaTitle,
      metaDescription: aiContent.metaDescription,
      tags: aiContent.tags,
    });

    dispatch(setIsKept({ metaTitle: true, metaDescription: true, tags: true }));
  };

  const handleAiRevertAll = () => {
    setFormData({
      ...formData,
      metaTitle: originalFormData.metaTitle,
      metaDescription: originalFormData.metaDescription,
      tags: originalFormData.tags,
    });

    dispatch(setIsKept({ metaTitle: false, metaDescription: false, tags: false }));
  };

  const imagesRef = useRef([]);

  useEffect(
    function updateImagesRef() {
      imagesRef.current = formData?.images?.map((img) => ({
        ...img,
        title: img.src?.split("/").reverse()[0].split(".")[0].replace(/\?.*/gim, ""),
      }));
    },
    [formData]
  );

  const formattedImages = useMemo(
    () =>
      formData?.images?.map((img) => ({
        ...img,
        title: img.src?.split("/").reverse()[0].split(".")[0].replace(/\?.*/gim, ""),
        product: apiData?.product,
      })),
    [formData?.images, apiData]
  );

  const handleOptimize = useCallback(
    (updatedImages) => {
      const updatedImageHash = updatedImages.reduce(
        (hash, img) => ({
          ...hash,
          [img.id]: img,
        }),
        {}
      );

      const updatedImageList = imagesRef.current.map((img) => {
        if (!updatedImageHash[img.id]) return img;
        else
          return {
            ...img,
            ...updatedImageHash[img.id],
            isOptimized: updatedImageHash[img.id].optimization_status === imageOptimization.OPTIMIZED,
            altText: img.altText,
            src: img.src,
          };
      });

      updatedImageList.forEach((img) => delete img.product);

      setFormData({ ...formData, images: updatedImageList });
    },
    [formData, apiData]
  );

  const { Banner: SyncErrBanner, showBanner } = useBanner({
    title: t("Product sync failed"),
    message: t(
      "The sync process failed to complete. Please try again. If the error persists please contact with our support team."
    ),
  });

  return (
    <>
      {isLoadingProduct && <DummyProductFix />}
      {!isLoadingProduct && !isEmpty(formData) && (
        <Page
          title={product.title}
          titleMetadata={
            !usingMultiLanguageMode ? <AIOptimizationStatusBadge status={product?.ai_optimization_status} /> : null
          }
          backAction={backAction}
          primaryAction={
            <SyncButton
              syncState={{ ongoing: isSyncingProduct || isSyncingTranslation }}
              title={usingMultiLanguageMode ? t("translation") : t("product")}
              callback={usingMultiLanguageMode ? syncTranslation : syncProduct}
            />
          }
          actionGroups={languageActions}
          pagination={{
            hasPrevious: pagination?.prev ? true : false,
            onPrevious: () => goToProduct(pagination?.prev, selectedLanguage),
            hasNext: pagination?.next ? true : false,
            onNext: () => goToProduct(pagination?.next, selectedLanguage),
          }}
        >
          <DiscardChangesModal />
          <SyncErrBanner />

          <ContextualSaveBar
            id="optimize-seo-product-details"
            open={hasUnsavedChanges}
            isLoading={isUpdatingProduct || isUpdatingTranslation}
            onSave={usingMultiLanguageMode ? updateProductTranslation : updateProduct}
            onDiscard={() => setShowDiscardChangeModal(true)}
          />

          <BlockStack gap="400">
            <FormErrorBanner
              entityType={analysisEntityTypes.PRODUCT}
              formErrors={formErrors}
              setFormErrors={setErrors}
              handleEditButtonClick={handleShopifyEditClick}
            />
            <Grid>
              <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 4, lg: 8, xl: 8 }}>
                <BlockStack gap="400">
                  <EditForm
                    product={product}
                    focusKeywordSuggestions={focusKeywordSuggestions}
                    formErrors={formErrors}
                    data={formData}
                    featuredImage={featuredImage}
                    onChange={handleChange}
                    urlPrefix={productURLPrefix}
                    originalFormData={originalFormData}
                    isURLChanged={isURLChanged}
                    createRedirectUrl={createRedirectUrl}
                    hideTagsInput={usingMultiLanguageMode}
                  />
                  <Suspense fallback={<EmptyContentCardSkeletion />}>
                    <AltTextEditForm
                      title={product.title}
                      images={formattedImages}
                      onChange={(images) => {
                        setFormData({ ...formData, images });
                      }}
                      onOptimize={handleOptimize}
                      emptyStateMsg="Add image to your product first"
                      emptyStateActionLabel="Add image"
                      onEmptyStateAction={() => handleEditProduct(id)}
                      imageOptimizerEnabled={hasImageOptimizer}
                      products={[product]}
                      multiLanugageMode={usingMultiLanguageMode}
                    />
                  </Suspense>

                  <Suspense fallback={<EmptyContentCardSkeletion />}>
                    <SocialMediaPreview
                      item={product}
                      type={types.PRODUCT}
                    />
                  </Suspense>
                </BlockStack>
              </Grid.Cell>

              <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 2, lg: 4, xl: 4 }}>
                <BlockStack gap="400">
                  <Suspense fallback={<EmptyContentCardSkeletion />}>
                    <OptimizationData
                      score={score}
                      optimizations={optimizationData}
                      showEditLink
                      onShopifyEditClick={handleShopifyEditClick}
                      // onCanonicalUrlClick={(e) => {
                      //   e.stopPropagation();
                      //   setShowCanonicalUrl(true);
                      // }}
                      onOptimizeClick={handleOptimizeClick}
                      aiContentCard={
                        <AiContentGeraratorCard
                          title={product.title}
                          description={product.description}
                          focusKeyword={formData?.focusKeyword}
                          setErrors={setErrors}
                          handleKeepAll={handleAiKeepAll}
                          handleRevertAll={handleAiRevertAll}
                          isSaved={isSaved}
                          setIsSaved={setIsSaved}
                        />
                      }
                    />
                  </Suspense>

                  {loadLowPriorityContent && <AppEmbedWarningBanner template={PAGE_TEMPLATES.FIX} />}

                  {loadLowPriorityContent ? (
                    <Suspense fallback={<EmptyContentCardSkeletion />}>
                      <NoIndexToggler
                        status={noIndexStatus}
                        onToggle={toggleNoIndexStatus}
                        loading={isUpdatingNoIndex}
                      />
                    </Suspense>
                  ) : (
                    <EmptyContentCardSkeletion />
                  )}
                  {loadLowPriorityContent ? (
                    <Suspense fallback={<EmptyContentCardSkeletion />}>
                      <NoFollowToggler
                        status={noFollowStatus}
                        onToggle={toggleNoFollowStatus}
                        loading={isUpdatingNoFollow}
                      />
                    </Suspense>
                  ) : (
                    <EmptyContentCardSkeletion />
                  )}
                </BlockStack>
              </Grid.Cell>
            </Grid>
          </BlockStack>
          {loadLowPriorityContent && (
            <PageActions
              primaryAction={{
                content: t("Save"),
                onAction: usingMultiLanguageMode ? updateProductTranslation : updateProduct,
                loading: isUpdatingProduct || isUpdatingTranslation,
                disabled: !hasUnsavedChanges,
              }}
            />
          )}
        </Page>
      )}
    </>
  );
}

export default ProductDetails;

import ProFeatureBanner from "@/components/common/ProFeatureBanner";
import TooltipWrapper from "@/components/common/TooltipWrapper";
import DummyPageSkeleton from "@/components/loader/DummyPageSkeleton";
import { useAppBridgeRedirect } from "@/hooks/useAppBridgeRedirect";
import { useMultiLanguageSetting } from "@/hooks/useMultiLanguageSetting";
import { Badge, Banner, BlockStack, Box, Button, Card, Divider, InlineStack, Text } from "@shopify/polaris";
import { LanguageTranslateIcon, ResetIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";

export default function MultiLanguage() {
  const user = useSelector((state) => state.user);
  const { t } = useTranslation();
  const redirect = useAppBridgeRedirect();

  const hasMultiLanguagePermission = user?.permission?.multi_language_seo;

  const {
    isFetching,

    enabled,
    primaryLanguage,
    shopLocales,

    isTogglingMultiLanguageSupport,
    toggleMultiLanguageSupport,

    selectedLocaleToSync,
    syncMultiLanguageData,
  } = useMultiLanguageSetting();

  if (isFetching) return <DummyPageSkeleton />;

  return (
    <BlockStack gap="400">
      <ProFeatureBanner
        title={"Multilingual SEO Support is available in Growth & Advanced plans only"}
        content="Upgrade your subscription to Growth or Advanced plan to access Multilingual SEO Support"
        applyForSpecificFeature
        featureKey="multi_language_seo"
        actionLabel="Unlock Multilingual SEO"
      />

      {shopLocales.length <= 1 && (
        <Banner
          tone="warning"
          title={t("You do not have Multilingual SEO support")}
          onDismiss={() => {}}
        >
          <BlockStack gap="200">
            <Box>
              <Text as="p">
                {t("To translate your store's pages into multiple languages, follow these steps:")}
                <Box paddingInlineStart="300">
                  <li>{t("Navigate to the 'Settings' option in the left sidebar of your Shopify dashboard")}</li>
                  <li>{t("Click on the 'Languages' tab in the left side")}</li>
                  <li>{t("Install the 'Shopify Translate & Adapt' app")}</li>
                  <li>{t("Open the app and translate the pages you want into your desired languages")}</li>
                </Box>
              </Text>
            </Box>

            <InlineStack align="start">
              <Button
                onClick={() => redirect.ADMIN_PATH(`/settings/languages`, { external: true })}
                disabled={!hasMultiLanguagePermission}
              >
                {t("Shopify settings")}
              </Button>
            </InlineStack>
          </BlockStack>
        </Banner>
      )}

      <Card>
        <InlineStack align="space-between">
          <BlockStack gap="150">
            <InlineStack
              align="start"
              blockAlign="center"
              gap="150"
            >
              <Text
                variant="headingSm"
                tone={!hasMultiLanguagePermission ? "subdued" : ""}
              >
                {t("Multilingual SEO Support")}
              </Text>
              <Badge
                tone={
                  !hasMultiLanguagePermission || shopLocales.length <= 1 ? "subdued" : enabled ? "success" : "critical"
                }
              >
                {enabled ? t("on") : t("off")}
              </Badge>
            </InlineStack>

            <Text tone={!hasMultiLanguagePermission ? "subdued" : ""}>
              {t("Enable to optimize your products for SEO that have been translated in multiple languages")}
            </Text>
          </BlockStack>

          <Box>
            <Button
              onClick={toggleMultiLanguageSupport}
              loading={isTogglingMultiLanguageSupport}
              disabled={!hasMultiLanguagePermission || shopLocales.length <= 1}
            >
              {t(`Turn ${enabled ? "off" : "on"}`)}
            </Button>
          </Box>
        </InlineStack>
      </Card>

      <Card>
        <BlockStack gap="400">
          <BlockStack gap="150">
            <Text
              as="h4"
              variant="headingSm"
              tone={!hasMultiLanguagePermission ? "subdued" : ""}
            >
              {t("Published Language")}
            </Text>
            <Text tone={!hasMultiLanguagePermission ? "subdued" : ""}>
              {t("Active in the markets they've been added to and visible to customers")}
            </Text>
          </BlockStack>

          <Card>
            <BlockStack gap="200">
              {shopLocales.map((locale, idx) => (
                <Box
                  paddingInline="100"
                  key={locale.locale}
                >
                  <InlineStack
                    align="space-between"
                    blockAlign="center"
                  >
                    <InlineStack
                      align="start"
                      gap="100"
                    >
                      <Text tone={!hasMultiLanguagePermission ? "subdued" : ""}>{locale.name}</Text>
                      {locale.locale === primaryLanguage && (
                        <Badge tone={!hasMultiLanguagePermission ? "subdued" : ""}>Default</Badge>
                      )}
                    </InlineStack>
                    <InlineStack
                      gap={"100"}
                      align="end"
                    >
                      {locale.locale !== primaryLanguage && (
                        <TooltipWrapper
                          show={locale.syncOngoing}
                          content={t("Syncing translations...")}
                        >
                          <Button
                            icon={ResetIcon}
                            variant="tertiary"
                            loading={locale.syncOngoing || locale.locale === selectedLocaleToSync}
                            onClick={() => syncMultiLanguageData(locale.locale)}
                            disabled={!hasMultiLanguagePermission || !enabled}
                          >
                            Sync
                          </Button>
                        </TooltipWrapper>
                      )}
                      <Button
                        icon={LanguageTranslateIcon}
                        variant="plain"
                        onClick={() =>
                          redirect.ADMIN_PATH(`/apps/translate-and-adapt?shopLocale=${locale.locale}`, {
                            external: true,
                          })
                        }
                        disabled={!hasMultiLanguagePermission || !enabled}
                      >
                        Translate
                      </Button>
                    </InlineStack>
                  </InlineStack>

                  {idx !== shopLocales.length - 1 && (
                    <Box
                      paddingBlockStart="150"
                      paddingBlockEnd="100"
                    >
                      <Divider />
                    </Box>
                  )}
                </Box>
              ))}
            </BlockStack>
          </Card>
        </BlockStack>
      </Card>
    </BlockStack>
  );
}

import RemoveBrandingCard from "@/components/sitemap/RemoveBrandingCard";
import { useAppQuery, useShopApi } from "@/hooks";
import { useUnsavedChanges } from "@/hooks/useUnsavedChanges";
import ContextualSaveBar from "@/modules/components/ContextualSaveBar";
import { setAllValuesToNull } from "@/utility/helpers";
import queryKeys from "@/utility/queryKeys";
import {
  BlockStack,
  Box,
  Button,
  Card,
  Checkbox,
  InlineGrid,
  InlineStack,
  Popover,
  Select,
  Tabs,
  Text,
  TextField,
} from "@shopify/polaris";
import { isEmpty } from "lodash";
import { useCallback, useEffect, useState } from "react";
import { HexColorPicker } from "react-colorful";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { useSearchParams } from "react-router-dom";

const initialState = {
  mainHeading: "Sitemap",
  description: "Looking for any particular page? Check out our sitemap for a list of all our pages.",
  productHeading: "Products",
  pageHeading: "Pages",
  blogHeading: "Blogs",
  collectionHeading: "Collections",
  buttonText: "View more",
  linkColor: "#005BD3",
  headingColor: "#91D0FF",
  buttonColor: "#29845A",
  buttonTextColor: "#FFFFFF",
  textColor: "#303030",
  backgroundColor: "#FFFFFF",
  textAlign: "left",
  columns: 3,
  homepageItemLimit: 10,
  hideProducts: false,
  hidePages: false,
  hideBlogs: false,
  hideCollections: false,

  homepageMetaTitle: "",
  homepageMetaDescription: "",

  productMetaTitle: "",
  productMetaDescription: "",

  pageMetaTitle: "",
  pageMetaDescription: "",

  blogMetaTitle: "",
  blogMetaDescription: "",

  collectionMetaTitle: "",
  collectionMetaDescription: "",
};

const colors = [
  { key: "linkColor", label: "Link Color", color: initialState.linkColor },
  { key: "headingColor", label: "Heading Color", color: initialState.headingColor },
  { key: "buttonColor", label: "Button Color", color: initialState.buttonColor },
  { key: "buttonTextColor", label: "Button Text Color", color: initialState.buttonTextColor },
  { key: "textColor", label: "Text Color", color: initialState.textColor },
  { key: "backgroundColor", label: "Background Color", color: initialState.backgroundColor },
];

const initialErrors = setAllValuesToNull(initialState);

const textAlignOption = [
  { label: "Left", value: "left" },
  { label: "Center", value: "center" },
  { label: "Right", value: "right" },
];

const columnsOption = [
  { label: "01", value: "1" },
  { label: "02", value: "2" },
  { label: "03", value: "3" },
  { label: "04", value: "4" },
];

const limitOption = [
  { label: "10", value: "10" },
  { label: "20", value: "20" },
  { label: "30", value: "30" },
  { label: "40", value: "40" },
  { label: "50", value: "50" },
  { label: "100", value: "100" },
];

const visibilityOptions = [
  { label: "Hide Products", value: "hideProducts" },
  { label: "Hide collections", value: "hideCollections" },
  { label: "Hide Pages", value: "hidePages" },
  { label: "Hide Blogs", value: "hideBlogs" },
];

const HTMLSitemap = () => {
  const [sitemapSetting, setSitemapSetting] = useState(initialState);
  const [originalSitemapSetting, setOriginalSitemapSetting] = useState(initialState);
  const [errors, setErrors] = useState(initialErrors);

  const [selectedTabIndex, setSelectedTabIndex] = useState(0);

  const shopApi = useShopApi();
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();

  const tabs = [
    {
      id: "basic",
      content: t("Basic"),
      accessibilityLabel: "Basic",
      panelID: "Basic",
    },
    {
      id: "meta",
      content: t("Meta Contents"),
      accessibilityLabel: "Meta Contents",
      panelID: "Meta Contents",
    },
  ];

  const setValue = (value, field) => setSitemapSetting((prevState) => ({ ...prevState, [field]: value }));

  // Track unsaved changes
  const { hasUnsavedChanges, DiscardChangesModal, setShowDiscardChangeModal } = useUnsavedChanges({
    originalData: originalSitemapSetting,
    currentData: sitemapSetting,
    onDiscardAction: () => {
      setSitemapSetting(originalSitemapSetting);
      setErrors(initialErrors);
    },
  });

  const { data, isLoading } = useAppQuery({
    queryKey: [queryKeys.HTML_SITEMAP_SETTING],
    queryFn: () => shopApi.getHtmlSitemapSetting(),
    reactQueryOptions: {
      staleTime: 0,
      onSuccess(data) {
        if (!isEmpty(data)) {
          let apiData = {};
          Object.keys(initialState).forEach((key) => (apiData[key] = data?.[key]));
          setSitemapSetting(apiData);
          setOriginalSitemapSetting(apiData);
        }
      },
    },
  });

  const { mutate: updateHtmlSitemap, isLoading: isUpdating } = useMutation({
    mutationFn: () => shopApi.updateHtmlSitemapSetting(sitemapSetting),
    onSuccess: () => {
      // Update original data after successful save
      setOriginalSitemapSetting(sitemapSetting);
    },
  });

  useEffect(
    function setSelectedTabFromSearchParam() {
      const tabId = searchParams.get("tab") || tabs[0].id;
      const tabIndex = tabs.findIndex((t) => t.id === tabId);

      setSelectedTabIndex(tabIndex);
    },
    [searchParams]
  );

  const handleTabChange = (idx) => {
    // Prevent tab switching if there are unsaved changes
    if (hasUnsavedChanges) {
      setShowDiscardChangeModal(true);
      return;
    }

    const tabId = tabs[idx].id;

    setSearchParams({
      ...Object.fromEntries(searchParams),
      tab: tabId,
    });
  };

  return (
    <>
      <DiscardChangesModal />

      <ContextualSaveBar
        id="html-sitemap-settings"
        open={hasUnsavedChanges}
        isLoading={isUpdating}
        onSave={updateHtmlSitemap}
        onDiscard={() => setShowDiscardChangeModal(true)}
      />

      <div className="ss-reports-tab">
        <Tabs
          tabs={tabs}
          selected={selectedTabIndex}
          onSelect={handleTabChange}
        />
      </div>

      <BlockStack gap="400">
        {selectedTabIndex === 0 && (
          <>
            <Card>
              <BlockStack gap="400">
                <Box>
                  <Text
                    as="h4"
                    variant="headingMd"
                  >
                    {t("Sitemap Home Page")}
                  </Text>
                  <Text
                    as="p"
                    tone="subdued"
                  >
                    {t("Customize the main heading and description for your HTML Sitemap home page from below")}
                  </Text>
                </Box>

                <Box maxWidth="500px">
                  <TextField
                    label={t("Main Heading")}
                    placeholder="Sitemap"
                    value={sitemapSetting?.mainHeading}
                    onChange={(value) => setValue(value, "mainHeading")}
                    error={errors["mainHeading"]}
                  />
                </Box>

                <Box maxWidth="500px">
                  <TextField
                    label={t("Description")}
                    placeholder=""
                    value={sitemapSetting?.description}
                    onChange={(value) => setValue(value, "description")}
                    error={errors["description"]}
                    multiline={4}
                  />
                </Box>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Box>
                  <Text
                    as="h4"
                    variant="headingMd"
                  >
                    {t("Section Headings")}
                  </Text>
                  <Text
                    as="p"
                    tone="subdued"
                  >
                    {t("Modify the section heading from below")}
                  </Text>
                </Box>

                <InlineGrid
                  gap="400"
                  columns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 2 }}
                >
                  <TextField
                    label={t("Product Heading")}
                    placeholder="Products"
                    value={sitemapSetting?.productHeading}
                    onChange={(value) => setValue(value, "productHeading")}
                    error={errors["productHeading"]}
                  />

                  <TextField
                    label={t("Collection Heading")}
                    placeholder="Collections"
                    value={sitemapSetting?.collectionHeading}
                    onChange={(value) => setValue(value, "collectionHeading")}
                    error={errors["collectionHeading"]}
                  />

                  <TextField
                    label={t("Page Heading")}
                    placeholder="Pages"
                    value={sitemapSetting?.pageHeading}
                    onChange={(value) => setValue(value, "pageHeading")}
                    error={errors["pageHeading"]}
                  />

                  <TextField
                    label={t("Blog Heading")}
                    placeholder="Blogs"
                    value={sitemapSetting?.blogHeading}
                    onChange={(value) => setValue(value, "blogHeading")}
                    error={errors["blogHeading"]}
                  />
                  <TextField
                    label={t("View more button text")}
                    placeholder="View more"
                    value={sitemapSetting?.buttonText}
                    onChange={(value) => setValue(value, "buttonText")}
                    error={errors["buttonText"]}
                  />
                </InlineGrid>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Box>
                  <Text
                    as="h4"
                    variant="headingMd"
                  >
                    {t("Colors")}
                  </Text>
                  <Text
                    as="p"
                    tone="subdued"
                  >
                    {t("Customize the colors of your HTML Sitemap page and match them with your brand identity")}
                  </Text>
                </Box>

                <Box>
                  <InlineGrid
                    gap="400"
                    columns={{ xs: 1, sm: 2, md: 3, lg: 3, xl: 3 }}
                  >
                    {colors.map((color) => (
                      <InlineStack
                        key={color.key}
                        gap="200"
                        blockAlign="center"
                        align="start"
                      >
                        <SSColorPicker
                          key={color.key}
                          color={sitemapSetting[color.key]}
                          setColor={(value) => setValue(value, color.key)}
                        />

                        <BlockStack>
                          <Text
                            as="p"
                            variant="bodyMd"
                          >
                            {t(color.label)}
                          </Text>
                          <Text
                            as="span"
                            variant="bodySm"
                            tone="subdued"
                          >
                            {sitemapSetting[color.key]?.toUpperCase()}
                          </Text>
                        </BlockStack>
                      </InlineStack>
                    ))}
                  </InlineGrid>
                </Box>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Box>
                  <Text
                    as="h4"
                    variant="headingMd"
                  >
                    {t("Styling")}
                  </Text>
                  <Text
                    as="p"
                    tone="subdued"
                  >
                    {t("Customize your page layout from below")}
                  </Text>
                </Box>

                <InlineGrid
                  gap="400"
                  columns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 2 }}
                >
                  <Select
                    label={t("Text Align")}
                    options={textAlignOption}
                    onChange={(value) => setValue(value, "textAlign")}
                    value={sitemapSetting?.textAlign}
                    error={errors["textAlign"]}
                  />

                  <Select
                    label={t("Columns")}
                    options={columnsOption}
                    onChange={(value) => {
                      setValue(value, "columns");
                    }}
                    value={String(sitemapSetting?.columns)}
                    error={errors["columns"]}
                  />

                  <Select
                    label={t("Number of items to show on sitemap homepage")}
                    options={limitOption}
                    onChange={(value) => setValue(value, "homepageItemLimit")}
                    value={sitemapSetting?.homepageItemLimit}
                    error={errors["homepageItemLimit"]}
                  />
                </InlineGrid>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Box>
                  <Text
                    as="h4"
                    variant="headingMd"
                  >
                    {t("Visibility")}
                  </Text>
                  <Text
                    as="p"
                    tone="subdued"
                  >
                    {t("Hide or show specific groups on the sitemap page")}
                  </Text>
                </Box>

                <InlineGrid
                  columns={{ xs: 1, sm: 3, md: 4, lg: 4, xl: 4 }}
                  gap="400"
                >
                  {visibilityOptions.map((op) => (
                    <Checkbox
                      id={op.value}
                      key={op.value}
                      label={t(op.label)}
                      value={op.value}
                      checked={sitemapSetting[op.value]}
                      onChange={(value) => setValue(value, op.value)}
                      error={errors[op.value]}
                    />
                  ))}
                </InlineGrid>
              </BlockStack>
            </Card>
          </>
        )}

        {selectedTabIndex === 1 && (
          <>
            <Card>
              <BlockStack gap="400">
                <Box>
                  <Text
                    as="h4"
                    variant="headingMd"
                  >
                    {t("Meta information for sitemap homepage")}
                  </Text>
                  <Text
                    as="p"
                    tone="subdued"
                  >
                    {t("Customize the meta title and description for your HTML Sitemap home page from below")}
                  </Text>
                </Box>

                <Box maxWidth="500px">
                  <TextField
                    label={t("Meta Title")}
                    placeholder={sitemapSetting?.mainHeading}
                    value={sitemapSetting?.homepageMetaTitle}
                    onChange={(value) => setValue(value, "homepageMetaTitle")}
                    error={errors["homepageMetaTitle"]}
                  />
                </Box>

                <Box maxWidth="500px">
                  <TextField
                    label={t("Meta Description")}
                    placeholder={sitemapSetting?.description}
                    value={sitemapSetting?.homepageMetaDescription}
                    onChange={(value) => setValue(value, "homepageMetaDescription")}
                    error={errors["homepageMetaDescription"]}
                    multiline={4}
                  />
                </Box>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Box>
                  <Text
                    as="h4"
                    variant="headingMd"
                  >
                    {t("Meta information for products sitemap")}
                  </Text>
                  <Text
                    as="p"
                    tone="subdued"
                  >
                    {t("Customize the meta title and description for your HTML Sitemap products page from below")}
                  </Text>
                </Box>

                <Box maxWidth="500px">
                  <TextField
                    label={t("Meta Title")}
                    placeholder={sitemapSetting?.productHeading}
                    value={sitemapSetting?.productMetaTitle}
                    onChange={(value) => setValue(value, "productMetaTitle")}
                    error={errors["productMetaTitle"]}
                  />
                </Box>

                <Box maxWidth="500px">
                  <TextField
                    label={t("Meta Description")}
                    placeholder={sitemapSetting?.description}
                    value={sitemapSetting?.productMetaDescription}
                    onChange={(value) => setValue(value, "productMetaDescription")}
                    error={errors["productMetaDescription"]}
                    multiline={4}
                  />
                </Box>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Box>
                  <Text
                    as="h4"
                    variant="headingMd"
                  >
                    {t("Meta information for collections sitemap")}
                  </Text>
                  <Text
                    as="p"
                    tone="subdued"
                  >
                    {t("Customize the meta title and description for your HTML Sitemap collections page from below")}
                  </Text>
                </Box>

                <Box maxWidth="500px">
                  <TextField
                    label={t("Meta Title")}
                    placeholder={sitemapSetting?.collectionHeading}
                    value={sitemapSetting?.collectionMetaTitle}
                    onChange={(value) => setValue(value, "collectionMetaTitle")}
                    error={errors["collectionMetaTitle"]}
                  />
                </Box>

                <Box maxWidth="500px">
                  <TextField
                    label={t("Meta Description")}
                    placeholder={sitemapSetting?.description}
                    value={sitemapSetting?.collectionMetaDescription}
                    onChange={(value) => setValue(value, "collectionMetaDescription")}
                    error={errors["collectionMetaDescription"]}
                    multiline={4}
                  />
                </Box>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Box>
                  <Text
                    as="h4"
                    variant="headingMd"
                  >
                    {t("Meta information for pages sitemap")}
                  </Text>
                  <Text
                    as="p"
                    tone="subdued"
                  >
                    {t("Customize the meta title and description for your HTML Sitemap pages page from below")}
                  </Text>
                </Box>

                <Box maxWidth="500px">
                  <TextField
                    label={t("Meta Title")}
                    placeholder={sitemapSetting?.pageHeading}
                    value={sitemapSetting?.pageMetaTitle}
                    onChange={(value) => setValue(value, "pageMetaTitle")}
                    error={errors["pageMetaTitle"]}
                  />
                </Box>

                <Box maxWidth="500px">
                  <TextField
                    label={t("Meta Description")}
                    placeholder={sitemapSetting?.description}
                    value={sitemapSetting?.pageMetaDescription}
                    onChange={(value) => setValue(value, "pageMetaDescription")}
                    error={errors["pageMetaDescription"]}
                    multiline={4}
                  />
                </Box>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Box>
                  <Text
                    as="h4"
                    variant="headingMd"
                  >
                    {t("Meta information for blog posts sitemap")}
                  </Text>
                  <Text
                    as="p"
                    tone="subdued"
                  >
                    {t("Customize the meta title and description for your HTML Sitemap blog posts page from below")}
                  </Text>
                </Box>

                <Box maxWidth="500px">
                  <TextField
                    label={t("Meta Title")}
                    placeholder={sitemapSetting?.blogHeading}
                    value={sitemapSetting?.blogMetaTitle}
                    onChange={(value) => setValue(value, "blogMetaTitle")}
                    error={errors["blogMetaTitle"]}
                  />
                </Box>

                <Box maxWidth="500px">
                  <TextField
                    label={t("Meta Description")}
                    placeholder={sitemapSetting?.description}
                    value={sitemapSetting?.blogMetaDescription}
                    onChange={(value) => setValue(value, "blogMetaDescription")}
                    error={errors["blogMetaDescription"]}
                    multiline={4}
                  />
                </Box>
              </BlockStack>
            </Card>
          </>
        )}

        <RemoveBrandingCard />

        <InlineStack align="end">
          <Button
            variant="primary"
            onClick={updateHtmlSitemap}
            loading={isUpdating}
            disabled={!hasUnsavedChanges}
          >
            {t("Save")}
          </Button>
        </InlineStack>
      </BlockStack>
    </>
  );
};

function SSColorPicker({ color, setColor }) {
  const [popoverActive, setPopoverActive] = useState(false);

  const togglePopoverActive = useCallback(() => setPopoverActive((popoverActive) => !popoverActive), []);

  return (
    <Popover
      active={popoverActive}
      activator={
        <div
          onClick={togglePopoverActive}
          style={{
            backgroundColor: color,
            width: "40px",
            height: "40px",
            border: "1px solid var(--p-color-border)",
            borderRadius: "40px",
            cursor: "pointer",
          }}
        />
      }
      autofocusTarget="first-node"
      onClose={togglePopoverActive}
    >
      <Box
        padding="400"
        width="232px"
      >
        <BlockStack gap="200">
          <HexColorPicker
            color={color}
            onChange={(value) => setColor(value.toUpperCase())}
          />

          <InlineStack
            gap="200"
            wrap={false}
          >
            <div
              style={{
                backgroundColor: color,
                width: "30px",
                height: "30px",
                border: "1px solid var(--p-color-border)",
                borderRadius: "var(--p-border-radius-150)",
              }}
            />
            <div>
              <TextField
                value={color}
                onChange={(value) => setColor(value.toUpperCase())}
              />
            </div>
          </InlineStack>
        </BlockStack>
      </Box>
    </Popover>
  );
}

export default HTMLSitemap;

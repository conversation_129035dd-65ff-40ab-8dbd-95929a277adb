import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, InlineStack, SkeletonBodyText, Text } from "@shopify/polaris";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import seoApps from "storeseo-enums/seoApps";
import EmptyMsgCard from "../../components/common/EmptyMsgCard.jsx";
import SeoMigratorOption from "../../components/seo-setting/SEOMigratorOption.jsx";
import { useAppQuery, useShopApi, useUtilityApi } from "../../hooks/index.js";
import useConfirmation from "../../hooks/useConfirmation.jsx";
import queryKeys from "../../utility/queryKeys.js";

export default function SeoMigrator() {
  const { t } = useTranslation();
  const utilityApi = useUtilityApi();
  const shopApi = useShopApi();
  const { renderConfirmation, showConfirmation, hideConfirmation } = useConfirmation();

  console.log("seoApps: ", seoApps);

  const [selectedSeoApp, setSelectedSeoApp] = useState(null);

  const { data: installedApps = [], isFetching } = useAppQuery({
    queryKey: queryKeys.INSTALLED_APPS_LIST,
    queryFn: utilityApi.getListOfInstalledSeoApps,
  });

  const { mutate: migrateData, isLoading } = useMutation({
    mutationFn: () => shopApi.migrateDataFromAnotherApp(selectedSeoApp),
    onSuccess: () => {
      hideConfirmation();
    },
  });

  const handleChange = (value, id) => {
    setSelectedSeoApp(value ? id : "");
  };

  return (
    <>
      <BlockStack gap="400">
        <Card>
          <BlockStack gap="200">
            <Text
              variant="headingMd"
              as="h4"
            >
              {t("Migrate Your SEO Data From Another App")}
            </Text>
            <Text
              as="p"
              tone="subdued"
            >
              {t(
                "Using multiple SEO apps together may cause unintended side effects! StoreSEO can detect any other SEO apps you have installed on your store & help you migrate important SEO data from those apps to StoreSEO. Please note that it's a one-way process & this will update all your product data; your existing SEO score may also change after this."
              )}
            </Text>

            {/*{!isFetching && installedApps.length < 1 && (*/}
            {/*  <Text*/}
            {/*    as="h5"*/}
            {/*    variant="headingSm"*/}
            {/*  >*/}
            {/*    {t("Note: You do not have any other SEO app installed")}*/}
            {/*  </Text>*/}
            {/*)}*/}
          </BlockStack>
        </Card>

        {isFetching && (
          <Card>
            <SkeletonBodyText lines={3} />
          </Card>
        )}
        {!isFetching && installedApps.length < 1 && (
          <EmptyMsgCard message={t("You do not have any other SEO app installed.")} />
        )}
        {Object.values(seoApps).map((app) => {
          return (
            installedApps.includes(app) && (
              <SeoMigratorOption
                key={seoApps.metadata[app].handle}
                option={{ ...seoApps.metadata[app], name: app }}
                checked={selectedSeoApp === app}
                onChange={handleChange}
              />
            )
          );
        })}
        {installedApps.length > 0 && (
          <InlineStack align="end">
            <Button
              onClick={showConfirmation}
              variant="primary"
            >
              {t("Migrate")}
            </Button>
          </InlineStack>
        )}
      </BlockStack>
      {renderConfirmation({
        title: "Are you sure?",
        content: "Data migration will begin if you proceed!",
        primaryAction: migrateData,
        primaryActionText: "Yes",
        loading: isLoading,
      })}
    </>
  );
}

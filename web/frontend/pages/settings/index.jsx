import { BlockStack, Box, Card, Grid, Text, TextField } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import LogoUploader<PERSON>iewer from "../../components/common/LogoUploaderViewer.jsx";
import GoogleIndexingToggler from "../../components/togglers/GoogleIndexingToggler.jsx";
// import JsonLDToggler from "../../components/togglers/JsonLDToggler.jsx";
import RedirectOutOfStockToggler from "../../components/togglers/RedirectOutOfStockToggler.jsx";
import { useAppQuery, useShopApi } from "../../hooks/index.js";
import useConfirmation from "../../hooks/useConfirmation.jsx";
import queryKeys from "../../utility/queryKeys.js";

export default function Settings() {
  const { t } = useTranslation();
  const user = useSelector((state) => state.user);
  const shopApi = useShopApi();

  const { shopName = "", url: shopURL = "", shopLogo, shop: shopDomain } = user || {};

  const { data: industries } = useAppQuery({
    queryKey: queryKeys.INDUSTRIES_LIST,
    queryFn: shopApi.getIndustries,
    reactQueryOptions: {
      staleTime: Infinity,
      refetchInterval: false,
      placeholderData: [],
    },
  });

  return (
    <BlockStack gap="400">
      <LogoUploaderViewer currentLogoURL={shopLogo} />
      <Card>
        <BlockStack gap="400">
          <Text
            variant="headingMd"
            as="h5"
          >
            {t("Basic Store Information")}
          </Text>
          <Grid>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 6, xl: 6 }}>
              <TextField
                label={t("Store Name")}
                value={shopName}
                disabled
                placeholder="WPDeveloper Demo"
                autoComplete="off"
                helpText={t("To modify this field, please visit your store's admin panel in Shopify")}
              />
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 6, xl: 6 }}>
              <TextField
                label={t("Store Domain")}
                value={shopURL}
                placeholder="wpdeveloper-demo.myshopify.com"
                autoComplete="off"
                helpText={t("To modify this field, please visit your store's admin panel in Shopify")}
                disabled
              />
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 6, xl: 6 }}>
              <TextField
                label={t("Industry")}
                value={industries.find((ind) => ind.id === user.industryId)?.title}
                autoComplete="off"
                disabled
              />
            </Grid.Cell>
          </Grid>
        </BlockStack>
      </Card>
      <Box
        position="relative"
        padding={0}
      >
        <ToggleSection />
      </Box>
    </BlockStack>
  );
}

function ToggleSection() {
  const user = useSelector((state) => state.user);
  const { renderConfirmation, showConfirmation, hideConfirmation } = useConfirmation();

  return (
    <Card>
      <BlockStack gap="200">
        {/* <JsonLDToggler isProUser={user.isPremium} /> */}
        <GoogleIndexingToggler isProUser={user.isPremium} />
        <RedirectOutOfStockToggler isProUser={user.isPremium} />
      </BlockStack>
    </Card>
  );
}

//@ts-check
import { useBackupList } from "@/lib/hooks/settings/backup-restore";
import BackupDataList from "@/modules/settings/backup-restore/BackupDataList";
import CreateBackupActionCard from "@/modules/settings/backup-restore/CreateBackupActionCard";
import { BlockStack } from "@shopify/polaris";

export default function BackupAndRestore() {
  const { data } = useBackupList();

  return (
    <BlockStack gap="400">
      {data && data.length > 0 && <CreateBackupActionCard />}
      <BackupDataList />
    </BlockStack>
  );
}

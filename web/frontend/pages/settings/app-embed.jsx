import { BlockStack } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import AppEmbedContent from "../../components/common/AppEmbedContent";
import AppEmbedWarningBanner from "../../components/common/AppEmbedWarningBanner";
import { PAGE_TEMPLATES } from "../../config";

export default function AppEmbed() {
  const { t } = useTranslation();

  return (
    <BlockStack gap="400">
      <AppEmbedWarningBanner
        template={PAGE_TEMPLATES.SETTINGS}
        showAction={false}
      />

      <AppEmbedContent />
    </BlockStack>
  );
}

import { BlockStack } from "@shopify/polaris";
import ImageOptimizerNotEnabledBanner from "../../components/image-optimizer/NotEnabledBanner";
import ImageOptimizerSettingsCard from "../../components/image-optimizer/SettingsCard";
import useUserAddon from "../../hooks/useUserAddon";

const ImageOptimizer = () => {
  const { hasImageOptimizer } = useUserAddon();

  return (
    <BlockStack gap="400">
      {!hasImageOptimizer && <ImageOptimizerNotEnabledBanner />}

      <ImageOptimizerSettingsCard />
    </BlockStack>
  );
};

export default ImageOptimizer;

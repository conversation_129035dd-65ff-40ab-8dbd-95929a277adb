import googleIntegrationSteps from "storeseo-enums/googleIntegrationSteps";
import { useTranslation } from "react-i18next";
import { showNotification, sSEOConfirm } from "../../utility/helpers";
import { useSelector } from "react-redux";
import { useAppQuery, useShop<PERSON><PERSON> } from "../../hooks";
import queryKeys from "../../utility/queryKeys";
import { useMutation, useQueryClient } from "react-query";
import { useNavigate, useSearchParams } from "react-router-dom";
import React, { useEffect } from "react";
import { isEmpty } from "lodash";
import { useAppBridgeRedirect } from "../../hooks/useAppBridgeRedirect";
import {
  BlockStack,
  Box,
  Button,
  Card,
  Icon,
  InlineStack,
  SkeletonBodyText,
  SkeletonDisplayText,
  Text,
} from "@shopify/polaris";
import { CheckCircleIcon } from "@shopify/polaris-icons";
import ResetIntegration from "../../components/google-integration/ResetIntegration.jsx";
import ProFeatureBanner from "../../components/common/ProFeatureBanner.jsx";
import { useBanner } from "../../hooks/useBanner.jsx";

const MESSAGES = {
  [googleIntegrationSteps.AUTHENTICATE]: "Google sign-in is",
  [googleIntegrationSteps.SITE_VERIFICATION]: "Google verification is",
  [googleIntegrationSteps.SEARCH_CONSOLE_ADD_SITE]: "Google search console is",
  [googleIntegrationSteps.SITEMAP_SUBMISSION]: "Sitemap submission to Google is",
  [googleIntegrationSteps.INSTANT_INDEXING]: "Permission for instant indexing is",
  [googleIntegrationSteps.ANALYTICS_DATA]: "Access To Google Analytics is",
};

const LABELS = {
  [googleIntegrationSteps.AUTHENTICATE]: "Sign-in now",
  [googleIntegrationSteps.SITE_VERIFICATION]: "Verify now",
  [googleIntegrationSteps.SEARCH_CONSOLE_ADD_SITE]: "Link now",
  [googleIntegrationSteps.SITEMAP_SUBMISSION]: "Permit now",
  [googleIntegrationSteps.INSTANT_INDEXING]: "Allow now",
  [googleIntegrationSteps.ANALYTICS_DATA]: "Grant Access",
};

const COMPLETED_STATUS_TEXTS = {
  [googleIntegrationSteps.AUTHENTICATE]: "complete",
  [googleIntegrationSteps.SITE_VERIFICATION]: "complete",
  [googleIntegrationSteps.SEARCH_CONSOLE_ADD_SITE]: "linked",
  [googleIntegrationSteps.SITEMAP_SUBMISSION]: "permitted",
  [googleIntegrationSteps.INSTANT_INDEXING]: "allowed",
  [googleIntegrationSteps.ANALYTICS_DATA]: "granted",
};

const INCOMPLETE_STATUS_TEXTS = {
  [googleIntegrationSteps.AUTHENTICATE]: "incomplete",
  [googleIntegrationSteps.SITE_VERIFICATION]: "incomplete",
  [googleIntegrationSteps.SEARCH_CONSOLE_ADD_SITE]: "unlinked",
  [googleIntegrationSteps.SITEMAP_SUBMISSION]: "not permitted",
  [googleIntegrationSteps.INSTANT_INDEXING]: "not allowed",
  [googleIntegrationSteps.ANALYTICS_DATA]: "not granted",
};

const DUMMY_INTEGRATION_INFO = {
  steps: {
    [googleIntegrationSteps.AUTHENTICATE]: false,
    [googleIntegrationSteps.SITE_VERIFICATION]: false,
    [googleIntegrationSteps.SEARCH_CONSOLE_ADD_SITE]: false,
    [googleIntegrationSteps.SITEMAP_SUBMISSION]: false,
    [googleIntegrationSteps.INSTANT_INDEXING]: false,
    [googleIntegrationSteps.ANALYTICS_DATA]: false,
  },
};

const PageSkeleton = ({ t }) => (
  <BlockStack gap="400">
    {/*<HorizontalStack align="space-between">
      <Text
        variant="headingMd"
        as="h2"
      >
        {t("Google Integration")}
      </Text>

      <Box width="7.5rem">
        <SkeletonDisplayText size="small" />
      </Box>
    </HorizontalStack>*/}
    {Array(7)
      .fill(1)
      .map((e, idx) => (
        <Card key={idx}>
          <InlineStack
            align="space-between"
            blockAlign="center"
          >
            <Box width="55%">
              <SkeletonBodyText lines={3} />
            </Box>
            <Box width="7.5rem">
              <SkeletonDisplayText size="small" />
            </Box>
          </InlineStack>
        </Card>
      ))}
  </BlockStack>
);

const IntegrationStep = ({ label, statusText, btnLabel, disabled, done = false, onClick = () => {} }) => {
  const { t } = useTranslation();
  const user = useSelector((state) => state.user);
  const hasGoogleConsolePermission = user?.isPremium && user?.permission?.google_console;

  return (
    <Card>
      <InlineStack
        align="space-between"
        blockAlign="center"
        gap="200"
      >
        <InlineStack gap="100">
          <Text>{t(label)}</Text>
          <Text fontWeight="bold">{t(statusText)}</Text>
          {done && (
            <InlineStack>
              <Icon
                source={CheckCircleIcon}
                tone="success"
              />
            </InlineStack>
          )}
        </InlineStack>
        <span style={{ width: "120px" }}>
          {!done && (
            <Button
              disabled={!hasGoogleConsolePermission || disabled}
              onClick={onClick}
              fullWidth
            >
              {t(btnLabel)}
            </Button>
          )}
        </span>
      </InlineStack>
    </Card>
  );
};

export default function GoogleIntegration() {
  const { t } = useTranslation();
  const shopApi = useShopApi();
  const user = useSelector((state) => state.user);
  const queryClient = useQueryClient();
  const [searchParams, setSearchParams] = useSearchParams();
  const redirect = useAppBridgeRedirect();
  const navigate = useNavigate();

  const queryAuthTarget = searchParams.get("authTarget");

  const { data, isFetching } = useAppQuery({
    queryKey: queryKeys.GOOGLE_INTEGRATION_INFO,
    queryFn: shopApi.getGoogleIntegrationInfo,
  });
  const { integrationInfo, authURLs } = data || {};

  const { Banner: ResetFailedBanner, showBanner } = useBanner({
    title: t("Google disconnect failed"),
    message: t(
      "Failed to disconnect properly from Google. It's on us. Please try again. If the error persists, contact with our support team."
    ),
    noMargin: true,
  });

  const { mutate: reset, isLoading: isResetingSetting } = useMutation({
    mutationFn: shopApi.resetGoogleIntegrationInfo,
    onSuccess: () => {
      queryClient.setQueryData(queryKeys.GOOGLE_INTEGRATION_INFO, {
        integrationInfo: DUMMY_INTEGRATION_INFO,
        authURLs,
      });
      queryClient.invalidateQueries(queryKeys.SITEMAP_INFO);
    },
    onError: () => showBanner(true),
  });

  const { mutate: updateGoogleIndexingStatus } = useMutation({
    mutationFn: shopApi.updateGoogleIndexingStatus,
  });

  useEffect(
    function showToastOnSuccessfulRedirect() {
      const params = Object.fromEntries(searchParams.entries());

      const { success, message } = params;

      if (success === "true") {
        showNotification({
          type: "success",
          message,
        });

        delete params.success;
        delete params.message;

        setSearchParams(params, { replace: true });
      }
    },
    [searchParams]
  );

  useEffect(() => {
    if (queryAuthTarget === googleIntegrationSteps.INSTANT_INDEXING && !isEmpty(user)) {
      const params = Object.fromEntries(searchParams.entries());
      delete params.authTarget;
      setSearchParams(params, { replace: true });

      sSEOConfirm({
        title: "Do you want to instantly index your products in Google now?",
        button: {
          confirm: { text: "Yes", class: "button__primary" },
        },
        callback: updateGoogleIndexingStatus,
      });
    }
  }, [queryAuthTarget, user]);

  const hasGoogleConsolePermission = user?.isPremium && user?.permission?.google_console;

  const { steps } = integrationInfo || {};
  const stepsArr = Object.values(googleIntegrationSteps);

  // remove the google analytics step till we get the app approved
  stepsArr.pop();

  const userHasAuthenticatedForAnalyticsData = steps?.[googleIntegrationSteps.ANALYTICS_DATA];

  const handleStepClick = (stepUrl) => {
    // if (!hasGoogleConsolePermission) {
    //   showProAlert();
    //   return;
    // }

    redirect.REMOTE(stepUrl);
  };

  // const { showProAlert } = useOutletContext();

  return (
    <>
      {isFetching && <PageSkeleton t={t} />}
      {!isFetching && (
        <BlockStack gap="400">
          <ResetFailedBanner />

          <ProFeatureBanner
            title={t("Google Integration is a PRO feature")}
            content={t("Upgrade your subscription to enable Google Integration")}
          />
          {/* Google integration steps */}
          {stepsArr.map((step, idx) => (
            <IntegrationStep
              key={idx}
              label={MESSAGES[step]}
              statusText={steps?.[step] ? COMPLETED_STATUS_TEXTS[step] : INCOMPLETE_STATUS_TEXTS[step]}
              btnLabel={LABELS[step]}
              disabled={idx !== 0 && !steps?.[stepsArr[idx - 1]]}
              done={steps?.[step]}
              onClick={() => handleStepClick(authURLs?.[step])}
            />
          ))}
          {/* {userHasAuthenticatedForAnalyticsData && <AnalyticsPropertyId />} */}

          <Card>
            <BlockStack gap="200">
              <Text
                variant="headingSm"
                as="h5"
              >
                {t("Enable Google Analytics")}
              </Text>
              <Text>{t("Enable analytics to see reports based on real data.")}</Text>
              <InlineStack>
                <Button
                  disabled={!user.isPremium}
                  onClick={() => navigate("/settings/google-analytics")}
                >
                  {t("Setup analytics")}
                </Button>
              </InlineStack>
            </BlockStack>
          </Card>
          {steps[googleIntegrationSteps.AUTHENTICATE] && (
            <ResetIntegration
              loading={isResetingSetting}
              callback={reset}
            />
          )}
        </BlockStack>
      )}
    </>
  );
}

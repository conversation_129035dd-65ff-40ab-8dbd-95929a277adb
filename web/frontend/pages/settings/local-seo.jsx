import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Card, Grid, InlineStack, Text, TextField } from "@shopify/polaris";
import { debounce, truncate } from "lodash";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation, useQueryClient } from "react-query";
import { useSelector } from "react-redux";
import AutocompleteInput from "../../components/common/AutocompleteInput.jsx";
import LogoUploader<PERSON>iewer from "../../components/common/LogoUploaderViewer.jsx";
import ProFeatureBanner from "../../components/common/ProFeatureBanner.jsx";
import SelectInput from "../../components/common/SelectInput.jsx";
import { socialMediaLabels } from "../../config/index.js";
import { useSeoApi } from "../../hooks/apiHooks/useSeoApi.js";
import { useAppQuery, useUtility<PERSON><PERSON> } from "../../hooks/index.js";
import { useBanner } from "../../hooks/useBanner.jsx";
import { scrollToTop } from "../../utility/helpers.jsx";
import queryKeys from "../../utility/queryKeys.js";

const inputCategoryTypes = {
  NORMAL: "",
  ADDRESS: "address",
  GEO: "geo",
  SOCIAL: "socialMediaLinks",
};

const REG_EXP_FOR_VALID_URL = /^(?:https|http):\/\/(?:www\.)?[\w\W]{2,}\.[a-z]{2,6}/gu;

const initialErrorState = { [inputCategoryTypes.SOCIAL]: {} };

const LocalSEO = () => {
  const [jsonldData, setJsonldData] = useState({});
  const [locations, setLocations] = useState([]);
  const [selectedLocation, setSelectedLocation] = useState({});
  const [prevAddress1, setPrevAddress1] = useState(null);
  const [errors, setErrors] = useState({ ...initialErrorState });
  const [uploadingLogo, setUploadingLogo] = useState(false);
  const [autoSuggestionInputText, setAutoSuggestionInputText] = useState(null);
  const [isFirstLoad, setIsFirstLoad] = useState(true);
  const [autoSuggestionOptions, setAutoSuggestionOptions] = useState([]);

  const { t } = useTranslation();
  const user = useSelector((state) => state.user);
  const seoApi = useSeoApi();
  const utilityApi = useUtilityApi();
  const queryClient = useQueryClient();

  const { data, isFetching } = useAppQuery({
    queryKey: queryKeys.LOCAL_SEO_DATA,
    queryFn: seoApi.getLocalSEOData,
    reactQueryOptions: {
      enabled: user.isPremium == true,
    },
  });

  useEffect(() => {
    if (!data || !isFirstLoad) return;

    const { jsonldData, locations } = data;
    const allLocations = [
      { id: "primary", name: "Primary address", ...jsonldData?.address, ...jsonldData?.geo },
      ...locations,
    ];

    setJsonldData(jsonldData);
    setLocations(allLocations);
    setSelectedLocation(allLocations[0]);
    setPrevAddress1(jsonldData?.address?.address1);
    setIsFirstLoad(false);
  }, [data]);

  const { data: addressAutocompleteSuggestions, isFetching: isLoadingAddressAutoCompleteSuggestions } = useAppQuery({
    queryKey: [queryKeys.ADDRESS_AUTO_COMPLETE_SUGGESTIONS, { input: autoSuggestionInputText }],
    queryFn: () => utilityApi.getAddressAutocompleteSuggestions(selectedLocation.address1),
    reactQueryOptions: {
      staleTime: Infinity,
      refetchInterval: false,
    },
  });

  useEffect(() => {
    if (addressAutocompleteSuggestions) {
      setAutoSuggestionOptions(addressAutocompleteSuggestions.map((s) => ({ label: s.description, value: s.placeId })));
    }
  }, [addressAutocompleteSuggestions]);

  for (let key of Object.keys(socialMediaLabels)) {
    useEffect(
      function clearSocialMediaUrlError() {
        const errs = { ...errors };
        errs[inputCategoryTypes.SOCIAL][key] = false;

        setErrors(errs);
      },
      [jsonldData[inputCategoryTypes.SOCIAL]?.[key]]
    );
  }

  const { Banner: UpdateFailedBanner, showBanner: showUpdateFailedBanner } = useBanner({
    title: t("Update failed"),
    message: t(
      "Failed to update local SEO data. Please try again. If the error persists please contact with our support team."
    ),
  });

  const { mutate: updateLocalSEOData, isLoading: isUpdating } = useMutation({
    mutationFn: ({ jsonldData, locations }) => seoApi.updateLocalSEOData(jsonldData, locations),
    onSuccess: (data, { jsonldData, locations }) =>
      queryClient.setQueryData(queryKeys.LOCAL_SEO_DATA, {
        jsonldData,
        locations,
      }),
    onError: () => {
      showUpdateFailedBanner(true);
      scrollToTop();
    },
  });

  const { Banner, showBanner } = useBanner({
    title: t("Failed to update your data"),
    message: t("Looks like you don't have an active subscription to StoreSEO! Please subscribe and try again."),
  });

  const _debLoadAddressSuggestions = useCallback(
    debounce((text) => setAutoSuggestionInputText(text), 600),
    []
  );

  // const resetAddress1FieldIfNeeded = () => {
  //   if (displayAutocompleteSuggestions) {
  //     handleAddressInputChange({ target: { name: "address1", value: prevAddress1 } });
  //     setDisplayAutoCompleteSuggestions(false);
  //   }
  // };

  const validateURL = (url) => {
    return new RegExp(REG_EXP_FOR_VALID_URL).test(url);
  };

  const validateFormData = () => {
    const errors = { ...initialErrorState };
    for (let pair of Object.entries(jsonldData[inputCategoryTypes.SOCIAL])) {
      const [key, value] = pair;
      errors[inputCategoryTypes.SOCIAL][key] = value ? !validateURL(value) : false;
    }

    setErrors(errors);

    return !Object.values(errors[inputCategoryTypes.SOCIAL]).reduce((e1, e2) => (e1 ? e1 : e2), false);
  };

  const handleChange = (value, id, inputCategory = inputCategoryTypes.NORMAL) => {
    const el = document.getElementById(id);
    const { ADDRESS, GEO, SOCIAL, NORMAL } = inputCategoryTypes;
    // const { name: fieldName, value } = e.target;
    const fieldName = el.name;
    const newData = { ...jsonldData };
    const updatedLocation = { ...selectedLocation };
    const newErrors = { ...errors };

    switch (inputCategory) {
      case ADDRESS:
      case GEO:
        // console.log("\n---");
        // console.log("prev location info: ", selectedLocation);
        // console.log("---");
        // newData[ADDRESS][fieldName] = value;
        updatedLocation[fieldName] = value;

        // console.log("new location info: ", updatedLocation);
        // console.log("---\n");
        setSelectedLocation(updatedLocation);
        break;
      // case GEO:
      //   newData[GEO][fieldName] = value;
      //   break;
      case SOCIAL:
        if (!newData[SOCIAL]) {
          newData[SOCIAL] = {};
        }

        newData[SOCIAL][fieldName] = value;
        // newErrors[SOCIAL][fieldName] = value ? !validateURL(value) : false;
        break;
      case NORMAL:
      default:
        newData[fieldName] = value;
        break;
    }

    setJsonldData(newData);
    setErrors(newErrors);
  };

  const handleAddressInputChange = (val, id) => handleChange(val, id, inputCategoryTypes.ADDRESS);

  const handleGeoInputChange = (val, id) => handleChange(val, id, inputCategoryTypes.GEO);

  const handleSocialMediaLinkChange = (val, id) => handleChange(val, id, inputCategoryTypes.SOCIAL);

  const handleLogoImageChange = (uploadPath) => {
    handleChange({ target: { name: "image", value: uploadPath } });
    setUploadingLogo(false);
    queryClient.setQueryData(queryKeys.LOCAL_SEO_DATA, {
      ...data,
      jsonldData: { ...data.jsonldData, logoURL: uploadPath },
    });
  };

  const handleAddressQueryChange = (val, id) => {
    handleAddressInputChange(val, id, inputCategoryTypes.ADDRESS);
    _debLoadAddressSuggestions(val);
  };

  const handleAddressAutocomplete = async (placeId) => {
    // const { ADDRESS, GEO } = inputCategoryTypes;
    const {
      latitude = "",
      longitude = "",
      state = "",
      ...addressFields
    } = (await utilityApi.getAddressDetailsByPlaceId(placeId)) ?? {};

    // const newData = { ...jsonldData };

    setPrevAddress1(addressFields.address1);
    setSelectedLocation({ ...selectedLocation, ...addressFields, latitude, longitude });
  };

  const handleLocationSelect = (selected) => {
    const updatedLocations = locations.map((loc) => {
      if (loc.id === selectedLocation.id) {
        return selectedLocation;
      } else return loc;
    });

    const newSelectedLocation = updatedLocations.find((loc) => loc.id == selected.value);

    setSelectedLocation(newSelectedLocation);
    setLocations(updatedLocations);
  };

  const handleForm = async () => {
    if (!user?.isSubscribed) {
      showBanner(true);
      scrollToTop();
      return;
    }

    if (validateFormData()) {
      const updatedLocations = locations.map((loc) =>
        loc.id == selectedLocation.id ? { ...selectedLocation } : { ...loc }
      );
      const allLocations = updatedLocations.filter((loc) => loc.id !== "primary");
      const localSEOAddress = updatedLocations.find((loc) => loc.id == "primary");
      const localSEOGeo = {
        latitude: localSEOAddress.latitude,
        longitude: localSEOAddress.longitude,
      };

      delete localSEOAddress.id;
      delete localSEOAddress.name;
      delete localSEOAddress.latitude;
      delete localSEOAddress.longitude;

      const updatedData = {
        ...jsonldData,
        [inputCategoryTypes.ADDRESS]: localSEOAddress,
        [inputCategoryTypes.GEO]: localSEOGeo,
      };

      updateLocalSEOData({ jsonldData: updatedData, locations: allLocations });
    }
  };

  const renderLogoUploader = () => {
    return (
      <LogoUploaderViewer
        currentLogoURL={user.shopLogo || jsonldData.logoURL}
        onUploadStart={() => setUploadingLogo(true)}
        onUploadFinish={handleLogoImageChange}
        title="Your business logo"
        disabled={!user.isPremium}
      />
    );
  };

  const renderStoreInfoInputFields = () => {
    const { name, url, telephone, priceRange } = jsonldData;

    return (
      <Card>
        <BlockStack gap="400">
          <Text
            variant="headingMd"
            as="h5"
          >
            {t("Basic Store Information")}
          </Text>
          <Grid>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 6, xl: 6 }}>
              <TextField
                label={t("Store Name")}
                name="name"
                value={name}
                disabled
                placeholder="Your store name"
                autoComplete="off"
                helpText={t("To modify this field, please visit your store's admin panel in Shopify")}
              />
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 6, xl: 6 }}>
              <TextField
                label={t("Store Domain")}
                name="url"
                value={url}
                placeholder="Your store's website url"
                autoComplete="off"
                helpText={t("To modify this field, please visit your store's admin panel in Shopify")}
                disabled
              />
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 6, xl: 6 }}>
              <TextField
                label={t("Phone")}
                name="telephone"
                value={telephone}
                type="tel"
                placeholder=""
                onChange={handleChange}
                autoComplete="off"
                disabled={!user.isPremium}
              />
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 6, xl: 6 }}>
              <TextField
                label={t("Price range")}
                name="priceRange"
                value={priceRange}
                type="text"
                placeholder="$$$"
                onChange={handleChange}
                autoComplete="off"
                disabled={!user.isPremium}
              />
            </Grid.Cell>
          </Grid>
        </BlockStack>
      </Card>
    );
  };

  const renderAddressInputFields = () => {
    const { id, address1, address2, city, zip, country, name, latitude, longitude } = selectedLocation || {};

    const options = locations.map((l) => ({ label: truncate(l.name), value: l.id }));
    const selected = options.find((o) => o.value === id);

    return (
      <Card>
        <BlockStack gap="400">
          <InlineStack
            align="space-between"
            blockAlign="center"
            gap="400"
          >
            <Text
              variant="headingMd"
              as="h5"
            >
              {t("Your Detailed Store Address")}
            </Text>
            <SelectInput
              title="Addresses"
              options={options}
              selected={selected}
              disabled={!user.isPremium}
              onSelect={handleLocationSelect}
            />
          </InlineStack>
          <AutocompleteInput
            name="address1"
            label={t("Address line 1")}
            value={address1}
            placeholder=""
            options={autoSuggestionOptions}
            loading={isLoadingAddressAutoCompleteSuggestions}
            onChange={handleAddressQueryChange}
            onSelect={handleAddressAutocomplete}
            disabled={!user.isPremium}
          />
          <Grid>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 6, xl: 6 }}>
              <TextField
                name="address2"
                type="text"
                label={t("Address line 2")}
                value={address2 || ""}
                onChange={handleAddressInputChange}
                autoComplete="off"
                disabled={!user.isPremium}
              />
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 6, xl: 6 }}>
              <TextField
                name="city"
                type="text"
                label={t("City")}
                value={city}
                onChange={handleAddressInputChange}
                autoComplete="off"
                disabled={!user.isPremium}
              />
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 6, xl: 6 }}>
              <TextField
                name="zip"
                type="text"
                label={t("Zip code")}
                value={zip}
                onChange={handleAddressInputChange}
                autoComplete="off"
                disabled={!user.isPremium}
              />
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 6, xl: 6 }}>
              <TextField
                name="country"
                type="text"
                label={t("Country")}
                value={country}
                onChange={handleAddressInputChange}
                autoComplete="off"
                disabled={!user.isPremium}
              />
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 6, xl: 6 }}>
              <TextField
                name="latitude"
                type="number"
                label={t("Latitude")}
                value={latitude}
                onChange={handleGeoInputChange}
                autoComplete="off"
                disabled={!user.isPremium}
              />
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 6, xl: 6 }}>
              <TextField
                name="longitude"
                type="number"
                label={t("Longitude")}
                value={longitude}
                onChange={handleGeoInputChange}
                autoComplete="off"
                disabled={!user.isPremium}
              />
            </Grid.Cell>
          </Grid>
        </BlockStack>
      </Card>
    );
  };

  const renderSocialMediaLinkInputFields = () => {
    return (
      <Card>
        <BlockStack gap="400">
          <Text
            variant="headingMd"
            as="h5"
          >
            {t("Social Media Links")}
          </Text>
          <Grid>
            {Object.entries(socialMediaLabels).map(([key, label]) => (
              <Grid.Cell
                columnSpan={{ xs: 6, sm: 3, md: 3, lg: 6, xl: 6 }}
                key={key}
              >
                <TextField
                  label={t(label)}
                  type="text"
                  name={key}
                  value={jsonldData?.socialMediaLinks?.[key] || ""}
                  onChange={handleSocialMediaLinkChange}
                  autoComplete="off"
                  disabled={!user.isPremium}
                  error={errors.socialMediaLinks[key] && t("Please enter a valid url")}
                />
              </Grid.Cell>
            ))}
          </Grid>
        </BlockStack>
      </Card>
    );
  };

  return (
    <>
      <Banner />
      <UpdateFailedBanner />

      <BlockStack gap="400">
        <ProFeatureBanner
          title="SEO Schema is a PRO feature"
          content="Upgrade your subscription to enable SEO Schema"
        />
        {renderLogoUploader()}
        {renderStoreInfoInputFields()}
        {renderAddressInputFields()}
        {renderSocialMediaLinkInputFields()}
        <InlineStack align="end">
          <Button
            loading={isUpdating}
            disabled={uploadingLogo || !user.isPremium}
            onClick={handleForm}
            variant="primary"
          >
            {t("Save changes")}
          </Button>
        </InlineStack>
      </BlockStack>

      {/* <ContentBlockerAlert /> */}
    </>
  );
};

export default LocalSEO;

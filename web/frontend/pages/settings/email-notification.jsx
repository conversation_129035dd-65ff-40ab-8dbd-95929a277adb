//@ts-check
import { useUnsavedChanges } from "@/hooks/useUnsavedChanges";
import { defaultValues } from "@/lib/form-default-value/emailNotificationSettingsConst";
import {
  useEmailNotificationSettings,
  useUpdateEmailNotificationSettings,
} from "@/lib/hooks/settings/email-notification";
import ContextualSaveBar from "@/modules/components/ContextualSaveBar";
import EmailNotificationSettingToggle from "@/modules/settings/email-notification/components/EmailNotificationSettingToggle";
import EmailSettingsFormElements from "@/modules/settings/email-notification/components/EmailSettingsFormElements";
import { yupResolver } from "@hookform/resolvers/yup";
import { Banner, BlockStack, Button, InlineStack, List } from "@shopify/polaris";
import { useMemo } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import EventTopics from "storeseo-enums/eventTopics";
import { EMAIL_NOTIFICATION_REPORT, OPTIONS } from "storeseo-enums/settings/email-notification";
import { emailNotificationSchema } from "storeseo-schema/settings/email-notification";

export default function EmailNotification() {
  const { t } = useTranslation();
  const { data: emailNotificationSettings, isLoading, isFetching } = useEmailNotificationSettings();
  const { mutate, isLoading: isLoadingMutation } = useUpdateEmailNotificationSettings();

  const configureEnabledEmails = {
    ...emailNotificationSettings,
    items: Object.fromEntries(
      Object.entries(emailNotificationSettings?.items || {}).filter(([, value]) => value?.configurable)
    ),
  };

  /**
   * @type {boolean}
   */
  const isEmailNotificationEnabled = emailNotificationSettings?.[EMAIL_NOTIFICATION_REPORT] ?? false;

  const {
    handleSubmit,
    formState: { errors },
    control,
    watch,
    reset,
  } = useForm({
    resolver: yupResolver(emailNotificationSchema),
    defaultValues,
    values: {
      [EMAIL_NOTIFICATION_REPORT]: isEmailNotificationEnabled,
      items: Object.fromEntries(
        [EventTopics.WEEKLY_STORE_REPORT, EventTopics.AUTO_AI_CONTENT_OPTIMIZATION_REPORT].map((reportType) => [
          reportType,
          {
            enabled: emailNotificationSettings?.items?.[reportType]?.enabled ?? defaultValues.items[reportType].enabled,
            day: emailNotificationSettings?.items?.[reportType]?.day ?? defaultValues.items[reportType].day,
            time: String(emailNotificationSettings?.items?.[reportType]?.time ?? defaultValues.items[reportType].time),
            configurable:
              emailNotificationSettings?.items?.[reportType]?.configurable ??
              defaultValues.items[reportType].configurable,
            instantNotification:
              emailNotificationSettings?.items?.[reportType]?.instantNotification ??
              defaultValues.items[reportType].instantNotification,
          },
        ])
      ),
    },
  });

  /**
   *
   * @param {import("yup").InferType<typeof emailNotificationSchema>} data
   * return {void}
   */

  const onSubmit = (data) => {
    const isEmailNotificationEnabled = data[EMAIL_NOTIFICATION_REPORT];

    const settings = Object.keys(data.items).reduce((object, key) => {
      return {
        ...object,
        [key]: {
          ...data.items[key],
          enabled: isEmailNotificationEnabled ? data.items[key].enabled : false,
          time: Number(data.items[key].time),
        },
      };
    }, {});

    const payload = {
      ...data,
      items: {
        ...emailNotificationSettings?.items,
        ...settings,
      },
    };

    mutate(payload);
  };

  const errorList = Object.keys(errors).map((key) => {
    switch (key) {
      case EventTopics.WEEKLY_STORE_REPORT:
        return t(`Invalid settings for ${OPTIONS[EventTopics.WEEKLY_STORE_REPORT].title}`);
      case EventTopics.AUTO_AI_CONTENT_OPTIMIZATION_REPORT:
        return t(`Invalid settings for ${OPTIONS[EventTopics.AUTO_AI_CONTENT_OPTIMIZATION_REPORT].title}`);
      default:
        break;
    }
  });

  const originalDataItems = useMemo(
    () =>
      configureEnabledEmails?.items
        ? Object.keys(configureEnabledEmails?.items).reduce(
            (previousValue, key) => ({
              ...previousValue,
              [key]: {
                ...configureEnabledEmails?.items?.[key],
                time: String(configureEnabledEmails?.items?.[key].time),
              },
            }),
            {}
          )
        : {},
    [configureEnabledEmails]
  );

  const originalData = {
    ...emailNotificationSettings,
    items: {
      ...(originalDataItems ? originalDataItems : {}),
    },
  };

  const { hasUnsavedChanges, setShowDiscardChangeModal, DiscardChangesModal } = useUnsavedChanges({
    originalData: isEmailNotificationEnabled ? { ...defaultValues, ...originalData } : defaultValues,
    currentData: isEmailNotificationEnabled ? watch() : defaultValues,
    onDiscardAction: reset,
  });

  return (
    <>
      <DiscardChangesModal />
      <ContextualSaveBar
        id="email-notification-settings"
        open={hasUnsavedChanges}
        isLoading={isLoadingMutation}
        onSave={handleSubmit(onSubmit)}
        onDiscard={() => setShowDiscardChangeModal(true)}
      />

      <form
        method="post"
        onSubmit={handleSubmit(onSubmit)}
      >
        <BlockStack gap="400">
          {errorList.length > 0 && (
            <Banner
              title={t("There are some errors with your submission:")}
              tone="warning"
            >
              <List>
                {errorList.map((error, index) => {
                  return <List.Item key={index}>{error}</List.Item>;
                })}
              </List>
            </Banner>
          )}

          <EmailNotificationSettingToggle control={control} />

          <EmailSettingsFormElements control={control} />

          <InlineStack align="end">
            <Button
              submit
              loading={isLoading || isLoadingMutation}
              disabled={isFetching}
              variant="primary"
            >
              {t("Save")}
            </Button>
          </InlineStack>
        </BlockStack>
      </form>
    </>
  );
}

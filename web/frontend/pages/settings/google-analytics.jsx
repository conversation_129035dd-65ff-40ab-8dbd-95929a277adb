import ProFeatureBanner from "@/components/common/ProFeatureBanner.jsx";
import AnalyticsPropertyIdInput from "@/components/google-integration/AnalyticsPropertyId";
import { useAppQuery, useShopApi } from "@/hooks";
import { useBanner } from "@/hooks/useBanner";
import { BlockStack, Box, Button, Card, DropZone, InlineStack, Link, Text, Thumbnail } from "@shopify/polaris";
import { AttachmentIcon } from "@shopify/polaris-icons";
import { useState } from "react";
import { Trans, useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { useSelector } from "react-redux";

export default function GoogleAnalytics() {
  const { t } = useTranslation();
  const user = useSelector((state) => state.user);
  const [openFileDialog, setOpenFileDialog] = useState(false);
  const [jsonFile, setJSONFile] = useState({
    name: "",
  });

  const { Banner, showBanner } = useBanner({
    title: t("You've tried to upload an invalid Google service JSON file."),
    noMargin: true,
  });

  const shopApi = useShopApi();
  const { mutate: uploadFile, isLoading: isUploading } = useMutation({
    mutationFn: ({ file }) => shopApi.uploadGoogleServiceJsonFile(file),
    onError: () => showBanner(true),
  });

  useAppQuery({
    queryKey: ["google-service-json"],
    queryFn: shopApi.getGoogleServiceJsonFile,
    reactQueryOptions: {
      onSuccess: ({ fileName }) => {
        setJSONFile({ name: fileName });
      },
      staleTime: 0,
    },
  });

  const handleAddJSON = (files) => {
    setJSONFile(files[0]);
    uploadFile({ file: files[0] });
  };

  return (
    <BlockStack gap="400">
      <ProFeatureBanner
        title="Google Analytics is a PRO feature"
        content="Upgrade your subscription to enable Google Analytics"
      />
      <Card>
        <BlockStack gap="200">
          <Text
            variant="headingSm"
            as="h5"
          >
            {t("Enable analytics API")}
          </Text>
          <Text>
            <Trans
              t={t}
              i18nKey="EnableAnalyticsAPISettings"
            >
              Enable analytics API in your Google Cloud Console project. You may
              <Link
                url="https://storeseo.com/docs/how-to-connect-storeseo-with-google-analytics/#step-2"
                target="_blank"
              >
                visit this link
              </Link>{" "}
              for detailed information.
            </Trans>
            {/* Enable analytics API in your Google Cloud Console project. You may{" "}
            <Link
              url="https://storeseo.com/docs/how-to-connect-storeseo-with-google-analytics/#step-2"
              target="_blank"
            >
              visit this link
            </Link>{" "}
            for detailed information. */}
          </Text>
          <InlineStack>
            <Button
              url="https://console.cloud.google.com/apis/library/analytics.googleapis.com"
              target="_blank"
              disabled={!user.isPremium}
            >
              {t("Enable API")}
            </Button>
          </InlineStack>
        </BlockStack>
      </Card>

      <Card>
        <BlockStack gap="200">
          <Text
            variant="headingSm"
            as="h5"
          >
            {t("Enable analytics data API")}
          </Text>
          <Text>
            <Trans
              t={t}
              i18nKey="EnableAnalyticsDataAPISettings"
            >
              Enable analytics data API in your Google Cloud Console project. You may
              <Link
                url="https://storeseo.com/docs/how-to-connect-storeseo-with-google-analytics/#step-3"
                target="_blank"
              >
                visit this link
              </Link>
              for detailed information.
            </Trans>
          </Text>
          <InlineStack>
            <Button
              url="https://console.cloud.google.com/apis/library/analyticsdata.googleapis.com"
              target="_blank"
              disabled={!user.isPremium}
            >
              {t("Enable API")}
            </Button>
          </InlineStack>
        </BlockStack>
      </Card>

      <Card>
        <BlockStack gap="200">
          <Text
            variant="headingSm"
            as="h5"
          >
            {t("Enable search console API")}
          </Text>
          <Text>
            <Trans i18nKey="EnableSearchConsoleAPISettings">
              Enable search console API in your Google Cloud Console project. You may
              <Link
                url="https://storeseo.com/docs/how-to-connect-storeseo-with-google-analytics/#step-4"
                target="_blank"
              >
                visit this link
              </Link>
              for detailed information.
            </Trans>
          </Text>
          <InlineStack>
            <Button
              url="https://console.cloud.google.com/apis/library/searchconsole.googleapis.com"
              target="_blank"
              disabled={!user.isPremium}
            >
              {t("Enable API")}
            </Button>
          </InlineStack>
        </BlockStack>
      </Card>

      <Card>
        <BlockStack gap="200">
          <Text
            variant="headingSm"
            as="h5"
          >
            {t("Setup Service account")}
          </Text>
          <Text>
            <Trans i18nKey="SetupServiceAccountSettings">
              Set up a service account in your Google Cloud console. You may visit
              <Link
                url="https://storeseo.com/docs/how-to-connect-storeseo-with-google-analytics/#step-5"
                target="_blank"
              >
                this link
              </Link>
              for detailed information.
            </Trans>
          </Text>
          <InlineStack>
            <Button
              url="https://console.cloud.google.com/projectselector/iam-admin/serviceaccounts/create?walkthrough_id=iam--create-service-account&_ga=2.*********.**********.**********-*********.**********#step_index=1"
              target="_blank"
              disabled={!user.isPremium}
            >
              {t("Setup service account")}
            </Button>
          </InlineStack>
        </BlockStack>
      </Card>

      <Card>
        <BlockStack
          gap="400"
          align="space-between"
        >
          <Text
            variant="headingSm"
            as="h5"
          >
            {t("Upload service JSON config file")}
          </Text>
          <Text>
            <Trans i18nKey="UploadServiceJSONConfigSettings">
              Upload your service JSON config file here. You may visit
              <Link
                url="https://storeseo.com/docs/how-to-connect-storeseo-with-google-analytics/#step-6"
                target="_blank"
              >
                this link
              </Link>
              for detailed information.
            </Trans>
          </Text>
          <Banner />
          <DropZone
            disabled={!user.isPremium}
            type="file"
            allowMultiple={false}
            dropOnPage={true}
            openFileDialog={openFileDialog}
            onFileDialogClose={() => {
              setOpenFileDialog(false);
            }}
            onDrop={handleAddJSON}
            accept="application/json"
            variableHeight={true}
          >
            {!jsonFile?.name || !user.isPremium ? (
              <DropZone.FileUpload
                actionTitle="Add JSON file"
                actionHint={t("Only .json format file allowed")}
              />
            ) : (
              <InlineStack
                gap="400"
                blockAlign="center"
                align="space-between"
                wrap={false}
              >
                <InlineStack
                  gap="400"
                  wrap={false}
                >
                  <Thumbnail
                    source={AttachmentIcon}
                    size="large"
                  />
                  <Box
                    maxWidth="420px"
                    wrap
                  >
                    <Text
                      as="h4"
                      variant="headingSm"
                      breakWord
                    >
                      {jsonFile.name}
                    </Text>
                  </Box>
                </InlineStack>

                <Box paddingInlineEnd="400">
                  <Button loading={isUploading}>{t("Replace file")}</Button>
                </Box>
              </InlineStack>
            )}
          </DropZone>
        </BlockStack>
      </Card>

      <AnalyticsPropertyIdInput isPremium={user.isPremium} />
      {/*<ContentBlockerAlert />*/}
    </BlockStack>
  );
}

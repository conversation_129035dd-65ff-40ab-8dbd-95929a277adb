import ImageAltText from "@/components/seo-setting/image-alt-text";
import MetaDescription from "@/components/seo-setting/meta-description";
import MetaTitle from "@/components/seo-setting/meta-title";
import { SEO_TAGS } from "@/config";
import { useAppQuery, useShopApi } from "@/hooks";
import { useUnsavedChanges } from "@/hooks/useUnsavedChanges";
import useIsRunningBackupRestore from "@/lib/hooks/useIsRunningBackupRestore";
import BackupRestoreInfoBanner from "@/modules/components/BackupRestoreInfoBanner";
import BackupRestoreRunningBanner from "@/modules/components/BackupRestoreRunningBanner";
import ContextualSaveBar from "@/modules/components/ContextualSaveBar";
import Modal from "@/modules/components/Modal";
import { useStoreSeo } from "@/providers/StoreSeoProvider";
import { setOptimizationTaskOngoing } from "@/store/features/OptimizationTask";
import queryKeys from "@/utility/queryKeys";
import { Banner, BlockStack, Box, Button, Form, InlineStack, RadioButton, Text } from "@shopify/polaris";
import { AlertTriangleIcon } from "@shopify/polaris-icons";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation, useQueryClient } from "react-query";
import { useDispatch, useSelector } from "react-redux";
import bpoOptions from "storeseo-enums/bpoOptions";

const radioButtonContents = [
  {
    label: "Non-Optimized Products Only",
    helpText:
      "Bulk product optimization will be applied only to the non-optimized products that have an SEO score below 75",
    value: bpoOptions.NON_OPTIMIZED_PRODUCTS,
  },
  {
    label: "All Products",
    helpText: "Bulk product optimization will be applied to all the products in your store",
    value: bpoOptions.ALL_PRODUCTS,
  },
];

export default function SeoSetting() {
  const { ongoing: optimizationTaskOngoing } = useSelector((s) => s.optimizationTask);
  const [seoTags, setSeoTags] = useState(SEO_TAGS);
  const [templateImgAltText, setTemplateImgAltText] = useState("");
  const [templatePageTitle, setTemplatePageTitle] = useState("");
  const [templateMetaDescription, setTemplateMetaDescription] = useState("");

  // Original data for tracking unsaved changes
  const [originalTemplateImgAltText, setOriginalTemplateImgAltText] = useState("");
  const [originalTemplatePageTitle, setOriginalTemplatePageTitle] = useState("");
  const [originalTemplateMetaDescription, setOriginalTemplateMetaDescription] = useState("");

  const [showModal, setShowModal] = useState(false);
  const [bpoOption, setBpoOption] = useState(bpoOptions.NON_OPTIMIZED_PRODUCTS);

  const { t } = useTranslation();
  const dispatch = useDispatch();
  const shopApi = useShopApi();
  const { doManualRefresh } = useStoreSeo();
  const queryClient = useQueryClient();
  const { isRunning: isRunningBackupOrRestore } = useIsRunningBackupRestore();

  // Track unsaved changes
  const { hasUnsavedChanges, DiscardChangesModal, setShowDiscardChangeModal } = useUnsavedChanges({
    originalData: {
      templatePageTitle: originalTemplatePageTitle,
      templateMetaDescription: originalTemplateMetaDescription,
      templateImgAltText: originalTemplateImgAltText,
    },
    currentData: {
      templatePageTitle,
      templateMetaDescription,
      templateImgAltText,
    },
    onDiscardAction: () => {
      setTemplatePageTitle(originalTemplatePageTitle);
      setTemplateMetaDescription(originalTemplateMetaDescription);
      setTemplateImgAltText(originalTemplateImgAltText);
    },
  });

  const { data, refetch } = useAppQuery({
    queryKey: queryKeys.SEO_SETTINGS,
    queryFn: shopApi.getBulkSEOUpdateData,
  });

  const { mutate: updateSeoSetting, isLoading: isUpdatingSeoSetting } = useMutation({
    mutationFn: () =>
      shopApi.updateSeoSetting({
        templatePageTitle,
        templateMetaDescription,
        templateImgAltText,
      }),
    onSuccess: ({ setting }) => {
      const existingData = queryClient.getQueryData(queryKeys.SEO_SETTINGS);
      queryClient.setQueryData(queryKeys.SEO_SETTINGS, {
        ...existingData,
        setting,
      });

      // Update original data after successful save
      setOriginalTemplatePageTitle(templatePageTitle);
      setOriginalTemplateMetaDescription(templateMetaDescription);
      setOriginalTemplateImgAltText(templateImgAltText);

      setShowModal(true);
    },
  });

  const { mutate: startAutoOptimization, isLoading: isStartingAutoOptimization } = useMutation({
    mutationFn: () => shopApi.startAutoOptimization(bpoOption),
    onSuccess: () => {
      dispatch(setOptimizationTaskOngoing(true));
      setShowModal(false);
    },
  });

  const handleBpoChange = useCallback((_, newValue) => setBpoOption(newValue), []);

  if (doManualRefresh) refetch();

  useEffect(() => {
    if (!data) return;

    if (SEO_TAGS.length) {
      setSeoTags(SEO_TAGS.map((tag) => ({ ...tag, selected: false })));
    }

    if (data.setting?.value) {
      const pageTitle = data.setting.value.pageTitle || "";
      const imgAltText = data.setting.value.imgAltText || "";
      const metaDescription = data.setting.value.metaDescription || "";

      setTemplatePageTitle(pageTitle);
      setTemplateImgAltText(imgAltText);
      setTemplateMetaDescription(metaDescription);

      // Set original data for tracking changes
      setOriginalTemplatePageTitle(pageTitle);
      setOriginalTemplateImgAltText(imgAltText);
      setOriginalTemplateMetaDescription(metaDescription);
    }
  }, [data]);

  const warningMarkup = (
    <Banner
      tone="warning"
      icon={AlertTriangleIcon}
      title={t("Warning: Choosing this option will override all your existing SEO values immediately")}
    >
      {/* <Text as="p">
        Caution: Do not apply the template if you have more than 50 images in one product. Due to Shopify API cost
        limitations, currently, only 50 images per product can be fetched. If you have more than 50 images in one
        product, applying these settings might delete your images and variants in that product. We are currently working
        on implementing this differently.
      </Text> */}
    </Banner>
  );

  return (
    <>
      <DiscardChangesModal />

      <ContextualSaveBar
        id="bulk-seo-update-settings"
        open={hasUnsavedChanges}
        isLoading={isUpdatingSeoSetting}
        onSave={updateSeoSetting}
        onDiscard={() => setShowDiscardChangeModal(true)}
      />

      <Box paddingBlockEnd={"4"}>
        <Form onSubmit={updateSeoSetting}>
          <BlockStack gap="400">
            <BackupRestoreRunningBanner />

            {!isRunningBackupOrRestore && <BackupRestoreInfoBanner />}

            {optimizationTaskOngoing ? (
              <Banner
                tone="warning"
                title={t("Optimization task is already running.")}
              >
                <Text as="p">
                  {t("Please wait for the current optimization task to complete before starting a new one.")}
                </Text>
              </Banner>
            ) : (
              warningMarkup
            )}

            <MetaTitle
              seoTags={seoTags}
              template={templatePageTitle}
              setTemplate={setTemplatePageTitle}
              disabled={optimizationTaskOngoing}
            />
            <MetaDescription
              seoTags={seoTags}
              template={templateMetaDescription}
              setTemplate={setTemplateMetaDescription}
              disabled={optimizationTaskOngoing}
            />
            <ImageAltText
              seoTags={seoTags}
              template={templateImgAltText}
              setTemplate={setTemplateImgAltText}
              disabled={optimizationTaskOngoing}
            />
            <InlineStack align="end">
              <Button
                submit
                loading={isStartingAutoOptimization || isUpdatingSeoSetting}
                variant="primary"
                disabled={isRunningBackupOrRestore || !hasUnsavedChanges || optimizationTaskOngoing}
              >
                {t("Update")}
              </Button>
            </InlineStack>
          </BlockStack>
        </Form>
      </Box>

      <Modal
        type="app-bridge"
        open={showModal}
        setOpen={setShowModal}
      >
        <Modal.Section>
          <Modal.TitleBar title={t("Bulk Product Optimization")}>
            <button
              variant="primary"
              onClick={startAutoOptimization}
              loading={isStartingAutoOptimization ? "" : undefined}
            >
              {t("Confirm!")}
            </button>
            <button onClick={() => setShowModal(false)}>{t("Cancel")}</button>
          </Modal.TitleBar>

          <Box padding="400">
            <BlockStack gap={"100"}>
              <Text
                as={"h4"}
                variant="headingMd"
              >
                {t("Select your bulk product optimization method")}:
              </Text>

              {radioButtonContents.map((option) => (
                <RadioButton
                  key={option.value}
                  label={t(option.label)}
                  helpText={t(option.helpText)}
                  checked={bpoOption === option.value}
                  id={option.value}
                  name="bpoOption"
                  onChange={handleBpoChange}
                />
              ))}
            </BlockStack>
          </Box>
        </Modal.Section>
      </Modal>
    </>
  );
}

//@ts-check
import ProFeatureBanner from "@/components/common/ProFeatureBanner.jsx";
import { useLlmsFileVersions } from "@/lib/hooks/settings/llms-txt-generator";
import GenerateLlmsTxtActionCard from "@/modules/settings/llms-txt-generator/GenerateLlmsTxtActionCard";
import LlmsTxtDataList from "@/modules/settings/llms-txt-generator/LlmsTxtDataList";
import { BlockStack } from "@shopify/polaris";

export default function LlmsTxtGenerator() {
  const { data, isLoading, isFetching } = useLlmsFileVersions();

  // Extract the actual file versions array from the API response
  const fileVersions = data?.versions || [];

  return (
    <BlockStack gap="400">
      <ProFeatureBanner
        title="LLMs.txt Generator is a PRO feature"
        content="Upgrade your subscription to generate LLMs.txt files for AI indexing"
        applyForSpecificFeature={true}
        featureKey="llms_txt_generation"
      />
      {fileVersions && fileVersions.length > 0 && (
        <GenerateLlmsTxtActionCard hasLlmsTxtFile={fileVersions.length > 0} />
      )}
      <LlmsTxtDataList
        data={fileVersions}
        isLoading={isLoading}
        pagination={data?.pagination}
        isFetching={isFetching}
      />
    </BlockStack>
  );
}

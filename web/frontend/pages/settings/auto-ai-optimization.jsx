//@ts-check

import useUserAddon from "@/hooks/useUserAddon";
import AutoAiOptimizationSettingsForm from "@/modules/settings/auto-ai-optimization/AutoAiOptimizationSettingsForm";
import AutoAiOptimizationToggle from "@/modules/settings/auto-ai-optimization/AutoAiOptimizationToggle";
import AutoAiOptimizationNotEnabledBanner from "@/modules/settings/auto-ai-optimization/NotEnabledBanner";
import { BlockStack, Tabs } from "@shopify/polaris";
import { useCallback, useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import ResourceType from "storeseo-enums/resourceType";

export default function AutoAIOptimization() {
  const { hasAiOptimizer } = useUserAddon();
  const [searchParams, setSearchParams] = useSearchParams();
  const [selected, setSelected] = useState(0);

  // Initialize selected tab from query params
  useEffect(() => {
    const tabParam = searchParams.get("tab");
    if (tabParam) {
      const tabIndex = tabs.findIndex((tab) => tab.panelID === tabParam);
      if (tabIndex !== -1) {
        setSelected(tabIndex);
      }
    }
  }, [searchParams]);

  const handleTabChange = useCallback(
    (selectedTabIndex) => {
      setSelected(selectedTabIndex);
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set("tab", tabs[selectedTabIndex].panelID);
      setSearchParams(newSearchParams);
    },
    [searchParams, setSearchParams]
  );

  const tabs = [
    {
      id: "product-1",
      content: "Product",
      accessibilityLabel: "Product",
      panelID: ResourceType.PRODUCT,
    },
    {
      id: "collection-1",
      content: "Collection",
      accessibilityLabel: "Collection",
      panelID: ResourceType.COLLECTION,
    },
    {
      id: "article-1",
      content: "Blog Post",
      accessibilityLabel: "Blog Post",
      panelID: ResourceType.ARTICLE,
    },
  ];
  return (
    <>
      <div className="ss-reports-tab">
        <Tabs
          tabs={tabs}
          selected={selected}
          onSelect={handleTabChange}
        />
      </div>
      <BlockStack gap="400">
        {!hasAiOptimizer && <AutoAiOptimizationNotEnabledBanner />}

        <AutoAiOptimizationToggle resourceType={tabs[selected].panelID} />

        <AutoAiOptimizationSettingsForm resourceType={tabs[selected].panelID} />
      </BlockStack>
    </>
  );
}

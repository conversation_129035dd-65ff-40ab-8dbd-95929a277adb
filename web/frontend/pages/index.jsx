import AiOptimizerPromoCard from "@/components/dashboard/AiOptimizerPromoCard.jsx";
import BlogAIAutoWriteCard from "@/components/dashboard/BlogAIAutoWriteCard.jsx";
import EnableMultiLanguage from "@/components/dashboard/EnableMultiLanguage.jsx";
import GiftAICreditCard from "@/components/dashboard/GiftAICreditCard.jsx";
import ScheduleACallCard from "@/components/dashboard/ScheduleACallCard.jsx";
import TrialUpgradeBanner from "@/components/subscription/TrialUpgradeBanner.jsx";
import { BlockStack, Box, Button, Card, InlineGrid, Page, Text } from "@shopify/polaris";
import { lazy } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { BANNER_AI_CREDIT_GIFT, BANNER_GET_STARTED, BANNER_SCHEDULE_A_CALL } from "storeseo-enums/cacheKeys";
import { AI_OPTIMIZER, IMAGE_OPTIMIZER } from "storeseo-enums/subscriptionAddonGroup";
import AppEmbedWarningBanner from "../components/common/AppEmbedWarningBanner.jsx";
import UsageLimitCard from "../components/common/UsageLimitCard.jsx";
import GetStartedCard from "../components/dashboard/GetStartedCard.jsx";
import PartnershipBanner from "../components/dashboard/PartnershipBanner.jsx";
import SEOGuideCard from "../components/dashboard/SEOGuideCard.jsx";
import StayTunedCard from "../components/dashboard/StayTunedCard.jsx";
import SubscriptionSuccessModal from "../components/modals/SubscriptionSuccessModal.jsx";
import StatisticsRow from "../components/statistics/StatisticsRow.jsx";
import { HELP_URLS, PAGE_TEMPLATES } from "../config/index.js";
import useUserAddon from "../hooks/useUserAddon";

// const ExclusivePartnershipCard = lazy(() => import("@/components/dashboard/ExclusivePartnershipCard.jsx"));
const OtherAppsByStoreware = lazy(() => import("@/components/dashboard/OtherAppsByStoreware.jsx"));
const WhatsNewSection = lazy(() => import("@/components/dashboard/WhatsNewSection.jsx"));
const AutoOptimizerCard = lazy(() => import("@/components/dashboard/AutoOptimizerCard.jsx"));

export default function HomePage() {
  const { t } = useTranslation();
  const user = useSelector((state) => state.user);
  const hiddenBanner = useSelector((state) => state.hiddenBanner);
  const { addons } = useUserAddon();

  // Calculate individual card visibility
  const hasAiCreditGiftCard =
    user.isPremium && !user.trialData?.isActive && !user.meta?.aiCreditGift && hiddenBanner[BANNER_AI_CREDIT_GIFT];
  const hasScheduleACallCard = hiddenBanner[BANNER_SCHEDULE_A_CALL];

  // Show the get started row only when there's actual content to display
  const hasGetStartedRow = hiddenBanner[BANNER_GET_STARTED] || hasAiCreditGiftCard || hasScheduleACallCard;

  const hasMultiColumnGetStarted = (hasAiCreditGiftCard && hiddenBanner[BANNER_GET_STARTED]) || hasScheduleACallCard;

  const getStartedColumnCount = hasMultiColumnGetStarted ? 2 : 1;
  const addonsColumnCount = Math.min(addons.length, 2);

  return (
    <Page>
      <BlockStack gap="400">
        <WelcomeSection />

        <TrialUpgradeBanner />

        <StatisticsRow />

        {hasGetStartedRow && (
          <InlineGrid
            columns={{ xs: 1, sm: 1, md: getStartedColumnCount, lg: getStartedColumnCount, xl: getStartedColumnCount }}
            gap={400}
          >
            <GetStartedCard />

            {/* {hasVerifyCard ? <VerifyStoreCard /> : <ScheduleACallCard />} */}
            {hasAiCreditGiftCard ? <GiftAICreditCard /> : <ScheduleACallCard />}
          </InlineGrid>
        )}

        <BlogAIAutoWriteCard />

        <Card>
          <BlockStack gap="400">
            <Text
              as="h4"
              variant="headingSm"
            >
              {t("Get HTML Sitemap For Your Store")}
            </Text>
            <Text
              as="p"
              tone="subdued"
            >
              {t(
                "Generate an HTML sitemap page instantly on your store and help your visitors find all of your products, pages, and collections in one place"
              )}
            </Text>

            <Box as="span">
              <Button url="/sitemaps/html-sitemap">{t("Generate HTML Sitemap")}</Button>
            </Box>
          </BlockStack>
        </Card>

        <AppEmbedWarningBanner page={PAGE_TEMPLATES.DASHBOARD} />

        <WhatsNewSection />
        <EnableMultiLanguage />

        {addonsColumnCount > 0 && (
          <>
            <InlineGrid
              columns={{ xs: 1, sm: 1, md: addonsColumnCount, lg: addonsColumnCount, xl: addonsColumnCount }}
              gap="400"
            >
              <UsageLimitCard
                title="Image Optimizer"
                group={IMAGE_OPTIMIZER}
                learnMoreButton={{
                  title: "How to increase my monthly usage limits?",
                  url: HELP_URLS.IMAGE_OPTIMIZER,
                }}
                action={{ content: "Upgrade" }}
              />
              <UsageLimitCard
                title="AI Content Optimizer"
                group={AI_OPTIMIZER}
                learnMoreButton={{
                  title: "What do i do if i need more credits for AI Content Optimizer?",
                  url: HELP_URLS.AI_OPTIMIZER,
                }}
                action={{ content: "Increase limit" }}
              />
            </InlineGrid>

            <AutoOptimizerCard />
          </>
        )}

        <AiOptimizerPromoCard />

        {/* <ScheduleACallCard /> */}

        <StayTunedCard />

        <OtherAppsByStoreware />

        <BlockStack gap={"400"}>
          <SEOGuideCard />

          {/* <ExclusivePartnershipCard /> */}
        </BlockStack>

        <PartnershipBanner />
      </BlockStack>

      {user?.isNewlyUpgraded && <SubscriptionSuccessModal />}
    </Page>
  );
}

function WelcomeSection() {
  const { t } = useTranslation();

  return (
    <BlockStack
      gap="200"
      align="center"
    >
      <Text
        variant="heading2xl"
        as="h1"
      >
        {t("Welcome to StoreSEO")}
      </Text>
      <Text
        variant="bodyLg"
        as={"p"}
      >
        {t("Drive sales and traffic with the power of StoreSEO and improve your search engine ranking")}
      </Text>
    </BlockStack>
  );
}

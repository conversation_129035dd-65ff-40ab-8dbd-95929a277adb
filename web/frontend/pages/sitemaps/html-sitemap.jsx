import HtmlSitemapLoader from "@/components/loader/HtmlSitemapLoader";
import CustomizeHtmlSitemapMeta from "@/components/sitemap/html-sitemap/CustomizeHtmlSitemapMeta";
import { BlockStack, Box, Card, Collapsible, Icon, InlineStack, Page, ProgressBar, Text } from "@shopify/polaris";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { Padding } from "../../components/common/CustomComponent";
import CompleteHtmlSitemapSetup from "../../components/sitemap/html-sitemap/CompleteSetup";
import CustomizeHtmlSitemap from "../../components/sitemap/html-sitemap/CustomizeHtmlSitemap";
import DeleteHtmlSitemap from "../../components/sitemap/html-sitemap/DeleteHtmlSitemap";
import EnableSitemap from "../../components/sitemap/html-sitemap/EnableSitemap";
import GenerateHtmlSitemap from "../../components/sitemap/html-sitemap/GenerateHtmlSitemap";
import RemoveBrandingCard from "../../components/sitemap/RemoveBrandingCard";
import { checkIcon } from "../../components/svg/Icons";
import { HTML_SITEMAP_URL } from "../../config";
import { useAppQuery, useShopApi } from "../../hooks";
import { useAppNavigation } from "../../hooks/useAppNavigation";
import queryKeys from "../../utility/queryKeys";

const StepCard = ({ title, open = false, completed = false, Content = () => <></>, onToggle = () => {} }) => {
  const { t } = useTranslation();

  return (
    <Box
      background={open ? "bg-fill-secondary" : ""}
      borderRadius="300"
      padding="300"
    >
      <BlockStack gap="200">
        <div
          style={{ cursor: !open ? "pointer" : "default" }}
          onClick={onToggle}
        >
          <InlineStack>
            {completed ? (
              <span style={{ marginRight: 10 }}>
                <Icon source={checkIcon} />
              </span>
            ) : (
              <span className="check-circle"></span>
            )}
            <Text
              variant="headingSm"
              as="h3"
            >
              {t(title)}
            </Text>
          </InlineStack>
        </div>

        <Collapsible
          open={open}
          id={title}
          transition={{ duration: "400ms", timingFunction: "ease-in-out" }}
        >
          <Padding padding="0 0 0 30px">
            <Content />
          </Padding>
        </Collapsible>
      </BlockStack>
    </Box>
  );
};

export default function HtmlSitemap() {
  const { t } = useTranslation();
  const appNavigation = useAppNavigation();
  const shopApi = useShopApi();
  const user = useSelector((s) => s.user);

  const { data, isLoading } = useAppQuery({
    queryKey: queryKeys.HTML_SITEMAP_SETUP_INFO,
    queryFn: shopApi.getHtmlSitemapSetupInfo,
  });

  const [selectedStep, setSelectedStep] = useState(0);
  const [completedStep, setCompletedStep] = useState(0);

  const steps = [
    {
      title: "Generate HTML Sitemap",
      Content: GenerateHtmlSitemap,
    },
    {
      title: "How to add the HTML Sitemap link to your store",
      Content: EnableSitemap,
    },
    {
      title: "Confirmation",
      Content: CompleteHtmlSitemapSetup,
    },
  ];

  useEffect(
    function setInitialSetupState() {
      if (data) {
        const completedStep = data.setupStep ? data.setupStep : data.setting ? 1 : 0;
        const selectedStep = completedStep >= steps.length ? 0 : completedStep + 1;

        setCompletedStep(completedStep);
        setSelectedStep(selectedStep);
      }
    },
    [data]
  );

  if (isLoading) return <HtmlSitemapLoader />;

  return (
    <Page
      title={t("HTML Sitemap")}
      backAction={appNavigation.backAction}
      secondaryActions={[
        {
          content: t("View Sitemap"),
          url: `${user.url}${HTML_SITEMAP_URL}`,
          disabled: completedStep < 1,
          target: "_blank",
        },
      ]}
      narrowWidth
    >
      <BlockStack gap="400">
        <Card padding="0">
          <Padding padding="16px 20px">
            <BlockStack gap="300">
              <Text as="p">{t("Complete the following steps to create an HTML sitemap for your store")}</Text>
              <div className="onboardin-header-progress">
                <div className="title">
                  <Text tone="subdued">
                    {completedStep} / {steps.length} {t("completed")}
                  </Text>
                </div>
                <div className="onboarding-progress">
                  <ProgressBar
                    progress={Math.round((completedStep / steps.length) * 100)}
                    tone="primary"
                    size="small"
                  />
                </div>
              </div>
            </BlockStack>
          </Padding>

          <Padding padding="4px 12px 12px 12px">
            <BlockStack gap="50">
              {steps.map((step, idx) => (
                <StepCard
                  key={idx}
                  title={step.title}
                  Content={step.Content}
                  open={selectedStep === idx + 1}
                  completed={completedStep >= idx + 1}
                  onToggle={() => setSelectedStep(idx + 1)}
                />
              ))}
            </BlockStack>
          </Padding>
        </Card>

        {completedStep >= 1 && <RemoveBrandingCard />}
        {completedStep >= 1 && <CustomizeHtmlSitemap />}
        {completedStep >= 1 && <CustomizeHtmlSitemapMeta />}
        {completedStep >= 2 && <DeleteHtmlSitemap />}
      </BlockStack>
    </Page>
  );
}

import {
  <PERSON><PERSON>,
  <PERSON>,
  BlockStack,
  Box,
  Button,
  Card,
  Icon,
  InlineGrid,
  InlineStack,
  Link,
  Page,
  ResourceItem,
  ResourceList,
  Select,
  Text,
  TextField,
  Thumbnail,
} from "@shopify/polaris";
import { MagicIcon, RefreshIcon, SearchIcon, ViewIcon, WandIcon } from "@shopify/polaris-icons";
import { useDebounce } from "@uidotdev/usehooks";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useSearchParams } from "react-router-dom";
import BlogAutoWriteJobStatus from "storeseo-enums/blogAutoWrite/jobStatus";
import { AI_OPTIMIZER } from "storeseo-enums/subscriptionAddonGroup";
import BlogAutoWriteModal from "../components/blog-auto-write/BlogAutoWriteModal.jsx";
import TooltipWrapper from "../components/common/TooltipWrapper.jsx";
import UsageLimitCard from "../components/common/UsageLimitCard.jsx";
import { browserEvents, emitter, HELP_URLS } from "../config/index.js";
import { useBlogApi } from "../hooks/apiHooks/useBlogApi.js";
import { useAppQuery } from "../hooks/index.js";
import { useAppNavigation } from "../hooks/useAppNavigation.js";

// Status constants for regeneration eligibility
const REGENERATION_ELIGIBLE_STATUSES = [
  BlogAutoWriteJobStatus.COMPLETED,
  BlogAutoWriteJobStatus.FAILED,
  BlogAutoWriteJobStatus.CANCELLED,
];

const IN_PROGRESS_STATUSES = [
  BlogAutoWriteJobStatus.PENDING,
  BlogAutoWriteJobStatus.GENERATING_CONTENT,
  BlogAutoWriteJobStatus.CREATING_DRAFT,
  BlogAutoWriteJobStatus.LINKING_ARTICLE,
  BlogAutoWriteJobStatus.ANALYZING_SEO,
  BlogAutoWriteJobStatus.GENERATING_IMAGE,
  BlogAutoWriteJobStatus.UPLOADING_IMAGE,
  BlogAutoWriteJobStatus.UPDATING_ARTICLE,
  BlogAutoWriteJobStatus.FINALIZING_SEO,
  BlogAutoWriteJobStatus.PUBLISHING,
];

import EmptyPage from "@/components/common/EmptyPage.jsx";
import TableEmptyState from "@/components/common/TableEmptyState.jsx";
import ConfirmationModal from "@/components/modals/ConfirmationModal.jsx";
import { useSyncBlogPosts } from "@/hooks/blogposts/index.jsx";
import { useAppBridgeRedirect } from "@/hooks/useAppBridgeRedirect.js";
import { BlogIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";
import { useBlogAutoWriteStatus } from "../hooks/useBlogAutoWriteStatus.js";
import { usePublishStatus } from "../hooks/usePublishStatus.js";
import useIndexTablePagination from "../lib/hooks/useIndexTablePagination.jsx";
import { formatDate, getQueryFromUrlSearchParam, prepareThumbnailURL } from "../utility/helpers.jsx";
import queryKeys from "../utility/queryKeys.js";

// Helper function to get the appropriate icon for blog auto-write status (same as articles table)
const getStatusIcon = (status) => {
  if (status === BlogAutoWriteJobStatus.COMPLETED) {
    return WandIcon;
  }
  return ViewIcon;
};

export default function AiBlogGenerator() {
  const { t } = useTranslation();
  const navigation = useAppNavigation();
  const blogApi = useBlogApi();

  const [searchParams] = useSearchParams();

  // Modal state for blog sync confirmation
  const [showSyncConfirmModal, setShowSyncConfirmModal] = useState(false);

  // Modal state for BlogAutoWriteModal
  const [isBlogAutoWriteModalOpen, setIsBlogAutoWriteModalOpen] = useState(false);

  // Global modal state for list items
  const [activeModalJobId, setActiveModalJobId] = useState(null);
  const [activeModalType, setActiveModalType] = useState(null); // 'progress' | 'regenerate'

  // Build query object for API call
  const query = useMemo(() => {
    const baseQuery = getQueryFromUrlSearchParam(searchParams);
    return {
      ...baseQuery,
      ai_generated: "true", // Always filter for AI-generated articles
    };
  }, [searchParams]);

  // Query key for React Query (same pattern as articles page)
  const queryKey = [queryKeys.ARTICLES_LIST, query];

  // Fetch AI-generated blogs using existing API with ai_generated filter
  const {
    data,
    // isFetching: isLoading,
    isLoading,
    refetch,
    isError,
    error,
  } = useAppQuery({
    queryKey,
    queryFn: () => blogApi.getArticlesWithPagination(query),
    reactQueryOptions: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchOnWindowFocus: false,
      keepPreviousData: true,
    },
  });

  // Extract data (same pattern as articles page)
  const { articles = [], pagination = {}, articleCount = 0 } = data || {};
  const aiBlogs = articles;

  // Handle opening the blog auto-write modal
  const handleOpenBlogAutoWrite = () => {
    setIsBlogAutoWriteModalOpen(true);
  };

  // Check if we have data
  const isEmpty = !isLoading && articleCount === 0;

  // Pagination configuration (same pattern as articles page)
  const { paginationConfigs } = useIndexTablePagination(pagination);

  const { mutate: syncBlogs, isLoading: isStartingSync } = useSyncBlogPosts({});

  const handleConfirmSync = useCallback(async () => {
    await syncBlogs();
    setShowSyncConfirmModal(false);
  }, [syncBlogs]);

  const handleBlogSyncTriggerViaEvent = useCallback(() => {
    setIsBlogAutoWriteModalOpen(false);
    setShowSyncConfirmModal(true);
  }, [setShowSyncConfirmModal, setIsBlogAutoWriteModalOpen]);

  useEffect(function startBlogSyncOnEventTrigger() {
    emitter.on(browserEvents.TRIGGER_BLOG_SYNC, handleBlogSyncTriggerViaEvent);

    return () => {
      emitter.off(browserEvents.TRIGGER_BLOG_SYNC, handleBlogSyncTriggerViaEvent);
    };
  }, []);

  // Error state
  if (isError) {
    return (
      <Page
        title="AI Blog Generator"
        backAction={navigation.backAction}
      >
        <BlockStack gap="500">
          <Banner
            tone="critical"
            title="Error loading AI blogs"
          >
            <Text>
              {error?.message || "Something went wrong while loading your AI generated blogs. Please try again."}
            </Text>
            <Box paddingBlockStart="200">
              <Button onClick={refetch}>Try Again</Button>
            </Box>
          </Banner>
        </BlockStack>
      </Page>
    );
  }

  // Main content with AI blogs list
  return (
    <Page
      title="AI Blog Generator"
      backAction={navigation.backAction}
    >
      {isEmpty ? (
        <EmptyPage
          heading={t("No Blogs? Use AI Blog Generator!")}
          content={t(
            "Since you don’t have any AI-generated blogs yet, start creating one now with our AI Blog Generator. Input your topic, tone, word limit, and preferences to produce engaging content for your Shopify store."
          )}
          withTableFilter={false}
          primaryAction={
            <Button
              variant="primary"
              onClick={handleOpenBlogAutoWrite}
              icon={MagicIcon}
            >
              {t("Generate Blog Post")}
            </Button>
          }
        />
      ) : (
        <BlockStack gap="500">
          <InlineGrid
            columns={2}
            gap="400"
          >
            <Card>
              <BlockStack gap="100">
                <Text
                  as="h4"
                  variant="headingSm"
                >
                  AI Blog Generator
                </Text>
                <Text
                  as="p"
                  variant="bodyMd"
                >
                  Create engaging blogs for your Shopify store in a few clicks. Simply add your preferences to get
                  started.
                </Text>
                <Box as="span">
                  <Button
                    variant="primary"
                    icon={MagicIcon}
                    onClick={handleOpenBlogAutoWrite}
                  >
                    Generate Blog post
                  </Button>
                </Box>
              </BlockStack>
            </Card>
            <UsageLimitCard
              title="AI Content Optimizer"
              group={AI_OPTIMIZER}
              learnMoreButton={{
                title: "What do i do if i need more credits for AI Content Optimizer?",
                url: HELP_URLS.AI_OPTIMIZER,
              }}
              action={{ content: "Increase limit" }}
            />
          </InlineGrid>

          <Card padding={0}>
            <ResourceList
              resourceName={{ singular: "AI blog", plural: "AI blogs" }}
              items={aiBlogs}
              renderItem={(item) => (
                <BlogListItem
                  item={item}
                  onOpenProgressModal={(jobId) => {
                    setActiveModalJobId(jobId);
                    setActiveModalType("progress");
                  }}
                  onOpenRegenerateModal={(jobId) => {
                    setActiveModalJobId(jobId);
                    setActiveModalType("regenerate");
                  }}
                />
              )}
              showHeader
              headerContent={`AI Generated Blogs (${aiBlogs.length})`}
              loading={isLoading}
              pagination={paginationConfigs}
              emptyState={
                <TableEmptyState
                  title="No blog posts found"
                  description="Try changing the filters or search term"
                  withIllustration
                />
              }
              filterControl={<BlogGeneratorFilters isOpen={(!isLoading && articleCount > 0) || articleCount > 0} />}
            />
          </Card>
        </BlockStack>
      )}

      {/* Blog Auto-Write Modal - For "Generate Blog" button */}
      <BlogAutoWriteModal
        isOpen={isBlogAutoWriteModalOpen}
        onClose={() => setIsBlogAutoWriteModalOpen(false)}
      />

      {/* Global Blog Auto-Write Modal - For list items */}
      {activeModalJobId && (
        <BlogAutoWriteModal
          isOpen={!!activeModalJobId}
          onClose={() => {
            setActiveModalJobId(null);
            setActiveModalType(null);
          }}
          initialView={activeModalType === "regenerate" ? "form" : undefined}
          initialJobId={activeModalType === "progress" ? activeModalJobId : undefined}
          regenerationJobId={activeModalType === "regenerate" ? activeModalJobId : undefined}
          isExplicitRegeneration={activeModalType === "regenerate"}
        />
      )}

      {/* Blog Sync Confirmation Modal */}
      <ConfirmationModal
        show={showSyncConfirmModal}
        onClose={setShowSyncConfirmModal}
        title={t("Sync blog posts")}
        content={t("Are you sure you want to sync blog posts from Shopify?")}
        primaryAction={handleConfirmSync}
        loading={isStartingSync}
      />
    </Page>
  );
}

// Separate component for individual blog list items to handle hooks properly
// Memoized to prevent unnecessary re-renders
const BlogListItem = React.memo(({ item, onOpenProgressModal, onOpenRegenerateModal }) => {
  const { handleEditArticle } = useAppBridgeRedirect();
  const { id, title, created_at, blog, image, published_at, article_id } = item;

  // Use hooks for consistent styling with articles table
  const autoWriteStatus = useBlogAutoWriteStatus(item);
  const publishStatus = usePublishStatus(published_at);
  const thumbnail = prepareThumbnailURL(image?.src);
  const formattedDate = formatDate(created_at);

  // Handle progress modal opening
  const handleOpenProgress = (e) => {
    e.stopPropagation();
    e.preventDefault();

    if (autoWriteStatus.jobId) {
      onOpenProgressModal(autoWriteStatus.jobId);
    }
  };

  // Handle regeneration modal opening
  const handleRegenerate = (e) => {
    e.stopPropagation();
    e.preventDefault();

    if (autoWriteStatus.jobId) {
      onOpenRegenerateModal(autoWriteStatus.jobId);
    }
  };

  return (
    <ResourceItem
      id={id}
      accessibilityLabel={`View details for ${title}`}
    >
      <InlineStack
        gap="100"
        align="start"
        blockAlign="center"
        wrap={true}
      >
        <Box width="45%">
          <InlineStack gap="400">
            {/* Column 1 - Featured Image */}
            <Thumbnail
              source={thumbnail}
              alt={image?.alt || title}
              // size="small"
            />

            {/* Column 2 - Blog Post Information */}
            <Box maxWidth="320px">
              <BlockStack gap="100">
                <Link
                  // url={`/optimize-seo/articles/${article_id}`}
                  removeUnderline
                  onClick={(e) => {
                    if (autoWriteStatus.shouldBlockNavigation) {
                      e.preventDefault();
                    } else {
                      handleEditArticle(article_id, { external: true });
                    }
                  }}
                >
                  <Text
                    as="h3"
                    variant="bodyMd"
                    fontWeight="medium"
                  >
                    {title || "Untitled"}
                  </Text>
                </Link>
                <Text
                  as="p"
                  variant="bodySm"
                  tone="subdued"
                  truncate
                >
                  {blog?.blog_title || "Blog"}
                </Text>
              </BlockStack>
            </Box>
          </InlineStack>
        </Box>

        <Box width="54%">
          <InlineStack
            gap="400"
            align="space-between"
          >
            {/* Column 3 - AI Generation Status */}
            <Button
              variant="plain"
              size="micro"
              onClick={handleOpenProgress}
            >
              <InlineStack
                gap="100"
                blockAlign="center"
                align="start"
                wrap={false}
              >
                <Icon
                  source={getStatusIcon(autoWriteStatus.status)}
                  tone={autoWriteStatus.statusTone}
                />
                <Text
                  variant="bodySm"
                  tone={autoWriteStatus.statusTone}
                  fontWeight="medium"
                  breakWord
                >
                  {autoWriteStatus.displayStatus}
                </Text>
              </InlineStack>
            </Button>

            {/* Column 4 - Shopify Publication Status */}
            <Box>
              <Badge tone={publishStatus.color}>{publishStatus.name}</Badge>
            </Box>

            {/* Column 5 - Generation Date */}
            <Box>
              <Text
                as="span"
                variant="bodySm"
                tone="subdued"
              >
                {formattedDate}
              </Text>
            </Box>

            {/* Column 6 - Action Buttons */}
            <Box>
              <InlineStack
                gap="200"
                align="end"
              >
                {/* Fix button */}
                {/* <TooltipWrapper content="Fix issue">
                  <Button
                    icon={ComposeIcon}
                    variant="tertiary"
                    size="micro"
                    url={`/optimize-seo/articles/${article_id}`}
                    disabled={autoWriteStatus.shouldBlockNavigation}
                    onClick={(e) => e.stopPropagation()}
                  />
                </TooltipWrapper> */}

                <TooltipWrapper content="Edit in Shopify Admin">
                  <Button
                    icon={BlogIcon}
                    onClick={(e) => {
                      handleEditArticle(article_id, { external: true });
                    }}
                    variant="tertiary"
                    size="micro"
                    disabled={IN_PROGRESS_STATUSES.includes(autoWriteStatus.status)}
                  />
                </TooltipWrapper>

                {/* Regenerate button - only for eligible statuses */}
                {autoWriteStatus.isAiGenerated && REGENERATION_ELIGIBLE_STATUSES.includes(autoWriteStatus.status) && (
                  <TooltipWrapper content="Regenerate blog">
                    <Button
                      icon={RefreshIcon}
                      variant="tertiary"
                      size="micro"
                      onClick={handleRegenerate}
                    />
                  </TooltipWrapper>
                )}
              </InlineStack>
            </Box>
          </InlineStack>
        </Box>
      </InlineStack>
    </ResourceItem>
  );
});

// BlogGeneratorFilters component - handles all filter control UI and state management
const BlogGeneratorFilters = ({ isOpen = true }) => {
  const { t } = useTranslation();
  // URL parameter management with filtering support
  const [searchParams, setSearchParams] = useSearchParams();
  // Extract search parameters
  const searchParamsQuery = useMemo(() => Object.fromEntries(searchParams.entries()), [searchParams]);

  // Search state with debouncing
  const [searchQueryValue, setSearchQueryValue] = useState(searchParamsQuery.search || "");
  const debouncedSearchTerm = useDebounce(searchQueryValue, 300);

  // Filter state from URL parameters
  const progressFilter = searchParamsQuery.progress || "all";
  const sortByParam = searchParamsQuery.sortBy || "";
  const sortOrderParam = searchParamsQuery.sortOrder || "";

  // Combine sortBy and sortOrder for select value (following the special handling requirement)
  const sortValue = sortByParam && sortOrderParam ? `${sortByParam}:${sortOrderParam}` : "";

  // Update URL parameters when search term changes (only for search debouncing)
  useEffect(() => {
    const currentParams = { ...searchParamsQuery };

    // Remove page when searching to reset pagination
    if (debouncedSearchTerm) {
      delete currentParams.page;
      currentParams.search = debouncedSearchTerm.trim();
    } else if (searchParamsQuery.search) {
      delete currentParams.search;
    }

    // Only update if search parameter actually changed
    if (currentParams.search !== searchParamsQuery.search) {
      setSearchParams(currentParams, { replace: true });
    }
  }, [debouncedSearchTerm, searchParamsQuery, setSearchParams]);

  // Filter change handlers
  const handleProgressFilterChange = useCallback(
    (value) => {
      const currentParams = { ...searchParamsQuery };
      delete currentParams.page; // Reset pagination

      if (value !== "all") {
        currentParams.progress = value;
      } else {
        delete currentParams.progress;
      }

      setSearchParams(currentParams, { replace: true });
    },
    [searchParamsQuery, setSearchParams]
  );

  const handleSortChange = useCallback(
    (value) => {
      const currentParams = { ...searchParamsQuery };
      delete currentParams.page; // Reset pagination

      if (value) {
        const [sortBy, sortOrder] = value.split(":");
        currentParams.sortBy = sortBy;
        currentParams.sortOrder = sortOrder;
      } else {
        delete currentParams.sortBy;
        delete currentParams.sortOrder;
      }

      setSearchParams(currentParams, { replace: true });
    },
    [searchParamsQuery, setSearchParams]
  );

  const handleClearFilters = useCallback(() => {
    setSearchQueryValue("");
    setSearchParams(undefined, { replace: true });
  }, [setSearchParams]);

  // Build query object for API call
  const query = useMemo(() => {
    const baseQuery = getQueryFromUrlSearchParam(searchParams);
    return {
      ...baseQuery,
      ai_generated: "true", // Always filter for AI-generated articles
    };
  }, [searchParams]);

  if (!isOpen) return null;

  return (
    <Box
      paddingBlock="200"
      paddingInline="025"
    >
      <InlineStack
        gap="150"
        blockAlign="center"
        align="space-between"
        wrap={false}
      >
        <Box width="100%">
          <TextField
            value={searchQueryValue}
            onChange={setSearchQueryValue}
            placeholder={t("Searching in all")}
            prefix={<Icon source={SearchIcon} />}
            clearButton
            onClearButtonClick={() => setSearchQueryValue("")}
          />
        </Box>
        <Box width="auto">
          <Select
            label="Filter by"
            key="progress"
            labelInline
            options={[
              { label: "Generation Status: All", value: "all" },
              { label: "Generation Status: Pending", value: "pending" },
              { label: "Generation Status: Ongoing", value: "ongoing" },
              { label: "Generation Status: Completed", value: "completed" },
            ]}
            value={progressFilter}
            onChange={handleProgressFilterChange}
          />
        </Box>
        <Box width="auto">
          <Select
            label="Sort by"
            key="sortBy"
            labelInline
            options={[
              { label: "Default", value: "" },
              { label: "Generation Status (Most Progressed)", value: "progress:desc" },
              { label: "Generation Status (Least Progressed)", value: "progress:asc" },
              { label: "Publish Status (Draft first)", value: "published_at:desc" },
              { label: "Publish Status (Published first)", value: "published_at:asc" },
            ]}
            value={sortValue}
            onChange={handleSortChange}
          />
        </Box>
        <Box>
          <Button
            variant="tertiary"
            onClick={handleClearFilters}
          >
            Cancel
          </Button>
        </Box>
      </InlineStack>
    </Box>
  );
};
import moment from "moment";
import { lazy, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { useAppQuery, useShopApi } from "../hooks";
import queryKeys from "../utility/queryKeys";
const AnalyticsOverview = lazy(() => import("../components/analytics/AnalyticsOverview"));
const DummyAnalytics = lazy(() => import("../components/loader/DummyAnalytics"));
// import KeywordsTable from "../components/analytics/KeywordsTable";
import AnalyticsLoader from "@/components/loader/AnalyticsLoader";
import { BlockStack, Page } from "@shopify/polaris";
import ConnectToGoogleWarning from "../components/analytics/ConnectToGoogleWarning.jsx";
import DateRangePicker from "../components/common/DateRangePicker";
import ProFeatureBanner from "../components/common/ProFeatureBanner.jsx";
import { useAppNavigation } from "../hooks/useAppNavigation.js";

export default function Analytics() {
  const shopApi = useShopApi();
  const { t } = useTranslation();
  const navigation = useAppNavigation();

  const [startDate, setStartDate] = useState(moment().subtract(6, "d").toDate());
  const [endDate, setEndDate] = useState(new Date());

  const [currentStartDate, setCurrentStartDate] = useState(moment().subtract(6, "d").toDate());
  const [currentEndDate, setCurrentEndDate] = useState(new Date());

  const user = useSelector((s) => s.user);
  const { data: googleIntegrationInfo, isFetching } = useAppQuery({
    queryKey: queryKeys.GOOGLE_INTEGRATION_INFO,
    queryFn: shopApi.getGoogleIntegrationInfo,
  });

  useEffect(
    function updateCurrentDates() {
      if (endDate && startDate < endDate) {
        setCurrentStartDate(startDate);
        setCurrentEndDate(endDate);
      }
    },
    [startDate, endDate]
  );

  const { steps, analyticsPropertyId } = googleIntegrationInfo?.integrationInfo || {};

  // const analyticsSetupComplete =
  //   user?.isPremium && steps?.[googleIntegrationSteps.ANALYTICS_DATA] && analyticsPropertyId;
  const analyticsSetupComplete = user?.isPremium && analyticsPropertyId;

  return (
    <>
      {isFetching && <AnalyticsLoader />}
      {!isFetching && (
        <Page
          title={t("Analytics")}
          backAction={navigation.backAction}
        >
          {!analyticsSetupComplete ? (
            <BlockStack gap={"600"}>
              <ProFeatureBanner
                title="Google Analytics is a PRO feature"
                content="Upgrade your subscription to enable Google Analytics"
              />
              {user?.isPremium && (
                <>
                  <ConnectToGoogleWarning />
                  {/*<Card>*/}
                  {/*  <Text*/}
                  {/*    tone="subdued"*/}
                  {/*    variant="bodySm"*/}
                  {/*  >*/}
                  {/*    {t(*/}
                  {/*      "Note: Below is a display of dummy data of how your analytics report will look like after you successfully connect StoreSEO to Google."*/}
                  {/*    )}*/}
                  {/*  </Text>*/}
                  {/*</Card>*/}
                </>
              )}
              <DummyAnalytics />
            </BlockStack>
          ) : (
            <BlockStack gap="600">
              <DateRangePicker
                startDate={startDate}
                endDate={endDate}
                onApply={({ since, until }) => {
                  setStartDate(since);
                  setEndDate(until);
                }}
              />
              <AnalyticsOverview
                startDate={currentStartDate}
                endDate={currentEndDate}
              />

              {/* <TopProductsTable
                startDate={currentStartDate}
                endDate={currentEndDate}
              /> */}

              {/* <KeywordsTable
                startDate={currentStartDate}
                endDate={currentEndDate}
              /> */}
            </BlockStack>
          )}
        </Page>
      )}
    </>
  );
}

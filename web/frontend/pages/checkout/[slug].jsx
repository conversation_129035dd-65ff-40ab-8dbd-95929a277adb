import {
  <PERSON><PERSON>,
  <PERSON>,
  BlockStack,
  Box,
  <PERSON>ton,
  Card,
  Divider,
  Icon,
  InlineGrid,
  InlineStack,
  Link,
  Page,
  Text,
  TextField,
} from "@shopify/polaris";
import { InfoIcon, XSmallIcon } from "@shopify/polaris-icons";
import { Fragment, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate, useParams, useSearchParams } from "react-router-dom";
import planInterval from "storeseo-enums/planInterval";
import { IMAGE_OPTIMIZER } from "storeseo-enums/subscriptionAddonGroup";
import subscriptionAddonInterval from "storeseo-enums/subscriptionAddonInterval";
import toastMessages from "storeseo-enums/toastMessages";
import AddonCard from "../../components/addon/AddonCard";
import AddonLoader from "../../components/addon/AddonLoader";
import { SUBSCRIPTION_ADDONS } from "../../config";
import { useAppQuery, useUtilityApi } from "../../hooks";
import { useSubscriptionApi } from "../../hooks/apiHooks/useSubscriptionApi";
import { useUserApi } from "../../hooks/apiHooks/useUserApi";
import { useAddonIsAllowed } from "../../hooks/useAddonIsAllowed";
import { useAppNavigation } from "../../hooks/useAppNavigation";
import UseConfirmation from "../../hooks/useConfirmation";
import { updateUser } from "../../store/features/User";
import { getQueryFromUrlSearchParam, showNotification } from "../../utility/helpers";

const initialPlanData = {
  name: "",
  type: "",
  narration: "",
  price: 0,
  discount: 0,
  subtotal: 0,
  coupon_code: null,
  coupon_desc: null,
  hundredPercentCouponApplied: false,
};

const Checkout = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { backAction, goToSubscription } = useAppNavigation();

  const user = useSelector((state) => state.user);
  const dispatch = useDispatch();
  const { slug } = useParams();
  const location = useLocation();
  const [searchParams, setSearchParams] = useSearchParams();
  const query = getQueryFromUrlSearchParam(searchParams);
  const subscriptionApi = useSubscriptionApi();
  const userApi = useUserApi();
  const { renderConfirmation, showConfirmation, hideConfirmation } = UseConfirmation();

  const [planData, setPlanData] = useState(initialPlanData);
  const [couponCode, setCouponCode] = useState(searchParams.get("coupon") || "");
  const [validCouponCode, setValidCouponCode] = useState("");
  const [couponError, setCouponError] = useState("");
  const [isCouponApplied, setIsCouponApplied] = useState(false);
  // const [isCurrentPlan, setIsCurrentPlan] = useState(false);
  const [showCouponInput, setShowCouponInput] = useState(false);
  const [showCouponQuestion, setShowCouponQuestion] = useState(true);
  const [selectedAddons, setSelectedAddons] = useState({
    [IMAGE_OPTIMIZER]: parseInt(query?.[IMAGE_OPTIMIZER]) || user?.addons?.[IMAGE_OPTIMIZER]?.id || null,
  });
  const [addons, setAddons] = useState([]);
  const [imageOptimizer, setImageOptimizer] = useState(
    addons.find((addon) => addon.id === selectedAddons[IMAGE_OPTIMIZER]) || null
  );
  const [totalPrice, setTotalPrice] = useState(0);
  const [allowOnlyAddon, setAllowOnlyAddon] = useState(false);

  useEffect(() => {
    if (user.planSlug === "visionary" && planData.slug === "visionary") {
      setAllowOnlyAddon(true);
    } else {
      setAllowOnlyAddon(false);
    }
  }, [user.planSlug, planData]);

  const allowAddon = useAddonIsAllowed(planData);

  const toggleCouponInput = () => {
    setShowCouponQuestion(!showCouponQuestion);
    setShowCouponInput(!showCouponInput);
  };

  const { data, refetch, isFetching } = useAppQuery({
    queryKey: `checkout_data_${slug}`,
    queryFn: () => subscriptionApi.getCheckoutData(slug, couponCode),
    reactQueryOptions: {
      onError: (err) => {
        navigate("/404");
      },
    },
  });

  useEffect(() => {
    if (data) {
      handleSetPlanData(data.planData);
    }

    setAddons(data?.addons || []);
  }, [data]);

  // useEffect(() => {
  //   setIsCurrentPlan(slug === user?.planSlug);
  // }, [slug, user]);

  useEffect(() => {
    if (!isCouponApplied) {
      refetch();
    }
  }, [isCouponApplied]);

  const handleSetPlanData = (planDetails) => {
    setPlanData(planDetails);
    if (planDetails.couponCode && planDetails.discount > 0) {
      setCouponCode(planDetails.couponCode);
      setValidCouponCode(planDetails.couponCode);
      setIsCouponApplied(true);
      setShowCouponQuestion(false);
    } else {
      setCouponCode("");
      setValidCouponCode("");
      setIsCouponApplied(false);
      setShowCouponQuestion(true);
    }
  };

  const { mutate: submitCouponValidate, isLoading: isCouponApplying } = useMutation({
    mutationFn: () => subscriptionApi.validateCoupon(slug, couponCode),
    onSuccess: (data) => {
      if (data) {
        handleSetPlanData(data);
        setShowCouponQuestion(false);
        setShowCouponInput(false);
      }
    },
    onError: (error) => {
      setCouponError(error.message);
    },
  });

  const applyCoupon = () => {
    setCouponError("");

    if (!couponCode) {
      setCouponError("Please input coupon code");
      return;
    }

    submitCouponValidate();
  };

  const utilityApi = useUtilityApi();
  const { isLoading: isMigrating, mutateAsync: startProductSync } = useMutation({
    mutationFn: () => utilityApi.submitDataMigrationInfo(null),
  });

  const { mutate: subscribeToPlan, isLoading: isSubmitting } = useMutation({
    mutationFn: async () => {
      return await subscriptionApi.handleSubscription(slug, validCouponCode, selectedAddons);
    },
    onSuccess: async ({ redirectTo, isFreePlan }) => {
      hideConfirmation();

      if (isFreePlan) {
        const { user } = await userApi.getAuthUserData();

        dispatch(updateUser({ ...user, isNewlyUpgraded: !isFreePlan }));

        startProductSync();
      }

      if (redirectTo !== undefined) navigate(redirectTo);
    },
  });

  const submitCheckout = async () => {
    const isDowngrade = user.planId && user.planSlug !== planData.slug && (planData.isFree || !planData.isUpgradable);

    if (isDowngrade) {
      showConfirmation();
      return;
    }

    return subscribeToPlan();
  };

  const removeCoupon = () => {
    navigate(location.pathname, { replace: true });
    handleSetPlanData({ ...planData, total: planData.subtotal });
    calculateTotalPrice();
    setCouponCode("");
    setValidCouponCode("");
    setIsCouponApplied(false);
    setShowCouponQuestion(true);
    showNotification({ message: toastMessages.COUPON_CLEARED, type: "success" });
  };

  const handleAddonSelect = (_, newValue) => {
    const [group, id] = newValue.split(":");
    const selectedItems = {
      ...selectedAddons,
      [group]: Number(id),
    };
    setSelectedAddons(selectedItems);

    setSearchParams({
      ...query,
      ...selectedItems,
    });
  };

  const addonBadgeMarkup = <Badge tone="info">{t("Add-on")}</Badge>;

  useEffect(() => {
    calculateTotalPrice();

    if (selectedAddons[IMAGE_OPTIMIZER]) {
      setImageOptimizer(addons.find((addon) => addon.id === selectedAddons[IMAGE_OPTIMIZER]) || null);
    }
  }, [selectedAddons, planData?.total, allowOnlyAddon]);

  const calculateTotalPrice = () => {
    const total = Object.values(selectedAddons)
      .filter((val) => val)
      ?.reduce(
        (price, id) => {
          let addon = addons.find((addon) => addon.id === id);
          let interval = planData.interval === planInterval.ANNUALLY && addon.group === IMAGE_OPTIMIZER ? 12 : 1;
          return price + addon?.subtotal * interval;
        },
        allowOnlyAddon ? 0 : planData.total
      );

    setTotalPrice(total);
  };

  const isDisabledCheckout = isFetching;

  const [showBanner, setShowBanner] = useState(false);

  useEffect(() => {
    setShowBanner(user?.isPremium && allowAddon && !allowOnlyAddon);
  }, [user, allowAddon, allowOnlyAddon]);

  return (
    <Page
      title={t("Checkout")}
      backAction={backAction}
      secondaryActions={[{ content: t("Change plan"), onAction: goToSubscription }]}
      narrowWidth
    >
      <BlockStack gap="400">
        {showBanner && (
          <Banner
            tone="info"
            title={t("Note")}
            onDismiss={() => setShowBanner(false)}
            icon={InfoIcon}
          >
            <Text as="p">
              {t(
                "As per the Shopify Subscription system, your current subscription plan will be canceled (with charges deducted for the days you have already used), and a new subscription will be created"
              )}
              .
            </Text>
          </Banner>
        )}
        {allowAddon && (
          <Card padding="0">
            <BlockStack align="space-between">
              <Box padding="400">
                <Text
                  as="h6"
                  variant="headingMd"
                >
                  {t("Available Addons")}
                </Text>
              </Box>

              {SUBSCRIPTION_ADDONS.filter((a) => a.type === subscriptionAddonInterval.MONTHLY).map(({ title, key }) => (
                <Fragment key={key}>
                  <Divider />
                  <Box padding="400">
                    <BlockStack gap="200">
                      <Text
                        as="h4"
                        variant="headingSm"
                      >
                        {t(title)}
                      </Text>

                      {isFetching ? (
                        <AddonLoader />
                      ) : (
                        <InlineGrid
                          columns="4"
                          gap="400"
                        >
                          {addons
                            .filter((a) => a.group === key)

                            .sort((a, b) => a.order - b.order)

                            .map((ad) => (
                              <AddonCard
                                key={ad.id}
                                addon={ad}
                                selectedAddons={selectedAddons}
                                handleAddonSelect={handleAddonSelect}
                              />
                            ))}
                        </InlineGrid>
                      )}
                    </BlockStack>
                  </Box>
                </Fragment>
              ))}
            </BlockStack>
          </Card>
        )}
        <Card padding="0">
          <BlockStack align="space-between">
            <Box padding="400">
              <Text
                as="h6"
                variant="headingMd"
              >
                {t("Pricing Details")}
              </Text>
            </Box>
            {!allowOnlyAddon && (
              <>
                <Divider />
                <Box padding="400">
                  <BlockStack gap="400">
                    <PricingRow
                      title={t("Plan Name")}
                      value={planData?.name}
                    />
                    <PricingRow
                      title={t("Plan Price")}
                      value={`$${planData?.price?.toFixed(2)}`}
                    />
                    {!planData.isFree && (
                      <PricingRow
                        title={t("Duration")}
                        value={
                          <>
                            {planData?.duration} {planData?.intervalText}
                            {planData?.duration > 1 && "s"}
                          </>
                        }
                      />
                    )}

                    <PricingRow
                      title={t("Sub Total")}
                      value={`$${planData?.subtotal?.toFixed(2)}`}
                    />
                    {showCouponQuestion && (
                      <InlineStack>
                        <Link
                          onClick={toggleCouponInput}
                          monochrome
                        >
                          {t("Do You Have Any Coupon?")}
                        </Link>
                      </InlineStack>
                    )}
                    {showCouponInput && (
                      <TextField
                        label={t("Give your coupon code")}
                        type="text"
                        value={couponCode}
                        onChange={setCouponCode}
                        onFocus={() => setCouponError("")}
                        placeholder={t("Coupon Code")}
                        autoComplete="off"
                        clearButton={true}
                        onClearButtonClick={() => setCouponCode("")}
                        connectedRight={
                          <Button
                            onClick={applyCoupon}
                            loading={isCouponApplying}
                          >
                            {t("Apply")}
                          </Button>
                        }
                        labelAction={{
                          content: t("Close"),
                          onAction: () => {
                            setCouponCode("");
                            setCouponError("");
                            toggleCouponInput();
                          },
                        }}
                        error={couponError}
                      />
                    )}
                    {isCouponApplied && (
                      <PricingRow
                        title={
                          <InlineStack gap="025">
                            {t("Discount")} ({planData?.couponCode})
                            <Link onClick={removeCoupon}>
                              <Icon
                                source={XSmallIcon}
                                tone="critical"
                              />
                            </Link>
                          </InlineStack>
                        }
                        value={`-$${planData?.discount?.toFixed(2)}`}
                      />
                    )}
                  </BlockStack>
                </Box>
              </>
            )}
            {imageOptimizer && (
              <>
                <Divider />
                <Box padding="400">
                  <PricingRow
                    title={
                      <InlineStack gap="200">
                        {t("Image Optimizer")}
                        {addonBadgeMarkup}
                      </InlineStack>
                    }
                    value={
                      <>
                        ${imageOptimizer?.subtotal?.toFixed(2)}
                        {planData.interval === planInterval.ANNUALLY && (
                          <>x12 = ${(imageOptimizer?.subtotal * 12).toFixed(2)}</>
                        )}
                        {/* {imageOptimizer.type !== "FREE" && imageOptimizer.interval === MONTHLY ? "/month" : ""} */}
                      </>
                    }
                  />
                </Box>
              </>
            )}
            <Divider />
            <Box padding="400">
              <BlockStack gap="400">
                <InlineStack
                  align="space-between"
                  blockAlign="center"
                >
                  <Text
                    as="h6"
                    variant="headingSm"
                  >
                    {t("Total")}
                  </Text>
                  <Text
                    as="h6"
                    variant="headingSm"
                  >
                    ${totalPrice?.toFixed(2)}
                  </Text>
                </InlineStack>
              </BlockStack>
            </Box>
          </BlockStack>
        </Card>
        <InlineStack align="end">
          <Button
            onClick={submitCheckout}
            loading={isSubmitting}
            disabled={isDisabledCheckout}
            variant="primary"
          >
            {t("Checkout")}
          </Button>
        </InlineStack>
      </BlockStack>

      {renderConfirmation({
        content:
          "Are you sure you want to downgrade your subscription? Your store data will be deleted according to the plan.",
        primaryAction: subscribeToPlan,
        loading: isSubmitting,
      })}
    </Page>
  );
};

const PricingRow = ({ title, value }) => {
  return (
    <InlineStack
      align="space-between"
      blockAlign="center"
    >
      <Text
        as="h6"
        variant="headingSm"
      >
        {title}
      </Text>

      <Text
        as="span"
        variant="headingSm"
      >
        {value}
      </Text>
    </InlineStack>
  );
};

export default Checkout;

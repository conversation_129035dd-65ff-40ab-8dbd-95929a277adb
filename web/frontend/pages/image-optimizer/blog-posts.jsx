//@ts-check
import ImageOptimizerTable from "@/components/image-optimizer/ImageOptimizerTable";
import { RESOURCE_CONFIGS } from "@/config/imageOptimizerConfig";
import { memo } from "react";
import ResourceType from "storeseo-enums/resourceType";

/**
 * Articles Image Optimizer Page
 * Uses the shared ImageOptimizerTable component with article-specific configuration
 */
const ArticlesImageOptimizer = memo(() => {
  return (
    <ImageOptimizerTable
      resourceType={ResourceType.ARTICLE}
      config={RESOURCE_CONFIGS[ResourceType.ARTICLE]}
    />
  );
});

export default ArticlesImageOptimizer;

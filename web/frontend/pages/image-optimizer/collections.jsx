//@ts-check
import ImageOptimizerTable from "@/components/image-optimizer/ImageOptimizerTable";
import { RESOURCE_CONFIGS } from "@/config/imageOptimizerConfig";
import { memo } from "react";
import ResourceType from "storeseo-enums/resourceType";

/**
 * Collections Image Optimizer Page
 * Uses the shared ImageOptimizerTable component with collection-specific configuration
 */
const CollectionsImageOptimizer = memo(() => {
  return (
    <ImageOptimizerTable
      resourceType={ResourceType.COLLECTION}
      config={RESOURCE_CONFIGS[ResourceType.COLLECTION]}
    />
  );
});

export default CollectionsImageOptimizer;

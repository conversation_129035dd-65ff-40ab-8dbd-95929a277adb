import { BlockStack, But<PERSON>, Card, FormLayout, Image, Page, TextField } from "@shopify/polaris";
import { useState } from "react";
import { useTranslation } from "react-i18next";

export default function Login() {
  const [shop, setShop] = useState("");
  const { t } = useTranslation();

  return (
    <Page
      title={t("Best Shopify SEO App")}
      subtitle={t("Login with your shop domain to install the app")}
      narrowWidth
    >
      <Card>
        <form
          method="get"
          action="/install"
          target="_blank"
        >
          <BlockStack
            align="center"
            inlineAlign="center"
          >
            <Image
              src="https://storeseo.com/wp-content/uploads/2023/12/storeSEO-logo.png.webp"
              width="150px"
            />
          </BlockStack>
          <FormLayout>
            <TextField
              type="text"
              name="shop"
              label="Shop domain"
              helpText="e.g: example.myshopify.com"
              value={shop}
              onChange={setShop}
              autoComplete="on"
            />
            <Button
              submit
              variant="primary"
            >
              {t("Login")}
            </Button>
          </FormLayout>
        </form>
      </Card>
    </Page>
  );
}

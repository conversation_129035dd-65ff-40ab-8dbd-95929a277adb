import { STEPS } from "@/config/onboarding.js";
import { BlockStack, Card, Page, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import OnboardingSteps from "storeseo-enums/onboardingSteps";
import toastMessages from "storeseo-enums/toastMessages";
import { useUserApi } from "../hooks/apiHooks/useUserApi.js";
import { useAppQuery, useUtilityApi } from "../hooks/index.js";
import { updateUser } from "../store/features/User.js";
import { getNextObject, showNotification } from "../utility/helpers.jsx";

function VerifySubscription() {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const userApi = useUserApi();

  const chargeId = new URLSearchParams(location.search).get("charge_id");

  const utilityApi = useUtilityApi();
  const { isLoading: isMigrating, mutateAsync: startProductSync } = useMutation({
    mutationFn: () => utilityApi.submitDataMigrationInfo(null),
  });

  // useEffect(() => {
  //   if (chargeId && !isMigrating) {
  //     startProductSync();
  //   }
  // }, [chargeId]);

  if (chargeId) {
    useAppQuery({
      queryKey: "SUBSCRIPTION_SUCCESS",
      queryFn: () => userApi.getAuthUserData(),
      reactQueryOptions: {
        staleTime: 0,
        refetchInterval: false,
        onSuccess: ({ user }) => {
          startProductSync();
          dispatch(updateUser({ ...user, isNewlyUpgraded: user.isOnboarded ? true : false }));
          showNotification({
            type: "success",
            message: toastMessages.SUBSCRIPTION_SUCCESSFUL,
          });

          setTimeout(() => {
            const nextStep = getNextObject(STEPS, OnboardingSteps.SUBSCRIPTION, "name");
            navigate(user.isOnboarded ? "/" : nextStep.pathname);
          }, 3000);
        },
      },
    });
  }

  return (
    <Page>
      <Card padding="3200">
        <BlockStack
          align="center"
          inlineAlign="center"
          gap={400}
        >
          {/* <Spinner size={"large"} /> */}
          <img
            src="https://cdn.storeseo.com/verify/Verifying-Subscription.gif"
            alt="Verifying subscription"
            width="172"
            height="168"
          />
          <Text
            as={"h4"}
            variant={"headingLg"}
          >
            {t("Verifying subscription. Please wait")}...
          </Text>
        </BlockStack>
      </Card>
    </Page>
  );
}

export default VerifySubscription;

// @ts-check
import SubscriptionV2Contents from "@/components/subscription/SubscriptionV2Contents.jsx";
import { useAppNavigation } from "@/hooks/useAppNavigation.js";
import { Page } from "@shopify/polaris";
import { useTranslation } from "react-i18next";

const Subscription = () => {
  const { t } = useTranslation();
  const { backAction } = useAppNavigation();

  return (
    <Page
      title={t("Power up your store with premium features")}
      subtitle={t("Choose a pricing plan that meets your needs!")}
      backAction={backAction}
    >
      <SubscriptionV2Contents />
    </Page>
  );
};

export default Subscription;

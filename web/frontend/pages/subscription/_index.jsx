import { useAppNavigation } from "@/hooks/useAppNavigation.js";
import { BlockStack, Button, ButtonGroup, Grid, InlineStack, Link, Page, Text } from "@shopify/polaris";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { ANNUALLY, MONTHLY, USAGE } from "storeseo-enums/subscriptionAddonInterval";
import SubscriptionContentLoader from "../../components/loader/SubscriptionContentLoader.jsx";
import PlanCard from "../../components/subscription/PlanCard.jsx";
import { useSubscriptionApi } from "../../hooks/apiHooks/useSubscriptionApi.js";
import { useUserApi } from "../../hooks/apiHooks/useUserApi.js";
import { useAppQuery } from "../../hooks/index.js";
import UseConfirmation from "../../hooks/useConfirmation.jsx";
import { updateUser } from "../../store/features/User.js";

const Subscription = () => {
  const { t } = useTranslation();
  const subscriptionApi = useSubscriptionApi();
  const userApi = useUserApi();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { backAction } = useAppNavigation();

  const [showInterval, setShowInterval] = useState(MONTHLY);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const user = useSelector((state) => state.user);

  const { data = {}, isFetching } = useAppQuery({
    queryKey: ["SUBSCRIPTION_DATA", user.planId],
    queryFn: () => subscriptionApi.getSubscriptionData(),
    reactQueryOptions: {
      // staleTime: 0,
      // refetchInterval: false,
      // onSuccess: (data) => {
      //   setPlans(data.plans);
      // },
    },
  });

  const { plans = [] } = data;
  const visiblePlans = plans.filter((plan) => plan.interval === showInterval);

  const freePlan = plans.find((plan) => plan.isFree);
  const visionaryPlan = plans.find((plan) => plan.interval === USAGE);
  const basicPlan = plans.find((plan) => plan.interval === MONTHLY);

  const { renderConfirmation, showConfirmation, hideConfirmation } = UseConfirmation();
  const { mutate: cancelSubscription, isLoading: isCancelling } = useMutation({
    mutationFn: subscriptionApi.handleSubscriptionCancel,
    onSuccess: async () => {
      hideConfirmation();
      userApi.getAuthUserData().then(({ user }) => {
        dispatch(updateUser({ ...user, isNewlyUpgraded: false }));
      });
      navigate("/");
    },
  });

  if (isFetching) return <SubscriptionContentLoader />;

  return (
    <Page
      title={t("Power up your store with premium features")}
      subtitle={t("Choose a pricing plan that meets your needs!")}
      backAction={backAction}
    >
      <BlockStack gap="400">
        {freePlan && (
          <PlanCard
            plan={freePlan}
            fullWidth
            basicPlan={basicPlan}
          />
        )}
        <InlineStack align="right">
          <ButtonGroup variant="segmented">
            <Button
              onClick={() => setShowInterval(MONTHLY)}
              pressed={showInterval === MONTHLY}
            >
              {t("Monthly")}
            </Button>
            <Button
              onClick={() => setShowInterval(ANNUALLY)}
              pressed={showInterval === ANNUALLY}
            >
              {t("Yearly")}
            </Button>
          </ButtonGroup>
        </InlineStack>

        <div className="pricing-wrap">
          <Grid columns={{ xs: 1, sm: 1, md: 2, lg: 3 }}>
            {visiblePlans.map((plan) => (
              <Grid.Cell key={plan.slug}>
                <PlanCard plan={plan} />
              </Grid.Cell>
            ))}
          </Grid>
        </div>

        {visionaryPlan && (
          <PlanCard
            plan={visionaryPlan}
            fullWidth
          />
        )}

        {user.isPremium && (
          <Text>
            <Link
              monochrome
              onClick={showConfirmation}
            >
              {t("Click here")}
            </Link>{" "}
            {t("to")} <Text as="strong">{t("cancel your subscription plan")}</Text>
          </Text>
        )}
      </BlockStack>
      {renderConfirmation({
        content:
          "Are you sure you want to cancel your subscription? Your store data will be deleted according to the plan.",
        primaryAction: cancelSubscription,
        primaryActionIsDestructive: true,
        primaryActionText: "Yes, Confirm!",
        loading: isCancelling,
      })}
    </Page>
  );
};

export default Subscription;

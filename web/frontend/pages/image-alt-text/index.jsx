import EmptyPage from "@/components/common/EmptyPage.jsx";
import TableEmptyState from "@/components/common/TableEmptyState.jsx";
import UsageLimitCard from "@/components/common/UsageLimitCard.jsx";
import ConfirmationModal from "@/components/modals/ConfirmationModal";
import ProductSyncButton from "@/components/products/ProductSyncButton.jsx";
import { TableReferencesProvider } from "@/components/TableReferencesContext";
import { EMPTYSTATE_IMAGES, HELP_URLS } from "@/config/index.js";
import { useAiContentApi, useImageApi, useProductApi } from "@/hooks/index.js";
import { useAppNavigation } from "@/hooks/useAppNavigation.js";
import { useAppQuery } from "@/hooks/useAppQuery.js";
import useUserAddon from "@/hooks/useUserAddon.js";
import { useGetAutoAiOptimizationSettings } from "@/lib/hooks/settings/auto-ai-optimization";
import useIndexTablePagination from "@/lib/hooks/useIndexTablePagination.jsx";
import useIndexTableSort from "@/lib/hooks/useIndexTableSort.jsx";
import useIsRunningBackupRestore from "@/lib/hooks/useIsRunningBackupRestore";
import BackupRestoreInfoBanner from "@/modules/components/BackupRestoreInfoBanner";
import BackupRestoreRunningBanner from "@/modules/components/BackupRestoreRunningBanner";
import AltTextIndexTableFilter from "@/modules/image-alt-text/AltTextIndexTableFilter.jsx";
import AltTextNotEnabled from "@/modules/image-alt-text/AltTextNotEnabled.jsx";
import AltTextTableRowItemSkeleton from "@/modules/image-alt-text/AltTextTableRowItemSkeleton.jsx";
import AltTextTableRows from "@/modules/image-alt-text/AltTextTableRows.jsx";
import AutoAiOptimizationSettingsModal from "@/modules/optimize-seo/AutoAiOptimizationSettingsModal.jsx";
import { getQueryFromUrlSearchParam } from "@/utility/helpers.jsx";
import queryKeys from "@/utility/queryKeys.js";
import {
  Badge,
  BlockStack,
  Card,
  IndexTable,
  Page,
  Text,
  useBreakpoints,
  useIndexResourceState,
} from "@shopify/polaris";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation, useQueryClient } from "react-query";
import { useSearchParams } from "react-router-dom";
import altTextOptimizationStatus from "storeseo-enums/altTextOptimization";
import ResourceType from "storeseo-enums/resourceType";
import { AI_OPTIMIZER } from "storeseo-enums/subscriptionAddonGroup";

const tableHeadings = [
  { title: "", key: "sl" },
  { title: "File Name", key: "title" },
  { title: "Alt Text", key: "alt_text" },
  { title: "Status", key: "alt_text_optimization_status" },
  { title: "Product Name", key: "product.title" },
  { title: "Action", key: "action", alignment: "center" },
];

const resourceName = {
  singular: "image",
  plural: "images",
};

const defaultRowItem = {
  id: null,
  fileName: null,
  alt_text: null,
  alt_text_optimization_status: null,
  product: null,
  created_at: null,
};
const DUMMY_IMAGES = new Array(10).fill(defaultRowItem).map((p) => ({
  ...p,
}));

const countSelectableImages = (images) => {
  const fliteredImages =
    images?.filter(
      (img) =>
        img.alt_text_optimization_status !== altTextOptimizationStatus.PENDING &&
        img.alt_text_optimization_status !== altTextOptimizationStatus.DISPATCHED
    ) || [];

  return fliteredImages.length;
};

export default function ImageAltTextGenerator() {
  const { t } = useTranslation();
  const { smDown: isSmallDevice } = useBreakpoints();
  const navigation = useAppNavigation();
  const imageApi = useImageApi();
  const [searchParams] = useSearchParams();
  const query = getQueryFromUrlSearchParam(searchParams);
  const queryClient = useQueryClient();
  const { aiOptimizerUsageLimit, aiOptimizerUsageCount, updateAiOptimizerUsage, hasAiOptimizer } = useUserAddon();
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [isAutoAiOptimizationSettingsModalOpen, setIsAutoAiOptimizationSettingsModalOpen] = useState(false);
  const { isRunning: isRunningBackupOrRestore } = useIsRunningBackupRestore();
  const { data: autoAiOptimizeSettings } = useGetAutoAiOptimizationSettings();

  const queryKey = [queryKeys.IMAGES_LIST, query];
  const { data, isFetching, isRefetching, isFetched } = useAppQuery({
    queryKey,
    queryFn: () => {
      return imageApi.getImages("PRODUCT", query);
    },
  });

  const { images = DUMMY_IMAGES, pagination = {}, totalCount } = data || {};

  const formattedImages =
    images?.map((img) => {
      return {
        ...img,
        title: img?.fileName?.split("?")[0],
        resources: img?.products?.map((item) => ({ ...item, resource_id: item.product_id })),
      };
    }) || [];

  const hasEmptyContent = isFetched && totalCount === 0;
  const selectableImagesCount = countSelectableImages(images);

  const { handleSort, sortDir, sortIndex, defaultSortDir } = useIndexTableSort({ tableHeadings });
  const { paginationConfigs } = useIndexTablePagination(pagination);
  const { selectedResources, allResourcesSelected, handleSelectionChange, clearSelection, removeSelectedResources } =
    useIndexResourceState(images);

  useEffect(() => {
    if (selectedResources.length) clearSelection();
  }, [searchParams]);

  const productApi = useProductApi();

  const { mutate: restoreImages, isLoading: isRestoringImage } = useMutation({
    mutationFn: productApi.undoOptimizedImgAltTexts,
    onSuccess: ({ images }) => {
      clearSelection();
      const currentImages = queryClient.getQueryData(queryKey);

      const updatedImages = currentImages?.images?.map((img) => {
        const findImage = images.find((item) => String(item.id) === String(img.id));
        if (selectedResources.includes(img.id) && findImage) {
          return {
            ...img,
            alt_text: findImage.alt_text,
            alt_text_optimization_status: altTextOptimizationStatus.RESTORED,
          };
        }
        return img;
      });

      queryClient.setQueryData(queryKey, {
        ...currentImages,
        images: updatedImages,
      });
    },
  });

  const handleListRestore = () => {
    /**
     * @type {{productId: string, imageId: string}[]}
     */
    const list = [];
    const imagesHash = images.reduce((hash, img) => ({ ...hash, [img.id]: img }), {});

    for (let id of selectedResources) {
      if (imagesHash[id]) {
        const image = imagesHash[id];
        const { id: productId } = image.products[0];

        list.push({
          productId,
          imageId: id,
        });
      }
    }

    restoreImages({ images: list });
  };

  const aiContentApi = useAiContentApi();
  const { mutate: bulkAltTextGenerate, isLoading: isBulkAltTextGenerationLoading } = useMutation({
    mutationFn: aiContentApi.generateBulkImageAltText,
    onSuccess: ({ creditUsage }) => {
      clearSelection();
      updateAiOptimizerUsage(creditUsage);

      const currentImages = queryClient.getQueryData(queryKey);
      const updatedImages = currentImages?.images?.map((img) => {
        if (selectedResources.includes(img.id)) {
          return {
            ...img,
            alt_text_optimization_status: altTextOptimizationStatus.PENDING,
          };
        }
        return img;
      });
      queryClient.setQueryData(queryKey, {
        ...currentImages,
        images: updatedImages,
      });
      setShowConfirmModal(false);
    },
  });

  const handleBulkAltTextGenerate = () => {
    /**
     * @type {{id: number, media_id: string, alt_text: string | null, alt_text_optimization_status: string}[]}
     */
    const list = [];
    const imagesHash = images.reduce((hash, img) => ({ ...hash, [img.id]: img }), {});

    for (let id of selectedResources) {
      if (imagesHash[id]) {
        const image = imagesHash[id];

        list.push({
          id,
          media_id: image.media_id,
          alt_text: image.alt_text,
          alt_text_optimization_status: image.alt_text_optimization_status,
        });
      }
    }
    bulkAltTextGenerate({ images: list });
  };

  // Remove selected resources if they are in pending or dispatched status
  useEffect(() => {
    const imagesHash = images?.reduce((hash, img) => ({ ...hash, [img.id]: img }), {});

    const filteredResources = isRunningBackupOrRestore
      ? selectedResources.map((id) => id)
      : selectedResources.filter(
          (id) =>
            imagesHash[id]?.alt_text_optimization_status === altTextOptimizationStatus.PENDING ||
            imagesHash[id]?.alt_text_optimization_status === altTextOptimizationStatus.DISPATCHED
        );

    if (filteredResources.length) removeSelectedResources(filteredResources);
  }, [selectedResources]);

  const limitExceeded = aiOptimizerUsageCount + selectedResources.length * 6 > aiOptimizerUsageLimit;

  const emptyStateMarkup = (
    <TableEmptyState
      title="No images found"
      description="Try changing the filters or search term"
      withIllustration
    />
  );

  if (!hasAiOptimizer) return <AltTextNotEnabled />;

  return (
    <Page
      fullWidth={!hasEmptyContent}
      title={t("Image Alt Text Generator")}
      titleMetadata={
        <Badge tone={autoAiOptimizeSettings?.[ResourceType.PRODUCT]?.status ? "success" : "warning"}>
          {t(
            `Auto AI Optimization: ${autoAiOptimizeSettings?.[ResourceType.PRODUCT]?.status ? "Enabled" : "Disabled"}`
          )}
        </Badge>
      }
      backAction={navigation.backAction}
      secondaryActions={[
        { content: t("Manage Settings"), onAction: () => setIsAutoAiOptimizationSettingsModalOpen(true) },
      ]}
    >
      <TableReferencesProvider>
        <BlockStack gap="400">
          <BackupRestoreRunningBanner />

          {!hasEmptyContent && (
            <>
              <UsageLimitCard
                title="AI Content Optimizer"
                group={AI_OPTIMIZER}
                learnMoreButton={{
                  title: "What do i do if i need more credits for AI Content Optimizer?",
                  url: HELP_URLS.AI_OPTIMIZER,
                }}
                action={{ content: "Increase limit" }}
              />

              <Card padding="0">
                <AltTextIndexTableFilter isLoading={isFetching || isRestoringImage || isBulkAltTextGenerationLoading} />

                <IndexTable
                  condensed={isSmallDevice}
                  resourceName={resourceName}
                  itemCount={selectableImagesCount || images?.length || 0}
                  selectedItemsCount={allResourcesSelected ? "All" : selectedResources.length}
                  onSelectionChange={handleSelectionChange}
                  headings={tableHeadings.map((heading) => ({
                    title: t(heading.title),
                    alignment: heading.alignment || "left",
                  }))}
                  selectable={true}
                  onSort={handleSort}
                  sortable={[false, false, false, true, false, false]}
                  sortColumnIndex={sortIndex}
                  sortDirection={sortDir}
                  defaultSortDirection={defaultSortDir}
                  pagination={paginationConfigs}
                  promotedBulkActions={[
                    {
                      content: `${t("Restore")} (${selectedResources.length})`,
                      onAction: handleListRestore,
                      disabled: isRestoringImage,
                    },
                    {
                      content: `${t("Generate with AI")} (${selectedResources.length})`,
                      onAction: () => setShowConfirmModal(true),
                      disabled: isBulkAltTextGenerationLoading || limitExceeded,
                    },
                  ]}
                  emptyState={emptyStateMarkup}
                >
                  {!(isFetching && !isRefetching) && formattedImages.length > 0
                    ? formattedImages?.map((image, index) => (
                        <AltTextTableRows
                          key={index}
                          image={image}
                          rowPosition={index}
                          selectedResources={selectedResources}
                          resourceType="PRODUCT"
                        />
                      ))
                    : DUMMY_IMAGES.map((_, index) => (
                        <IndexTable.Row
                          key={index}
                          id={index.toString()}
                          position={index}
                        >
                          <AltTextTableRowItemSkeleton key={index} />
                        </IndexTable.Row>
                      ))}
                </IndexTable>

                <ConfirmationModal
                  show={showConfirmModal}
                  onClose={() => {
                    setShowConfirmModal(false);
                    clearSelection();
                  }}
                  title="Confirm"
                  primaryAction={handleBulkAltTextGenerate}
                  loading={isBulkAltTextGenerationLoading}
                  size="base"
                >
                  <BlockStack gap="200">
                    <BackupRestoreInfoBanner />
                    <Text as={"p"}>
                      {t("Are you sure, you want to generate image alt text for the selected images?")}
                    </Text>
                    <Text
                      as={"p"}
                      fontWeight="bold"
                    >
                      {t("Note: AI Credit will be used to generate image alt text.")}
                    </Text>
                  </BlockStack>
                </ConfirmationModal>
              </Card>
            </>
          )}
          {hasEmptyContent && (
            <EmptyPage
              heading="Add or sync your products before generating images ALT text with StoreSEO AI"
              content="Before generating images ALT text, you need to add products or sync them first"
              primaryAction={<ProductSyncButton />}
              image={EMPTYSTATE_IMAGES.imageOptimizer}
            />
          )}
        </BlockStack>
      </TableReferencesProvider>
      <AutoAiOptimizationSettingsModal
        resourceType="PRODUCT"
        isOpen={isAutoAiOptimizationSettingsModalOpen}
        setIsOpen={setIsAutoAiOptimizationSettingsModalOpen}
      />
    </Page>
  );
}

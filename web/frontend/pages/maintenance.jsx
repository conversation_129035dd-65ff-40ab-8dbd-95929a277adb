import { MAINTENANCE_ILLUSTRATION } from "@/config";
import { BlockStack, Card, Page, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";

const Maintenance = () => {
  const { t } = useTranslation();
  return (
    <Page>
      <Card padding="1600">
        <BlockStack
          gap="400"
          inlineAlign="center"
        >
          <img
            role="presentation"
            src={MAINTENANCE_ILLUSTRATION}
            alt="Maintenance Mode"
            width={170}
            height={170}
          />

          <Text
            as={"h4"}
            variant="headingXl"
          >
            {t("Maintenance Mode")}
          </Text>

          <BlockStack gap="100">
            <Text
              as={"p"}
              variant="bodyLg"
              alignment="center"
            >
              {t("StoreSEO is currently undergoing scheduled maintenance.")}
            </Text>
            <Text
              as={"p"}
              variant="bodyLg"
              alignment="center"
            >
              {t("We apologize for any inconvenience and appreciate your patience.")}
            </Text>
          </BlockStack>
        </BlockStack>
      </Card>
    </Page>
  );
};

export default Maintenance;

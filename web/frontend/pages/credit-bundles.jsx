import { useGetAutoAiOptimizationSettings } from "@/lib/hooks/settings/auto-ai-optimization";
import AutoAiOptimizationSettingsModal from "@/modules/optimize-seo/AutoAiOptimizationSettingsModal";
import { Badge, BlockStack, Button, ButtonGroup, Card, InlineGrid, InlineStack, Page, Text } from "@shopify/polaris";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useSearchParams } from "react-router-dom";
import { BANNER_CREDIT_BUNDLE_GUIDE } from "storeseo-enums/cacheKeys";
import ResourceType from "storeseo-enums/resourceType";
import { AI_OPTIMIZER } from "storeseo-enums/subscriptionAddonGroup";
import AddonCard from "../components/addon/AddonCard";
import AddonLoader from "../components/addon/AddonLoader";
import DismissableBanner from "../components/common/DismissableBanner";
import UsageLimitCard from "../components/common/UsageLimitCard";
import { HELP_URLS } from "../config";
import { useAppQuery } from "../hooks";
import { useSubscriptionApi } from "../hooks/apiHooks/useSubscriptionApi";
import { useAppNavigation } from "../hooks/useAppNavigation";
import { getQueryFromUrlSearchParam } from "../utility/helpers";

export default function CreditBundles() {
  const { t } = useTranslation();
  const { backAction } = useAppNavigation();
  const subscriptionApi = useSubscriptionApi();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const user = useSelector((state) => state.user);

  const { data: autoAiOptimizeSettings } = useGetAutoAiOptimizationSettings();

  const [searchParams, setSearchParams] = useSearchParams();
  const query = getQueryFromUrlSearchParam(searchParams);

  const [selectedAddons, setSelectedAddons] = useState({
    [AI_OPTIMIZER]: parseInt(query?.[AI_OPTIMIZER]) || user?.addons?.[AI_OPTIMIZER]?.id,
  });
  const [isAutoAiOptimizationSettingsModalOpen, setIsAutoAiOptimizationSettingsModalOpen] = useState(false);

  const { data: { groups } = {}, isFetching } = useAppQuery({
    queryKey: ["CREDIT_BUNDLES"],
    queryFn: () => subscriptionApi.getCreditBundles(),
    reactQueryOptions: {
      onError: (err) => {
        navigate("/404");
      },
    },
  });

  const { mutate: goToCheckout, isLoading: isSubmitting } = useMutation({
    mutationFn: () => subscriptionApi.handleCreditPurchase(selectedAddons),
    onSuccess: () => {
      // dispatch(setOnboardStep(!user.isOnboarded ? 7 : null));
    },
  });

  const handleAddonSelect = (_, newValue) => {
    const [group, id] = newValue.split(":");
    const selectedItems = {
      ...selectedAddons,
      [group]: Number(id),
    };
    setSelectedAddons(selectedItems);

    setSearchParams({
      ...query,
      ...selectedItems,
    });
  };

  const disableConfirmBtn = Object.values(selectedAddons).filter((val) => val).length === 0;

  return (
    <Page
      title={t("AI Content Optimizer")}
      titleMetadata={
        <Badge tone={autoAiOptimizeSettings?.[ResourceType.PRODUCT]?.status ? "success" : "warning"}>
          {t(
            `Auto AI Optimization: ${autoAiOptimizeSettings?.[ResourceType.PRODUCT]?.status ? "Enabled" : "Disabled"}`
          )}
        </Badge>
      }
      backAction={backAction}
      secondaryActions={[
        { content: t("Manage Settings"), onAction: () => setIsAutoAiOptimizationSettingsModalOpen(true) },
      ]}
      narrowWidth
    >
      <BlockStack gap={400}>
        <DismissableBanner
          mediaCard
          mediaCardSize="medium"
          bannerKey={BANNER_CREDIT_BUNDLE_GUIDE}
          title={t("Optimize for SEO within seconds with AI Content Optimizer")}
          content={t(
            "Get SEO-optimized meta titles, descriptions, and tags within seconds with the help of our AI Content Optimizer and quickly optimize your Shopify store with ease."
          )}
          primaryAction={{
            content: t("Learn more"),
            url: "https://storeseo.com/docs/how-to-use-storeseo-ai-content-optimizer/",
            target: "_blank",
          }}
          illustration="/img/credit-bundles-ai.png"
        />

        <UsageLimitCard
          title="AI Content Optimizer"
          group={AI_OPTIMIZER}
          learnMoreButton={{
            title: "What do i do if i need more credits for AI Content Optimizer?",
            url: HELP_URLS.AI_OPTIMIZER,
          }}
        />

        <Card>
          <BlockStack gap={400}>
            <BlockStack gap={100}>
              <Text
                as="h4"
                variant="headingSm"
              >
                {t("Credit bundle")}
              </Text>
              <Text
                as="p"
                variant="bodySm"
              >
                {t("Purchase your preferred bundle to unlock our AI Content Optimizer")}
              </Text>
            </BlockStack>

            {isFetching ? (
              <AddonLoader items={3} />
            ) : (
              groups &&
              groups.map((group) => (
                <InlineGrid
                  key={group.group}
                  columns="3"
                  gap="400"
                >
                  {group.items
                    .sort((a, b) => a.order - b.order)
                    .map((ad) => (
                      <AddonCard
                        key={ad.id}
                        addon={ad}
                        selectedAddons={selectedAddons}
                        handleAddonSelect={handleAddonSelect}
                      />
                    ))}
                </InlineGrid>
              ))
            )}
          </BlockStack>
        </Card>

        <InlineStack align="end">
          <ButtonGroup>
            {/* {showSkipBtn && <Button onClick={handleSkipClick}>{t("Skip")}</Button>} */}
            <Button
              variant="primary"
              loading={isSubmitting}
              onClick={goToCheckout}
              disabled={disableConfirmBtn}
            >
              {t("Confirm")}
            </Button>
          </ButtonGroup>
        </InlineStack>
      </BlockStack>
      <AutoAiOptimizationSettingsModal
        resourceType="PRODUCT"
        isOpen={isAutoAiOptimizationSettingsModalOpen}
        setIsOpen={setIsAutoAiOptimizationSettingsModalOpen}
      />
    </Page>
  );
}

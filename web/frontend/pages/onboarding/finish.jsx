import { FINISH_PAGE_CONTENT, HELP_LINKS } from "@/config/onboarding";
import { openCrispChat } from "@/utility/crisp";
import { BlockStack, Box, Button, Card, InlineStack, Text, useBreakpoints } from "@shopify/polaris";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { useNavigate, useOutletContext } from "react-router-dom";

const OnboardingFinish = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const user = useSelector((state) => state.user);
  const { updateOnboardingStatus, prevStep } = useOutletContext();
  const [pageContent, setPageContent] = useState(FINISH_PAGE_CONTENT.success);

  const [contentWidth, setContentWidth] = useState("60%");
  const breakpoints = useBreakpoints();

  useEffect(() => {
    if (breakpoints.mdDown) {
      setContentWidth("100%");
    } else {
      setContentWidth("50%");
    }
  }, [breakpoints]);

  useEffect(() => {
    if (user?.isPremium) {
      setPageContent(FINISH_PAGE_CONTENT.success);
    } else {
      setPageContent(FINISH_PAGE_CONTENT.verify);
    }
  }, [user]);

  // useEffect(() => {
  //   const timer = setTimeout(() => {
  //     navigate("/");
  //   }, 10000);

  //   return () => clearTimeout(timer);
  // }, [location.pathname]);

  return (
    <Card padding={"1600"}>
      <InlineStack align="center">
        <Box width={contentWidth}>
          <BlockStack gap={"800"}>
            <BlockStack
              gap={"400"}
              inlineAlign="center"
            >
              <BlockStack inlineAlign="center">
                <Box
                  width="214"
                  height="258"
                >
                  <pageContent.illustration />
                </Box>
                <Text
                  as="h4"
                  variant="headingLg"
                  alignment="center"
                >
                  {t(pageContent.title)}
                </Text>
              </BlockStack>

              <Text
                as="p"
                variant="bodyMd"
                alignment="center"
                tone="subdued"
              >
                {t(pageContent.description)}
              </Text>

              <InlineStack
                align="center"
                gap={"200"}
              >
                {pageContent.secondaryButton && (
                  <Button
                    onClick={() => {
                      updateOnboardingStatus.mutate();
                      navigate(pageContent.secondaryButton.url);
                    }}
                  >
                    {t(pageContent.secondaryButton.content)}
                  </Button>
                )}

                {pageContent.primaryButton && (
                  <Button
                    target="_blank"
                    variant="primary"
                    url={pageContent.primaryButton.url}
                    onClick={() => {
                      updateOnboardingStatus.mutate();
                    }}
                  >
                    {t(pageContent.primaryButton.content)}
                  </Button>
                )}
              </InlineStack>
            </BlockStack>
            <InlineStack
              align="center"
              gap={"400"}
            >
              {HELP_LINKS.map((link) => {
                if (link.type === "live_chat") {
                  return (
                    <Button
                      variant="plain"
                      key={link.name}
                      icon={link.icon}
                      onClick={openCrispChat}
                    >
                      {t(link.title)}
                    </Button>
                  );
                }
                return (
                  <Button
                    variant="plain"
                    key={link.name}
                    url={link.url}
                    icon={link.icon}
                    target="_blank"
                  >
                    {t(link.title)}
                  </Button>
                );
              })}
            </InlineStack>
          </BlockStack>
        </Box>
      </InlineStack>
    </Card>
  );
};

export default OnboardingFinish;

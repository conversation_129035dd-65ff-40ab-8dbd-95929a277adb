import { SCHEDULE_A_CALL_URL } from "@/config";
import { BADGE_TYPES, HIGHLIGHTED_FEAUTERES } from "@/config/onboarding";
import { Badge, BlockStack, Box, Button, CalloutCard, Card, Grid, Icon, InlineStack, Text } from "@shopify/polaris";
import { CheckCircleIcon, PhoneIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";
import { useOutletContext } from "react-router-dom";

const Onboarding = () => {
  const { t } = useTranslation();
  const { updateOnboardingStatus } = useOutletContext();

  return (
    <BlockStack gap="400">
      <Card>
        <BlockStack gap={"600"}>
          <Text
            as="h4"
            variant="headingLg"
          >
            {t("Easily Boost Your Store’s Visibility")}
          </Text>

          <Grid columns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 2 }}>
            {HIGHLIGHTED_FEAUTERES.map((feature, index) => (
              <Grid.Cell key={feature.title}>
                <InlineStack
                  gap={"200"}
                  align="start"
                  blockAlign="start"
                  wrap={false}
                >
                  <Box>
                    <Icon source={CheckCircleIcon} />
                  </Box>
                  <Box maxWidth="75%">
                    <BlockStack gap={"100"}>
                      <InlineStack gap={"100"}>
                        <Text
                          as="h4"
                          variant="headingMd"
                        >
                          {t(feature.title)}
                        </Text>
                        {feature.badge === BADGE_TYPES.pro && <Badge tone="warning-strong">{t("Pro")}</Badge>}
                        {feature.badge === BADGE_TYPES.addon && <Badge tone="info">{t("Add-On")}</Badge>}
                        {feature.badge === BADGE_TYPES.selected && (
                          <Badge tone="warning-strong">{t("Starting from Growth")}</Badge>
                        )}
                      </InlineStack>
                      <Text
                        as="p"
                        variant="bodySm"
                        tone="subdued"
                      >
                        {t(feature.description)}
                      </Text>
                    </BlockStack>
                  </Box>
                </InlineStack>
              </Grid.Cell>
            ))}
          </Grid>

          <InlineStack align="end">
            <Button
              variant="primary"
              onClick={updateOnboardingStatus.mutate}
              loading={updateOnboardingStatus.isLoading}
            >
              {t("Continue")}
            </Button>
          </InlineStack>
        </BlockStack>
      </Card>

      <CalloutCard
        title={t("Need help? Schedule a call for assistance.")}
        illustration="https://cdn.storeseo.com/onboard-call.png"
        primaryAction={{
          content: t("Get Free SEO Consultation"),
          url: SCHEDULE_A_CALL_URL,
          target: "_blank",
          icon: PhoneIcon,
        }}
      >
        <Text
          as="p"
          variant="bodyMd"
        >
          {t("Get exclusive support from an SEO expert. As a valued StoreSEO user, you can schedule a call for FREE.")}
        </Text>
      </CalloutCard>
    </BlockStack>
  );
};

export default Onboarding;

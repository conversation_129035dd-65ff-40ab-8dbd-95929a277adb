import Radial<PERSON>hart from "@/components/charts/RadialChart";
import AnalysisScanning from "@/components/onboarding/AnalysisScanning";
import { AREA_OF_IMPROVEMENTS } from "@/config/onboarding";
import { useShopA<PERSON> } from "@/hooks";
import { useIssueBadgeColor } from "@/hooks/useLabelColor";
import {
  Badge,
  BlockStack,
  Box,
  Button,
  Card,
  Divider,
  Grid,
  InlineStack,
  Text,
  useBreakpoints,
} from "@shopify/polaris";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { useNavigate, useOutletContext } from "react-router-dom";

const OnboardingAnalysis = () => {
  const { t } = useTranslation();
  const { updateOnboardingStatus, prevStep, nextStep } = useOutletContext();
  const navigate = useNavigate();
  const breakpoints = useBreakpoints();
  const shopApi = useShopApi();

  const [isScanning, setIsScanning] = useState(true);
  const [contentWidth, setContentWidth] = useState("60%");
  const [analysisData, setAnalysisData] = useState(null);

  useEffect(() => {
    const timer = setTimeout(() => {
      getAnalysisData();
    }, 5000);

    return () => clearTimeout(timer);
  }, [location.pathname]);

  useEffect(() => {
    if (breakpoints.mdDown) {
      setContentWidth("100%");
    } else {
      setContentWidth("50%");
    }
  }, [breakpoints]);

  const { mutate: getAnalysisData, isLoading } = useMutation({
    mutationKey: "getOnboardingAnalysisData",
    mutationFn: shopApi.getOnboardingAnalysisData,
    onSuccess: ({ analysis }) => {
      setAnalysisData(analysis || {});
      setIsScanning(false);
    },
  });

  if (isScanning)
    return (
      <AnalysisScanning
        onSkip={() => {
          updateOnboardingStatus.mutate();
          navigate(nextStep.pathname);
        }}
      />
    );

  return (
    <Card>
      <BlockStack gap={"400"}>
        <BlockStack
          gap={"400"}
          inlineAlign="center"
        >
          <Box maxWidth={contentWidth}>
            <BlockStack
              gap={"200"}
              inlineAlign="center"
            >
              <RadialChart
                score={analysisData?.seoScore || 0}
                radius={60}
                strokeWidth={16}
                dimension={100}
                size={"medium"}
                showPercent={false}
              />
              <Text
                as="h4"
                variant="headingLg"
                alignment="center"
              >
                {t("Your Current SEO Score is: {{CURRENT_SCORE}}", { CURRENT_SCORE: analysisData?.seoScore || 0 })}
              </Text>

              <Text
                as="p"
                tone="subdued"
                alignment="center"
              >
                {t(
                  "Our initial scan has covered {{PRODUCT_COUNT}} products from your store. If you have a large number of products, the rest will be scanned in the background.",
                  { PRODUCT_COUNT: analysisData?.totalProducts || 0 }
                )}
              </Text>
            </BlockStack>
          </Box>
        </BlockStack>

        <Divider />

        <BlockStack gap={"100"}>
          <Text
            as="h4"
            variant="headingMd"
          >
            {t("Areas of Improvement")}
          </Text>

          <Text
            as="p"
            tone="subdued"
          >
            {t("Make your store more visible in search engines via better rankings and easily drive traffic & sales")}
          </Text>
        </BlockStack>

        <Grid columns={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2 }}>
          {AREA_OF_IMPROVEMENTS.map((feature, index) => (
            <AOIItem
              index={index}
              key={feature.key}
              feature={feature}
              issues={analysisData?.issues?.[feature.key] || 0}
              total={analysisData?.totals?.[feature.key] || 0}
            />
          ))}
        </Grid>

        <InlineStack
          align="end"
          gap={"200"}
        >
          <Button onClick={() => navigate(prevStep.pathname)}>{t("Back")}</Button>
          <Button
            variant="primary"
            loading={updateOnboardingStatus.isLoading}
            onClick={updateOnboardingStatus.mutate}
          >
            {t("Continue")}
          </Button>
        </InlineStack>
      </BlockStack>
    </Card>
  );
};

/**
 *
 * @param {{feature: {key: string, image: string, title: string, description: string}, issues: number, total: number, index: number}} param0
 * @returns
 */
const AOIItem = ({ feature, issues, index, total }) => {
  const { t } = useTranslation();
  const badgeColor = useIssueBadgeColor(issues, total);

  return (
    <Grid.Cell key={feature.key}>
      <BlockStack gap={"400"}>
        <InlineStack
          gap={"200"}
          align="start"
          blockAlign="start"
          wrap={false}
        >
          <Box>
            <img
              src={feature.image}
              alt={feature.title}
              width={64}
              height={64}
            />
          </Box>
          <InlineStack
            wrap={false}
            align="space-between"
            blockAlign="center"
          >
            <Box maxWidth="70%">
              <BlockStack gap={"100"}>
                <Text
                  as="h4"
                  variant="headingMd"
                >
                  {t(feature.title)}
                </Text>
                <Text
                  as="p"
                  variant="bodySm"
                  tone="subdued"
                >
                  {t(feature.description, { COUNT: issues, TOTAL: total })}
                </Text>
              </BlockStack>
            </Box>

            <Box>
              <Badge tone={badgeColor}>{t("{{NO_OF_ISSUES}} Issues", { NO_OF_ISSUES: issues || "No" })}</Badge>
            </Box>
          </InlineStack>
        </InlineStack>
        {index < AREA_OF_IMPROVEMENTS.length - 2 && <Divider />}
      </BlockStack>
    </Grid.Cell>
  );
};

export default OnboardingAnalysis;

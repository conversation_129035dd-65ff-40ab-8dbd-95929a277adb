//@ts-check

/**
 * Blog Preferences Utility
 * Handles storing and retrieving user blog preferences
 */

const STORAGE_KEYS = {
  LAST_USED_BLOG_ID: 'storeseo_last_used_blog_id',
  BLOG_USAGE_STATS: 'storeseo_blog_usage_stats',
};

/**
 * @typedef {Object} BlogUsageStats
 * @property {number} blogId - Blog ID
 * @property {number} usageCount - Number of times used
 * @property {string} lastUsed - ISO timestamp of last usage
 */

/**
 * Get the last used blog ID from localStorage
 * @returns {string|null} Blog ID or null if not found
 */
export const getLastUsedBlogId = () => {
  try {
    return localStorage.getItem(STORAGE_KEYS.LAST_USED_BLOG_ID);
  } catch (error) {
    console.warn('Failed to get last used blog ID:', error);
    return null;
  }
};

/**
 * Save the last used blog ID to localStorage
 * @param {string} blogId - Blog ID to save
 */
export const setLastUsedBlogId = (blogId) => {
  try {
    localStorage.setItem(STORAGE_KEYS.LAST_USED_BLOG_ID, blogId);
    updateBlogUsageStats(blogId);
  } catch (error) {
    console.warn('Failed to save last used blog ID:', error);
  }
};

/**
 * Get blog usage statistics from localStorage
 * @returns {BlogUsageStats[]} Array of blog usage statistics
 */
export const getBlogUsageStats = () => {
  try {
    const stats = localStorage.getItem(STORAGE_KEYS.BLOG_USAGE_STATS);
    return stats ? JSON.parse(stats) : [];
  } catch (error) {
    console.warn('Failed to get blog usage stats:', error);
    return [];
  }
};

/**
 * Update blog usage statistics
 * @param {string} blogId - Blog ID to update stats for
 */
export const updateBlogUsageStats = (blogId) => {
  try {
    const stats = getBlogUsageStats();
    const existingIndex = stats.findIndex(stat => stat.blogId === parseInt(blogId));
    
    if (existingIndex >= 0) {
      // Update existing entry
      stats[existingIndex].usageCount += 1;
      stats[existingIndex].lastUsed = new Date().toISOString();
    } else {
      // Add new entry
      stats.push({
        blogId: parseInt(blogId),
        usageCount: 1,
        lastUsed: new Date().toISOString(),
      });
    }
    
    // Keep only the last 10 blog usage records
    const sortedStats = stats
      .sort((a, b) => new Date(b.lastUsed).getTime() - new Date(a.lastUsed).getTime())
      .slice(0, 10);
    
    localStorage.setItem(STORAGE_KEYS.BLOG_USAGE_STATS, JSON.stringify(sortedStats));
  } catch (error) {
    console.warn('Failed to update blog usage stats:', error);
  }
};

/**
 * Get the most frequently used blog ID
 * @param {Array} availableBlogs - Array of available blogs to filter by
 * @returns {string|null} Most frequently used blog ID or null
 */
export const getMostUsedBlogId = (availableBlogs = []) => {
  try {
    const stats = getBlogUsageStats();
    const availableBlogIds = availableBlogs.map(blog => blog.id);
    
    // Filter stats to only include available blogs
    const availableStats = stats.filter(stat => 
      availableBlogIds.includes(stat.blogId)
    );
    
    if (availableStats.length === 0) {
      return null;
    }
    
    // Sort by usage count (descending) and return the most used
    const mostUsed = availableStats.sort((a, b) => b.usageCount - a.usageCount)[0];
    return mostUsed.blogId.toString();
  } catch (error) {
    console.warn('Failed to get most used blog ID:', error);
    return null;
  }
};

/**
 * Smart blog selection algorithm
 * @param {Array} blogs - Array of available blogs
 * @param {string} currentSelection - Currently selected blog ID
 * @returns {string|null} Recommended blog ID or null
 */
export const getRecommendedBlogId = (blogs = [], currentSelection = '') => {
  if (!blogs || blogs.length === 0) {
    return null;
  }
  
  // Don't override existing selection
  if (currentSelection) {
    return null;
  }
  
  // Filter to only synced blogs
  const syncedBlogs = blogs.filter(blog => blog.is_synced);
  if (syncedBlogs.length === 0) {
    return null;
  }
  
  // Strategy 1: Single blog auto-selection
  if (syncedBlogs.length === 1) {
    return syncedBlogs[0].id.toString();
  }
  
  // Strategy 2: Most recently used blog
  const lastUsedBlogId = getLastUsedBlogId();
  if (lastUsedBlogId) {
    const lastUsedBlog = syncedBlogs.find(blog => blog.id.toString() === lastUsedBlogId);
    if (lastUsedBlog) {
      return lastUsedBlogId;
    }
  }
  
  // Strategy 3: Most frequently used blog
  const mostUsedBlogId = getMostUsedBlogId(syncedBlogs);
  if (mostUsedBlogId) {
    const mostUsedBlog = syncedBlogs.find(blog => blog.id.toString() === mostUsedBlogId);
    if (mostUsedBlog) {
      return mostUsedBlogId;
    }
  }
  
  // Strategy 4: Blog with most articles (fallback)
  const blogWithMostArticles = syncedBlogs.reduce((prev, current) => {
    const prevCount = prev.article_count || 0;
    const currentCount = current.article_count || 0;
    return currentCount > prevCount ? current : prev;
  });
  
  return blogWithMostArticles.id.toString();
};

/**
 * Clear all blog preferences (useful for testing or reset)
 */
export const clearBlogPreferences = () => {
  try {
    localStorage.removeItem(STORAGE_KEYS.LAST_USED_BLOG_ID);
    localStorage.removeItem(STORAGE_KEYS.BLOG_USAGE_STATS);
  } catch (error) {
    console.warn('Failed to clear blog preferences:', error);
  }
};

/**
 * Get blog preference summary for debugging
 * @returns {Object} Summary of current preferences
 */
export const getBlogPreferencesSummary = () => {
  return {
    lastUsedBlogId: getLastUsedBlogId(),
    usageStats: getBlogUsageStats(),
    storageKeys: STORAGE_KEYS,
  };
};

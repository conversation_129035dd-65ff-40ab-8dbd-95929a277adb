import moment from "moment";
const TRANSACTION_ID_PREFIXES = ["", "0", "00", "000", "0000", "00000"];
const TRANSACTION_ID_LENGTH = 6;
const BRAND_SHORT_CODE = "SSEO";

export const PAYMENT_STATUS = {
  PAID: "Paid",
  UNPAID: "Due",
  UNKNOWN: "",
};

export const formatDate = (dateTime, formatStr = "DD MMM, YYYY") => moment(dateTime).format(formatStr);

export const generateTransactionID = (shortId) => {
  const prefixIdx = TRANSACTION_ID_LENGTH - shortId.toString().length;
  const prefixStr = prefixIdx <= 0 ? TRANSACTION_ID_PREFIXES[0] : TRANSACTION_ID_PREFIXES[prefixIdx];

  return `${BRAND_SHORT_CODE}-${prefixStr}${shortId}`;
};

export const getPaymentStatus = (transactionDetails) => {
  const { type, is_paid: isPaid } = transactionDetails;
  let paymentStatus = PAYMENT_STATUS.PAID;

  if (type.toLowerCase() === "free") paymentStatus = PAYMENT_STATUS.UNKNOWN;
  else if (!isPaid) paymentStatus = PAYMENT_STATUS.UNPAID;

  return paymentStatus;
};

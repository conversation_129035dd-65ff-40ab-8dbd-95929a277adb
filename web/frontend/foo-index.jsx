
import { onCLS, onLCP } from "web-vitals";
onLCP((metric) => console.log("LCP: ", { ...metric }), { reportAllChanges: true });
onCLS((metric) => console.log("CLS: ", { ...metric }), { reportAllChanges: true });

async function loadApp() {
  setTimeout(async () => {
    const { default: renderApp } = await import("./index");
    console.log("\n---");
    console.log("rendering app...");
    renderApp();
    console.log("Done!\n---");
  }, 350);
}

loadApp();

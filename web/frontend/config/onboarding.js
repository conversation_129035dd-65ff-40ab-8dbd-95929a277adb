import { ChatIcon, FileIcon, PlayCircleIcon } from "@shopify/polaris-icons";
import { ANALYSIS, FINISH, SUBSCRIPTION, WELCOME } from "storeseo-enums/onboardingSteps";
import { SCHEDULE_A_CALL_URL, STORESEO_DOCS_URL, STORESEO_YOUTUBE_URL, VERIFY_URL } from "./index";

import OnboardingDone from "@/components/svg/OnboardingDone";
import OnboardingVerify from "@/components/svg/OnboardingVerify";

export const STEPS = [
  {
    name: WELCOME,
    title: "Welcome!",
    subtitle: "Finish the onboarding steps and start optimizing your store for SEO",
    pathname: "/onboarding",
  },
  {
    name: SUBSCRIPTION,
    title: "Power up your store with premium features",
    subtitle: "Choose a pricing plan that meets your needs!",
    pathname: "/onboarding/pricing",
  },
  {
    name: ANALYSIS,
    title: "Scanning your products",
    subtitle: "Get the SEO score of your store from StoreSEO based on the scan",
    pathname: "/onboarding/analysis",
  },
  {
    name: <PERSON><PERSON><PERSON><PERSON>,
    title: "You are all set!",
    subtitle: "You have successfully finished all the onboarding steps",
    pathname: "/onboarding/finish",
  },
];

export const BADGE_TYPES = {
  none: "none",
  pro: "pro",
  addon: "addon",
  selected: "selected",
};

export const HIGHLIGHTED_FEAUTERES = [
  {
    title: "Optimize SEO",
    description: "Make your store more visible to customers in search engines like Google",
    badge: BADGE_TYPES.none,
  },
  {
    title: "Image Optimizer",
    description: "Optimize images with resize and compression settings for faster loading",
    badge: BADGE_TYPES.addon,
  },
  {
    title: "Keyword Analytics",
    description: "Pick the best keywords by monitoring search volume, competition and more",
    badge: BADGE_TYPES.none,
  },
  {
    title: "Google Analytics Integration",
    description: "Integrate with StoreSEO easily and analyze real-time performance",
    badge: BADGE_TYPES.none,
  },
  {
    title: "AI Content Optimizer",
    description: "Instantly generate SEO content for products, collections and articles",
    badge: BADGE_TYPES.addon,
  },
  {
    title: "SEO Schema Options",
    description: "Enable & customize with 1-click enable option and zero coding",
    badge: BADGE_TYPES.none,
  },
  {
    title: "Image Alt Text Generator",
    description: "Ensure SEO-friendly images that are more likely to appear in searches",
    badge: BADGE_TYPES.addon,
  },
  {
    title: "Multilingual SEO",
    description: "Optimize products translated with Shopify Translate & Adapt App to target the local audience",
    badge: BADGE_TYPES.selected,
  },
  {
    title: "Sitemaps Configuration",
    description: "Control search engine visibility with noindex & nofollow settings",
    badge: BADGE_TYPES.none,
  },
  {
    title: "LLMs.txt Generator",
    description:
      "Boost your Shopify store's visibility in AI search engines, making it easier for AI to recommend your products",
    badge: BADGE_TYPES.none,
  },
  {
    title: "And More!",
    description: "Get access to additional features like Bulk SEO Optimization, SEO Data Backup & Restore and others",
    badge: BADGE_TYPES.none,
  },
];

export const AREA_OF_IMPROVEMENTS = [
  {
    key: "meta_title",
    title: "Meta Title",
    description: "Keep within 50-70 characters as recommended for search engines",
    image: "https://cdn.storeseo.com/onboarding-ai/aoi_1.png",
  },
  {
    key: "product_desc",
    title: "Product Description",
    description: "Keep within 50 to 300 words to ensure SEO-friendly content",
    image: "https://cdn.storeseo.com/onboarding-ai/aoi_2.png",
  },
  {
    key: "meta_desc",
    title: "Meta Description",
    description: "Keep within 120-165 characters as recommended for search engines",
    image: "https://cdn.storeseo.com/onboarding-ai/aoi_2.png",
  },
  {
    key: "focus_keyword_density",
    title: "Focus Keyword Density",
    description: "Keep 3-6 times for product description to ensure SEO-friendly content",
    image: "https://cdn.storeseo.com/onboarding-ai/aoi_4.png",
  },
  {
    key: "missing_alt_txt",
    title: "Image Alt Text",
    description: "Add alt-text to {{COUNT}} out of {{TOTAL}} images to make sure they are SEO-friendly",
    image: "https://cdn.storeseo.com/onboarding-ai/aoi_5.png",
  },
  {
    key: "not_optimized_images",
    title: "Optimized Images",
    description: "Optimize {{COUNT}} out of {{TOTAL}} images to improve SEO and page speed",
    image: "https://cdn.storeseo.com/onboarding-ai/aoi_6.png",
  },
];

export const FINISH_PAGE_CONTENT = {
  verify: {
    title: "Talk To Us, Verify Your Store & Increase Product Limit From 25 To 50 For FREE!",
    description:
      "Schedule a call for FREE as a StoreSEO merchant with our SEO expert to double your product limit and remove StoreSEO branding from the HTML Sitemap page.",
    // image: "https://cdn.storeseo.com/onboarding/verify.svg",
    illustration: OnboardingVerify,
    primaryButton: {
      content: "Verify your Store",
      url: VERIFY_URL,
    },
    secondaryButton: {
      content: "Go to Dashboard",
      url: "/",
    },
  },
  success: {
    title: "Start Optimizing Your Store!",
    description:
      "You can now use StoreSEO to optimize your Shopify store and boost search engine visibility with the app’s powerful features.",
    // image: "https://cdn.storeseo.com/onboarding/done.svg",
    illustration: OnboardingDone,
    primaryButton: {
      content: "Get Free SEO Consultation",
      url: SCHEDULE_A_CALL_URL,
    },
    secondaryButton: {
      content: "Go to Dashboard",
      url: "/",
    },
  },
};

export const HELP_LINKS = [
  {
    name: "tutorial",
    icon: PlayCircleIcon,
    title: "Watch Tutorial",
    url: STORESEO_YOUTUBE_URL,
    type: "external",
  },
  {
    name: "documentation",
    icon: FileIcon,
    title: "Read Documentation",
    url: STORESEO_DOCS_URL,
    type: "external",
  },
  {
    name: "live_chat",
    icon: ChatIcon,
    title: "Quick Support",
    type: "live_chat",
  },
];

//@ts-check
import ResourceType from "storeseo-enums/resourceType";

/**
 * @template T
 * @typedef {import("@shopify/polaris/build/ts/src/types.js").NonEmptyArray<T>} NonEmptyArray
 */

/**
 * @typedef {Object} ResourceConfig
 * @property {NonEmptyArray<{ title: string, key: string, alignment?: "start" | "center" | "end" }>} tableHeadings
 * @property {{ singular: string, plural: string }} resourceName
 * @property {string} emptyStateTitle
 * @property {string} emptyStateDescription
 * @property {string} resourceDisplayName
 */

/**
 * Configuration for different resource types in image optimizer
 * @type {Record<string, ResourceConfig>}
 */
export const RESOURCE_CONFIGS = {
  [ResourceType.PRODUCT]: {
    tableHeadings: [
      { title: "", key: "sl" },
      { title: "File name", key: "file_name" },
      { title: "Status", key: "optimization_status" },
      { title: "File size", key: "file_size" },
      { title: "Product", key: "product.title" },
      { title: "Action", key: "action", alignment: "center" },
    ],
    resourceName: {
      singular: "image",
      plural: "images",
    },
    emptyStateTitle: "No images found",
    emptyStateDescription: "Try changing the filters or search term",
    resourceDisplayName: "products",
  },
  [ResourceType.COLLECTION]: {
    tableHeadings: [
      { title: "", key: "sl" },
      { title: "File name", key: "file_name" },
      { title: "Status", key: "optimization_status" },
      { title: "File size", key: "file_size" },
      { title: "Collection", key: "collection.title" },
      { title: "Action", key: "action", alignment: "center" },
    ],
    resourceName: {
      singular: "image",
      plural: "images",
    },
    emptyStateTitle: "No images found",
    emptyStateDescription: "Try changing the filters or search term",
    resourceDisplayName: "collections",
  },
  [ResourceType.ARTICLE]: {
    tableHeadings: [
      { title: "", key: "sl" },
      { title: "File name", key: "file_name" },
      { title: "Status", key: "optimization_status" },
      { title: "File size", key: "file_size" },
      { title: "Article", key: "article.title" },
      { title: "Action", key: "action", alignment: "center" },
    ],
    resourceName: {
      singular: "image",
      plural: "images",
    },
    emptyStateTitle: "No images found",
    emptyStateDescription: "Try changing the filters or search term",
    resourceDisplayName: "blog-posts",
  },
};

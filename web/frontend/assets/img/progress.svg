<svg id="e80vERm72hw1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 321.7 113.7" shape-rendering="geometricPrecision" text-rendering="geometricPrecision"><path id="e80vERm72hw2" d="M304.4,101.9L30.8,101.9C26.5,101.9,23,98.4,23,94.1L23,94.1C23,89.8,26.5,86.3,30.8,86.3L304.4,86.3C308.7,86.3,312.2,89.8,312.2,94.1L312.2,94.1C312.2,98.3,308.7,101.9,304.4,101.9Z" fill="rgb(226,255,207)" stroke="none" stroke-width="1"/><g id="e80vERm72hw3"><circle id="e80vERm72hw4" r="3.7" transform="matrix(1 0 0 1 61.5 94)" fill="rgb(204,242,179)" stroke="rgb(179,219,154)" stroke-width="1" stroke-linecap="round" stroke-miterlimit="10" stroke-dasharray="6,6"/><circle id="e80vERm72hw5" r="3.7" transform="matrix(1 0 0 1 40.100006 94.000002)" fill="rgb(204,242,179)" stroke="rgb(179,219,154)" stroke-width="1" stroke-linecap="round" stroke-miterlimit="10" stroke-dasharray="6,6"/><circle id="e80vERm72hw6" r="3.7" transform="matrix(1 0 0 1 84.5 94)" fill="rgb(204,242,179)" stroke="rgb(179,219,154)" stroke-width="1" stroke-linecap="round" stroke-miterlimit="10" stroke-dasharray="6,6"/><circle id="e80vERm72hw7" r="3.7" transform="matrix(1 0 0 1 108.5 94)" fill="rgb(204,242,179)" stroke="rgb(179,219,154)" stroke-width="1" stroke-linecap="round" stroke-miterlimit="10" stroke-dasharray="6,6"/><circle id="e80vERm72hw8" r="3.7" transform="matrix(1 0 0 1 132.9 94)" fill="rgb(204,242,179)" stroke="rgb(179,219,154)" stroke-width="1" stroke-linecap="round" stroke-miterlimit="10" stroke-dasharray="6,6"/><circle id="e80vERm72hw9" r="3.7" transform="matrix(1 0 0 1 156.9 94)" fill="rgb(204,242,179)" stroke="rgb(179,219,154)" stroke-width="1" stroke-linecap="round" stroke-miterlimit="10" stroke-dasharray="6,6"/><circle id="e80vERm72hw10" r="3.7" transform="matrix(1 0 0 1 179.9 94)" fill="rgb(204,242,179)" stroke="rgb(179,219,154)" stroke-width="1" stroke-linecap="round" stroke-miterlimit="10" stroke-dasharray="6,6"/><circle id="e80vERm72hw11" r="3.7" transform="matrix(1 0 0 1 203.9 94)" fill="rgb(204,242,179)" stroke="rgb(179,219,154)" stroke-width="1" stroke-linecap="round" stroke-miterlimit="10" stroke-dasharray="6,6"/><circle id="e80vERm72hw12" r="3.7" transform="matrix(1 0 0 1 227.2 94)" fill="rgb(204,242,179)" stroke="rgb(179,219,154)" stroke-width="1" stroke-linecap="round" stroke-miterlimit="10" stroke-dasharray="6,6"/><circle id="e80vERm72hw13" r="3.7" transform="matrix(1 0 0 1 251.2 94)" fill="rgb(204,242,179)" stroke="rgb(179,219,154)" stroke-width="1" stroke-linecap="round" stroke-miterlimit="10" stroke-dasharray="6,6"/><circle id="e80vERm72hw14" r="3.7" transform="matrix(1 0 0 1 274.2 94)" fill="rgb(204,242,179)" stroke="rgb(179,219,154)" stroke-width="1" stroke-linecap="round" stroke-miterlimit="10" stroke-dasharray="6,6"/><circle id="e80vERm72hw15" r="3.7" transform="matrix(1 0 0 1 298.2 94)" fill="rgb(204,242,179)" stroke="rgb(179,219,154)" stroke-width="1" stroke-linecap="round" stroke-miterlimit="10" stroke-dasharray="6,6"/></g><g id="e80vERm72hw16"><g id="e80vERm72hw17"><polygon id="e80vERm72hw18" points="108.4,52.3 76.5,44.2 76.3,44.2 76.3,62.1 108.4,52.3" fill="rgb(70,119,37)" stroke="none" stroke-width="1"/><polygon id="e80vERm72hw19" points="40.1,53.2 40.1,53.2 72.7,63.2 72.7,63.3 72.7,63.3 76.3,62.1 76.3,44.2" fill="rgb(77,140,41)" stroke="none" stroke-width="1"/></g><path id="e80vERm72hw20" d="M62.4,75.4L62.4,75.4C57.8,75.4,54.1,71.7,54.1,67.1L54.1,61.6C54.1,57,57.8,53.3,62.4,53.3L62.4,53.3C67,53.3,70.7,57,70.7,61.6L70.7,67C70.7,71.6,67,75.4,62.4,75.4Z" fill="rgb(62,102,37)" stroke="none" stroke-width="1"/><path id="e80vERm72hw21" d="M66.6,74.5L66.6,74.5C63.9,71.9,63.7,67.5,66.3,64.8L72.1,58.7C74.7,56,79.1,55.8,81.8,58.4L81.8,58.4C84.5,61,84.7,65.4,82.1,68.1L76.3,74.2C73.7,77,69.4,77.1,66.6,74.5Z" fill="rgb(62,102,37)" stroke="none" stroke-width="1"/><path id="e80vERm72hw22" d="M82.4,71.6L82.4,71.6C79.1,68.5,78.9,63.2,82.1,59.8L85.9,55.8C89,52.5,94.3,52.3,97.7,55.5L97.7,55.5C101,58.6,101.2,63.9,98,67.3L94.2,71.3C91,74.6,85.7,74.8,82.4,71.6Z" fill="rgb(62,102,37)" stroke="none" stroke-width="1"/><g id="e80vERm72hw23"><g id="e80vERm72hw24"><polygon id="e80vERm72hw25" points="40.1,53.2 40.1,86.6 72.7,86.6 72.7,63.3" fill="rgb(0,116,0)" stroke="none" stroke-width="1"/><polygon id="e80vERm72hw26" points="72.7,63.3 72.7,63.3 72.7,86.6 72.7,86.6 108.4,86.1 108.4,52.3" fill="rgb(0,148,4)" stroke="none" stroke-width="1"/></g><polygon id="e80vERm72hw27" points="72.7,63.2 40.1,53.2 72.7,63.3" fill="rgb(221,168,110)" stroke="none" stroke-width="1"/></g><g id="e80vERm72hw28"><path id="e80vERm72hw29" d="M45.5,67.9C42.7,65.2,41,62.6,40.1,60.3L40.1,86.6L72.7,86.6L72.7,86.6C56.4,86.1,54.8,76.7,45.5,67.9Z" fill="rgb(0,101,0)" stroke="none" stroke-width="1"/><path id="e80vERm72hw30" d="M80.3,81.4C77.9,82.9,74.5,85.8,72.6,86.5L72.6,86.5L72.6,86.5L108.4,86.1L108.4,52.3L108,52.4C105.3,61.5,98.9,69.6,80.3,81.4Z" fill="rgb(12,135,12)" stroke="none" stroke-width="1"/></g><g id="e80vERm72hw31"><path id="e80vERm72hw32" d="M72.7,63.3C72.7,63.3,65.4,61.2,56.4,58.4C47.4,55.6,40.1,53.3,40.1,53.3C40.1,53.3,47.4,55.4,56.4,58.2C65.4,60.9,72.7,63.3,72.7,63.3Z" clip-rule="evenodd" fill="rgb(239,176,120)" fill-rule="evenodd" stroke="none" stroke-width="1"/></g><polygon id="e80vERm72hw33" points="72.7,63.3 79.5,76.5 115.1,62.6 113.6,62.7 114.1,61.1 110.6,58.7 112.6,58.7 108.4,52.3" fill="rgb(85,153,75)" stroke="none" stroke-width="1"/><polygon id="e80vERm72hw34" points="72.6,63.2 69.5,69 66.9,69.9 67.8,72.1 66.1,72 64,70.9 65.6,76 33.4,63.5 40.1,53.2" fill="rgb(85,153,75)" stroke="none" stroke-width="1"/><g id="e80vERm72hw35"><path id="e80vERm72hw36" d="M112.4,58.4L108.5,52.4L72.7,63.2L75.4,68.5C80.4,68.2,85.9,66.6,90.5,62.9C99.8,55.6,108.9,57.3,112.4,58.4Z" fill="rgb(131,196,120)" stroke="none" stroke-width="1"/><path id="e80vERm72hw37" d="M48.6,55.8C52.8,58.2,57.7,61.3,59.2,63.7C60.5,65.8,64.6,67.7,69.8,68.3L72.6,63.1L48.6,55.8Z" fill="rgb(131,196,120)" stroke="none" stroke-width="1"/></g></g><g id="e80vERm72hw38" transform="matrix(1 0 0 1 -119.135645 0.000002)"><g id="e80vERm72hw39"><polygon id="e80vERm72hw40" points="108.4,52.3 76.5,44.2 76.3,44.2 76.3,62.1 108.4,52.3" fill="rgb(70,119,37)" stroke="none" stroke-width="1"/><polygon id="e80vERm72hw41" points="40.1,53.2 40.1,53.2 72.7,63.2 72.7,63.3 72.7,63.3 76.3,62.1 76.3,44.2" fill="rgb(77,140,41)" stroke="none" stroke-width="1"/></g><path id="e80vERm72hw42" d="M62.4,75.4L62.4,75.4C57.8,75.4,54.1,71.7,54.1,67.1L54.1,61.6C54.1,57,57.8,53.3,62.4,53.3L62.4,53.3C67,53.3,70.7,57,70.7,61.6L70.7,67C70.7,71.6,67,75.4,62.4,75.4Z" fill="rgb(62,102,37)" stroke="none" stroke-width="1"/><path id="e80vERm72hw43" d="M66.6,74.5L66.6,74.5C63.9,71.9,63.7,67.5,66.3,64.8L72.1,58.7C74.7,56,79.1,55.8,81.8,58.4L81.8,58.4C84.5,61,84.7,65.4,82.1,68.1L76.3,74.2C73.7,77,69.4,77.1,66.6,74.5Z" fill="rgb(62,102,37)" stroke="none" stroke-width="1"/><path id="e80vERm72hw44" d="M82.4,71.6L82.4,71.6C79.1,68.5,78.9,63.2,82.1,59.8L85.9,55.8C89,52.5,94.3,52.3,97.7,55.5L97.7,55.5C101,58.6,101.2,63.9,98,67.3L94.2,71.3C91,74.6,85.7,74.8,82.4,71.6Z" fill="rgb(62,102,37)" stroke="none" stroke-width="1"/><g id="e80vERm72hw45"><g id="e80vERm72hw46"><polygon id="e80vERm72hw47" points="40.1,53.2 40.1,86.6 72.7,86.6 72.7,63.3" fill="rgb(0,116,0)" stroke="none" stroke-width="1"/><polygon id="e80vERm72hw48" points="72.7,63.3 72.7,63.3 72.7,86.6 72.7,86.6 108.4,86.1 108.4,52.3" fill="rgb(0,148,4)" stroke="none" stroke-width="1"/></g><polygon id="e80vERm72hw49" points="72.7,63.2 40.1,53.2 72.7,63.3" fill="rgb(221,168,110)" stroke="none" stroke-width="1"/></g><g id="e80vERm72hw50"><path id="e80vERm72hw51" d="M45.5,67.9C42.7,65.2,41,62.6,40.1,60.3L40.1,86.6L72.7,86.6L72.7,86.6C56.4,86.1,54.8,76.7,45.5,67.9Z" fill="rgb(0,101,0)" stroke="none" stroke-width="1"/><path id="e80vERm72hw52" d="M80.3,81.4C77.9,82.9,74.5,85.8,72.6,86.5L72.6,86.5L72.6,86.5L108.4,86.1L108.4,52.3L108,52.4C105.3,61.5,98.9,69.6,80.3,81.4Z" fill="rgb(12,135,12)" stroke="none" stroke-width="1"/></g><g id="e80vERm72hw53"><path id="e80vERm72hw54" d="M72.7,63.3C72.7,63.3,65.4,61.2,56.4,58.4C47.4,55.6,40.1,53.3,40.1,53.3C40.1,53.3,47.4,55.4,56.4,58.2C65.4,60.9,72.7,63.3,72.7,63.3Z" clip-rule="evenodd" fill="rgb(239,176,120)" fill-rule="evenodd" stroke="none" stroke-width="1"/></g><polygon id="e80vERm72hw55" points="72.7,63.3 79.5,76.5 115.1,62.6 113.6,62.7 114.1,61.1 110.6,58.7 112.6,58.7 108.4,52.3" fill="rgb(85,153,75)" stroke="none" stroke-width="1"/><polygon id="e80vERm72hw56" points="72.6,63.2 69.5,69 66.9,69.9 67.8,72.1 66.1,72 64,70.9 65.6,76 33.4,63.5 40.1,53.2" fill="rgb(85,153,75)" stroke="none" stroke-width="1"/><g id="e80vERm72hw57"><path id="e80vERm72hw58" d="M112.4,58.4L108.5,52.4L72.7,63.2L75.4,68.5C80.4,68.2,85.9,66.6,90.5,62.9C99.8,55.6,108.9,57.3,112.4,58.4Z" fill="rgb(131,196,120)" stroke="none" stroke-width="1"/><path id="e80vERm72hw59" d="M48.6,55.8C52.8,58.2,57.7,61.3,59.2,63.7C60.5,65.8,64.6,67.7,69.8,68.3L72.6,63.1L48.6,55.8Z" fill="rgb(131,196,120)" stroke="none" stroke-width="1"/></g></g><g id="e80vERm72hw60" transform="matrix(1 0 0 1 -91.345721 0.000004)"><g id="e80vERm72hw61"><polygon id="e80vERm72hw62" points="295,42.1 262.3,37.3 229.5,41.1 261.2,45.3" fill="rgb(143,196,0)" stroke="none" stroke-width="1"/><g id="e80vERm72hw63"><path id="e80vERm72hw64" d="M262.3,37.3L246.8,39.1C248.5,41,252.5,43.2,262,41.6C272.2,39.8,277.4,42,279.7,43.6L295,42.1L262.3,37.3Z" fill="rgb(156,219,0)" stroke="none" stroke-width="1"/></g><g id="e80vERm72hw65"><polygon id="e80vERm72hw66" points="261.2,45.3 229.5,41.1 229.3,41.1 229.3,86.5 261.4,86.6 261.4,45.3" fill="rgb(0,116,0)" stroke="none" stroke-width="1"/><polygon id="e80vERm72hw67" points="295,42.1 261.4,45.3 261.4,85.8 261.4,86.6 295.1,86.5 295.1,42.1" fill="rgb(0,148,4)" stroke="none" stroke-width="1"/></g><polygon id="e80vERm72hw68" points="243.6,39.5 275.6,43.9 275.6,47.5 281.1,46.4 281.1,43.4 249.7,38.7" fill="rgb(222,217,216)" stroke="none" stroke-width="1"/><g id="e80vERm72hw69"><path id="e80vERm72hw70" d="M236.2,64.3C233.7,67.1,231.4,69,229.3,70.1L229.3,86.5L261.4,86.6L261.4,75.5C252,67.3,245.2,54.2,236.2,64.3Z" fill="rgb(0,101,0)" stroke="none" stroke-width="1"/><path id="e80vERm72hw71" d="M274.3,81.1C269.4,81.3,265.2,78.9,261.4,75.5L261.4,86.6L261.5,86.6L295.1,86.5L295.1,45C292.8,57.3,286.8,80.4,274.3,81.1Z" fill="rgb(0,124,4)" stroke="none" stroke-width="1"/></g><g id="e80vERm72hw72"><path id="e80vERm72hw73" d="M256.8,40.5C258.1,40.4,259.7,40.4,261.4,40.5L249.7,38.8L249.5,38.8C250.4,39.7,252.6,40.9,256.8,40.5Z" fill="rgb(237,231,230)" stroke="none" stroke-width="1"/><path id="e80vERm72hw74" d="M281.1,43.4L269.3,41.6C273.6,42.5,277.2,43.8,277.4,44.8C277.5,45.5,277.6,46.3,277.5,47L281.1,46.3L281.1,43.4Z" fill="rgb(237,231,230)" stroke="none" stroke-width="1"/></g><g id="e80vERm72hw75"><path id="e80vERm72hw76" d="M261.4,45.3C261.4,45.3,254.2,44.5,245.3,43.3C236.4,42.1,229.3,41.1,229.3,41.1C229.3,41.1,236.5,41.9,245.4,43.1C254.2,44.3,261.4,45.3,261.4,45.3Z" clip-rule="evenodd" fill="rgb(18,37,0)" fill-rule="evenodd" stroke="none" stroke-width="1"/></g></g><g id="e80vERm72hw77" opacity="0.3"><path id="e80vERm72hw78" d="M249.2,64.6C249.2,64.4,249.1,64.2,248.9,64.1C248.8,64,248.7,64,248.6,64C248.4,64,248.2,64.2,248,64.5L244.1,72.3C243.9,72.7,244,73.2,244.3,73.4C244.4,73.5,244.5,73.5,244.6,73.5C244.8,73.5,245,73.3,245.2,73L249.1,65.2C249.2,65,249.3,64.8,249.2,64.6Z" fill="rgb(217,229,209)" stroke="none" stroke-width="1"/><path id="e80vERm72hw79" d="M245.5,66.5C245.5,66.3,245.4,66.1,245.2,66C245.1,65.9,245,65.9,244.9,65.9C244.7,65.9,244.5,66.1,244.3,66.4L241.3,72.4C241.1,72.8,241.2,73.3,241.5,73.5C241.6,73.6,241.7,73.6,241.8,73.6C242,73.6,242.2,73.4,242.4,73.1L245.4,67.1C245.5,66.9,245.5,66.7,245.5,66.5Z" fill="rgb(217,229,209)" stroke="none" stroke-width="1"/><path id="e80vERm72hw80" d="M242.4,67.5C242.4,67.3,242.3,67.1,242.1,67C242,66.9,241.9,66.9,241.8,66.9C241.6,66.9,241.4,67.1,241.2,67.4L239.7,70.2C239.6,70.4,239.6,70.6,239.6,70.8C239.6,71,239.7,71.2,239.9,71.3C240,71.4,240.1,71.4,240.2,71.4C240.4,71.4,240.6,71.2,240.8,70.9L242.3,68.1C242.4,67.9,242.4,67.7,242.4,67.5Z" fill="rgb(217,229,209)" stroke="none" stroke-width="1"/><path id="e80vERm72hw81" d="M241.3,75.2C241.3,74.8,241,74.6,240.7,74.6C240.7,74.6,240.6,74.6,240.6,74.6C240.2,74.7,240,75.2,240,75.6C240,76,240.3,76.2,240.6,76.2C240.6,76.2,240.7,76.2,240.7,76.2C241.1,76.1,241.3,75.6,241.3,75.2Z" fill="rgb(217,229,209)" stroke="none" stroke-width="1"/><path id="e80vERm72hw82" d="M244.2,74.7C244.2,74.3,243.9,74.1,243.6,74.1C243.6,74.1,243.5,74.1,243.5,74.1C243.1,74.2,242.9,74.7,242.9,75.1C242.9,75.5,243.2,75.7,243.5,75.7C243.5,75.7,243.6,75.7,243.6,75.7C243.8,75.6,243.9,75.5,244,75.3C244.2,75.2,244.2,74.9,244.2,74.7Z" fill="rgb(217,229,209)" stroke="none" stroke-width="1"/><path id="e80vERm72hw83" d="M246.5,67.7C246.3,68.1,245.9,68.2,245.6,68.1C245.3,67.9,245.2,67.5,245.4,67.1L245.3,67.2L243,71.8C243,71.8,243,71.8,243,71.8C243,71.8,243,71.8,243,71.8C243,71.8,243,71.8,243,71.8C243.1,71.6,243.3,71.5,243.5,71.5C243.9,71.4,244.1,71.8,244.1,72.2C244.1,72.3,244.1,72.4,244,72.5L245.4,69.8L246.5,67.7Z" fill="rgb(217,229,209)" stroke="none" stroke-width="1"/></g><path id="e80vERm72hw84" d="M268.8,57.7C268.8,57.7,268.8,57.7,268.8,57.7C268.8,57.7,268.8,57.7,268.8,57.7Z" fill="rgb(217,229,209)" stroke="none" stroke-width="1"/><g id="e80vERm72hw85"><ellipse id="e80vERm72hw86" rx="8.8" ry="6.9" transform="matrix(0.5699 -0.8217 0.8217 0.5699 279.254293 60.938279)" fill="rgb(38,163,38)" stroke="none" stroke-width="1"/><polyline id="e80vERm72hw87" points="275.2,62.9 277.3,64.6 281.5,59.1" fill="none" stroke="rgb(174,239,132)" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10"/></g></g><g id="e80vERm72hw88"><g id="e80vERm72hw89"><polygon id="e80vERm72hw90" points="295,42.1 262.3,37.3 229.5,41.1 261.2,45.3" fill="rgb(143,196,0)" stroke="none" stroke-width="1"/><g id="e80vERm72hw91"><path id="e80vERm72hw92" d="M262.3,37.3L246.8,39.1C248.5,41,252.5,43.2,262,41.6C272.2,39.8,277.4,42,279.7,43.6L295,42.1L262.3,37.3Z" fill="rgb(156,219,0)" stroke="none" stroke-width="1"/></g><g id="e80vERm72hw93"><polygon id="e80vERm72hw94" points="261.2,45.3 229.5,41.1 229.3,41.1 229.3,86.5 261.4,86.6 261.4,45.3" fill="rgb(0,116,0)" stroke="none" stroke-width="1"/><polygon id="e80vERm72hw95" points="295,42.1 261.4,45.3 261.4,85.8 261.4,86.6 295.1,86.5 295.1,42.1" fill="rgb(0,148,4)" stroke="none" stroke-width="1"/></g><polygon id="e80vERm72hw96" points="243.6,39.5 275.6,43.9 275.6,47.5 281.1,46.4 281.1,43.4 249.7,38.7" fill="rgb(222,217,216)" stroke="none" stroke-width="1"/><g id="e80vERm72hw97"><path id="e80vERm72hw98" d="M236.2,64.3C233.7,67.1,231.4,69,229.3,70.1L229.3,86.5L261.4,86.6L261.4,75.5C252,67.3,245.2,54.2,236.2,64.3Z" fill="rgb(0,101,0)" stroke="none" stroke-width="1"/><path id="e80vERm72hw99" d="M274.3,81.1C269.4,81.3,265.2,78.9,261.4,75.5L261.4,86.6L261.5,86.6L295.1,86.5L295.1,45C292.8,57.3,286.8,80.4,274.3,81.1Z" fill="rgb(0,124,4)" stroke="none" stroke-width="1"/></g><g id="e80vERm72hw100"><path id="e80vERm72hw101" d="M256.8,40.5C258.1,40.4,259.7,40.4,261.4,40.5L249.7,38.8L249.5,38.8C250.4,39.7,252.6,40.9,256.8,40.5Z" fill="rgb(237,231,230)" stroke="none" stroke-width="1"/><path id="e80vERm72hw102" d="M281.1,43.4L269.3,41.6C273.6,42.5,277.2,43.8,277.4,44.8C277.5,45.5,277.6,46.3,277.5,47L281.1,46.3L281.1,43.4Z" fill="rgb(237,231,230)" stroke="none" stroke-width="1"/></g><g id="e80vERm72hw103"><path id="e80vERm72hw104" d="M261.4,45.3C261.4,45.3,254.2,44.5,245.3,43.3C236.4,42.1,229.3,41.1,229.3,41.1C229.3,41.1,236.5,41.9,245.4,43.1C254.2,44.3,261.4,45.3,261.4,45.3Z" clip-rule="evenodd" fill="rgb(18,37,0)" fill-rule="evenodd" stroke="none" stroke-width="1"/></g></g><g id="e80vERm72hw105" opacity="0.3"><path id="e80vERm72hw106" d="M249.2,64.6C249.2,64.4,249.1,64.2,248.9,64.1C248.8,64,248.7,64,248.6,64C248.4,64,248.2,64.2,248,64.5L244.1,72.3C243.9,72.7,244,73.2,244.3,73.4C244.4,73.5,244.5,73.5,244.6,73.5C244.8,73.5,245,73.3,245.2,73L249.1,65.2C249.2,65,249.3,64.8,249.2,64.6Z" fill="rgb(217,229,209)" stroke="none" stroke-width="1"/><path id="e80vERm72hw107" d="M245.5,66.5C245.5,66.3,245.4,66.1,245.2,66C245.1,65.9,245,65.9,244.9,65.9C244.7,65.9,244.5,66.1,244.3,66.4L241.3,72.4C241.1,72.8,241.2,73.3,241.5,73.5C241.6,73.6,241.7,73.6,241.8,73.6C242,73.6,242.2,73.4,242.4,73.1L245.4,67.1C245.5,66.9,245.5,66.7,245.5,66.5Z" fill="rgb(217,229,209)" stroke="none" stroke-width="1"/><path id="e80vERm72hw108" d="M242.4,67.5C242.4,67.3,242.3,67.1,242.1,67C242,66.9,241.9,66.9,241.8,66.9C241.6,66.9,241.4,67.1,241.2,67.4L239.7,70.2C239.6,70.4,239.6,70.6,239.6,70.8C239.6,71,239.7,71.2,239.9,71.3C240,71.4,240.1,71.4,240.2,71.4C240.4,71.4,240.6,71.2,240.8,70.9L242.3,68.1C242.4,67.9,242.4,67.7,242.4,67.5Z" fill="rgb(217,229,209)" stroke="none" stroke-width="1"/><path id="e80vERm72hw109" d="M241.3,75.2C241.3,74.8,241,74.6,240.7,74.6C240.7,74.6,240.6,74.6,240.6,74.6C240.2,74.7,240,75.2,240,75.6C240,76,240.3,76.2,240.6,76.2C240.6,76.2,240.7,76.2,240.7,76.2C241.1,76.1,241.3,75.6,241.3,75.2Z" fill="rgb(217,229,209)" stroke="none" stroke-width="1"/><path id="e80vERm72hw110" d="M244.2,74.7C244.2,74.3,243.9,74.1,243.6,74.1C243.6,74.1,243.5,74.1,243.5,74.1C243.1,74.2,242.9,74.7,242.9,75.1C242.9,75.5,243.2,75.7,243.5,75.7C243.5,75.7,243.6,75.7,243.6,75.7C243.8,75.6,243.9,75.5,244,75.3C244.2,75.2,244.2,74.9,244.2,74.7Z" fill="rgb(217,229,209)" stroke="none" stroke-width="1"/><path id="e80vERm72hw111" d="M246.5,67.7C246.3,68.1,245.9,68.2,245.6,68.1C245.3,67.9,245.2,67.5,245.4,67.1L245.3,67.2L243,71.8C243,71.8,243,71.8,243,71.8C243,71.8,243,71.8,243,71.8C243,71.8,243,71.8,243,71.8C243.1,71.6,243.3,71.5,243.5,71.5C243.9,71.4,244.1,71.8,244.1,72.2C244.1,72.3,244.1,72.4,244,72.5L245.4,69.8L246.5,67.7Z" fill="rgb(217,229,209)" stroke="none" stroke-width="1"/></g><path id="e80vERm72hw112" d="M268.8,57.7C268.8,57.7,268.8,57.7,268.8,57.7C268.8,57.7,268.8,57.7,268.8,57.7Z" fill="rgb(217,229,209)" stroke="none" stroke-width="1"/><g id="e80vERm72hw113"><ellipse id="e80vERm72hw114" rx="8.8" ry="6.9" transform="matrix(0.5699 -0.8217 0.8217 0.5699 279.254293 60.938279)" fill="rgb(38,163,38)" stroke="none" stroke-width="1"/><polyline id="e80vERm72hw115" points="275.2,62.9 277.3,64.6 281.5,59.1" fill="none" stroke="rgb(174,239,132)" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10"/></g></g><path id="e80vERm72hw116" d="M210.2,86.2L128.8,86.2L128.8,24.6C128.8,18.9,133.4,14.3,139.1,14.3L200,14.3C205.7,14.3,210.3,18.9,210.3,24.6L210.3,86.2Z" transform="matrix(1 0 0 1.036082 0 -1.81312)" fill="rgb(204,242,179)" stroke="none" stroke-width="1"/><g id="e80vERm72hw117" opacity="0.5"><path id="e80vERm72hw118" d="M160.9,73.1C160.2,73.1,159.6,72.9,159.2,72.8L159.2,71.8C159.7,72,160.2,72.1,160.6,72.1C161.3,72.1,161.7,71.8,161.7,71.1C161.7,70.4,161.4,70.2,160.6,69.7C159.7,69.2,159.1,68.8,159.1,67.6C159.1,66.3,159.9,65.6,161.4,65.6C161.9,65.6,162.4,65.7,162.9,65.8L162.8,66.7C162.5,66.6,162.1,66.5,161.7,66.5C161.2,66.5,160.6,66.6,160.6,67.4C160.6,67.9,160.8,68,161.7,68.6C162.7,69.2,163.2,69.6,163.2,70.9C163.2,72.4,162.4,73.1,160.9,73.1Z" fill="rgb(138,181,105)" stroke="none" stroke-width="1"/><path id="e80vERm72hw119" d="M166.1,73.1C165,73.1,164.6,72.6,164.6,71.5L164.6,68.5L163.9,68.5L163.9,67.6L164.6,67.6L164.7,66.3L165.9,66.3L165.9,67.6L167.1,67.6L167.1,68.5L165.9,68.5L165.9,71.5C165.9,72,166,72.3,166.7,72.3C166.8,72.3,166.9,72.3,167.1,72.2L167.1,73C166.8,73,166.5,73.1,166.1,73.1Z" fill="rgb(138,181,105)" stroke="none" stroke-width="1"/><path id="e80vERm72hw120" d="M170.2,73.1C168.3,73.1,168,72,168,70.9L168,69.5C168,68,168.7,67.3,170.2,67.3C171.7,67.3,172.4,68,172.4,69.5L172.4,70.9C172.4,72.4,171.7,73.1,170.2,73.1ZM170.2,68.2C169.5,68.2,169.4,68.8,169.4,69.2L169.4,71.2C169.4,71.6,169.5,72.2,170.2,72.2C170.9,72.2,171,71.6,171,71.2L171,69.2C171,68.6,170.7,68.2,170.2,68.2Z" fill="rgb(138,181,105)" stroke="none" stroke-width="1"/><path id="e80vERm72hw121" d="M173.3,73L173.3,67.6L174.6,67.6L174.6,68.6C174.9,67.8,175.4,67.5,176.1,67.5C176.1,67.5,176.1,67.5,176.2,67.5L176.2,68.8C176.2,68.8,176.2,68.8,176.1,68.8C175.6,68.8,175,69,174.7,69.3L174.7,69.3L174.7,73L173.3,73L173.3,73Z" fill="rgb(138,181,105)" stroke="none" stroke-width="1"/><path id="e80vERm72hw122" d="M179.3,73.1C177.8,73.1,176.9,72.3,176.9,70.9L176.9,69.5C176.9,68.1,177.6,67.4,179,67.4C180.4,67.4,181.1,68,181.1,69.4L181.1,70.5L178.3,70.5L178.3,71C178.3,71.8,178.8,72.2,179.7,72.2C180.1,72.2,180.5,72.1,180.9,71.9L180.9,72.8C180.5,73,179.9,73.1,179.3,73.1ZM179,68.2C178.3,68.2,178.2,68.7,178.2,69.2L178.2,69.9L179.7,69.9L179.7,69.2C179.8,68.7,179.7,68.2,179,68.2Z" fill="rgb(138,181,105)" stroke="none" stroke-width="1"/><path id="e80vERm72hw123" d="M183.7,73.1C183,73.1,182.4,72.9,182,72.8L182,71.8C182.5,72,183,72.1,183.4,72.1C184.1,72.1,184.5,71.8,184.5,71.1C184.5,70.4,184.2,70.2,183.4,69.7C182.5,69.2,181.9,68.8,181.9,67.6C181.9,66.3,182.7,65.6,184.2,65.6C184.7,65.6,185.2,65.7,185.7,65.8L185.6,66.7C185.3,66.6,184.9,66.5,184.5,66.5C184,66.5,183.4,66.6,183.4,67.4C183.4,67.9,183.6,68,184.5,68.6C185.5,69.2,186,69.6,186,70.9C186,72.4,185.3,73.1,183.7,73.1Z" fill="rgb(138,181,105)" stroke="none" stroke-width="1"/><path id="e80vERm72hw124" d="M187,73L187,65.8L190.9,65.8L190.9,66.7L188.5,66.7L188.5,68.8L190.5,68.8L190.5,69.7L188.5,69.7L188.5,72L191,72L191,73L187,73Z" fill="rgb(138,181,105)" stroke="none" stroke-width="1"/><path id="e80vERm72hw125" d="M194.4,73.1C192.7,73.1,191.8,72.1,191.8,70.3L191.8,68.3C191.8,66.5,192.8,65.5,194.4,65.5C196.1,65.5,197,66.4,197,68.3L197,70.3C197.1,72.1,196.1,73.1,194.4,73.1ZM194.4,66.6C193.6,66.6,193.3,67,193.3,68.1L193.3,70.7C193.3,71.7,193.6,72.1,194.4,72.1C195.4,72.1,195.5,71.4,195.5,70.7L195.5,68.1C195.5,67.1,195.2,66.6,194.4,66.6Z" fill="rgb(138,181,105)" stroke="none" stroke-width="1"/><path id="e80vERm72hw126" d="M156.5,61.7C156.4,61.4,156.2,61.1,155.9,60.9C155.7,60.8,155.5,60.7,155.3,60.7C154.9,60.7,154.5,60.9,154.2,61.3L147.5,72.5C147.1,73.1,147.3,73.9,147.9,74.2C148.1,74.3,148.3,74.4,148.5,74.4C148.9,74.4,149.3,74.2,149.6,73.8L156.3,62.6C156.5,62.3,156.6,62,156.5,61.7Z" fill="rgb(138,181,105)" stroke="none" stroke-width="1"/><path id="e80vERm72hw127" d="M149.5,63.8C149.4,63.5,149.2,63.2,148.9,63C148.7,62.9,148.5,62.8,148.3,62.8C147.9,62.8,147.5,63,147.2,63.4L142,71.9C141.6,72.5,141.8,73.3,142.4,73.6C142.6,73.7,142.8,73.8,143,73.8C143.4,73.8,143.8,73.6,144.1,73.2L149.3,64.7C149.5,64.4,149.6,64.1,149.5,63.8Z" fill="rgb(138,181,105)" stroke="none" stroke-width="1"/><path id="e80vERm72hw128" d="M143.7,64.5C143.6,64.2,143.4,63.9,143.1,63.7C142.9,63.6,142.7,63.5,142.5,63.5C142.1,63.5,141.7,63.7,141.4,64.1L138.9,68.1C138.7,68.4,138.7,68.7,138.8,69C138.9,69.3,139.1,69.6,139.3,69.8C139.5,69.9,139.7,70,139.9,70C140.3,70,140.7,69.8,141,69.4L143.5,65.4C143.7,65.2,143.7,64.9,143.7,64.5Z" fill="rgb(138,181,105)" stroke="none" stroke-width="1"/><path id="e80vERm72hw129" d="M142.3,76.2C142.2,75.6,141.7,75.1,141.1,75.1C141,75.1,141,75.1,140.9,75.1C140.2,75.2,139.8,75.8,139.9,76.5C140,77.1,140.5,77.6,141.1,77.6C141.2,77.6,141.2,77.6,141.3,77.6C141.9,77.6,142.4,76.9,142.3,76.2Z" fill="rgb(138,181,105)" stroke="none" stroke-width="1"/><path id="e80vERm72hw130" d="M147.8,76.2C147.7,75.6,147.2,75.1,146.6,75.1C146.5,75.1,146.5,75.1,146.4,75.1C145.7,75.2,145.3,75.8,145.4,76.5C145.5,77.1,146,77.6,146.6,77.6C146.7,77.6,146.7,77.6,146.8,77.6C147.1,77.5,147.4,77.4,147.6,77.1C147.8,76.9,147.8,76.6,147.8,76.2Z" fill="rgb(138,181,105)" stroke="none" stroke-width="1"/><path id="e80vERm72hw131" d="M151.5,65.9C151.1,66.4,150.4,66.6,149.9,66.3C149.3,66,149.1,65.2,149.4,64.6L149.3,64.8L145.3,71.3C145.3,71.3,145.3,71.3,145.3,71.3C145.3,71.3,145.3,71.3,145.3,71.3C145.3,71.3,145.3,71.3,145.3,71.3C145.5,71,145.9,70.9,146.2,70.9C146.9,70.9,147.5,71.5,147.5,72.2C147.5,72.4,147.5,72.5,147.4,72.7L149.8,68.8L151.5,65.9Z" fill="rgb(138,181,105)" stroke="none" stroke-width="1"/><path id="e80vERm72hw132" d="M145.3,71.3C145.3,71.3,145.4,71.3,145.3,71.3C145.4,71.3,145.3,71.3,145.3,71.3Z" fill="rgb(138,181,105)" stroke="none" stroke-width="1"/></g><g id="e80vERm72hw133"><path id="e80vERm72hw134" d="M159,41.8C159.1,42.2,159.3,42.7,159.5,43.1L158.9,43.9C158.7,44.2,158.7,44.5,158.9,44.8L160,45.9C160.2,46.1,160.6,46.2,160.9,45.9L161.7,45.3C162.1,45.5,162.5,45.7,163,45.9L163.1,46.9C163.1,47.2,163.4,47.5,163.8,47.5L165.4,47.5C165.7,47.5,166,47.3,166.1,46.9L166.2,45.9C166.7,45.8,167.2,45.6,167.6,45.3L168.4,45.9C168.7,46.1,169,46.1,169.3,45.9L170.4,44.8C170.6,44.6,170.7,44.2,170.4,43.9L169.8,43.1C170,42.7,170.2,42.2,170.4,41.7L171.3,41.6C171.6,41.6,171.9,41.3,171.9,40.9L171.9,39.3C171.9,39,171.7,38.7,171.3,38.6L170.4,38.5C170.3,38,170.1,37.6,169.8,37.1L170.4,36.4C170.6,36.1,170.6,35.8,170.4,35.5L169.3,34.4C169.1,34.2,168.7,34.1,168.4,34.4L167.7,34.9C167.3,34.6,166.8,34.4,166.3,34.3L166.2,33.4C166.2,33.1,165.9,32.8,165.5,32.8L163.9,32.8C163.6,32.8,163.3,33,163.2,33.4L163.1,34.3C162.6,34.4,162.1,34.6,161.6,34.9L160.9,34.3C160.6,34.1,160.3,34.1,160,34.3L158.9,35.4C158.7,35.6,158.6,36,158.9,36.3L159.5,37.1C159.3,37.5,159.1,38,159,38.5L158,38.6C157.7,38.6,157.4,38.9,157.4,39.3L157.4,41C157.4,41.3,157.6,41.6,158,41.7L159,41.8ZM164.6,37.6C166,37.6,167.2,38.8,167.2,40.2C167.2,41.6,166,42.8,164.6,42.8C163.2,42.8,162,41.6,162,40.2C162,38.8,163.2,37.6,164.6,37.6Z" fill="rgb(0,124,4)" stroke="none" stroke-width="1"/><path id="e80vERm72hw135" d="M179.9,42.1L179.1,41.4C178.8,41.2,178.5,41.2,178.2,41.4L177.7,41.8C177.3,41.6,176.9,41.5,176.5,41.4L176.4,40.8C176.3,40.5,176,40.3,175.7,40.3L174.6,40.4C174.3,40.4,174,40.7,174,41L174,41.6C173.6,41.7,173.2,42,172.8,42.2L172.3,41.8C172,41.6,171.7,41.7,171.4,41.9L170.7,42.7C170.5,43,170.5,43.3,170.7,43.6L171.2,44.1C171,44.5,170.9,44.9,170.9,45.3L170.2,45.4C169.9,45.5,169.7,45.8,169.7,46.1L169.8,47.2C169.8,47.5,170.1,47.8,170.4,47.8L171.1,47.8C171.2,48.1,171.4,48.5,171.6,48.8L171.2,49.4C171,49.7,171.1,50,171.3,50.3L172.1,51C172.4,51.2,172.7,51.2,173,51L173.5,50.5C173.9,50.7,174.2,50.8,174.6,50.9L174.7,51.6C174.8,51.9,175.1,52.1,175.4,52.1L176.5,52C176.8,52,177.1,51.7,177.1,51.4L177.1,50.7C177.5,50.6,177.9,50.4,178.2,50.1L178.8,50.5C179.1,50.7,179.4,50.6,179.7,50.4L180.4,49.6C180.6,49.3,180.6,49,180.4,48.7L179.9,48.2C180.1,47.8,180.2,47.4,180.3,47L180.9,46.9C181.2,46.8,181.4,46.5,181.4,46.2L181.3,45.1C181.3,44.8,181,44.5,180.7,44.5L180,44.5C179.9,44.1,179.7,43.8,179.5,43.4L179.9,42.9C180.2,42.7,180.2,42.4,179.9,42.1ZM175.9,48.3C174.7,48.4,173.7,47.5,173.6,46.4C173.5,45.2,174.4,44.2,175.5,44.1C176.7,44,177.7,44.9,177.8,46C177.9,47.2,177.1,48.2,175.9,48.3Z" fill="rgb(0,124,4)" stroke="none" stroke-width="1"/><path id="e80vERm72hw136" d="M162.7,51.2C162.4,51.2,162.1,51.5,162.1,51.8L162.1,52.5C162.1,52.8,162.3,53.1,162.7,53.2L163.2,53.3C163.3,53.6,163.4,53.9,163.5,54.1L163.2,54.5C163,54.8,163,55.1,163.2,55.4L163.7,55.9C163.9,56.1,164.3,56.2,164.6,56L165,55.7C165.3,55.9,165.6,56,165.9,56.1L166,56.6C166,56.9,166.3,57.2,166.6,57.2L167.3,57.2C167.6,57.2,167.9,57,168,56.6L168.1,56.1C168.4,56,168.7,55.9,169,55.7L169.4,56C169.7,56.2,170,56.2,170.3,56L170.8,55.5C171,55.3,171.1,54.9,170.9,54.6L170.6,54.2C170.8,53.9,170.9,53.6,171,53.3L171.4,53.3C171.7,53.3,172,53,172,52.7L172,52C172,51.7,171.8,51.4,171.4,51.3L171,51.2C170.9,50.9,170.8,50.6,170.6,50.3L170.9,50C171.1,49.7,171.1,49.4,170.9,49.1L170.4,48.6C170.2,48.4,169.8,48.3,169.5,48.5L169.2,48.7C168.9,48.5,168.6,48.4,168.3,48.3L168.3,47.9C168.3,47.6,168,47.3,167.7,47.3L167,47.3C166.7,47.3,166.4,47.5,166.3,47.9L166.2,48.3C165.9,48.4,165.5,48.5,165.2,48.7L164.9,48.4C164.6,48.2,164.3,48.2,164,48.4L163.5,48.9C163.3,49.1,163.2,49.5,163.4,49.8L163.7,50.2C163.5,50.5,163.4,50.8,163.3,51.1L162.7,51.2ZM167,50.5C167.9,50.5,168.7,51.3,168.7,52.2C168.7,53.1,167.9,53.9,167,53.9C166.1,53.9,165.3,53.1,165.3,52.2C165.2,51.2,166,50.4,167,50.5Z" fill="rgb(0,124,4)" stroke="none" stroke-width="1"/></g><polygon id="e80vERm72hw137" points="0,-2.986345 0.877665,-1.208002 2.840183,-0.922831 1.420091,0.461416 1.755329,2.416004 0,1.493172 -1.755329,2.416004 -1.420091,0.461416 -2.840183,-0.922831 -0.877665,-1.208002 0,-2.986345" transform="matrix(0 0 0 0 247.200758 31.427171)" fill="rgb(236,208,9)" stroke="none" stroke-width="0"/><polygon id="e80vERm72hw138" points="0,-2.986345 0.877665,-1.208002 2.840183,-0.922831 1.420091,0.461416 1.755329,2.416004 0,1.493172 -1.755329,2.416004 -1.420091,0.461416 -2.840183,-0.922831 -0.877665,-1.208002 0,-2.986345" transform="matrix(0 0 0 0 255.45 51.295687)" fill="rgb(236,208,9)" stroke="none" stroke-width="0"/><polygon id="e80vERm72hw139" points="0,-2.986345 0.877665,-1.208002 2.840183,-0.922831 1.420091,0.461416 1.755329,2.416004 0,1.493172 -1.755329,2.416004 -1.420091,0.461416 -2.840183,-0.922831 -0.877665,-1.208002 0,-2.986345" transform="matrix(0 0 0 0 232.340183 47.117328)" fill="rgb(236,208,9)" stroke="none" stroke-width="0"/><polygon id="e80vERm72hw140" points="0,-2.986345 0.877665,-1.208002 2.840183,-0.922831 1.420091,0.461416 1.755329,2.416004 0,1.493172 -1.755329,2.416004 -1.420091,0.461416 -2.840183,-0.922831 -0.877665,-1.208002 0,-2.986345" transform="matrix(0 0 0 0 287.764203 49.182816)" fill="rgb(236,208,9)" stroke="none" stroke-width="0"/><script><![CDATA[!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):(t="undefined"!=typeof globalThis?globalThis:t||self).__SVGATOR_PLAYER__=n()}(this,(function(){"use strict";function t(n){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(n)}function n(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}function e(t,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function r(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}function i(t){return(i=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function u(t,n){return(u=Object.setPrototypeOf||function(t,n){return t.__proto__=n,t})(t,n)}function o(t,n){return!n||"object"!=typeof n&&"function"!=typeof n?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):n}function a(t){var n=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var e,r=i(t);if(n){var u=i(this).constructor;e=Reflect.construct(r,arguments,u)}else e=r.apply(this,arguments);return o(this,e)}}function l(t,n,e){return(l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,n,e){var r=function(t,n){for(;!Object.prototype.hasOwnProperty.call(t,n)&&null!==(t=i(t)););return t}(t,n);if(r){var u=Object.getOwnPropertyDescriptor(r,n);return u.get?u.get.call(e):u.value}})(t,n,e||t)}var s=f(Math.pow(10,-6));function f(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6;if(Number.isInteger(t))return t;var e=Math.pow(10,n);return Math.round((+t+Number.EPSILON)*e)/e}function c(t,n){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s;return Math.abs(t-n)<e}var h=Math.PI/180;function v(t){return t}function d(t,n,e){var r=1-e;return 3*e*r*(t*r+n*e)+e*e*e}function y(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;return t<0||t>1||e<0||e>1?null:c(t,n)&&c(e,r)?v:function(i){if(i<=0)return t>0?i*n/t:0===n&&e>0?i*r/e:0;if(i>=1)return e<1?1+(i-1)*(r-1)/(e-1):1===e&&t<1?1+(i-1)*(n-1)/(t-1):1;for(var u,o=0,a=1;o<a;){var l=d(t,e,u=(o+a)/2);if(c(i,l))break;l<i?o=u:a=u}return d(n,r,u)}}function g(){return 1}function p(t){return 1===t?1:0}function m(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(1===t){if(0===n)return p;if(1===n)return g}var e=1/t;return function(t){return t>=1?1:(t+=n*e)-t%e}}function b(t,n,e){return t>=.5?e:n}function w(t,n,e){return 0===t||n===e?n:t*(e-n)+n}function x(t,n,e){var r=w(t,n,e);return r<=0?0:r}function A(t,n,e){return 0===t?n:1===t?e:{x:w(t,n.x,e.x),y:w(t,n.y,e.y)}}function k(t,n,e){return 0===t?n:1===t?e:{x:x(t,n.x,e.x),y:x(t,n.y,e.y)}}function _(t,n,e){var r=function(t,n,e){return Math.round(w(t,n,e))}(t,n,e);return r<=0?0:r>=255?255:r}function S(t,n,e){return 0===t?n:1===t?e:{r:_(t,n.r,e.r),g:_(t,n.g,e.g),b:_(t,n.b,e.b),a:w(t,null==n.a?1:n.a,null==e.a?1:e.a)}}function M(t,n,e){if(0===t)return n;if(1===t)return e;var r=n.length;if(r!==e.length)return b(t,n,e);for(var i=[],u=0;u<r;u++)i.push(S(t,n[u],e[u]));return i}function E(t,n,e){var r=n.length;if(r!==e.length)return b(t,n,e);for(var i=new Array(r),u=0;u<r;u++)i[u]=w(t,n[u],e[u]);return i}function O(t,n){for(var e=[],r=0;r<t;r++)e.push(n);return e}function B(t,n){if(--n<=0)return t;var e=(t=Object.assign([],t)).length;do{for(var r=0;r<e;r++)t.push(t[r])}while(--n>0);return t}var I,P=function(){function t(e){n(this,t),this.list=e,this.length=e.length}return r(t,[{key:"setAttribute",value:function(t,n){for(var e=this.list,r=0;r<this.length;r++)e[r].setAttribute(t,n)}},{key:"removeAttribute",value:function(t){for(var n=this.list,e=0;e<this.length;e++)n[e].removeAttribute(t)}},{key:"style",value:function(t,n){for(var e=this.list,r=0;r<this.length;r++)e[r].style[t]=n}}]),t}(),N=/-./g,j=function(t,n){return n.toUpperCase()};function T(t){return"function"==typeof t?t:b}function R(t){return t?"function"==typeof t?t:Array.isArray(t)?function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:v;if(!Array.isArray(t))return n;switch(t.length){case 1:return m(t[0])||n;case 2:return m(t[0],t[1])||n;case 4:return y(t[0],t[1],t[2],t[3])||n}return n}(t,null):function(t,n){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:v;switch(t){case"linear":return v;case"steps":return m(n.steps||1,n.jump||0)||e;case"bezier":case"cubic-bezier":return y(n.x1||0,n.y1||0,n.x2||0,n.y2||0)||e}return e}(t.type,t.value,null):null}function F(t,n,e){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=n.length-1;if(t<=n[0].t)return r?[0,0,n[0].v]:n[0].v;if(t>=n[i].t)return r?[i,1,n[i].v]:n[i].v;var u,o=n[0],a=null;for(u=1;u<=i;u++){if(!(t>n[u].t)){a=n[u];break}o=n[u]}return null==a?r?[i,1,n[i].v]:n[i].v:o.t===a.t?r?[u,1,a.v]:a.v:(t=(t-o.t)/(a.t-o.t),o.e&&(t=o.e(t)),r?[u,t,e(t,o.v,a.v)]:e(t,o.v,a.v))}function C(t,n){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return t&&t.length?"function"!=typeof n?null:("function"!=typeof e&&(e=null),function(r){var i=F(r,t,n);return null!=i&&e&&(i=e(i)),i}):null}function q(t,n){return t.t-n.t}function L(n,e,r,i,u){var o,a="@"===r[0],l="#"===r[0],s=I[r],f=b;switch(a?(o=r.substr(1),r=o.replace(N,j)):l&&(r=r.substr(1)),t(s)){case"function":if(f=s(i,u,F,R,r,a,e,n),l)return f;break;case"string":f=C(i,T(s));break;case"object":if((f=C(i,T(s.i),s.f))&&"function"==typeof s.u)return s.u(e,f,r,a,n)}return f?function(t,n,e){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(r)return t instanceof P?function(r){return t.style(n,e(r))}:function(r){return t.style[n]=e(r)};if(Array.isArray(n)){var i=n.length;return function(r){var u=e(r);if(null==u)for(var o=0;o<i;o++)t[o].removeAttribute(n);else for(var a=0;a<i;a++)t[a].setAttribute(n,u)}}return function(r){var i=e(r);null==i?t.removeAttribute(n):t.setAttribute(n,i)}}(e,r,f,a):null}function V(n,e,r,i){if(!i||"object"!==t(i))return null;var u=null,o=null;return Array.isArray(i)?o=function(t){if(!t||!t.length)return null;for(var n=0;n<t.length;n++)t[n].e&&(t[n].e=R(t[n].e));return t.sort(q)}(i):(o=i.keys,u=i.data||null),o?L(n,e,r,o,u):null}function z(t,n,e){if(!e)return null;var r=[];for(var i in e)if(e.hasOwnProperty(i)){var u=V(t,n,i,e[i]);u&&r.push(u)}return r.length?r:null}function D(t,n){if(!n.duration||n.duration<0)return null;var e=function(t,n){if(!n)return null;var e=[];if(Array.isArray(n))for(var r=n.length,i=0;i<r;i++){var u=n[i];if(2===u.length){var o=null;if("string"==typeof u[0])o=t.getElementById(u[0]);else if(Array.isArray(u[0])){o=[];for(var a=0;a<u[0].length;a++)if("string"==typeof u[0][a]){var l=t.getElementById(u[0][a]);l&&o.push(l)}o=o.length?1===o.length?o[0]:new P(o):null}if(o){var s=z(t,o,u[1]);s&&(e=e.concat(s))}}}else for(var f in n)if(n.hasOwnProperty(f)){var c=t.getElementById(f);if(c){var h=z(t,c,n[f]);h&&(e=e.concat(h))}}return e.length?e:null}(t,n.elements);return e?function(t,n){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1/0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,i=arguments.length>4&&void 0!==arguments[4]&&arguments[4],u=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,o=t.length,a=r>0?n:0;i&&e%2==0&&(a=n-a);var l=null;return function(s,f){var c=s%n,h=1+(s-c)/n;f*=r,i&&h%2==0&&(f=-f);var v=!1;if(h>e)c=a,v=!0,-1===u&&(c=r>0?0:n);else if(f<0&&(c=n-c),c===l)return!1;l=c;for(var d=0;d<o;d++)t[d](c);return v}}(e,n.duration,n.iterations||1/0,n.direction||1,!!n.alternate,n.fill||1):null}Number.isInteger||(Number.isInteger=function(t){return"number"==typeof t&&isFinite(t)&&Math.floor(t)===t}),Number.EPSILON||(Number.EPSILON=2220446049250313e-31);var U=function(){function t(e,r){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};n(this,t),this._id=0,this._running=!1,this._rollingBack=!1,this._animations=e,this.duration=r.duration,this.alternate=r.alternate,this.fill=r.fill,this.iterations=r.iterations,this.direction=i.direction||1,this.speed=i.speed||1,this.fps=i.fps||100,this.offset=i.offset||0,this.rollbackStartOffset=0}return r(t,[{key:"_rollback",value:function(){var t=this,n=1/0,e=null;this.rollbackStartOffset=this.offset,this._rollingBack||(this._rollingBack=!0,this._running=!0);this._id=window.requestAnimationFrame((function r(i){if(t._rollingBack){null==e&&(e=i);var u=i-e,o=t.rollbackStartOffset-u,a=Math.round(o*t.speed);if(a>t.duration&&n!=1/0){var l=!!t.alternate&&a/t.duration%2>1,s=a%t.duration;a=(s+=l?t.duration:0)||t.duration}var f=t.fps?1e3/t.fps:0,c=Math.max(0,a);if(c<n-f){t.offset=c,n=c;for(var h=t._animations,v=h.length,d=0;d<v;d++)h[d](c,t.direction)}var y=!1;if(t.iterations>0&&-1===t.fill){var g=t.iterations*t.duration,p=g==a;a=p?0:a,t.offset=p?0:t.offset,y=a>g}a>0&&t.offset>=a&&!y?t._id=window.requestAnimationFrame(r):t.stop()}}))}},{key:"_start",value:function(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=-1/0,r=null,i={},u=function u(o){t._running=!0,null==r&&(r=o);var a=Math.round((o-r+n)*t.speed),l=t.fps?1e3/t.fps:0;if(a>e+l&&!t._rollingBack){t.offset=a,e=a;for(var s=t._animations,f=s.length,c=0,h=0;h<f;h++)i[h]?c++:(i[h]=s[h](a,t.direction),i[h]&&c++);if(c===f)return void t._stop()}t._id=window.requestAnimationFrame(u)};this._id=window.requestAnimationFrame(u)}},{key:"_stop",value:function(){this._id&&window.cancelAnimationFrame(this._id),this._running=!1,this._rollingBack=!1}},{key:"play",value:function(){!this._rollingBack&&this._running||(this._rollingBack=!1,this.rollbackStartOffset>this.duration&&(this.offset=this.rollbackStartOffset-(this.rollbackStartOffset-this.offset)%this.duration,this.rollbackStartOffset=0),this._start(this.offset))}},{key:"stop",value:function(){this._stop(),this.offset=0,this.rollbackStartOffset=0;var t=this.direction,n=this._animations;window.requestAnimationFrame((function(){for(var e=0;e<n.length;e++)n[e](0,t)}))}},{key:"reachedToEnd",value:function(){return this.iterations>0&&this.offset>=this.iterations*this.duration}},{key:"restart",value:function(){this._stop(),this.offset=0,this._start()}},{key:"pause",value:function(){this._stop()}},{key:"reverse",value:function(){this.direction=-this.direction}}],[{key:"build",value:function(n,e){return(n=function(t,n){if(I=n,!t||!t.root||!Array.isArray(t.animations))return null;for(var e=document.getElementsByTagName("svg"),r=!1,i=0;i<e.length;i++)if(e[i].id===t.root&&!e[i].svgatorAnimation){(r=e[i]).svgatorAnimation=!0;break}if(!r)return null;var u=t.animations.map((function(t){return D(r,t)})).filter((function(t){return!!t}));return u.length?{element:r,animations:u,animationSettings:t.animationSettings,options:t.options||void 0}:null}(n,e))?{el:n.element,options:n.options||{},player:new t(n.animations,n.animationSettings,n.options)}:null}}]),t}();!function(){for(var t=0,n=["ms","moz","webkit","o"],e=0;e<n.length&&!window.requestAnimationFrame;++e)window.requestAnimationFrame=window[n[e]+"RequestAnimationFrame"],window.cancelAnimationFrame=window[n[e]+"CancelAnimationFrame"]||window[n[e]+"CancelRequestAnimationFrame"];window.requestAnimationFrame||(window.requestAnimationFrame=function(n){var e=Date.now(),r=Math.max(0,16-(e-t)),i=window.setTimeout((function(){n(e+r)}),r);return t=e+r,i},window.cancelAnimationFrame=window.clearTimeout)}();var W=function(t,n){var e=!1,r=null;return function(i){e&&clearTimeout(e),e=setTimeout((function(){return function(){for(var i=0,u=window.innerHeight,o=0,a=window.innerWidth,l=t.parentNode;l instanceof Element;){var s=window.getComputedStyle(l);if("visible"!==s.overflowY||"visible"!==s.overflowX){var f=l.getBoundingClientRect();"visible"!==s.overflowY&&(i=Math.max(i,f.top),u=Math.min(u,f.bottom)),"visible"!==s.overflowX&&(o=Math.max(o,f.left),a=Math.min(a,f.right))}if(l===l.parentNode)break;l=l.parentNode}e=!1;var c=t.getBoundingClientRect(),h=Math.min(c.height,Math.max(0,i-c.top)),v=Math.min(c.height,Math.max(0,c.bottom-u)),d=Math.min(c.width,Math.max(0,o-c.left)),y=Math.min(c.width,Math.max(0,c.right-a)),g=(c.height-h-v)/c.height,p=(c.width-d-y)/c.width,m=Math.round(g*p*100);null!==r&&r===m||(r=m,n(m))}()}),100)}},Y=function(){function t(e,r,i){n(this,t),r=Math.max(1,r||1),r=Math.min(r,100),this.el=e,this.onTresholdChange=i&&i.call?i:function(){},this.tresholdPercent=r||1,this.currentVisibility=null,this.visibilityCalculator=W(e,this.onVisibilityUpdate.bind(this)),this.bindScrollWatchers(),this.visibilityCalculator()}return r(t,[{key:"bindScrollWatchers",value:function(){for(var t=this.el.parentNode;t&&(t.addEventListener("scroll",this.visibilityCalculator),t!==t.parentNode&&t!==document);)t=t.parentNode}},{key:"onVisibilityUpdate",value:function(t){var n=this.currentVisibility>=this.tresholdPercent,e=t>=this.tresholdPercent;if(null===this.currentVisibility||n!==e)return this.currentVisibility=t,void this.onTresholdChange(e);this.currentVisibility=t}}]),t}();function G(t){return f(t)+""}function H(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:" ";return t&&t.length?t.map(G).join(n):""}function Q(t){return G(t.x)+","+G(t.y)}function X(t){return t?null==t.a||t.a>=1?"rgb("+t.r+","+t.g+","+t.b+")":"rgba("+t.r+","+t.g+","+t.b+","+t.a+")":"transparent"}var Z={f:null,i:k,u:function(t,n){return function(e){var r=n(e);t.setAttribute("rx",G(r.x)),t.setAttribute("ry",G(r.y))}}},J={f:null,i:function(t,n,e){return 0===t?n:1===t?e:{width:x(t,n.width,e.width),height:x(t,n.height,e.height)}},u:function(t,n){return function(e){var r=n(e);t.setAttribute("width",G(r.width)),t.setAttribute("height",G(r.height))}}},K=Math.sin,$=Math.cos,tt=Math.acos,nt=Math.asin,et=Math.tan,rt=Math.atan2,it=Math.PI/180,ut=180/Math.PI,ot=Math.sqrt,at=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,u=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0;n(this,t),this.m=[e,r,i,u,o,a],this.i=null,this.w=null,this.s=null}return r(t,[{key:"determinant",get:function(){var t=this.m;return t[0]*t[3]-t[1]*t[2]}},{key:"isIdentity",get:function(){if(null===this.i){var t=this.m;this.i=1===t[0]&&0===t[1]&&0===t[2]&&1===t[3]&&0===t[4]&&0===t[5]}return this.i}},{key:"point",value:function(t,n){var e=this.m;return{x:e[0]*t+e[2]*n+e[4],y:e[1]*t+e[3]*n+e[5]}}},{key:"translateSelf",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(!t&&!n)return this;var e=this.m;return e[4]+=e[0]*t+e[2]*n,e[5]+=e[1]*t+e[3]*n,this.w=this.s=this.i=null,this}},{key:"rotateSelf",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(t%=360){var n=K(t*=it),e=$(t),r=this.m,i=r[0],u=r[1];r[0]=i*e+r[2]*n,r[1]=u*e+r[3]*n,r[2]=r[2]*e-i*n,r[3]=r[3]*e-u*n,this.w=this.s=this.i=null}return this}},{key:"scaleSelf",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;if(1!==t||1!==n){var e=this.m;e[0]*=t,e[1]*=t,e[2]*=n,e[3]*=n,this.w=this.s=this.i=null}return this}},{key:"skewSelf",value:function(t,n){if(n%=360,(t%=360)||n){var e=this.m,r=e[0],i=e[1],u=e[2],o=e[3];t&&(t=et(t*it),e[2]+=r*t,e[3]+=i*t),n&&(n=et(n*it),e[0]+=u*n,e[1]+=o*n),this.w=this.s=this.i=null}return this}},{key:"resetSelf",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,u=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0,o=this.m;return o[0]=t,o[1]=n,o[2]=e,o[3]=r,o[4]=i,o[5]=u,this.w=this.s=this.i=null,this}},{key:"recomposeSelf",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;return this.isIdentity||this.resetSelf(),t&&(t.x||t.y)&&this.translateSelf(t.x,t.y),n&&this.rotateSelf(n),e&&(e.x&&this.skewSelf(e.x,0),e.y&&this.skewSelf(0,e.y)),!r||1===r.x&&1===r.y||this.scaleSelf(r.x,r.y),i&&(i.x||i.y)&&this.translateSelf(i.x,i.y),this}},{key:"decompose",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,e=this.m,r=e[0]*e[0]+e[1]*e[1],i=[[e[0],e[1]],[e[2],e[3]]],u=ot(r);if(0===u)return{origin:{x:f(e[4]),y:f(e[5])},translate:{x:f(t),y:f(n)},scale:{x:0,y:0},skew:{x:0,y:0},rotate:0};i[0][0]/=u,i[0][1]/=u;var o=e[0]*e[3]-e[1]*e[2]<0;o&&(u=-u);var a=i[0][0]*i[1][0]+i[0][1]*i[1][1];i[1][0]-=i[0][0]*a,i[1][1]-=i[0][1]*a;var l=ot(i[1][0]*i[1][0]+i[1][1]*i[1][1]);if(0===l)return{origin:{x:f(e[4]),y:f(e[5])},translate:{x:f(t),y:f(n)},scale:{x:f(u),y:0},skew:{x:0,y:0},rotate:0};i[1][0]/=l,i[1][1]/=l,a/=l;var s=0;return i[1][1]<0?(s=tt(i[1][1])*ut,i[0][1]<0&&(s=360-s)):s=nt(i[0][1])*ut,o&&(s=-s),a=rt(a,ot(i[0][0]*i[0][0]+i[0][1]*i[0][1]))*ut,o&&(a=-a),{origin:{x:f(e[4]),y:f(e[5])},translate:{x:f(t),y:f(n)},scale:{x:f(u),y:f(l)},skew:{x:f(a),y:0},rotate:f(s)}}},{key:"toString",value:function(){return null===this.s&&(this.s="matrix("+this.m.map((function(t){return f(t)})).join(" ")+")"),this.s}}]),t}();Object.freeze({M:2,L:2,Z:0,H:1,V:1,C:6,Q:4,T:2,S:4,A:7});var lt={},st=null;function ft(n){var e=function(){if(st)return st;if("object"!==("undefined"==typeof document?"undefined":t(document))||!document.createElementNS)return{};var n=document.createElementNS("http://www.w3.org/2000/svg","svg");return n&&n.style?(n.style.position="absolute",n.style.opacity="0.01",n.style.zIndex="-9999",n.style.left="-9999px",n.style.width="1px",n.style.height="1px",st={svg:n}):{}}().svg;if(!e)return function(t){return null};var r=document.createElementNS(e.namespaceURI,"path");r.setAttributeNS(null,"d",n),r.setAttributeNS(null,"fill","none"),r.setAttributeNS(null,"stroke","none"),e.appendChild(r);var i=r.getTotalLength();return function(t){var n=r.getPointAtLength(i*t);return{x:n.x,y:n.y}}}function ct(t){return lt[t]?lt[t]:lt[t]=ft(t)}function ht(t,n,e,r){if(!t||!r)return!1;var i=["M",t.x,t.y];if(n&&e&&(i.push("C"),i.push(n.x),i.push(n.y),i.push(e.x),i.push(e.y)),n?!e:e){var u=n||e;i.push("Q"),i.push(u.x),i.push(u.y)}return n||e||i.push("L"),i.push(r.x),i.push(r.y),i.join(" ")}function vt(t,n,e,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,u=ht(t,n,e,r),o=ct(u);try{return o(i)}catch(t){return null}}function dt(t,n,e,r){var i=1-r;return i*i*t+2*i*r*n+r*r*e}function yt(t,n,e,r){return 2*(1-r)*(n-t)+2*r*(e-n)}function gt(t,n,e,r){var i=arguments.length>4&&void 0!==arguments[4]&&arguments[4],u=vt(t,n,null,e,r);return u||(u={x:dt(t.x,n.x,e.x,r),y:dt(t.y,n.y,e.y,r)}),i&&(u.a=pt(t,n,e,r)),u}function pt(t,n,e,r){return Math.atan2(yt(t.y,n.y,e.y,r),yt(t.x,n.x,e.x,r))}function mt(t,n,e,r,i){var u=i*i;return i*u*(r-t+3*(n-e))+3*u*(t+e-2*n)+3*i*(n-t)+t}function bt(t,n,e,r,i){var u=1-i;return 3*(u*u*(n-t)+2*u*i*(e-n)+i*i*(r-e))}function wt(t,n,e,r,i){var u=arguments.length>5&&void 0!==arguments[5]&&arguments[5],o=vt(t,n,e,r,i);return o||(o={x:mt(t.x,n.x,e.x,r.x,i),y:mt(t.y,n.y,e.y,r.y,i)}),u&&(o.a=xt(t,n,e,r,i)),o}function xt(t,n,e,r,i){return Math.atan2(bt(t.y,n.y,e.y,r.y,i),bt(t.x,n.x,e.x,r.x,i))}function At(t,n,e){return t+(n-t)*e}function kt(t,n,e){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i={x:At(t.x,n.x,e),y:At(t.y,n.y,e)};return r&&(i.a=_t(t,n)),i}function _t(t,n){return Math.atan2(n.y-t.y,n.x-t.x)}function St(t,n,e){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(Et(n)){if(Ot(e))return gt(n,e.start,e,t,r)}else if(Et(e)){if(n.end)return gt(n,n.end,e,t,r)}else{if(n.end)return e.start?wt(n,n.end,e.start,e,t,r):gt(n,n.end,e,t,r);if(e.start)return gt(n,e.start,e,t,r)}return kt(n,e,t,r)}function Mt(t,n,e){var r=St(t,n,e,!0);return r.a=function(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return n?t+Math.PI:t}(r.a)/h,r}function Et(t){return!t.type||"corner"===t.type}function Ot(t){return null!=t.start&&!Et(t)}var Bt=new at;var It={f:function(t){return t?t.join(" "):""},i:function(n,e,r){if(0===n)return e;if(1===n)return r;var i=e.length;if(i!==r.length)return b(n,e,r);for(var u,o=new Array(i),a=0;a<i;a++){if((u=t(e[a]))!==t(r[a]))return b(n,e,r);if("number"===u)o[a]=w(n,e[a],r[a]);else{if(e[a]!==r[a])return b(n,e,r);o[a]=e[a]}}return o}},Pt={f:null,i:E,u:function(t,n){return function(e){var r=n(e);t.setAttribute("x1",G(r[0])),t.setAttribute("y1",G(r[1])),t.setAttribute("x2",G(r[2])),t.setAttribute("y2",G(r[3]))}}},Nt={f:G,i:w},jt={f:G,i:function(t,n,e){var r=w(t,n,e);return r<=0?0:r>=1?1:r}},Tt={f:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:" ";return t&&t.length>0&&(t=t.map((function(t){return f(t,4)}))),H(t,n)},i:function(t,n,e){var r,i,u,o=n.length,a=e.length;if(o!==a)if(0===o)n=O(o=a,0);else if(0===a)a=o,e=O(o,0);else{var l=(u=(r=o)*(i=a)/function(t,n){for(var e;n;)e=n,n=t%n,t=e;return t||1}(r,i))<0?-u:u;n=B(n,Math.floor(l/o)),e=B(e,Math.floor(l/a)),o=a=l}for(var s=[],c=0;c<o;c++)s.push(f(x(t,n[c],e[c])));return s}};function Rt(t,n,e,r,i,u,o,a){return n=function(t,n,e){for(var r,i,u,o=t.length-1,a={},l=0;l<=o;l++)(r=t[l]).e&&(r.e=n(r.e)),r.v&&"g"===(i=r.v).t&&i.r&&(u=e.getElementById(i.r))&&(a[i.r]=u.querySelectorAll("stop"));return a}(t,r,a),function(r){var i,u=e(r,t,Ft);return u?"c"===u.t?X(u.v):"g"===u.t?(n[u.r]&&function(t,n){for(var e=0,r=t.length;e<r;e++)t[e].setAttribute("stop-color",X(n[e]))}(n[u.r],u.v),(i=u.r)?"url(#"+i+")":"none"):"none":"none"}}function Ft(t,n,e){if(0===t)return n;if(1===t)return e;if(n&&e){var r=n.t;if(r===e.t)switch(n.t){case"c":return{t:r,v:S(t,n.v,e.v)};case"g":if(n.r===e.r)return{t:r,v:M(t,n.v,e.v),r:n.r}}}return b(t,n,e)}var Ct={blur:k,brightness:x,contrast:x,"drop-shadow":function(t,n,e){return 0===t?n:1===t?e:{blur:k(t,n.blur,e.blur),offset:A(t,n.offset,e.offset),color:S(t,n.color,e.color)}},grayscale:x,"hue-rotate":w,invert:x,opacity:x,saturate:x,sepia:x};function qt(t,n,e){if(0===t)return n;if(1===t)return e;var r=n.length;if(r!==e.length)return b(t,n,e);for(var i,u=[],o=0;o<r;o++){if(n[o].type!==e[o].type)return n;if(!(i=Ct[n[o].type]))return b(t,n,e);u.push({type:n.type,value:i(t,n[o].value,e[o].value)})}return u}var Lt={blur:function(t){return t?function(n){t.setAttribute("stdDeviation",Q(n))}:null},brightness:function(t,n,e){return(t=zt(e,n))?function(n){n=G(n),t.map((function(t){return t.setAttribute("slope",n)}))}:null},contrast:function(t,n,e){return(t=zt(e,n))?function(n){var e=G((1-n)/2);n=G(n),t.map((function(t){t.setAttribute("slope",n),t.setAttribute("intercept",e)}))}:null},"drop-shadow":function(t,n,e){var r=e.getElementById(n+"-blur");if(!r)return null;var i=e.getElementById(n+"-offset");if(!i)return null;var u=e.getElementById(n+"-flood");return u?function(t){r.setAttribute("stdDeviation",Q(t.blur)),i.setAttribute("dx",G(t.offset.x)),i.setAttribute("dy",G(t.offset.y)),u.setAttribute("flood-color",X(t.color))}:null},grayscale:function(t){return t?function(n){t.setAttribute("values",H(function(t){return[.2126+.7874*(t=1-t),.7152-.7152*t,.0722-.0722*t,0,0,.2126-.2126*t,.7152+.2848*t,.0722-.0722*t,0,0,.2126-.2126*t,.7152-.7152*t,.0722+.9278*t,0,0,0,0,0,1,0]}(n)))}:null},"hue-rotate":function(t){return t?function(n){return t.setAttribute("values",G(n))}:null},invert:function(t,n,e){return(t=zt(e,n))?function(n){n=G(n)+" "+G(1-n),t.map((function(t){return t.setAttribute("tableValues",n)}))}:null},opacity:function(t,n,e){return(t=e.getElementById(n+"-A"))?function(n){return t.setAttribute("tableValues","0 "+G(n))}:null},saturate:function(t){return t?function(n){return t.setAttribute("values",G(n))}:null},sepia:function(t){return t?function(n){return t.setAttribute("values",H(function(t){return[.393+.607*(t=1-t),.769-.769*t,.189-.189*t,0,0,.349-.349*t,.686+.314*t,.168-.168*t,0,0,.272-.272*t,.534-.534*t,.131+.869*t,0,0,0,0,0,1,0]}(n)))}:null}};var Vt=["R","G","B"];function zt(t,n){var e=Vt.map((function(e){return t.getElementById(n+"-"+e)||null}));return-1!==e.indexOf(null)?null:e}var Dt={fill:Rt,"fill-opacity":jt,stroke:Rt,"stroke-opacity":jt,"stroke-width":Nt,"stroke-dashoffset":{f:G,i:w},"stroke-dasharray":Tt,opacity:jt,transform:function(n,e,r,i){if(!(n=function(n,e){if(!n||"object"!==t(n))return null;var r=!1;for(var i in n)n.hasOwnProperty(i)&&(n[i]&&n[i].length?(n[i].forEach((function(t){t.e&&(t.e=e(t.e))})),r=!0):delete n[i]);return r?n:null}(n,i)))return null;var u=function(t,i,u){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;return n[t]?r(i,n[t],u):e&&e[t]?e[t]:o};return e&&e.a&&n.o?function(t){var e=r(t,n.o,Mt);return Bt.recomposeSelf(e,u("r",t,w,0)+e.a,u("k",t,A),u("s",t,A),u("t",t,A)).toString()}:function(t){return Bt.recomposeSelf(u("o",t,St,null),u("r",t,w,0),u("k",t,A),u("s",t,A),u("t",t,A)).toString()}},"#filter":function(t,n,e,r,i,u,o,a){if(!n.items||!t||!t.length)return null;var l=function(t,n){var e=(t=t.map((function(t){return t&&Lt[t[0]]?(n.getElementById(t[1]),Lt[t[0]](n.getElementById(t[1]),t[1],n)):null}))).length;return function(n){for(var r=0;r<e;r++)t[r]&&t[r](n[r].value)}}(n.items,a);return l?(t=function(t,n){return t.map((function(t){return t.e=n(t.e),t}))}(t,r),function(n){l(e(n,t,qt))}):null},"#line":Pt,points:{f:H,i:E},d:It,r:Nt,"#size":J,"#radius":Z,_:function(t,n){if(Array.isArray(t))for(var e=0;e<t.length;e++)this[t[e]]=n;else this[t]=n}};return function(t){!function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),n&&u(t,n)}(o,t);var e=a(o);function o(){return n(this,o),e.apply(this,arguments)}return r(o,null,[{key:"build",value:function(t){var n=l(i(o),"build",this).call(this,t,Dt);if(!n)return null;var e=n.el,r=n.options,u=n.player;return function(t,n,e){if("click"===e.start){return void n.addEventListener("click",(function(){switch(e.click){case"freeze":return!t._running&&t.reachedToEnd()&&(t.offset=0),t._running?t.pause():t.play();case"restart":return t.offset>0?t.restart():t.play();case"reverse":var n=!t._rollingBack&&t._running,r=t.reachedToEnd();return n||r&&1===t.fill?(t.pause(),r&&(t.offset=t.duration-1),t._rollback()):r?t.restart():t.play();case"none":default:return!t._running&&t.offset?t.restart():t.play()}}))}if("hover"===e.start)return n.addEventListener("mouseenter",(function(){return t.reachedToEnd()?t.restart():t.play()})),void n.addEventListener("mouseleave",(function(){switch(e.hover){case"freeze":return t.pause();case"reset":return t.stop();case"reverse":return t.pause(),t._rollback();case"none":default:return}}));if("scroll"===e.start)return void new Y(n,e.scroll||25,(function(n){n?t.reachedToEnd()?t.restart():t.play():t.pause()}));t.play()}(u,e,r),u}}]),o}(U)}));
__SVGATOR_PLAYER__.build({"root":"e80vERm72hw1","animations":[{"duration":3000,"direction":1,"iterations":0,"fill":1,"alternate":false,"speed":1,"elements":{"e80vERm72hw4":{"transform":{"data":{"o":{"x":61.499998,"y":94.000004,"type":"corner"},"t":{"x":0.000002,"y":-0.000004}},"keys":{"r":[{"t":0,"v":0},{"t":1000,"v":360},{"t":2000,"v":360},{"t":3000,"v":360}]}}},"e80vERm72hw5":{"transform":{"data":{"o":{"x":40.100004,"y":94.000006,"type":"corner"},"t":{"x":0.000002,"y":-0.000004}},"keys":{"r":[{"t":0,"v":0},{"t":1000,"v":360},{"t":2000,"v":360},{"t":3000,"v":360}]}}},"e80vERm72hw6":{"transform":{"data":{"o":{"x":84.500004,"y":94.000004,"type":"corner"},"t":{"x":-0.000004,"y":-0.000004}},"keys":{"r":[{"t":0,"v":0},{"t":1000,"v":360},{"t":2000,"v":360},{"t":3000,"v":360}]}}},"e80vERm72hw7":{"transform":{"data":{"o":{"x":108.500004,"y":94.000004,"type":"corner"},"t":{"x":-0.000004,"y":-0.000004}},"keys":{"r":[{"t":0,"v":0},{"t":1000,"v":360},{"t":2000,"v":360},{"t":3000,"v":360}]}}},"e80vERm72hw8":{"transform":{"data":{"o":{"x":132.899994,"y":94.000004,"type":"corner"},"t":{"x":0.000006,"y":-0.000004}},"keys":{"r":[{"t":0,"v":0},{"t":1000,"v":360},{"t":2000,"v":360},{"t":3000,"v":360}]}}},"e80vERm72hw9":{"transform":{"data":{"o":{"x":156.899994,"y":94.000004,"type":"corner"},"t":{"x":0.000006,"y":-0.000004}},"keys":{"r":[{"t":0,"v":0},{"t":1000,"v":360},{"t":2000,"v":360},{"t":3000,"v":360}]}}},"e80vERm72hw10":{"transform":{"data":{"o":{"x":179.899994,"y":94.000004,"type":"corner"},"t":{"x":0.000006,"y":-0.000004}},"keys":{"r":[{"t":0,"v":0},{"t":1000,"v":360},{"t":2000,"v":360},{"t":3000,"v":360}]}}},"e80vERm72hw11":{"transform":{"data":{"o":{"x":203.899994,"y":94.000004,"type":"corner"},"t":{"x":0.000006,"y":-0.000004}},"keys":{"r":[{"t":0,"v":0},{"t":1000,"v":360},{"t":2000,"v":360},{"t":3000,"v":360}]}}},"e80vERm72hw12":{"transform":{"data":{"o":{"x":227.199997,"y":94.000004,"type":"corner"},"t":{"x":0.000003,"y":-0.000004}},"keys":{"r":[{"t":0,"v":0},{"t":1000,"v":360},{"t":2000,"v":360},{"t":3000,"v":360}]}}},"e80vERm72hw13":{"transform":{"data":{"o":{"x":251.199997,"y":94.000004,"type":"corner"},"t":{"x":0.000003,"y":-0.000004}},"keys":{"r":[{"t":0,"v":0},{"t":1000,"v":360},{"t":2000,"v":360},{"t":3000,"v":360}]}}},"e80vERm72hw14":{"transform":{"data":{"o":{"x":274.199997,"y":94.000004,"type":"corner"},"t":{"x":0.000003,"y":-0.000004}},"keys":{"r":[{"t":0,"v":0},{"t":1000,"v":360},{"t":2000,"v":360},{"t":3000,"v":360}]}}},"e80vERm72hw15":{"transform":{"data":{"o":{"x":298.199997,"y":94.000004,"type":"corner"},"t":{"x":0.000003,"y":-0.000004}},"keys":{"r":[{"t":0,"v":0},{"t":1000,"v":360},{"t":2000,"v":360},{"t":3000,"v":360}]}}},"e80vERm72hw16":{"transform":{"data":{"t":{"x":-74.25,"y":-65.400003}},"keys":{"o":[{"t":0,"v":{"x":74.25,"y":65.400003,"type":"corner"},"e":[0.42,0,0.58,1]},{"t":1000,"v":{"x":170.2,"y":65.400005,"type":"corner"}}]}}},"e80vERm72hw38":{"transform":{"data":{"t":{"x":-74.25,"y":-65.400003}},"keys":{"o":[{"t":0,"v":{"x":-44.885645,"y":65.400005,"type":"corner"},"e":[0.42,0,0.58,1]},{"t":1000,"v":{"x":74.25,"y":65.400003,"type":"corner"}}]}}},"e80vERm72hw60":{"transform":{"data":{"t":{"x":-262.199997,"y":-61.950003}},"keys":{"o":[{"t":0,"v":{"x":170.854276,"y":61.950007,"type":"corner"},"e":[0.42,0,0.58,1]},{"t":1000,"v":{"x":262.199997,"y":61.950003,"type":"corner"}}]}}},"e80vERm72hw88":{"transform":{"data":{"t":{"x":-262.199997,"y":-61.950003}},"keys":{"o":[{"t":0,"v":{"x":262.199997,"y":61.950003,"type":"corner"},"e":[0.42,0,0.58,1]},{"t":1000,"v":{"x":358.899574,"y":61.950003,"type":"corner"}}]}}},"e80vERm72hw134":{"transform":{"data":{"o":{"x":164.650009,"y":40.149998,"type":"corner"},"t":{"x":-164.650009,"y":-40.149998}},"keys":{"r":[{"t":0,"v":0},{"t":3000,"v":360}]}}},"e80vERm72hw135":{"transform":{"data":{"o":{"x":175.550018,"y":46.199995,"type":"corner"},"t":{"x":-175.550018,"y":-46.199995}},"keys":{"r":[{"t":0,"v":360},{"t":3000,"v":0}]}}},"e80vERm72hw136":{"transform":{"data":{"o":{"x":167.04998,"y":52.249998,"type":"corner"},"t":{"x":-167.04998,"y":-52.249998}},"keys":{"r":[{"t":0,"v":0},{"t":3000,"v":360}]}}},"e80vERm72hw137":{"transform":{"data":{"o":{"x":247.200758,"y":31.427171,"type":"corner"}},"keys":{"r":[{"t":800,"v":180,"e":[0.42,0,0.58,1]},{"t":1100,"v":540}],"s":[{"t":800,"v":{"x":0,"y":0},"e":[0.42,0,0.58,1]},{"t":1100,"v":{"x":1.690299,"y":1.690299}},{"t":1200,"v":{"x":0,"y":0}}]}}},"e80vERm72hw138":{"transform":{"data":{"o":{"x":255.45,"y":51.295687,"type":"corner"}},"keys":{"r":[{"t":800,"v":180,"e":[0.42,0,0.58,1]},{"t":1100,"v":540}],"s":[{"t":800,"v":{"x":0,"y":0},"e":[0.42,0,0.58,1]},{"t":1100,"v":{"x":1.36305,"y":1.36305}},{"t":1200,"v":{"x":0,"y":0}}]}}},"e80vERm72hw139":{"transform":{"data":{"o":{"x":232.340183,"y":47.117328,"type":"corner"}},"keys":{"r":[{"t":1000,"v":180,"e":[0.42,0,0.58,1]},{"t":1300,"v":540}],"s":[{"t":1000,"v":{"x":0,"y":0},"e":[0.42,0,0.58,1]},{"t":1300,"v":{"x":1,"y":1}},{"t":1400,"v":{"x":1,"y":0}}]}}},"e80vERm72hw140":{"transform":{"data":{"o":{"x":287.764203,"y":49.182816,"type":"corner"}},"keys":{"r":[{"t":1100,"v":180,"e":[0.42,0,0.58,1]},{"t":1400,"v":540}],"s":[{"t":1100,"v":{"x":0,"y":0},"e":[0.42,0,0.58,1]},{"t":1400,"v":{"x":1,"y":1}},{"t":1500,"v":{"x":0,"y":0}}]}}}}}],"options":{"start":"load","hover":null,"click":null,"scroll":null,"font":"embed","exportedIds":"unique","svgFormat":"animated","title":"SEO Process"},"animationSettings":{"duration":3000,"direction":1,"iterations":0,"fill":1,"alternate":false,"speed":1}})]]></script></svg>
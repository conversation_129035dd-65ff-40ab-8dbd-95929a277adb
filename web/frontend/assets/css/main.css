.ss-header {
  box-shadow: 0px -1px 0px 0px #e1e3e5 inset;
  position: sticky;
  top: 0;
  background: var(--p-color-bg-surface-secondary-hover);
  z-index: 99;
  padding-right: var(--p-space-600);
}

.ss-header .Polaris-Box {
  padding: 0;
}

.ss-header .Polaris-Tabs__Tab {
  background: transparent;
  padding: 26px 12px;
}

.ss-header .Polaris-Tabs__Tab:not([aria-disabled="true"]):hover,
.ss-header .Polaris-Tabs__Tab:not([aria-disabled="true"]):focus {
  background: transparent;
}

.ss-header .Polaris-Tabs__TabContainer .Polaris-Text--semibold {
  font-weight: 400;
  color: #616a75;
  font-size: 14px;
}

.ss-header .Polaris-Tabs__Tab.Polaris-Tabs__Tab--active .Polaris-Text--semibold {
  color: #202223;
}

.ss-header .Polaris-Tabs__Tab--active {
  position: relative;
}

.ss-header .Polaris-Tabs__Tab--active::before {
  position: absolute;
  bottom: 1px;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--p-color-bg-fill-brand);
  content: "";
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
}

/* .ss-header  */
.dashboard-quick-card .Polaris-CalloutCard__Image {
  width: auto;
  height: 110px;
  /*height: 145px;*/
}

/* .betterdocs-faq .Polaris-CalloutCard__Image {
  width: auto;
  height: 90px;
} */

.footer {
  padding: var(--p-space-400);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--p-space-600);
}

.footer .footer__logo {
  line-height: 1;
}

.footer .footer__logo img {
  height: 28px;
}

.footer .copyright__text .link {
  text-decoration: none;
  color: var(--p-color-text-secondary);
  font-weight: 700;
}

@media all and (max-width: 991px) {
  .footer .footer__logo {
    order: 1;
    line-height: 1;
  }

  .footer .footer-language {
    order: 2;
  }

  .footer .Polaris-Text--root {
    flex: 1 1 100%;
    order: 3;
    text-align: center;
  }
}

.template--wrapper .collapse-header svg {
  width: 30px;
  height: 30px;
}

.seo-analytics-list .Polaris-HorizontalStack {
  padding: var(--p-space-400);
}

.report-page .Polaris-Tabs__Outer .Polaris-Box .Polaris-Tabs {
  padding-left: 0;
  padding-right: 0;
}

.settings-sidebar .Polaris-Navigation {
  background: transparent;
}
.settings-sidebar .Polaris-Navigation .Polaris-Navigation__PrimaryNavigation:hover {
  background-color: unset;
  scrollbar-color: unset;
}

.settings-sidebar .Polaris-Navigation__ListItem:not(:first-child) .Polaris-Navigation__ItemInnerWrapper {
  border-top: 0;
}

.settings-sidebar .Polaris-Navigation__ItemWrapper {
  padding-top: 5px;
  padding-bottom: 5px;
}

.settings-sidebar .Polaris-Navigation__ItemInnerWrapper--selected {
  background: var(--p-color-bg-surface-active);
}

/* .settings-sidebar .Polaris-ShadowBevel { */
/*height: 100%;*/
/* } */

.settings-sidebar > .Polaris-Box {
  height: calc(100vh - 90px);
  position: sticky;
  top: 70px;
}

.pricing-block {
  overflow: auto;
}

.pricing-area {
  display: grid;
  grid-template-columns: 250px minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr) minmax(
      135px,
      1fr
    );
  min-width: 930px;
}

.pricing-area .body-row > div {
  min-width: 128px;
}

.pricing-area.p-col-4 {
  grid-template-columns: 250px minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr);
}

.pricing-area.p-col-3 {
  grid-template-columns: 250px minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr);
}

.pricing-area.p-col-6 {
  grid-template-columns:
    250px minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr)
    minmax(135px, 1fr);
}

.pricing-area .pricing-header {
  grid-column: 2 / -1;
  display: grid;
  grid-template-columns: minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr);
  background: #fff;
  border-top-right-radius: 5px;
  border-top-left-radius: 5px;
}

.pricing-area.p-col-4 .pricing-header {
  grid-template-columns: minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr);
}

.pricing-area.p-col-3 .pricing-header {
  grid-template-columns: minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr);
}

.pricing-area.p-col-6 .pricing-header {
  grid-template-columns: minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr) minmax(
      135px,
      1fr
    );
}

.pricing-area .pricing-header .Polaris-BlockStack {
  height: 100%;
  padding: var(--p-space-400) var(--p-space-200);
  border-top: 1px solid #c9cccf;
  border-left: 1px solid #c9cccf;
}

.pricing-area .pricing-header .Polaris-BlockStack:first-child {
  border-top-left-radius: 5px;
}

.pricing-area .pricing-header .Polaris-BlockStack:last-child {
  border-right: 1px solid #c9cccf;
  border-top-right-radius: 5px;
}

.pricing-area .pricing-body {
  grid-column: 1 / -1;
  border-bottom-right-radius: 5px;
  border-bottom-left-radius: 5px;
}

.pricing-area .pricing-body .body-row {
  display: grid;
  grid-template-columns: 250px minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr) minmax(
      135px,
      1fr
    );
}

.pricing-area.p-col-4 .pricing-body .body-row {
  grid-template-columns: 250px minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr);
}

.pricing-area.p-col-3 .pricing-body .body-row {
  grid-template-columns: 250px minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr);
}

.pricing-area.p-col-6 .pricing-body .body-row {
  grid-template-columns:
    250px minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr) minmax(135px, 1fr)
    minmax(135px, 1fr);
}

.pricing-area .pricing-body .body-row > div {
  background: #fff;
  border-collapse: collapse;
  padding: 16px;
  border-top: 1px solid #c9cccf;
  border-left: 1px solid #c9cccf;
}

.pricing-area .pricing-body .body-row > div:last-child {
  border-right: 1px solid #c9cccf;
}

.pricing-area .pricing-body .body-row:last-child {
  border-bottom: 1px solid #c9cccf;
  border-bottom-right-radius: 5px;
  border-bottom-left-radius: 5px;
}

.pricing-area .pricing-body .body-row:last-child > div:first-child {
  border-bottom-left-radius: 5px;
}

.pricing-area .pricing-body .body-row:last-child > div:last-child {
  border-bottom-right-radius: 5px;
}

.pricing-area .pricing-body .body-row:first-child > div:first-child {
  border-top-left-radius: 5px;
}

.content-blocker {
  position: relative;
}

.content-blocker.full-page .modal-wrap {
  align-items: flex-start;
  padding-top: 15%;
}

.modal-wrap {
  background-color: rgba(255, 255, 255, 0.5);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 98;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: auto;
  backdrop-filter: blur(2px);
  border-radius: var(--p-border-radius-200);
}

.modal {
  background: var(--p-color-bg-surface);
  max-width: 620px;
  width: 90%;
  border-radius: var(--p-border-radius-400);
  box-shadow: var(--p-shadow-600);
  overflow: hidden;
}

.modal.modal-sm {
  max-width: 420px;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid var(--p-color-border);
  background-color: var(--p-color-bg-surface-tertiary);
}

.modal-header .modal-title {
  font-size: 18px;
  font-weight: 700;
  color: var(--p-color-bg-inverse);
}

.modal-header .Polaris-Icon {
  width: 24px;
  height: 24px;
  cursor: pointer;
  margin: 0;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-top: 1px solid var(--p-color-border);
}

.analytics .tab-nav-item {
  position: relative;
  cursor: pointer;
}

.analytics .tab-nav-item::before {
  position: absolute;
  content: "";
  width: 100%;
  height: 3px;
  background: var(--p-color-bg-fill-brand);
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
  bottom: -1px;
  left: 0;
  right: 0;
  opacity: 0;
  visibility: hidden;
  z-index: 2;
}

.analytics .tab-nav-item.active::before {
  opacity: 1;
  visibility: visible;
}

/* .analytics .Polaris-ShadowBevel { */
/*height: 100%;*/
/* } */

.checkout-content-wrap .Polaris-Grid-Cell .Polaris-Box {
  height: 100%;
}

.checkout-content-wrap .Polaris-Grid-Cell .Polaris-ShadowBevel {
  height: 100%;
}

.migrate-option .Polaris-Choice {
  display: flex;
  align-items: center;
  padding-top: 0;
}

.onboard-header-icon .Polaris-Icon {
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.onboardin-header-progress {
  display: flex;
  align-items: center;
  gap: 10px;
}

.onboardin-header-progress .title {
  flex: 0 0 auto;
}

.onboarding-progress {
  flex: 1;
}

@media all and (max-width: 767px) {
  .onboardin-header-progress {
    flex-direction: column;
    align-items: flex-start;
  }

  .onboarding-progress {
    width: 100%;
  }
}

.check-circle {
  position: relative;
  display: inline-block;
  min-width: 20px;
  width: 20px;
  height: 20px;
  border: 2px dashed #8a8a8a;
  border-radius: 20px;
  margin-right: 10px;
  cursor: pointer;
}

.ss-nav {
  display: flex;
  align-items: center;
  list-style: none;
  padding: 0 0 0 0;
  margin: 0;
  gap: 2px;
}

.ss-nav .nav-item .Polaris-Link {
  color: var(--p-color-text-secondary);
  font-weight: 400;
  text-decoration: none;
  font-size: 14px;
  padding: 16px 16px;
  line-height: 1.2;
  position: relative;
  display: inline-block;
}

.ss-nav .nav-item .Polaris-Link:focus {
  outline: unset;
}

.ss-nav .nav-item.active .Polaris-Link {
  color: var(--p-color-bg-inverse);
}

.ss-nav .nav-item .Polaris-Link:hover {
  color: var(--p-color-bg-inverse);
}

.ss-nav .nav-item.active .Polaris-Link::before {
  position: absolute;
  content: "";
  width: 100%;
  height: 3px;
  background: var(--p-color-bg-fill-brand);
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
  bottom: 0;
  left: 0;
  right: 0;
  opacity: 1;
  visibility: visible;
  z-index: 2;
}

.ss-nav-wrap .Polaris-Button--iconOnly {
  display: none;
}

@media all and (max-width: 991px) {
  .ss-nav-wrap {
    padding: 16px;
    position: relative;
  }

  .ss-nav-wrap .Polaris-Button--iconOnly {
    display: block;
  }

  .ss-nav {
    /* display: none; */
    visibility: hidden;
    opacity: 0;
    transition: all 0.3s ease-in-out;
    display: block;
    position: absolute;
    top: 100%;
    left: 16px;
    background: #fff;
    min-width: 160px;
    box-shadow: var(--p-shadow-300);
    border-bottom-right-radius: var(--p-border-radius-200);
    border-bottom-left-radius: var(--p-border-radius-200);
  }

  .ss-nav.show {
    opacity: 1;
    visibility: visible;
  }

  .ss-nav .nav-item .Polaris-Link {
    display: block;
    padding: 10px 16px;
  }

  .ss-nav.show .nav-item .Polaris-Link {
    width: 100%;
  }

  .ss-nav.show .nav-item.active .Polaris-Link::before {
    width: 3px;
    height: 20px;
    border-radius: 0 4px 4px 0;
    top: 8px;
  }

  .Polaris-Page > .Polaris-Box {
    padding-left: 0;
    padding-right: 0;
  }
}

.Polaris-Page--fullWidth {
  max-width: 1680px !important;
}

.analytics .Polaris-Grid > .Polaris-Grid-Cell .Polaris-Box {
  height: 100%;
}

.Polaris-IndexTable__TableCell.break_coll_content {
  word-wrap: break-word!important;
}

.Polaris-IndexTable__TableCell {
  white-space: break-spaces !important;
}

.Polaris-Frame {
  --pc-sidebar-width: 240px !important;
}

.Polaris-Navigation {
  max-width: 240px !important;
}

.settings-sidebar .mobile-nav {
  display: none;
}

.settings-layout {
  display: flex;
  gap: 20px;
  min-height: calc(100vh - 170px);
}

.settings-sidebar {
  flex: 0 0 240px;
}

.settings-content {
  flex: 0 0 calc(100% - 260px);
  width: calc(100% - 260px);
  overflow: hidden;
}

@media all and (max-width: 991px) {
  .settings-layout {
    flex-direction: column;
  }

  .settings-sidebar {
    position: relative;
    flex: 0 !important;
    z-index: 111;
  }

  .settings-sidebar .mobile-nav {
    display: block;
  }

  .settings-sidebar .mobile-nav .Polaris-Icon {
    margin: 0;
  }

  .settings-sidebar > .Polaris-ShadowBevel {
    position: absolute;
    height: auto;
    top: calc(100% + 10px);
    left: 0;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .settings-sidebar.show > .Polaris-ShadowBevel {
    opacity: 1;
    visibility: visible;
  }

  .settings-sidebar.show > .Polaris-ShadowBevel .Polaris-Navigation {
    padding-top: 5px;
    padding-bottom: 5px;
  }

  .settings-content {
    flex: 1 1 100%;
    width: 100%;
  }
}

@media all and (max-width: 499px) {
  .Polaris-Page {
    padding: 0 16px !important;
  }

  .ss-header {
    padding-right: var(--p-space-200);
  }
}

@media all and (max-width: 991px) {
  .ss-dash-illustration > svg {
    width: 140px;
    height: max-content;
  }
}

.skeleton-wrapper {
  font-size: 0;
}

.skeleton-box {
  display: inline-block;
  position: relative;
  overflow: hidden;
  background: #dde0e4;
  border-radius: 5px;
}

.skeleton-box:after {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transform: translateX(-100%);
  background: #dde0e4;
  content: "";
  /* background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 0, rgba(255, 255, 255, 0.2) 20%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0));
  animation: shimmer 2s infinite; */
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

.pricing-area .skeleton-wrapper {
  line-height: 1;
}

.overview-item {
  height: 100%;
}

.overview-item > .Polaris-ShadowBevel {
  height: 100%;
}

.Polaris-ResourceList__HeaderWrapper--isSticky {
  border-top-left-radius: var(--p-border-radius-400) !important;
  border-top-right-radius: var(--p-border-radius-400) !important;
}

.ss-custom--checkbox .Polaris-Choice.Polaris-Checkbox__ChoiceLabel {
  align-items: flex-start;
}

.ss-custom--checkbox .Polaris-Choice__Control {
  margin-top: 2px;
}

.width-24 {
  width: 1.5rem;
}
.width-table_thumbnail {
  width: 3.75rem;
}
.width-resource_table_title {
  min-width: 12.5rem;
  max-width: 15vw;
}
.width-50 {
  width: 50px;
}

.width-60 {
  width: 60px;
}

.width-80 {
  width: 80px;
}

.width-100 {
  width: 100px;
}

.width-120 {
  width: 120px;
}

.width-150 {
  width: 150px;
}

.width-180 {
  width: 180px;
}

.width-200 {
  width: 200px;
}
.width-full {
  width: 100%;
}

.logo__uploader {
  display: flex;
  align-items: center;
}

.logo__uploader .input__button {
  margin-bottom: 0;
}

.logo__uploader .input__button input {
  display: none;
}

.logo__uploader .input__button span {
  font-size: var(--p-font-size-350);
  font-weight: var(--p-font-weight-medium);
  line-height: var(--p-font-line-height-400);
  border-radius: var(--p-border-radius-100);
  border: var(--p-border-width-025) solid var(--p-color-border-tertiary);
  cursor: pointer;
}

.promo-banner .left-image {
  background: #1e1428;
  padding: 0 20px;
  width: 140px;
  height: 108px;
  display: flex;
  align-items: flex-end;
}

.pricing-wrap .Polaris-Grid-Cell .Polaris-ShadowBevel {
  height: 100%;
}
.pricing-wrap .Polaris-Grid-Cell .Polaris-ShadowBevel .Polaris-BlockStack {
  height: 100%;
}

span.ss-close-button {
  position: absolute;
  right: 6px;
  top: 6px;
}

@media (min-width: 48em) {
  .Polaris-Navigation__PrimaryNavigation {
    padding-top: var(--p-space-200);
    padding-bottom: var(--p-space-200);
  }
}

.ss-reports-tab {
  margin-bottom: var(--p-space-400);
}
.ss-reports-tab .Polaris-Box {
  padding: 0;
}

.ss-reports-tab ul.Polaris-Tabs {
  padding-left: 0;
}
.Polaris-Navigation__LogoContainer {
  display: none !important;
}

.custom__button a.Polaris-Button {
  width: 40px;
  height: 40px;
  border-radius: var(--p-border-radius-full);
  box-shadow: none;
  background: var(--p-color-bg-surface-secondary);
}

.custom__button a.Polaris-Button:hover {
  background: var(--p-color-bg-surface-secondary-active);
}
.addon-select span.Polaris-Choice__Control {
  display: none;
}

.addon-select .Polaris-Choice__Label {
  width: 100%;
}

.learn--more--btn .Polaris-Link,
.learn--more--btn .Polaris-Link:hover {
  text-decoration: none !important;
}

.height-1200 {
  min-height: var(--p-height-1200);
  height: var(--p-height-1200);
}

@media (max-width: 30.625rem) {
  .Polaris-IndexFilters.Polaris-IndexFilters__IndexFiltersSticky {
    width: calc(100vw - 2rem);
    left: 1rem;
    top: 0;
  }
}

.notification-icon {
  position: fixed;
  width: 35px;
  height: 35px;
  bottom: 100px;
  right: 36px;
  z-index: 1000;
  line-height: 0;
  background: var(--p-color-bg-surface-inverse);
  color: white;
  border-radius: 40px;
  cursor: pointer;
}

@media all and (max-width: 479px) {
  .notification-icon {
    bottom: 80px;
    right: 23px;
  }
}

.shake-animation {
  animation: horizontal-shaking 0.5s;
  animation-iteration-count: infinite;
}

@keyframes horizontal-shaking {
  0% { transform: translateX(0) }
  20% { transform: translateX(2px) }
  30% { transform: translateX(5px) }
  40% { transform: translateX(-2px) }
  50% { transform: translateX(-5px) }
  60% { transform: translateX(2px) }
  70% { transform: translateX(5px) }
  80% { transform: translateX(-2px) }
  90% { transform: translateX(-5px) }
  100% { transform: translateX(0) }
}

.Polaris-IndexTable__Table--unselectable.Polaris-IndexTable__Table--sticky .Polaris-IndexTable__TableCell:first-child {
  background-color: transparent !important;
}

.Polaris-LegacyCard + .Polaris-LegacyCard {
  margin-top: unset !important;
}

.fade-carousel .react-multi-carousel-track {
  transition: opacity .4s ease-in-out !important;
}

.fade-carousel .react-multi-carousel-item {
  opacity: 0;
  transition: opacity .4s ease-in-out;
}

.fade-carousel .react-multi-carousel-item--active {
  opacity: 1;
  z-index: 2;
}
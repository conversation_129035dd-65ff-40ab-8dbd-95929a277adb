* {
  margin: 0;
  padding: 0;
  outline: none;
  border: none;
  box-sizing: border-box;
}
/* body {
  font-family: inherit;
  font-weight: 400;
  min-height: 100vh;
  background: #fff;
  font-size: 14px;
} */

a {
  color: inherit;
  transition: color 0.3s ease, background 0.3s ease, border 0.3s ease, box-shadow 0.3s ease;
  text-decoration: none;
  display: inline-block;
}
a:hover {
  text-decoration: underline;
}

label {
  margin-bottom: 0;
}

h1,
h2,
h3,
h4,
h5,
h6,
p {
  margin: 0;
  padding: 0;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 700;
  line-height: 1.3;
}

.sh-container {
  max-width: 980px;
  margin: 0 auto;
}
.sh-header {
  padding: 16px 0;
}
.load-more {
  text-align: center;
  margin-bottom: 30px;
  margin-top: 20px;
}
.sh-button {
  padding: 6px 12px;
  border-radius: 8px;
  /* font-size: 12px;
  font-weight: 600; */
  cursor: pointer;
  border: 0;
  box-shadow: none;
}
.border-bottom {
  border-bottom: 1px solid #ebebeb;
}
.sh-header .title {
  font-size: 36px;
  font-weight: 700;
}
.sh-header .subtitle {
  font-size: 14px;
  margin-top: 8px;
  font-weight: 400;
}

.sh-block {
  padding: 16px 0;
  /* column-count: 3; */
  display: grid;
  margin-bottom: 30px;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

.sh-block .sh-link-group {
  margin: 0;
}

.sh-block .sh-link-group .link-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 7px;
}
.sh-block .sh-link-group .link-menu {
  padding: 0;
  margin: 0;
  list-style: none;
}
.sh-block .sh-link-group .link-menu .link-item:not(:last-child) {
  margin-bottom: 7px;
}
/* .sh-block .sh-link-group .link-menu .link-item a {
  font-size: 20px;
  font-weight: 400;
} */

.sh-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}
.sh-footer {
  font-size: 16px;
  font-weight: 400;
  padding-bottom: 20px;
  padding-top: 20px;
}

@media all and (max-width: 991px) {
  .sh-block {
    column-count: 2;
    column-gap: 30px;
  }
}

@media all and (max-width: 575px) {
  .sh-block {
    column-count: 1;
    column-gap: 30px;
  }
}

import { useCallback } from 'react';
import { useAppQuery } from './useAppQuery';

/**
 * API-Driven Feature Flags Hook
 *
 * Single source of truth: Backend controls all feature flags
 * No duplication: Frontend fetches flags from backend API
 * Runtime updates: Flags can be changed without rebuilding frontend
 * Uses useAppQuery: Follows established codebase patterns with caching and error handling
 */

// Safe defaults used when API fails or is loading
const SAFE_DEFAULTS = {
  BLOG_AUTO_WRITE: {
    IMAGE_GENERATION: false, // Safe: disable by default
    BULK_GENERATION: false,
    SCHEDULING: false,
    TEMPLATES: false,
  },
  AI: {
    SUGGESTIONS: false,
    AUTO_OPTIMIZATION: true, // Safe to keep enabled
    CONTENT_ANALYSIS: true,  // Safe to keep enabled
  },
  UI: {
    NEW_DASHBOARD: false,
    DARK_MODE: false,
    ADVANCED_FILTERS: true, // Safe to keep enabled
  },
  INTEGRATIONS: {
    SOCIAL_MEDIA: false,
    ANALYTICS: true, // Safe to keep enabled
    WEBHOOKS: false,
  }
};

/**
 * Feature Flag Service for Frontend
 * Handles flag resolution from API-fetched data
 */
class FrontendFeatureFlagService {
  /**
   * Get feature flag value from flags object
   * @param {Object} flags - Flags object from API
   * @param {string} flagPath - Dot notation path (e.g., 'BLOG_AUTO_WRITE.IMAGE_GENERATION')
   * @param {boolean} defaultValue - Default value if not found
   * @returns {boolean}
   */
  static get(flags, flagPath, defaultValue = false) {
    try {
      const pathParts = flagPath.split('.');
      let current = flags;

      for (const part of pathParts) {
        if (!current || current[part] === undefined) {
          return defaultValue;
        }
        current = current[part];
      }

      return current;
    } catch (error) {
      console.error(`[FeatureFlag] Error accessing ${flagPath}:`, error);
      return defaultValue;
    }
  }

  /**
   * Check if feature is enabled
   * @param {Object} flags - Flags object from API
   * @param {string} flagPath - Dot notation path
   * @returns {boolean}
   */
  static isEnabled(flags, flagPath) {
    return this.get(flags, flagPath, false);
  }

  /**
   * Get all flags for a category
   * @param {Object} flags - Flags object from API
   * @param {string} category - Category name
   * @returns {Object}
   */
  static getCategory(flags, category) {
    return flags[category] || {};
  }
}

/**
 * Main Feature Flags Hook
 * Fetches flags from backend API using useAppQuery pattern
 *
 * @returns {Object} Feature flags state and methods
 */
export const useFeatureFlags = () => {
  // Use standard useAppQuery pattern
  const {
    data,
    isLoading: loading,
    error,
    refetch,
  } = useAppQuery({
    url: "/feature-flags",
    reactQueryOptions: {
      staleTime: 5 * 60 * 1000, // 5 minutes - flags don't change often
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: 2,
      onError: (err) => {
        console.error("[FeatureFlags] Failed to fetch feature flags:", err);
      },
    },
  });

  // Extract flags from response or use safe defaults
  const flags = data?.flags || SAFE_DEFAULTS;

  // Generic helper methods
  const isEnabled = useCallback((flagPath) => {
    return FrontendFeatureFlagService.isEnabled(flags, flagPath);
  }, [flags]);

  const isDisabled = useCallback((flagPath) => {
    return !isEnabled(flagPath);
  }, [isEnabled]);

  const getCategory = useCallback((category) => {
    return FrontendFeatureFlagService.getCategory(flags, category);
  }, [flags]);

  const get = useCallback((flagPath, defaultValue = false) => {
    return FrontendFeatureFlagService.get(flags, flagPath, defaultValue);
  }, [flags]);

  // Namespace objects (mirroring backend structure)
  const blogs = useCallback(() => ({
    autoWrite: {
      isEnabled: () => isEnabled('BLOG_AUTO_WRITE.IMAGE_GENERATION'),
      isImageGenerationEnabled: () => isEnabled('BLOG_AUTO_WRITE.IMAGE_GENERATION'),
      isBulkGenerationEnabled: () => isEnabled('BLOG_AUTO_WRITE.BULK_GENERATION'),
      isSchedulingEnabled: () => isEnabled('BLOG_AUTO_WRITE.SCHEDULING'),
      isTemplatesEnabled: () => isEnabled('BLOG_AUTO_WRITE.TEMPLATES'),
      getAll: () => getCategory('BLOG_AUTO_WRITE')
    }
  }), [isEnabled, getCategory]);

  // Note: Only implementing blog namespace for now
  // Other namespaces (ui, ai, integrations) can be added when needed

  return {
    // Raw data and state
    flags,
    loading,
    error,
    refetch,

    // Generic methods (for flexibility)
    isEnabled,
    isDisabled,
    getCategory,
    get,

    // Namespace objects (clean API)
    blogs: blogs()
  };
};

/**
 * Clean Namespace API Examples:
 *
 * const featureFlags = useFeatureFlags();
 *
 * // Blog features (currently implemented)
 * featureFlags.blogs.autoWrite.isImageGenerationEnabled()
 * featureFlags.blogs.autoWrite.isBulkGenerationEnabled()
 * featureFlags.blogs.autoWrite.isSchedulingEnabled()
 * featureFlags.blogs.autoWrite.isTemplatesEnabled()
 *
 * // Generic access (still available)
 * featureFlags.isEnabled('BLOG_AUTO_WRITE.IMAGE_GENERATION')
 * featureFlags.getCategory('BLOG_AUTO_WRITE')
 */

/**
 * Higher-Order Component for Feature Flag Wrapping
 *
 * @param {string} flagPath - Feature flag path
 * @param {React.Component} fallback - Component to render when disabled
 * @returns {Function} HOC function
 *
 * Usage:
 * const ImageGenerationForm = withFeatureFlag('BLOG_AUTO_WRITE.IMAGE_GENERATION')(
 *   () => <ImageForm />,
 *   () => <div>Feature temporarily disabled</div>
 * );
 */
export const withFeatureFlag = (flagPath, fallback = null) => (Component) => {
  return function FeatureFlagWrapper(props) {
    const { isEnabled, loading, error } = useFeatureFlags();

    // Debug logging
    if (process.env.NODE_ENV === "development") {
      console.log("[withFeatureFlag] Flag:", flagPath, "Loading:", loading, "Enabled:", isEnabled(flagPath));
    }

    if (loading) {
      return null; // or a loading spinner
    }

    if (error) {
      console.error("[withFeatureFlag] Error loading flags:", error);
      return fallback; // Safe fallback on error
    }

    if (!isEnabled(flagPath)) {
      return fallback;
    }

    return <Component {...props} />;
  };
};

/**
 * Feature Flag Component for Conditional Rendering
 *
 * Usage:
 * <FeatureFlag flag="BLOG_AUTO_WRITE.IMAGE_GENERATION">
 *   <ImageGenerationForm />
 * </FeatureFlag>
 *
 * <FeatureFlag flag="BLOG_AUTO_WRITE.IMAGE_GENERATION" fallback={<div>Disabled</div>}>
 *   <ImageGenerationForm />
 * </FeatureFlag>
 */
export const FeatureFlag = ({ flag, children, fallback = null, showLoadingState = false }) => {
  const { isEnabled, loading, error } = useFeatureFlags();

  // Debug logging
  if (process.env.NODE_ENV === "development") {
    console.log("[FeatureFlag] Flag:", flag, "Loading:", loading, "Enabled:", isEnabled(flag));
  }

  if (loading && showLoadingState) {
    return null; // or a loading spinner
  }

  if (error) {
    console.error("[FeatureFlag] Error loading flags:", error);
    return fallback; // Safe fallback on error
  }

  if (!isEnabled(flag)) {
    return fallback;
  }

  return children;
};

// Export the service for direct usage
export { FrontendFeatureFlagService };

// Default export
export default useFeatureFlags;

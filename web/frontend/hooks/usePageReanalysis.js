import { debounce, isEmpty } from "lodash";
import { useCallback, useEffect, useState } from "react";
import { useMutation } from "react-query";
import { usePageApi } from "./apiHooks/usePageApi";

export function usePageReanalysis(id, formData, apiData) {
  const [score, setScore] = useState(0);
  const [optimizationData, setOptimizationData] = useState({});
  const [focusKeywordSuggestions, setFocusKeywordSuggestions] = useState([]);

  const pageApi = usePageApi();
  const { mutate: reanalysePage } = useMutation({
    mutationFn: () => pageApi.calculateOptimizationData(id, formData),
    onSuccess: ({ score, optimizationData, focusKeywordSuggestions }) => {
      setScore(score);
      setOptimizationData(optimizationData);
      // setFocusKeywordSuggestions(focusKeywordSuggestions);
    },
  });

  const _debReanalysePage = useCallback(debounce(reanalysePage, 1200), []);

  useEffect(() => {
    if (!isEmpty(formData)) _debReanalysePage();
  }, [formData]);

  useEffect(
    function resetStateOnDataChange() {
      if (!isEmpty(apiData)) {
        setScore(apiData.page.score);
        setOptimizationData(apiData.optimizationData);
        setFocusKeywordSuggestions(apiData.focusKeywordSuggestions);
      }
    },
    [apiData]
  );

  return {
    score,
    optimizationData,
    focusKeywordSuggestions,
  };
}

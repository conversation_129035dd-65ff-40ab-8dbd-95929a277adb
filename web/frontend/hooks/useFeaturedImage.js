import { isEmpty } from "lodash";
import { useMemo } from "react";
import types from "storeseo-enums/analysisEntityTypes";

export const useFeaturedImage = (item, type) => {
  const featuredImage = useMemo(() => {
    if (isEmpty(item)) return {};

    if (type === types.PRODUCT) {
      return item.featuredImage || {};
    } else if (type === types.ARTICLE || type === types.COLLECTION || type === types.DOC) {
      return item.image || {};
    } else if (type === types.PAGE) return {};
  }, [item, type]);

  return featuredImage;
};

import { useCallback } from "react";
import { useMutation, useQueryClient } from "react-query";
import queryKeys from "../utility/queryKeys";
import { useBlogApi } from "./apiHooks/useBlogApi";
import { useBlogAutoWriteModal } from "./useBlogAutoWriteModal";
import useUserAddon from "./useUserAddon";

/**
 * Blog Auto-Write Main Hook
 *
 * Provides a clean interface for blog auto-write functionality including
 * modal state management, credit estimation, and job creation.
 *
 * Job progress tracking is handled by the BlogAutoWriteJobProgress component
 * to avoid duplicate API calls and maintain separation of concerns.
 */
export function useBlogAutoWrite() {
  const queryClient = useQueryClient();
  const blogApi = useBlogApi();
  const { updateAiOptimizerUsage } = useUserAddon();

  // Direct API mutations (merged from useBlogAutoWriteApi)
  const getCreditEstimate = useMutation({
    mutationFn: (inputData) => blogApi.getBlogAutoWriteCreditEstimate(inputData),
    onSuccess: () => {
      // Invalidate credit-related queries for consistency
      queryClient.invalidateQueries(["credit-usage"]);
    },
  });

  const createJob = useMutation({
    mutationFn: (inputData) => blogApi.createBlogAutoWriteJob(inputData),
    onSuccess: ({ data }) => {
      // Update UI with reserved credits immediately (using estimatedCredits)
      if (data?.estimatedCredits) {
        updateAiOptimizerUsage(data.estimatedCredits);
      }

      // Invalidate articles list to show the new dummy article
      queryClient.invalidateQueries([queryKeys.ARTICLES_LIST]);
    },
  });

  const regenerateJob = useMutation({
    mutationFn: ({ jobId, inputOverrides }) => blogApi.regenerateBlogAutoWriteJob(jobId, inputOverrides),
    onSuccess: ({ data }) => {
      // Update UI with reserved credits for new job (using estimatedCredits)
      if (data?.estimatedCredits) {
        updateAiOptimizerUsage(data.estimatedCredits);
      }

      // Invalidate articles list to show updated status
      queryClient.invalidateQueries([queryKeys.ARTICLES_LIST]);
    },
  });

  // Focused hooks for specific responsibilities
  const modal = useBlogAutoWriteModal();

  // Orchestrated methods that coordinate between hooks
  const submitCreditEstimate = useCallback(
    async (inputData) => {
      return getCreditEstimate.mutateAsync(inputData);
    },
    [getCreditEstimate]
  );

  const submitCreateJob = useCallback(
    async (inputData) => {
      const result = await createJob.mutateAsync(inputData);
      return result;
    },
    [createJob]
  );

  const submitRegenerateJob = useCallback(
    async (jobId, inputOverrides = {}) => {
      const result = await regenerateJob.mutateAsync({ jobId, inputOverrides });
      return result;
    },
    [regenerateJob]
  );

  const cancelJob = useCallback(() => {
    modal.closeModal();
  }, [modal]);

  return {
    // Modal state and controls
    isModalOpen: modal.isOpen,
    currentStep: modal.currentStep,
    formData: modal.formData,
    openModal: modal.openModal,
    closeModal: modal.closeModal,
    goToStep: modal.goToStep,
    nextStep: modal.nextStep,
    previousStep: modal.previousStep,
    updateFormData: modal.updateFormData,

    // API operations (direct mutations)
    getCreditEstimate: submitCreditEstimate,
    isCreditEstimateLoading: getCreditEstimate.isLoading,
    creditEstimateError: getCreditEstimate.error,
    creditEstimateData: getCreditEstimate.data?.data,

    createJob: submitCreateJob,
    isCreatingJob: createJob.isLoading,
    createJobError: createJob.error,

    regenerateJob: submitRegenerateJob,
    isRegeneratingJob: regenerateJob.isLoading,
    regenerateJobError: regenerateJob.error,

    // Actions
    cancelJob,
  };
}

import queryKeys from "@/utility/queryKeys";
import { usePageApi } from "../apiHooks/usePageApi";
import { useAppQuery } from "../useAppQuery";
import { useMutation } from "react-query";
import { useDispatch } from "react-redux";
import { setPageSyncStatus } from "@/store/features/PageSync";

/**
 * Custom hook to sync pages from shopify
 * @param {object} mutationOptions
 * @returns
 */
export const useSyncPages = (mutationOptions) => {
  const pageApi = usePageApi();
  const dispatch = useDispatch();
  return useMutation({
    mutationFn: () => pageApi.syncPagesFromShopify(),
    onSuccess: () => dispatch(setPageSyncStatus(true)),
    ...mutationOptions,
  });
};

export const useGetPagesSitemaps = ({ query }, reactQueryOptions = {}) => {
  const pageApi = usePageApi();
  return useAppQuery({
    queryKey: [queryKeys.PAGE_SITEMAP_LIST, query],
    queryFn: async () => await pageApi.getPaginatedSitemapList(query),
    reactQueryOptions: {
      staleTime: 0,
      ...reactQueryOptions,
    },
  });
};

/**
 * Custom hook to update page index status
 * @param {*} mutationOptions
 * @returns
 */
export const useUpdatePageIndexStatus = (mutationOptions) => {
  const pageApi = usePageApi();
  return useMutation({
    mutationFn: (id) => pageApi.toggleNoIndexStatus(id),
    ...mutationOptions,
  });
};

/**
 * Custom hook to update page index status
 * @param {*} mutationOptions
 * @returns
 */
export const useUpdatePageFollowStatus = (mutationOptions) => {
  const pageApi = usePageApi();
  return useMutation({
    mutationFn: (id) => pageApi.toggleNoFollowStatus(id),
    ...mutationOptions,
  });
};

/**
 * Custom hook to update page sitemap status
 * @param {*} mutationOptions
 * @returns
 */
export const useUpdatePageSitemapStatus = (mutationOptions) => {
  const page = usePageApi();
  return useMutation({
    mutationFn: ({ id, status }) => page.updatePageSitemapStatus(id, status),
    ...mutationOptions,
  });
};

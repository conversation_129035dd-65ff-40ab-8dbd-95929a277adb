import { useSearchParams } from "react-router-dom";
import { sortOrders } from "../config";
import { useCallback } from "react";

/**
 * @typedef {object} Sort
 * @property {string} sortBy
 * @property {string} sortOrder
 */

/**
 * @callback SetSortParams
 * @param {string} sortBy
 */

/**
 * @callback getSortArrowClassname
 * @param {string} sortField
 * @returns {string}
 */

const arrowClassnames = {
  up: "ss-arrow-top",
  down: "ss-arrow-down",
};

/**
 * @returns {{ sort: Sort, setSort: SetSortParams, sortArrowClassname: getSortArrowClassname}}
 */
export const useSort = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const currentSortBy = searchParams.get("sortBy");
  const currentSortOrder = searchParams.get("sortOrder");

  /**
   * @type {SetSortParams}
   */
  const setSort = (sortBy) => {
    const sortParams = {
      sortBy,
      sortOrder:
        sortBy !== currentSortBy
          ? sortOrders.ASC
          : currentSortOrder === sortOrders.ASC
          ? sortOrders.DESC
          : sortOrders.ASC,
    };

    setSearchParams({
      ...Object.fromEntries(searchParams.entries()),
      ...sortParams,
      page: 1,
    });
  };

  /**
   * @type {getSortArrowClassname}
   */
  const sortArrowClassname = (sortField) => {
    if (sortField === currentSortBy && currentSortOrder === sortOrders.ASC) return arrowClassnames.down;
    else if (sortField === currentSortBy && currentSortOrder === sortOrders.DESC) return arrowClassnames.up;

    return "";
  };

  return {
    sort: {
      sortBy: currentSortBy,
      sortOrder: currentSortOrder,
    },
    setSort,
    sortArrowClassname,
  };
};

import { useMemo } from "react";
import BlogAutoWriteJobStatus from "storeseo-enums/blogAutoWrite/jobStatus";

// Blog generation statuses that indicate auto-write is in progress
const IN_PROGRESS_STATUSES = [
  BlogAutoWriteJobStatus.PENDING,
  BlogAutoWriteJobStatus.GENERATING_CONTENT,
  BlogAutoWriteJobStatus.CREATING_DRAFT,
  BlogAutoWriteJobStatus.LINKING_ARTICLE,
  BlogAutoWriteJobStatus.ANALYZING_SEO,
  BlogAutoWriteJobStatus.GENERATING_IMAGE,
  BlogAutoWriteJobStatus.UPLOADING_IMAGE,
  BlogAutoWriteJobStatus.UPDATING_ARTICLE,
  BlogAutoWriteJobStatus.FINALIZING_SEO,
  BlogAutoWriteJobStatus.PUBLISHING,
];

/**
 * Hook to determine if an article has blog auto-write in progress
 * @param {Object} article - Article object
 * @returns {Object} Status information
 */
export const useBlogAutoWriteStatus = (article) => {
  return useMemo(() => {
    // Check if article has an auto-write job
    const autoWriteJob = article?.autoWriteJob;
    const isAiGenerated = !!autoWriteJob;

    // Check if the generation is currently in progress
    const isInProgress = isAiGenerated && IN_PROGRESS_STATUSES.includes(autoWriteJob.status);

    // Check if navigation should be blocked (in progress OR failed)
    const shouldBlockNavigation =
      isAiGenerated &&
      (IN_PROGRESS_STATUSES.includes(autoWriteJob.status) || autoWriteJob.status === BlogAutoWriteJobStatus.FAILED);

    // Get display status
    const getDisplayStatus = () => {
      if (!isAiGenerated) return null;

      switch (autoWriteJob.status) {
        case BlogAutoWriteJobStatus.COMPLETED:
          return "AI Generated";
        case BlogAutoWriteJobStatus.FAILED:
          return "Generation: Failed";
        case BlogAutoWriteJobStatus.PENDING:
          return "Generation: Pending";
        default:
          // All in-progress statuses show as "ongoing"
          return "Generation: Ongoing";
      }
    };

    // Get status color for Polaris Text
    const getStatusTone = () => {
      if (!isAiGenerated) return null;

      switch (autoWriteJob.status) {
        case BlogAutoWriteJobStatus.COMPLETED:
          return "magic";
        case BlogAutoWriteJobStatus.FAILED:
          return "critical";
        case BlogAutoWriteJobStatus.PENDING:
          return "attention";
        default:
          return "info"; // For in-progress statuses
      }
    };

    return {
      isAiGenerated,
      isInProgress,
      shouldBlockNavigation,
      status: autoWriteJob?.status,
      displayStatus: getDisplayStatus(),
      statusTone: getStatusTone(),
      jobId: autoWriteJob?.id,
      progress: autoWriteJob?.progress || 0,
    };
  }, [article?.autoWriteJob]);
};

export default useBlogAutoWriteStatus;

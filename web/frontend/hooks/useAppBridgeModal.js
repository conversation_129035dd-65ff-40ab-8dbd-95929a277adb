import { MODAL_IDS } from "@/config";
import { useAppBridge } from "@shopify/app-bridge-react";

export function useAppBridgeModal() {
  const appBridge = useAppBridge();

  const showCheckoutModal = () => {
    setTimeout(() => appBridge.modal.show(MODAL_IDS.CHECKOUT), 100);
  };

  const hideCheckoutModal = () => {
    setTimeout(() => appBridge.modal.hide(MODAL_IDS.CHECKOUT), 100);
  };

  const showPurchaseModal = () => {
    setTimeout(() => appBridge.modal.show(MODAL_IDS.PURCHASE), 100);
  };

  const hidePurchaseModal = () => {
    setTimeout(() => appBridge.modal.hide(MODAL_IDS.PURCHASE), 100);
  };

  return { showCheckoutModal, hideCheckoutModal, showPurchaseModal, hidePurchaseModal };
}

import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import BlogType from "storeseo-enums/blogAutoWrite/blogType";
import ToneType from "storeseo-enums/blogAutoWrite/toneType";
import WordCountRange from "storeseo-enums/blogAutoWrite/wordCountRange";
import { blogAutoWriteInputSchema } from "storeseo-schema/blog/autoWrite";

/**
 * Blog Auto-Write Form Hook
 *
 * UI-only form hook that handles form state and validation.
 * Business logic and submission are handled by parent components.
 */
export function useBlogAutoWriteForm() {

  // Use shared validation schema from storeseo-schema package
  const validationSchema = blogAutoWriteInputSchema;

  // Form setup
  const form = useForm({
    resolver: yupResolver(validationSchema),
    mode: "onChange",
    defaultValues: {
      topic: "",
      keyword: "",
      targetBlog: "",
      blogType: BlogType.GUIDE,
      wordCount: WordCountRange.RANGE_800_1200,
      tone: ToneType.CONVERSATIONAL,
      customInstructions: "",
      generateFeaturedImage: false,
      featuredImageDescription: "",
    },
  });

  return {
    ...form,
  };
}

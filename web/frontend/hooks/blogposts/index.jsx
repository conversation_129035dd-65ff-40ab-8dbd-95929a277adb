import queryKeys from "@/utility/queryKeys";
import { useAppQuery } from "../useAppQuery";
import { useMutation } from "react-query";
import { useBlogApi } from "../apiHooks/useBlogApi";
import { setBlogSyncStatus } from "@/store/features/BlogSync";
import { useDispatch } from "react-redux";

/**
 * Custom hook to sync blog posts from shopify
 * @param {object} mutationOptions
 * @returns
 */
export const useSyncBlogPosts = (mutationOptions) => {
  const blogApi = useBlogApi();
  const dispatch = useDispatch();
  return useMutation({
    mutationFn: () => blogApi.syncBlogsFromShopify(),
    onSuccess: () => dispatch(setBlogSyncStatus(true)),
    ...mutationOptions,
  });
};

export const useGetBlogPostsSitemaps = ({ query }, reactQueryOptions = {}) => {
  const blogApi = useBlogApi();
  return useAppQuery({
    queryKey: [queryKeys.ARTICLE_SITEMAP_LIST, query],
    queryFn: async () => await blogApi.getPaginatedSitemapList(query),
    reactQueryOptions: {
      staleTime: 0,
      ...reactQueryOptions,
    },
  });
};

/**
 * Custom hook to update blog posts index status
 * @param {*} mutationOptions
 * @returns
 */
export const useUpdateBlogPostsIndexStatus = (mutationOptions) => {
  const blogApi = useBlogApi();
  return useMutation({
    mutationFn: (id) => blogApi.toggleNoIndexStatus(id),
    ...mutationOptions,
  });
};

/**
 * Custom hook to update blog posts index status
 * @param {*} mutationOptions
 * @returns
 */
export const useUpdateBlogPostsFollowStatus = (mutationOptions) => {
  const blogApi = useBlogApi();
  return useMutation({
    mutationFn: (id) => blogApi.toggleNoFollowStatus(id),
    ...mutationOptions,
  });
};

/**
 * Custom hook to update blog posts sitemap status
 * @param {*} mutationOptions
 * @returns
 */
export const useUpdateBlogPostsSitemapStatus = (mutationOptions) => {
  const blogApi = useBlogApi();
  return useMutation({
    mutationFn: ({ id, status }) => blogApi.updateBlogPostsSitemapStatus(id, status),
    ...mutationOptions,
  });
};

import { useMemo } from "react";
import { useSelector } from "react-redux";
import types from "storeseo-enums/analysisEntityTypes";

export const useItemUrl = (item, type) => {
  const user = useSelector((state) => state.user);

  const url = useMemo(() => {
    // if (!item?.handle || !item?.url) return "";

    switch (type) {
      case types.PRODUCT:
        return `/products/${item?.handle}`;
      case types.PAGE:
        return `/pages/${item?.handle}`;
      case types.ARTICLE:
        return item?.url.replace(/(?:https:\/\/)[^\/]*/gim, "");
      case types.COLLECTION:
        return `/collections/${item?.handle}`;
      case types.DOC:
        return `/docs/${item?.handle}`;
    }
  }, [item?.handle, item?.url, type]);

  return `${user?.url}${url}`;
};

// export const useItemUrl = (item, type) => {
//   const user = useSelector((state) => state.user);

//   const url = useMemo(() => {
//     if (isEmpty(item)) return "";

//     if (type === types.PRODUCT) return item?.online_store_url || item?.online_store_preview_url;
//     if (type === types.PAGE) return `${user?.url}/pages/${item?.handle}`;
//     if (type === types.ARTICLE) return item?.url;
//     if (type === types.COLLECTION) return `${user?.url}/collections/${item?.handle}`;
//     if (type === types.DOC) return `${user?.url}/docs/${item?.handle}`;
//   }, [item, type]);

//   return url;
// };

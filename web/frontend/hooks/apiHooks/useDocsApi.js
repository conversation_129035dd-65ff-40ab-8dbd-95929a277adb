import { useTranslation } from "react-i18next";
import { useApiClient } from "../useApiClient";
import { setErrorsFromResponse, showNotification } from "@/utility/helpers";

export const useDocsApi = () => {
  const { t } = useTranslation();
  const apiClient = useApiClient();

  /**
   * @param {{key: string}} query
   * @returns {Promise <{docs: any[], pagination: object}>}
   */
  const getPaginatedDocsList = async (query) => {
    return await apiClient("/docs", {
      query,
    });
  };

  /**
   * Get doc with optimization data by id
   * @param {any} id
   * @returns
   */
  const getDocWithOptimizationData = async (id) => {
    try {
      const data = await apiClient(`/docs/${id}`);
      return data;
    } catch (err) {
      console.log("Error fetching doc", err);
      throw new Error(err.message);
    }
  };

  /**
   * Calculate optimization data for a doc
   * @param {any} id
   * @param {object} data
   * @returns
   */
  const calculateOptimizationData = async (id, data) => {
    try {
      const resData = await apiClient(`/docs/${id}/optimization`, {
        method: "POST",
        body: JSON.stringify(data),
      });
      return resData;
    } catch (err) {
      throw new Error("Something went wrong!");
    }
  };

  /**
   * Update single doc by id
   * @param {any} id
   * @param {object} data
   * @param {Function} setErrors
   */
  const updateSingleDoc = async (id, data, setErrors) => {
    try {
      const { message, doc } = await apiClient(`/docs/${id}`, {
        method: "PUT",
        body: JSON.stringify(data),
      });
      showNotification({ type: "success", message: t(message) });
      return doc;
    } catch (err) {
      setErrorsFromResponse(err, setErrors);
      throw new Error(err.message);
    }
  };

  /**
   * @param {number} id
   * @returns {Promise<boolean>}
   */
  const syncDoc = async (id) => {
    try {
      const { message } = await apiClient(`/docs/sync/${id}`, {
        method: "POST",
        body: JSON.stringify({ sync: true }),
      });
      showNotification({ type: "success", message: t(message) });
      return true;
    } catch (err) {
      console.log("Error in docs sync: ", err);
      throw new Error("Docs sync start failed.");
    }
  };

  /**
   * @returns {Promise<boolean>}
   */
  const syncDocs = async () => {
    try {
      const { message } = await apiClient("/docs/sync", {
        method: "POST",
        body: JSON.stringify({ sync: true }),
      });

      showNotification({ type: "success", message: t(message) });
      return true;
    } catch (err) {
      console.log("Error in docs sync: ", err);
      throw new Error("Docs sync start failed.");
    }
  };

  const docCount = async () => {
    try {
      const count = await apiClient("/docs/count");
      return count;
    } catch (error) {
      console.error("Error in docs count: ", error);
      throw new Error("Failed to get docs count");
    }
  };

  const updateDocNoIndex = async (id, noindex) => {
    try {
      const { message, noIndexStatus } = await apiClient(`/docs/${id}/meta-robots`, {
        method: "PUT",
        body: JSON.stringify({ noindex }),
      });

      showNotification({
        type: "success",
        message: t(message),
      });

      return { noIndexStatus };
    } catch (error) {
      throw new Error("Error toggling no-index status!");
    }
  };

  const updateDocNoFollow = async (id, nofollow) => {
    try {
      const { message, noFollowStatus } = await apiClient(`/docs/${id}/meta-robots`, {
        method: "PUT",
        body: JSON.stringify({ nofollow }),
      });

      showNotification({
        type: "success",
        message: t(message),
      });

      return { noFollowStatus };
    } catch (error) {
      throw new Error("Error toggling no-follow status!");
    }
  };

  return {
    getPaginatedDocsList,
    syncDocs,
    syncDoc,
    getDocWithOptimizationData,
    calculateOptimizationData,
    updateSingleDoc,
    docCount,
    updateDocNoFollow,
    updateDocNoIndex,
  };
};

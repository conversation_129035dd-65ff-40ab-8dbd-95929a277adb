import { useTranslation } from "react-i18next";
import ResourceType from "storeseo-enums/resourceType";
import { showNotification } from "../../utility/helpers";
import { useApiClient } from "../useApiClient";

/**
 * @typedef {keyof Pick<typeof ResourceType, "PRODUCT" | "COLLECTION" | "PAGE" | "ARTICLE">} ImageResourceType
 */

export const useImageApi = () => {
  const { t } = useTranslation();
  const apiClient = useApiClient();

  /**
   * Retrieves images based on the specified type and query parameters.
   *
   * @param {ImageResourceType} resourceType
   * @param {*} query
   * @returns {Promise<*>} - The response data from the API.
   */
  const getImages = async (resourceType, query) => {
    const type = resourceType.toLowerCase();
    return apiClient(`/images/${type}`, {
      query,
    });
  };

  /**
   * Optimizes an image with a given configuration.
   *
   * @param {*} conf - Configuration for image optimization.
   * @param {ImageResourceType} resourceType - Type of the resource being optimized.
   * @returns {Promise<*>} - The response data from the API.
   */
  const optimizeImage = async (conf, resourceType) => {
    const type = resourceType.toLowerCase();
    const data = await apiClient(`/images/${type}/optimize`, {
      method: "POST",
      body: JSON.stringify(conf),
    });

    showNotification({ message: t("Image optimized") });

    return data;
  };

  const optimizeImagesViaQueue = async (conf, resourceType) => {
    const type = resourceType.toLowerCase();
    const data = await apiClient(`/images/${type}/optimize/queue`, {
      method: "POST",
      body: JSON.stringify(conf),
    });

    showNotification({ message: t("Image optimization started") });
    return data;
  };

  const saveOptimizedImages = async (images) => {
    const data = await apiClient("/images/optimize/save", {
      method: "PUT",
      body: JSON.stringify(images),
    });

    showNotification({ message: t("Image updated") });

    return data;
  };

  /**
   * Restores an image with a given configuration.
   *
   * @param {*} conf - Configuration for restoring the image.
   * @param {ImageResourceType} resourceType - Type of the resource being restored.
   * @returns {Promise<*>} - The response data from the API.
   */
  const restoreImage = async (conf, resourceType) => {
    const type = resourceType.toLowerCase();
    const data = await apiClient(`/images/${type}/restore`, {
      method: "POST",
      body: JSON.stringify(conf),
    });

    showNotification({ message: t("Image(s) restored") });

    return data;
  };

  const getSettings = async () =>
    apiClient("/images/settings", {
      method: "GET",
    });

  const updateSettings = async (conf) =>
    apiClient("/images/settings", {
      method: "POST",
      body: JSON.stringify(conf),
    });

  return {
    getImages,
    optimizeImagesViaQueue,
    optimizeImage,
    saveOptimizedImages,
    restoreImage,
    getSettings,
    updateSettings,
  };
};

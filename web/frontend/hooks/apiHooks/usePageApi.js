import { useTranslation } from "react-i18next";
import { setErrorsFromResponse, showNotification } from "../../utility/helpers";
import { useApiClient } from "../useApiClient";

export function usePageApi() {
  const { t } = useTranslation();
  const apiClient = useApiClient();

  const uploadSocialMediaPreviewImage = async (pageId, imageFile, imageFor) => {
    try {
      const formData = new FormData();
      formData.append(imageFor, imageFile, imageFile?.name);
      const { facebookImagePreviewURL, twitterImagePreviewURL } = await apiClient(
        `/pages/${pageId}/social-media-images`,
        {
          method: "PUT",
          headers: {},
          body: formData,
        }
      );

      showNotification({
        type: "success",
        message: toastMessages.PREVIEW_IMAGE_UPLOADED,
      });

      return { updateSuccessful: true, facebookImagePreviewURL, twitterImagePreviewURL };
    } catch (err) {
      console.log("Error uploading social media preview image", err);
      return { updateSuccessful: false };
    }
  };

  const updateCanonicalUrl = async (pageId, canonicalUrl) => {
    try {
      const {
        data: { message },
      } = await Client.put(`pages/${pageId}/canonical-url`, {
        canonicalUrl,
      });

      return { updateSuccessful: true, message: message };
    } catch (err) {
      console.log("Error updating canonical url: ", err);
      return { updateSuccessful: false, message: "Something went wrong." };
    }
  };

  const syncPageFromShopify = async (pageId) => {
    try {
      const { message } = await apiClient(`/pages/sync/${pageId}`, {
        method: "POST",
        body: JSON.stringify({ sync: true }),
      });
      showNotification({
        type: "success",
        message: t(message),
      });
      return true;
    } catch (err) {
      throw err;
    }
  };

  const syncPagesFromShopify = async () => {
    try {
      const { message } = await apiClient("/pages/sync", {
        method: "POST",
        body: JSON.stringify({ sync: true }),
      });

      showNotification({ type: "success", message: t(message) });

      return true;
    } catch (err) {
      console.log("Sync pages from shopify err: ", err);
      // processAndShowErrorNotification(err);
      throw new Error("Pages sync failed");
    }
  };

  const getPagesWithPagination = async (query) => {
    try {
      return apiClient(`/pages`, {
        query,
      });
    } catch (err) {
      return null;
    }
  };

  const getPageWithOptimizationData = async (id) => apiClient(`/pages/${id}`);

  const updatePageMetaData = async (id, formData, setFormErrors = () => {}) => {
    try {
      const { message, page } = await apiClient(`/pages/${id}/update`, {
        method: "PUT",
        body: JSON.stringify(formData),
      });

      showNotification({ type: "success", message: t(message) });

      return page;
    } catch (err) {
      setErrorsFromResponse(err, setFormErrors);
      // processAndShowErrorNotification(err, setFormErrors);
      throw new Error("API call error!");
    }
  };

  const calculateOptimizationData = async (id, data) => {
    try {
      const resData = await apiClient(`/pages/${id}/optimization`, {
        method: "POST",
        body: JSON.stringify(data),
      });
      return resData;
    } catch (err) {
      // processAndShowErrorNotification(err);
      throw new Error("Something went wrong!");
    }
  };

  const getTotalShopifyPagesCount = async () => {
    try {
      const { count } = await apiClient(`/pages/count`);
      return count;
    } catch (err) {
      return null;
    }
  };

  const toggleNoIndexStatus = async (pageId, queryString = "") => {
    try {
      const { message, noIndexStatus } = await apiClient(`/pages/${pageId}/no-index?${queryString}`, {
        method: "PUT",
      });

      showNotification({
        type: "success",
        message: t(message),
      });

      return { noIndexStatus };
    } catch (err) {
      // processAndShowErrorNotification(err);
      console.log("Error toggling no-index status: ", err);
      throw new Error("Something went wrong!");
    }
  };

  const toggleNoFollowStatus = async (pageId, queryString = "") => {
    try {
      const { message, noFollowStatus } = await apiClient(`/pages/${pageId}/no-follow?${queryString}`, {
        method: "PUT",
      });

      showNotification({
        type: "success",
        message: t(message),
      });

      return { noFollowStatus };
    } catch (err) {
      // processAndShowErrorNotification(err);
      console.log("Error toggling no-follow status: ", err);
      throw new Error("Something went wrong!");
    }
  };
  const getPaginatedSitemapList = async (query) =>
    apiClient("/sitemaps/page", {
      query,
    });

  const updatePageSitemapStatus = async (pageId, status) => {
    try {
      const data = {
        sitemap: {
          id: pageId,
          status,
        },
      };

      const { sitemapStatus, message } = await apiClient(`/sitemaps/page`, {
        method: "POST",
        body: JSON.stringify(data),
      });

      showNotification({
        type: "success",
        message: t(message),
      });

      return { sitemapStatus };
    } catch (err) {
      // processAndShowErrorNotification(err);
      throw new Error("Something went wrong!");
    }
  };

  return {
    syncPagesFromShopify,
    syncPageFromShopify,
    getPagesWithPagination,
    getPageWithOptimizationData,
    uploadSocialMediaPreviewImage,
    updatePageMetaData,
    calculateOptimizationData,
    getTotalShopifyPagesCount,
    toggleNoIndexStatus,
    toggleNoFollowStatus,
    getPaginatedSitemapList,
    updatePageSitemapStatus,
  };
}

// import { useTranslation } from "react-i18next";
import { useApiClient } from "../useApiClient";

export default function useEmailNotificationApi() {
  //   const { t } = useTranslation();
  const apiClient = useApiClient();

  const getEmailNotificationSettings = async () => {
    try {
      const { setting } = await apiClient(`/setting/email-notification`);
      return setting;
    } catch (err) {
      // processAndShowErrorNotification(err);
      console.log("Error fetching email notification settings: ", err);
      return null;
    }
  };

  const postEmailNotificationSettings = async (data) => {
    try {
      const { setting } = await apiClient(`/setting/email-notification`, {
        method: "POST",
        body: JSON.stringify(data),
      });
      return setting;
    } catch (err) {
      // processAndShowErrorNotification(err);
      console.log("Error storing email notification settings: ", err);
      return null;
    }
  };

  return {
    getEmailNotificationSettings,
    postEmailNotificationSettings,
  };
}

// // @ts-check
import { useTranslation } from "react-i18next";
import ResourceType from "storeseo-enums/resourceType";
import { autoAiOptimizationSchema } from "storeseo-schema/settings/autoAiOptimization";
import { setErrorsFromResponse, showNotification } from "../../utility/helpers";
import { useApiClient } from "../useApiClient";

/**
 * @typedef {keyof Pick<typeof ResourceType, "PRODUCT" | "COLLECTION" | "PAGE" | "ARTICLE">} AiResourceType
 */

export const useCollectionApi = () => {
  const { t } = useTranslation();
  const apiClient = useApiClient();

  /**
   * Get paginated list of collections
   * @param {{ [key]: string }} query
   * @returns {Promise<{collections: any[], pagination: object, collectionCount: number}>}
   */
  const getPaginatedCollectionsList = async (query) =>
    await apiClient("/collections", {
      query,
    });

  /**
   * Sync collections from Shopify
   * @returns {Promise<boolean>}
   */
  const syncCollectionsFromShopify = async () => {
    try {
      const { message } = await apiClient("/collections/sync", {
        method: "POST",
        body: JSON.stringify({ sync: true }),
      });

      showNotification({ type: "success", message: t(message) });
      return true;
    } catch (err) {
      console.log("err in collections sync: ", err);
      throw new Error("Collections sync start failed.");
    }
  };

  /**
   * Get total count of Shopify collections
   * @returns {Promise<number>}
   */
  const getTotalShopifyCollectionsCount = async () => {
    try {
      const { collectionsCount } = await apiClient(`/collections/count`);
      return collectionsCount;
    } catch (err) {
      return null;
    }
  };

  /**
   * Get collection with optimization data by id
   * @param {any} id
   * @returns
   */
  const getCollectionWithOptimizationData = async (id) => {
    try {
      const data = await apiClient(`/collections/${id}`);
      return data;
    } catch (err) {
      console.log("Error fetching collection", err);
      throw new Error(err.message);
    }
  };

  /**
   * Update single collection by id
   * @param {any} id
   * @param {object} data
   * @param {Function} setErrors
   */
  const updateSingleCollection = async (id, data, setErrors) => {
    try {
      const { message, collection } = await apiClient(`/collections/${id}`, {
        method: "PUT",
        body: JSON.stringify(data),
      });
      showNotification({ type: "success", message: t(message) });
      return collection;
    } catch (err) {
      setErrorsFromResponse(err, setErrors);
      throw new Error(err.message);
    }
  };

  /**
   * Calculate optimization data for a collection
   * @param {any} id
   * @param {object} data
   * @returns
   */
  const calculateOptimizationData = async (id, data) => {
    try {
      const resData = await apiClient(`/collections/${id}/optimization`, {
        method: "POST",
        body: JSON.stringify(data),
      });
      return resData;
    } catch (err) {
      throw new Error("Something went wrong!");
    }
  };

  const updateCollectionIndexStatus = async (id, queryString = "") => {
    try {
      const { message, noIndexStatus } = await apiClient(`/collections/${id}/no-index?${queryString}`, {
        method: "PUT",
      });

      showNotification({
        type: "success",
        message: t(message),
      });

      return { noIndexStatus };
    } catch (err) {
      throw new Error("Error toggling no-index status!");
    }
  };

  const updateCollectionFollowStatus = async (id, queryString = "") => {
    try {
      const { message, noFollowStatus } = await apiClient(`/collections/${id}/no-follow?${queryString}`, {
        method: "PUT",
      });

      showNotification({
        type: "success",
        message: t(message),
      });

      return { noFollowStatus };
    } catch (err) {
      throw new Error("Error toggling no-follow status!");
    }
  };

  const getPaginatedSitemapList = async (query) =>
    apiClient("/sitemaps/collection", {
      query,
    });

  const updateCollectionSitemapStatus = async (collectionId, status) => {
    try {
      const data = {
        sitemap: {
          id: collectionId,
          status,
        },
      };

      const { sitemapStatus, message } = await apiClient(`/sitemaps/collection`, {
        method: "POST",
        body: JSON.stringify(data),
      });

      showNotification({
        type: "success",
        message: t(message),
      });

      return { sitemapStatus };
    } catch (err) {
      // processAndShowErrorNotification(err);
      throw new Error("Something went wrong!");
    }
  };

  /**
   *
   * @param {{resourceList: {id: string}[], settings: import("yup").InferType<typeof autoAiOptimizationSchema>, resourceType: AiResourceType }} props
   * @returns
   */
  const generateBulkAiContent = async ({ resourceList, settings, resourceType }) => {
    const type = resourceType.toLowerCase();

    try {
      const data = await apiClient(`/ai/${type}/generate-ai-content/queue`, {
        method: "POST",
        body: JSON.stringify({
          resourceList,
          settings,
        }),
      });
      showNotification({ message: t(data?.message) });
      return data;
    } catch (err) {
      throw err;
    }
  };

  /**
   *
   * @param {{resourceList: {id: string}[], resourceType: AiResourceType}} props
   * @returns {Promise<{message: string, status: boolean, collections: {id: string}[]}>}
   */
  const restoreBulkAiContent = async ({ resourceList, resourceType }) => {
    const type = resourceType.toLowerCase();

    try {
      const result = await apiClient(`/ai/${type}/restore-ai-content`, {
        method: "POST",
        body: JSON.stringify({ resourceList }),
      });

      showNotification({ type: "success", message: t(result?.message) });
      return result;
    } catch (err) {
      throw new Error("Something went wrong!");
    }
  };

  return {
    getPaginatedCollectionsList,
    syncCollectionsFromShopify,
    getTotalShopifyCollectionsCount,
    updateSingleCollection,
    getCollectionWithOptimizationData,
    calculateOptimizationData,
    updateCollectionIndexStatus,
    updateCollectionFollowStatus,
    getPaginatedSitemapList,
    updateCollectionSitemapStatus,
    generateBulkAiContent,
    restoreBulkAiContent,
  };
};

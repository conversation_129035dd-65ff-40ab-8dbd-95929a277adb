import { processAndReturnError, showNotification } from "../../utility/helpers.jsx";
import { useApiClient } from "../useApiClient.js";
import { useAppBridgeRedirect } from "../useAppBridgeRedirect.js";

export const useSubscriptionApi = () => {
  const apiClient = useApiClient();
  const redirect = useAppBridgeRedirect();

  const getSubscriptionData = async (query) => {
    try {
      return await apiClient("/subscription", { query });
    } catch (err) {
      console.error("getSubscriptionData", err);
    }
  };

  const getCheckoutData = async (slug, couponCode = undefined) => {
    try {
      return await apiClient(`/checkout/${slug}`, { query: { coupon: couponCode } });
    } catch (err) {
      throw err;
    }
  };

  const validateCoupon = async (planSlug, couponCode = undefined) => {
    try {
      const { planData, message } = await apiClient("/validate-coupon", {
        method: "POST",
        body: JSON.stringify({
          slug: planSlug,
          coupon: couponCode,
        }),
      });

      showNotification({
        message,
        type: "success",
      });

      return planData;
    } catch (err) {
      const errorMessage = await processAndReturnError(err);
      throw new Error(errorMessage);
    }
  };

  const handleSubscription = async (planSlug, couponCode = undefined, selectedAddons = {}, withTrial = false) => {
    try {
      const { confirmationUrl, message, isFreeSubscription, isOnboarded } = await apiClient("/subscribe-to-plan", {
        method: "POST",
        body: JSON.stringify({
          slug: planSlug,
          coupon: couponCode,
          addons: selectedAddons,
          withTrial,
        }),
      });

      if (isFreeSubscription) {
        showNotification({ message });
        // const nextStep = getNextObject(STEPS, OnboardingSteps.SUBSCRIPTION, "name");
        // return { isFreePlan: isFreeSubscription, redirectTo: isOnboarded ? "/" : nextStep.pathname };
        // return { confirmationUrl, message, isFreeSubscription, isOnboarded };
      }

      return { confirmationUrl, message, isFreeSubscription, isOnboarded };
    } catch (err) {
      // await processAndShowErrorNotification(err);
      throw new Error("Failed!");
    }
  };

  const handleSwitchToPaidPlan = async () => {
    try {
      const { confirmationUrl, appSubscription } = await apiClient("/switch-to-paid-plan", {
        method: "POST",
      });

      return { confirmationUrl, appSubscription };
    } catch (err) {
      throw new Error("Failed!");
    }
  };

  const handleSubscriptionCancel = async () => {
    try {
      const { message } = await apiClient("/subscription-cancel", {
        method: "POST",
      });
      showNotification({ message });
    } catch (err) {
      // await processAndShowErrorNotification(err);
      throw new Error("Failed!");
    }
  };

  const subscribeToFreePlan = async (planSlug) => {
    try {
      const { message } = await apiClient("/subscribe-to-free-plan", {
        method: "POST",
        body: JSON.stringify({
          slug: planSlug,
        }),
      });

      showNotification({
        type: "success",
        message: message,
      });

      return { isFreePlan: true, redirectTo: "/" };
    } catch (err) {
      // await processAndShowErrorNotification(err);
      throw new Error("Failed!");
    }
  };

  const handleHundredPercentCouponSubscription = async (planSlug, couponCode = undefined) => {
    try {
      const { message } = await apiClient("/subscribe-with-free-coupon", {
        method: "POST",
        body: JSON.stringify({
          slug: planSlug,
          coupon: couponCode,
        }),
      });

      showNotification({
        type: "success",
        message: message,
      });
      return { isFreePlan: false, redirectTo: "/" };
    } catch (err) {
      // await processAndShowErrorNotification(err);
      throw new Error("Failed!");
    }
  };

  const getCreditBundles = async () => {
    try {
      return await apiClient(`/credit-bundles`);
    } catch (err) {
      throw err;
    }
  };

  const handleCreditPurchase = async (selectedAddons = {}) => {
    try {
      const res = await apiClient("/credit-purchase", {
        method: "POST",
        body: JSON.stringify({ addons: selectedAddons }),
      });

      redirect.REMOTE(res.confirmationUrl);
    } catch (err) {
      console.error("err =", err);
      throw new Error("Failed!");
    }
  };

  return {
    getSubscriptionData,
    getCheckoutData,
    validateCoupon,
    handleSubscription,
    subscribeToFreePlan,
    handleHundredPercentCouponSubscription,
    handleSubscriptionCancel,
    getCreditBundles,
    handleCreditPurchase,
    handleSwitchToPaidPlan,
  };
};

import { useTranslation } from "react-i18next";
import { useApiClient } from "../useApiClient";

export default function useSitemapApi() {
  const { t } = useTranslation();
  const apiClient = useApiClient();

  const getShopSitemapInfo = async () => {
    try {
      return await apiClient("/sitemaps/info");
    } catch (error) {
      console.error(`Error fetching shop sitemap info. Error: `, error);
      throw new Error(t("Something went wrong."));
    }
  };

  return { getShopSitemapInfo };
}

import { showNotification } from "@/utility/helpers";
import { useTranslation } from "react-i18next";
import { localBusinessAPISchema, organizationAPISchema, productMerchantAPISchema } from "storeseo-schema/local-seo";
import { useApiClient } from "../useApiClient";

export const useLocalSEOApi = () => {
  const { t } = useTranslation();
  const apiClient = useApiClient();

  const getBreadCrumbSchemaSettings = async () => {
    try {
      const response = await apiClient(`/local-seo/breadcrumb-schema`);
      return response.data;
    } catch (err) {
      console.log("Error fetching breadcrumb schema settings: ", err);
      return null;
    }
  };

  /**
   *
   * @param {{status: boolean}} data
   * @returns {Promise<{status: boolean}>}
   */
  const updateBreadCrumbSchemaSettings = async (data) => {
    try {
      const response = await apiClient(`/local-seo/breadcrumb-schema`, {
        method: "PUT",
        body: JSON.stringify(data),
      });

      showNotification({ message: t(response?.message) });
      return response.data;
    } catch (err) {
      console.log("Error updating breadcrumb schema settings: ", err);
      return null;
    }
  };

  const getCollectionSchemaSettings = async () => {
    try {
      const response = await apiClient(`/local-seo/collection-schema`);
      return response.data;
    } catch (err) {
      console.log("Error fetching collection schema settings: ", err);
      return null;
    }
  };

  /**
   *
   * @param {{status: boolean}} data
   * @returns {Promise<{status: boolean}>}
   */
  const updateCollectionSchemaSettings = async (data) => {
    try {
      const response = await apiClient(`/local-seo/collection-schema`, {
        method: "PUT",
        body: JSON.stringify(data),
      });

      showNotification({ message: t(response?.message) });
      return response.data;
    } catch (err) {
      console.log("Error updating collection schema settings: ", err);
      return null;
    }
  };

  const getBlogSchemaSettings = async () => {
    try {
      const response = await apiClient(`/local-seo/blog-schema`);
      return response.data;
    } catch (err) {
      console.log("Error fetching blog schema settings: ", err);
      return null;
    }
  };

  /**
   *
   * @param {{status: boolean}} data
   * @returns {Promise<{status: boolean}>}
   */
  const updateBlogSchemaSettings = async (data) => {
    try {
      const response = await apiClient(`/local-seo/blog-schema`, {
        method: "PUT",
        body: JSON.stringify(data),
      });

      showNotification({ message: t(response?.message) });
      return response.data;
    } catch (err) {
      console.log("Error updating blog schema settings: ", err);
      return null;
    }
  };

  const getProductSchemaSettings = async () => {
    try {
      const response = await apiClient(`/local-seo/product-schema`);
      return response.data;
    } catch (err) {
      console.log("Error fetching product schema settings: ", err);
      return null;
    }
  };

  /**
   *
   * @param {{status: boolean}} data
   * @returns {Promise<{status: boolean}>}
   */
  const updateProductSchemaSettings = async (data) => {
    try {
      const response = await apiClient(`/local-seo/product-schema`, {
        method: "PUT",
        body: JSON.stringify(data),
      });

      showNotification({ message: t(response?.message) });
      return response.data;
    } catch (err) {
      console.log("Error updating product schema settings: ", err);
      return null;
    }
  };
  /**
   * @returns {Promise<import("yup").InferType<typeof productMerchantAPISchema>>}
   */
  const getProductMerchantSchemaSettings = async () => {
    try {
      const response = await apiClient(`/local-seo/product-merchant-schema`);
      return response.data;
    } catch (err) {
      console.log("Error fetching product merchant schema settings: ", err);
      return null;
    }
  };

  /**
   *
   * @param {import("yup").InferType<typeof productMerchantAPISchema>} data
   * @returns {Promise<import("yup").InferType<typeof productMerchantAPISchema>>}
   */
  const updateProductMerchantSchemaSettings = async (data) => {
    try {
      const response = await apiClient(`/local-seo/product-merchant-schema`, {
        method: "PUT",
        body: JSON.stringify(data),
      });

      showNotification({ message: t(response?.message) });
      return response.data;
    } catch (err) {
      if (err instanceof Response) {
        throw await err.json();
      }
      return null;
    }
  };

  const getArticleSchemaSettings = async () => {
    try {
      const response = await apiClient(`/local-seo/article-schema`);
      return response.data;
    } catch (err) {
      console.log("Error fetching article schema settings: ", err);
      return null;
    }
  };

  /**
   *
   * @param {{status: boolean}} data
   * @returns {Promise<{status: boolean}>}
   */
  const updateArticleSchemaSettings = async (data) => {
    try {
      const response = await apiClient(`/local-seo/article-schema`, {
        method: "PUT",
        body: JSON.stringify(data),
      });

      showNotification({ message: t(response?.message) });
      return response.data;
    } catch (err) {
      console.log("Error updating article schema settings: ", err);
      return null;
    }
  };
  /**
   * @returns {Promise<import("yup").InferType<typeof organizationAPISchema>}
   */
  const getOrganizationSchemaSettings = async () => {
    try {
      const response = await apiClient(`/local-seo/organization-schema`);
      return response.data;
    } catch (err) {
      console.log("Error fetching organization schema settings: ", err);
      return null;
    }
  };

  /**
   *
   * @param {import("yup").InferType<typeof organizationAPISchema>} data
   * @returns {Promise<import("yup").InferType<typeof organizationAPISchema>>}
   */
  const updateOrganizationSchemaSettings = async (data) => {
    try {
      const response = await apiClient(`/local-seo/organization-schema`, {
        method: "PUT",
        body: JSON.stringify(data),
      });

      showNotification({ message: t(response?.message) });
      return response.data;
    } catch (err) {
      if (err instanceof Response) {
        throw await err.json();
      }
      return null;
    }
  };
  /**
   * @returns {Promise<import("yup").InferType<typeof localBusinessAPISchema>>}
   */
  const getLocalBusinessSchemaSettings = async () => {
    try {
      const response = await apiClient(`/local-seo/local-business-schema`);
      return response.data;
    } catch (err) {
      console.log("Error fetching local business schema settings: ", err);
      return null;
    }
  };

  /**
   *
   * @param {import("yup").InferType<typeof localBusinessAPISchema>} data
   * @returns {Promise<import("yup").InferType<typeof localBusinessAPISchema>>}
   */
  const updateLocalBusinessSchemaSettings = async (data) => {
    try {
      const response = await apiClient(`/local-seo/local-business-schema`, {
        method: "PUT",
        body: JSON.stringify(data),
      });

      showNotification({ message: t(response?.message) });
      return response.data;
    } catch (err) {
      if (err instanceof Response) {
        throw await err.json();
      }
      return null;
    }
  };

  return {
    getBreadCrumbSchemaSettings,
    updateBreadCrumbSchemaSettings,
    getCollectionSchemaSettings,
    updateCollectionSchemaSettings,
    getBlogSchemaSettings,
    updateBlogSchemaSettings,
    getProductSchemaSettings,
    updateProductSchemaSettings,
    getProductMerchantSchemaSettings,
    updateProductMerchantSchemaSettings,
    getArticleSchemaSettings,
    updateArticleSchemaSettings,
    getOrganizationSchemaSettings,
    updateOrganizationSchemaSettings,
    getLocalBusinessSchemaSettings,
    updateLocalBusinessSchemaSettings,
  };
};

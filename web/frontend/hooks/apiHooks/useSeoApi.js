import {
  GOOGLE_INDEXING_STATUS_API_ENDPOING,
  JSON_LD_API_ENDPOINT,
  LOCAL_SEO_API_ENDPOINT,
  REDIRECT_OUT_OF_STOCK_API_ENDPOINT,
  THEME_MOD_TOGGLER_API_ENDPOINT,
} from "../../utility/apiRoutes";
import { showNotification } from "../../utility/helpers";
import { useApiClient } from "../useApiClient";

export function useSeoApi() {
  const apiClient = useApiClient();

  const getLocalSEOData = async () => {
    try {
      const { jsonld_data: jsonldData, locations } = await apiClient(LOCAL_SEO_API_ENDPOINT);

      return { jsonldData, locations };
    } catch (err) {
      console.log("Error while fetching local-seo data: ", err);
      return null;
    }
  };

  const updateLocalSEOData = async (data, locations) => {
    try {
      const formData = new FormData();
      formData.append("data", JSON.stringify(data));
      formData.append("locations", JSON.stringify(locations));

      const { message, errors } = await apiClient(LOCAL_SEO_API_ENDPOINT, {
        method: "PUT",
        body: formData,
        headers: {},
      });

      showNotification({
        message: toastMessages.DATA_UPDATED,
        type: "success",
      });

      return { updateSuccessful: true, message: message || errors };
    } catch (err) {
      console.log("Error updating local seo data: ", err);
      throw new Error("Update failed");
    }
  };

  const getJSONLDStatus = async () => {
    try {
      const { isEnabled } = await apiClient(JSON_LD_API_ENDPOINT);
      return isEnabled;
    } catch (err) {
      console.log("error fetching JSON-LD status: ", err);
      return false;
    }
  };

  const toggleJSONLDStatus = async () => {
    try {
      const { isEnabled, message } = await apiClient(JSON_LD_API_ENDPOINT, { method: "PUT" });
      showNotification({ type: "success", message });
      return isEnabled;
    } catch (err) {
      console.log("Error toggling JSON-LD status: ", err);
      // processAndShowErrorNotification(err);
      throw new Error("Something went wrong!");
    }
  };

  const getGoogleIndexingStatus = async () => {
    try {
      const { isEnabled, hasIndexingPermission } = await apiClient(GOOGLE_INDEXING_STATUS_API_ENDPOING);
      return { isEnabled, hasIndexingPermission };
    } catch (err) {
      return null;
    }
  };

  const updateGoogleIndexingStatus = async (status) => {
    try {
      const { isEnabled, message } = await apiClient(GOOGLE_INDEXING_STATUS_API_ENDPOING, {
        method: "PUT",
        body: JSON.stringify({ status }),
      });

      showNotification({ type: "success", message });

      return isEnabled;
    } catch (err) {
      // processAndShowErrorNotification(err);
      throw new Error("Something went wrong!");
    }
  };

  const getRedirectOutOfStockInfo = async () => {
    try {
      const data = await apiClient(REDIRECT_OUT_OF_STOCK_API_ENDPOINT);
      return data;
    } catch (err) {
      console.log("Error retireving redirect out of stock info. Error: ", err);
      return null;
    }
  };

  const updateRedirectOutOfStockInfo = async (data) => {
    try {
      const { newStatus, redirectURL, message } = await apiClient(REDIRECT_OUT_OF_STOCK_API_ENDPOINT, {
        method: "PUT",
        body: JSON.stringify(data),
      });
      showNotification({ type: "success", message });
      return { status: newStatus, redirectURL };
    } catch (err) {
      // processAndShowErrorNotification(err);
      throw new Error("Something went wrong!");
    }
  };

  const toggleThemeModifications = async (status = false) => {
    try {
      const { message } = await apiClient(THEME_MOD_TOGGLER_API_ENDPOINT, {
        method: "PUT",
        body: JSON.stringify({ status }),
      });

      showNotification({ type: "success", message });
    } catch (err) {
      // processAndShowErrorNotification(err);
    }
  };

  return {
    getLocalSEOData,
    updateLocalSEOData,
    getJSONLDStatus,
    toggleJSONLDStatus,
    getGoogleIndexingStatus,
    updateGoogleIndexingStatus,
    getRedirectOutOfStockInfo,
    updateRedirectOutOfStockInfo,
    toggleThemeModifications,
  };
}

//@ts-check
import { showNotification } from "@/utility/helpers";
import { useTranslation } from "react-i18next";
import { useApiClient } from "../useApiClient";

export const useLlmsTxtApi = () => {
  const apiClient = useApiClient();
  const { t } = useTranslation();

  /**
   * Get current LLMs.txt settings for the shop
   * @returns {Promise<{settings: object, summary: object}>}
   */
  const getLlmsSettings = async () => {
    try {
      const { settings, summary } = await apiClient("/llms/settings");
      return { settings, summary };
    } catch (err) {
      throw new Error(err.message);
    }
  };

  /**
   * Update LLMs.txt settings
   * @param {object} settings - The settings object
   * @returns {Promise<{settings: object, summary: object}>}
   */
  const updateLlmsSettings = async (settings) => {
    try {
      const {
        message,
        settings: updatedSettings,
        summary,
      } = await apiClient("/llms/settings", {
        method: "PUT",
        body: JSON.stringify(settings),
      });

      showNotification({ message: t(message) });
      return { settings: updatedSettings, summary };
    } catch (err) {
      throw new Error(err.message);
    }
  };

  /**
   * Reset LLMs.txt settings to defaults
   * @returns {Promise<{settings: object, summary: object}>}
   */
  const resetLlmsSettings = async () => {
    try {
      const { message, settings, summary } = await apiClient("/llms/settings/reset", {
        method: "POST",
      });

      showNotification({ message: t(message) });
      return { settings, summary };
    } catch (err) {
      throw new Error(err.message);
    }
  };

  /**
   * Update specific resource enablement
   * @param {string} resource - Resource type (products, collections, etc.)
   * @param {boolean} enabled - Whether to enable or disable
   * @returns {Promise<{settings: object, summary: object}>}
   */
  const updateResourceEnabled = async (resource, enabled) => {
    try {
      const { message, settings, summary } = await apiClient("/llms/settings/resource", {
        method: "PUT",
        body: JSON.stringify({ resource, enabled }),
      });

      showNotification({ message: t(message) });
      return { settings, summary };
    } catch (err) {
      throw new Error(err.message);
    }
  };

  /**
   * Update schedule setting
   * @param {string} schedule - Schedule type (daily, weekly, monthly)
   * @returns {Promise<{settings: object, summary: object}>}
   */
  const updateSchedule = async (schedule) => {
    try {
      const { message, settings, summary } = await apiClient("/llms/settings/schedule", {
        method: "PUT",
        body: JSON.stringify({ schedule }),
      });

      showNotification({ message: t(message) });
      return { settings, summary };
    } catch (err) {
      throw new Error(err.message);
    }
  };

  /**
   * Trigger manual LLMs.txt generation
   * @param {object} settings - Settings for generation
   * @returns {Promise<{requestId: string, settings: object, enabledResources: string[]}>}
   */
  const generateLlmsTxt = async (settings) => {
    try {
      const {
        message,
        requestId,
        settings: updatedSettings,
        enabledResources,
      } = await apiClient("/llms/generate", {
        method: "POST",
        body: JSON.stringify(settings),
      });

      showNotification({ message: t(message) });
      return { requestId, settings: updatedSettings, enabledResources };
    } catch (err) {
      throw new Error(err.message);
    }
  };

  /**
   * Get LLMs.txt file versions (paginated)
   * @param {object} params - Query parameters for pagination
   * @returns {Promise<{versions: object[], pagination: object}>}
   */
  const getLlmsFileVersions = async (params = {}) => {
    try {
      const queryString = new URLSearchParams(params).toString();
      const url = `/llms/versions${queryString ? `?${queryString}` : ""}`;
      return await apiClient(url);
    } catch (err) {
      throw new Error(err.message);
    }
  };

  /**
   * Get specific file version details
   * @param {string} id - File version ID
   * @returns {Promise<object>}
   */
  const getLlmsFileVersion = async (id) => {
    try {
      const { data } = await apiClient(`/llms/versions/${id}`);
      return data;
    } catch (err) {
      throw new Error(err.message);
    }
  };

  /**
   * Download specific file version
   * @param {string} id - File version ID
   * @returns {Promise<void>}
   */
  const downloadLlmsFile = async (id) => {
    try {
      // This will redirect to the GCP URL
      window.open(`/api/llms/download/${id}`, "_blank");
    } catch (err) {
      throw new Error(err.message);
    }
  };

  /**
   * Copy file URL to clipboard
   * @param {string} fileUrl - The file URL to copy
   * @returns {Promise<void>}
   */
  const copyFileUrl = async (fileUrl) => {
    try {
      await navigator.clipboard.writeText(fileUrl);
      showNotification({ message: t("File URL copied to clipboard") });
    } catch (err) {
      throw new Error("Failed to copy URL to clipboard");
    }
  };

  /**
   * Delete specific file version
   * @param {string} id - File version ID
   * @returns {Promise<object>}
   */
  const deleteLlmsFile = async (id) => {
    try {
      const { message, deletedFile } = await apiClient(`/llms/versions/${id}`, {
        method: "DELETE",
      });

      showNotification({ message: t(message) });
      return { deletedFile };
    } catch (err) {
      throw new Error(err.message);
    }
  };

  return {
    getLlmsSettings,
    updateLlmsSettings,
    resetLlmsSettings,
    updateResourceEnabled,
    updateSchedule,
    generateLlmsTxt,
    getLlmsFileVersions,
    getLlmsFileVersion,
    downloadLlmsFile,
    copyFileUrl,
    deleteLlmsFile,
  };
};

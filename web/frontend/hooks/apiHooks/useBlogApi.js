import { useTranslation } from "react-i18next";
import ResourceType from "storeseo-enums/resourceType";
import { setErrorsFromResponse, showNotification } from "../../utility/helpers";
import { useApiClient } from "../useApiClient";

/**
 * @typedef {keyof Pick<typeof ResourceType, "PRODUCT" | "COLLECTION" | "PAGE" | "ARTICLE">} AiResourceType
 */

export function useBlogApi() {
  const { t } = useTranslation();
  const apiClient = useApiClient();

  const getArticlesWithPagination = async (query) => {
    try {
      const data = await apiClient("/articles", {
        query,
      });

      return data;
    } catch (err) {
      console.log("Error fetching all articles. Error: ", err);
      return {};
    }
  };

  const getArticleWithOptimizationData = async (articleId) => {
    try {
      const data = await apiClient(`/articles/${articleId}`);
      return data;
    } catch (err) {
      console.log("Error fetching article", err);
      throw new Error(err.message);
    }
  };

  const uploadArticleSocialMediaPreviewImage = async (articleId, imageFile, imageFor) => {
    try {
      const formData = new FormData();
      formData.append(imageFor, imageFile, imageFile?.name);
      const { facebookImagePreviewURL, twitterImagePreviewURL } = await apiClient(
        `/articles/${articleId}/social-media-images`,
        {
          method: "PUT",
          body: formData,
          headers: {},
        }
      );

      showNotification({ type: "success", message: toastMessages.PREVIEW_IMAGE_UPLOADED });
      return { facebookImagePreviewURL, twitterImagePreviewURL };
    } catch (err) {
      console.log("Error uploading social media preview image", err);
      return { updateSuccessful: false };
    }
  };

  const syncArticleFromShopify = async (articleId) => {
    try {
      const { message, article } = await apiClient(`/articles/${articleId}/sync`, { method: "PUT" });
      showNotification({
        type: "success",
        message,
      });

      return article;
    } catch (err) {
      // processAndShowErrorNotification(err);
      throw err;
    }
  };

  const updateCanonicalUrl = async (articleId, canonicalUrl) => {
    try {
      const {
        data: { message },
      } = await Client.put(`/articles/${articleId}/canonical-url`, {
        canonicalUrl,
      });

      return { updateSuccessful: true, message: message };
    } catch (err) {
      console.log("Error updating canonical url: ", err);
      return { updateSuccessful: false, message: "Something went wrong." };
    }
  };

  const syncBlogsFromShopify = async () => {
    try {
      const { message } = await apiClient("/articles/sync", {
        method: "POST",
        body: JSON.stringify({ sync: true }),
      });

      showNotification({ type: "success", message: t(message) });

      return true;
    } catch (err) {
      // processAndShowErrorNotification(err);
      console.log("Sync blogs from shopify err: ", err);
      throw new Error("Article sync failed");
    }
  };

  const calculateOptimizationData = async (articleId, data) => {
    try {
      const resData = await apiClient(`/articles/${articleId}/optimization`, {
        method: "POST",
        body: JSON.stringify(data),
      });
      return resData;
    } catch (err) {
      // processAndShowErrorNotification(err);
      throw new Error("Something went wrong!");
    }
  };

  const updateArticle = async (articleId, formData, setFormErrors = () => {}) => {
    try {
      const { message, article } = await apiClient(`/articles/${articleId}`, {
        method: "PUT",
        body: JSON.stringify(formData),
      });

      showNotification({ type: "success", message: t(message) });

      return article;
    } catch (err) {
      setErrorsFromResponse(err, setFormErrors);
      // processAndShowErrorNotification(err, setFormErrors);
      throw new Error("API call error!");
    }
  };

  // const updateFeatureImgAltText = async (articleId, image) => {
  //   try {
  //     const { message, article } = await apiClient(`/articles/${articleId}/image-alt-texts`, {
  //       method: "PUT",
  //       body: JSON.stringify({ image }),
  //     });

  //     showNotification({ type: "success", message });
  //     return article;
  //   } catch (err) {
  //     // processAndShowErrorNotification(err);
  //     throw new Error("Something went wrong!");
  //   }
  // };

  const getTotalShopifyArticlesCount = async () => {
    try {
      const { count } = await apiClient(`/articles/count`);
      return count;
    } catch (err) {
      return null;
    }
  };

  const toggleNoIndexStatus = async (articleId, queryString = "") => {
    try {
      const { message, noIndexStatus } = await apiClient(`/articles/${articleId}/no-index?${queryString}`, {
        method: "PUT",
      });

      showNotification({
        type: "success",
        message: t(message),
      });

      return { noIndexStatus };
    } catch (err) {
      // processAndShowErrorNotification(err);
      console.log("Error toggling no-index status: ", err);
      throw new Error("Something went wrong!");
    }
  };

  const toggleNoFollowStatus = async (articleId, queryString = "") => {
    try {
      const { message, noFollowStatus } = await apiClient(`/articles/${articleId}/no-follow?${queryString}`, {
        method: "PUT",
      });

      showNotification({
        type: "success",
        message: t(message),
      });

      return { noFollowStatus };
    } catch (err) {
      // processAndShowErrorNotification(err);
      console.log("Error toggling no-follow status: ", err);
      throw new Error("Something went wrong!");
    }
  };

  const getPaginatedSitemapList = async (query) =>
    apiClient("/sitemaps/article", {
      query,
    });

  const updateBlogPostsSitemapStatus = async (articleId, status) => {
    try {
      const data = {
        sitemap: {
          id: articleId,
          status,
        },
      };

      const { sitemapStatus, message } = await apiClient(`/sitemaps/article`, {
        method: "POST",
        body: JSON.stringify(data),
      });

      showNotification({
        type: "success",
        message: t(message),
      });

      return { sitemapStatus };
    } catch (err) {
      // processAndShowErrorNotification(err);
      throw new Error("Something went wrong!");
    }
  };

  /**
   *
   * @param {{resourceList: {id: string}[], settings: import("yup").InferType<typeof autoAiOptimizationSchema>, resourceType: AiResourceType }} props
   * @returns
   */
  const generateBulkAiContent = async ({ resourceList, settings, resourceType }) => {
    const type = resourceType.toLowerCase();

    try {
      const data = await apiClient(`/ai/${type}/generate-ai-content/queue`, {
        method: "POST",
        body: JSON.stringify({
          resourceList,
          settings,
        }),
      });
      showNotification({ message: t(data?.message) });
      return data;
    } catch (err) {
      throw err;
    }
  };

  /**
   *
   * @param {{resourceList: {id: string}[], resourceType: AiResourceType}} props
   * @returns {Promise<{message: string, status: boolean, articles: {id: string}[]}>}
   */
  const restoreBulkAiContent = async ({ resourceList, resourceType }) => {
    const type = resourceType.toLowerCase();

    try {
      const result = await apiClient(`/ai/${type}/restore-ai-content`, {
        method: "POST",
        body: JSON.stringify({ resourceList }),
      });

      showNotification({ type: "success", message: t(result?.message) });
      return result;
    } catch (err) {
      throw new Error("Something went wrong!");
    }
  };

  /**
   * Get credit estimate for blog auto-write
   * @param {Object} inputData - Blog generation input data
   * @returns {Promise<Object>} Credit estimation response
   */
  const getBlogAutoWriteCreditEstimate = async (inputData) => {
    try {
      const data = await apiClient("/blog-auto-write/credit-estimate", {
        method: "POST",
        body: JSON.stringify(inputData),
      });
      return data;
    } catch (err) {
      throw err; // ✅ Preserve full error object with response data
    }
  };

  /**
   * Create blog auto-write job
   * @param {Object} inputData - Blog generation input data
   * @returns {Promise<Object>} Job creation response
   */
  const createBlogAutoWriteJob = async (inputData) => {
    try {
      const data = await apiClient("/blog-auto-write", {
        method: "POST",
        body: JSON.stringify(inputData),
      });
      return data;
    } catch (err) {
      throw err; // ✅ Preserve full error object with response data
    }
  };

  /**
   * Get blog auto-write job data (status, progress, input_data, etc.)
   * @param {string} jobId - Job ID
   * @returns {Promise<Object>} Complete job data response
   */
  const getBlogAutoWriteJob = async (jobId) => {
    try {
      const data = await apiClient(`/blog-auto-write/${jobId}`);
      return data;
    } catch (err) {
      throw new Error(err.message || "Failed to get job data");
    }
  };

  /**
   * Regenerate blog auto-write job
   * @param {string} jobId - Original job ID to regenerate
   * @param {Object} inputOverrides - Optional input data overrides
   * @returns {Promise<Object>} Regeneration response with new job ID
   */
  const regenerateBlogAutoWriteJob = async (jobId, inputOverrides = {}) => {
    try {
      const data = await apiClient(`/blog-auto-write/${jobId}/regenerate`, {
        method: "POST",
        body: JSON.stringify(inputOverrides),
      });
      return data;
    } catch (err) {
      throw err; // ✅ Preserve full error object with response data
    }
  };

  return {
    getArticlesWithPagination,
    syncBlogsFromShopify,
    getArticleWithOptimizationData,
    calculateOptimizationData,
    updateArticle,
    // updateFeatureImgAltText,
    uploadArticleSocialMediaPreviewImage,
    syncArticleFromShopify,
    getTotalShopifyArticlesCount,
    toggleNoFollowStatus,
    toggleNoIndexStatus,
    getPaginatedSitemapList,
    updateBlogPostsSitemapStatus,
    generateBulkAiContent,
    restoreBulkAiContent,
    // Blog Auto-Write methods
    getBlogAutoWriteCreditEstimate,
    createBlogAutoWriteJob,
    getBlogAutoWriteJob,
    regenerateBlogAutoWriteJob,
  };
}

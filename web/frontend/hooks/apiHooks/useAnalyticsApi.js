import {
  ANALYTICS_REPORT_BY_DATE_ENDPOINT,
  KEYWORD_ANALYTICS_REPORT_BY_DATE_ENDPOINT,
  PRODUCTS_ANALYTICS_REPORT_ENDPOINT,
  SEARCH_CONSOLE_ANALYTICS_QUERIES_ENDPOINT,
} from "../../utility/apiRoutes";
import { processAndShowErrorNotification } from "../../utility/helpers";
import { useApiClient } from "../useApiClient";

export function useAnalyticsApi() {
  const apiClient = useApiClient();

  /**
   * @param {{ limit?: number, page?: number, dateRange: import("storeseo-enums/analytics/jsDocTypes").dateRange}}
   * @returns {Promise<import("storeseo-enums/analytics/jsDocTypes").PageReportWithPrevData[]>}
   */
  const getProductsReport = async ({ dateRange, limit, page }) => {
    try {
      const data = await apiClient(PRODUCTS_ANALYTICS_REPORT_ENDPOINT, {
        method: "POST",
        body: JSON.stringify({
          dateRange,
          limit,
          page,
        }),
      });

      return data;
    } catch (err) {
      console.log("err: ", err);
      // processAndShowErrorNotification(err);
      throw new Error("Something went wrong");
    }
  };

  /**
   * @param {{ limit?: number, page?: number, dateRange: import("storeseo-enums/analytics/jsDocTypes").dateRange}}
   * @returns {Promise<import("storeseo-enums/searchConsole/jsDocTypes").SearchConsoleAnalyticsQueryApiResponse>}
   */
  const getSearchConsoleQueriesReport = async ({ dateRange, limit, page }) => {
    try {
      const data = await apiClient(SEARCH_CONSOLE_ANALYTICS_QUERIES_ENDPOINT, {
        method: "POST",
        body: JSON.stringify({
          dateRange,
          limit,
          page,
        }),
      });

      return data;
    } catch (err) {
      console.log("err: ", err);
      // processAndShowErrorNotification(err);
      throw new Error("Something went wrong");
    }
  };

  /**
   * @param {{ dateRange: import("storeseo-enums/analytics/jsDocTypes").dateRange}}
   * @returns {Promise<import("storeseo-enums/analytics/jsDocTypes").KeywordsReportWithPrevData>}
   */
  const getKeywordsReport = async ({ dateRange }) => {
    try {
      const data = await apiClient(KEYWORD_ANALYTICS_REPORT_BY_DATE_ENDPOINT, {
        method: "POST",
        body: JSON.stringify({
          dateRange,
        }),
      });

      return data;
    } catch (err) {
      console.log("err: ", err);
      // processAndShowErrorNotification(err);
      throw new Error("Something went wrong");
    }
  };

  /**
   * @param {{ metrics: string[], dateRange: import("storeseo-enums/analytics/jsDocTypes").dateRange}} param0
   * @returns {Promise<import("storeseo-enums/analytics/jsDocTypes").SingleDimensionMetricsWithPrevData[]>}
   */
  const getReportByDate = async ({ dateRange, metrics, limit, page }) => {
    try {
      const data = await apiClient(ANALYTICS_REPORT_BY_DATE_ENDPOINT, {
        method: "POST",
        body: JSON.stringify({
          dateRange,
          limit,
          page,
          metrics,
        }),
      });

      return data;
    } catch (err) {
      console.log("err: ", err);
      // processAndShowErrorNotification(err);
      throw new Error("Something went wrong");
    }
  };

  return {
    getProductsReport,
    getKeywordsReport,
    getReportByDate,
    getSearchConsoleQueriesReport,
  };
}

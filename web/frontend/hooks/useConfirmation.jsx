// @ts-check
import { MODAL_IDS } from "@/config/index.js";
import { Modal, TitleBar, useAppBridge } from "@shopify/app-bridge-react";
import { Box, Text } from "@shopify/polaris";
import { useTranslation } from "react-i18next";

const UseConfirmation = () => {
  const { t } = useTranslation();
  const appBridge = useAppBridge();

  const showConfirmation = () => appBridge.modal.show(MODAL_IDS.CONFIRMATION);

  const hideConfirmation = () => appBridge.modal.hide(MODAL_IDS.CONFIRMATION);

  /**
   * Renders a confirmation modal dialog
   * @param {Object} props - The props object
   * @param {string} [props.title="Confirm!"] - The title of the confirmation modal
   * @param {string|React.ReactNode} [props.content="Are you sure?"] - The content to display in the modal body
   * @param {string} [props.primaryActionText="Confirm"] - The text for the primary action button
   * @param {() => void} [props.primaryAction=()=>{}] - Callback function executed when primary action is clicked
   * @param {boolean} [props.primaryActionIsDestructive=false] - Whether the primary action is destructive
   * @param {boolean} [props.loading=false] - Loading state of the primary action button
   * @param {() => void} [props.onClose=()=>{}] - Callback function executed when modal is closed
   * @param {('small'|'base'|'large'|'max')} [props.size="small"] - Size variant of the modal
   * @returns {JSX.Element} The confirmation modal component
   */
  const renderConfirmation = ({
    title = "Confirm!",
    content = "Are you sure?",
    primaryActionText = "Confirm",
    primaryAction = () => {},
    primaryActionIsDestructive = false,
    loading = false,
    onClose = () => {},
    size = "small",
  }) => (
    // <ConfirmationModal
    //   show={show}
    //   title={title}
    //   content={content}
    //   primaryAction={primaryAction}
    //   primaryActionText={primaryActionText}
    //   primaryActionIsDestructive={primaryActionIsDestructive}
    //   loading={loading}
    //   onClose={() => {
    //     hideConfirmation();
    //     onClose();
    //   }}
    // />

    <Modal
      id={MODAL_IDS.CONFIRMATION}
      variant={size}
      onHide={onClose}
    >
      <TitleBar title={t(title)}>
        <button onClick={hideConfirmation}>{t("Cancel")}</button>
        <button
          onClick={primaryAction}
          variant="primary"
          tone={primaryActionIsDestructive ? "critical" : undefined}
          loading={loading ? "" : undefined}
        >
          {t(primaryActionText)}
        </button>
      </TitleBar>
      <Box padding={"400"}>{typeof content === "string" ? <Text as="p">{t(content)}</Text> : content}</Box>
    </Modal>
  );

  return { renderConfirmation, showConfirmation, hideConfirmation };
};

export default UseConfirmation;

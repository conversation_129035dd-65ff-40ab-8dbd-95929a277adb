import { browserEvents, emitter } from "../config";
import { sSEOConfirm } from "../utility/helpers";

export const useConfirmChanges = () => {
  return (onCancelCallback) => {
    sSEOConfirm({
      title: "",
      msg: "Your unsaved changes will be lost. Save changes before changing tab?",
      button: {
        confirm: {
          text: "Save changes",
          class: "button__primary",
        },
        cancel: {
          onClick: onCancelCallback,
        },
      },
      callback: () => {
        emitter.emit(browserEvents.SAVE_CHANGES);
        onCancelCallback();
      },
    });
  };
};

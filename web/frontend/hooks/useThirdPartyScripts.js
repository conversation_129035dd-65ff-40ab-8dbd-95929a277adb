import Clarity from "@microsoft/clarity";
import { Crisp } from "crisp-sdk-web";
import { isEmpty } from "lodash";

export const useThirdPartyScripts = () => {
  return {
    crisp(user) {
      // if (!process?.env?.FRONTEND_APP_URL?.startsWith("https://app.storeseo.com")) return;
      if (!isEmpty(user) && user.shop) {
        const websiteId = process.env?.FRONTEND_CRISP_WEBSITE_ID || "57c11e5d-c42e-4758-a784-9a30be2fac1a";
        Crisp.configure(websiteId, {
          tokenId: encodeURIComponent(user.shop),
        });

        Crisp.user.setEmail(user.email);
        Crisp.user.setNickname(user.shopName);
        Crisp.session.setData([
          ["shop-name", user.shopName],
          ["domain", user.shop],
          ["url", user.url],
          ["plan", user.planName],
          ["total-products", user.shopifyProductCount],
        ]);
      }
    },

    googleAnalytics() {
      if (!process?.env?.FRONTEND_APP_URL?.startsWith("https://app.storeseo.com")) return;

      const analyticsId = process.env?.FRONTEND_GOOGLE_ANALYTICS_ID || "G-8PB6Q28L9E";
      const script = document.createElement("script");
      script.src = `https://www.googletagmanager.com/gtag/js?id=${analyticsId}`;
      document.body.appendChild(script);
      window.dataLayer = window.dataLayer || [];

      function gtag() {
        dataLayer.push(arguments);
      }

      gtag("js", new Date());
      gtag("config", analyticsId);
    },

    clarityTracking(user) {
      // if (!process?.env?.FRONTEND_APP_URL?.startsWith("https://app.storeseo.com")) return;

      const projectId = "pmys4xil14";

      Clarity.init(projectId);
      if (!isEmpty(user)) {
        Clarity.identify(user.shop.toString());
        Clarity.setTag("domain", user.shop);
        Clarity.setTag("shop-name", user.shopName);
        Clarity.setTag("email", user.email);
        Clarity.setTag("url", user.url);
        Clarity.setTag("plan", user.planName);
      }
      console.info("⏬ Clarity tracking script loaded...");
    },
  };
};

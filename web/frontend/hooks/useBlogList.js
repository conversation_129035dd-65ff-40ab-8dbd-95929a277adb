//@ts-check
import { useAppQuery } from "./useAppQuery";
import { useApiClient } from "./useApiClient";
import queryKeys from "../utility/queryKeys";

/**
 * @typedef {Object} Blog
 * @property {number} id - Blog database ID
 * @property {string} title - Blog title
 * @property {string} handle - Blog URL handle
 * @property {boolean} is_synced - Whether blog is synced
 * @property {number} [article_count] - Number of articles in blog
 * @property {string} [template_suffix] - Theme template suffix
 * @property {string[]} [tags] - Blog tags
 * @property {string} created_at - Creation timestamp
 * @property {string} updated_at - Last update timestamp
 */

/**
 * @typedef {Object} BlogListResponse
 * @property {Blog[]} blogs - Array of blogs
 * @property {Object} pagination - Pagination information
 * @property {number} pagination.total - Total number of blogs
 * @property {number} pagination.page - Current page
 * @property {number} pagination.limit - Items per page
 * @property {boolean} pagination.hasNext - Whether there are more pages
 * @property {boolean} pagination.hasPrev - Whether there are previous pages
 */

/**
 * @typedef {Object} BlogListOptions
 * @property {boolean} [syncedOnly] - Only return synced blogs
 * @property {string} [search] - Search term for blog titles
 * @property {string} [sortBy] - Sort field (title, created_at, updated_at)
 * @property {string} [sortOrder] - Sort order (asc, desc)
 * @property {number} [page] - Page number for pagination
 * @property {number} [limit] - Items per page
 */

/**
 * Custom hook for fetching blogs from the API
 * 
 * @param {BlogListOptions} [options] - Query options for filtering and pagination
 * @param {import("react-query").UseQueryOptions} [reactQueryOptions] - React Query options
 * @returns {import("react-query").UseQueryResult<BlogListResponse>}
 */
export const useBlogList = (options = {}, reactQueryOptions = {}) => {
  const apiClient = useApiClient();

  // Default options
  const defaultOptions = {
    syncedOnly: true, // Only show synced blogs by default
    search: "",
    sortBy: "title",
    sortOrder: "asc",
    page: 1,
    limit: 50,
  };

  const queryOptions = { ...defaultOptions, ...options };

  // Build query parameters
  const queryParams = {
    synced_only: queryOptions.syncedOnly,
    search: queryOptions.search,
    sort_by: queryOptions.sortBy,
    sort_order: queryOptions.sortOrder,
    page: queryOptions.page,
    limit: queryOptions.limit,
  };

  // Remove empty parameters
  const cleanedParams = Object.fromEntries(
    Object.entries(queryParams).filter(([_, value]) => 
      value !== "" && value !== null && value !== undefined
    )
  );

  return useAppQuery({
    queryKey: [queryKeys.BLOG_LIST, cleanedParams],
    queryFn: async () => {
      const response = await apiClient("/blogs", {
        query: cleanedParams,
      });
      return response;
    },
    reactQueryOptions: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchOnWindowFocus: false,
      refetchInterval: false,
      ...reactQueryOptions,
    },
  });
};

/**
 * Custom hook for fetching all synced blogs (simplified version)
 * 
 * @param {import("react-query").UseQueryOptions} [reactQueryOptions] - React Query options
 * @returns {import("react-query").UseQueryResult<Blog[]>}
 */
export const useSyncedBlogs = (reactQueryOptions = {}) => {
  const { data, ...rest } = useBlogList(
    { syncedOnly: true, limit: 100 }, // Get all synced blogs
    reactQueryOptions
  );

  return {
    data: data?.blogs || [],
    ...rest,
  };
};

/**
 * Custom hook for fetching blogs with search functionality
 * 
 * @param {string} searchTerm - Search term for blog titles
 * @param {import("react-query").UseQueryOptions} [reactQueryOptions] - React Query options
 * @returns {import("react-query").UseQueryResult<Blog[]>}
 */
export const useSearchBlogs = (searchTerm = "", reactQueryOptions = {}) => {
  const { data, ...rest } = useBlogList(
    { 
      search: searchTerm,
      syncedOnly: true,
      limit: 20,
    },
    {
      enabled: searchTerm.length >= 2, // Only search if term is at least 2 characters
      ...reactQueryOptions,
    }
  );

  return {
    data: data?.blogs || [],
    ...rest,
  };
};

/**
 * Custom hook for fetching blog statistics
 * 
 * @param {import("react-query").UseQueryOptions} [reactQueryOptions] - React Query options
 * @returns {import("react-query").UseQueryResult<{total: number, synced: number, unsynced: number}>}
 */
export const useBlogStats = (reactQueryOptions = {}) => {
  const apiClient = useApiClient();

  return useAppQuery({
    queryKey: [queryKeys.BLOG_STATS],
    queryFn: async () => {
      const response = await apiClient("/blogs/stats");
      return response;
    },
    reactQueryOptions: {
      staleTime: 10 * 60 * 1000, // 10 minutes
      refetchOnWindowFocus: false,
      ...reactQueryOptions,
    },
  });
};

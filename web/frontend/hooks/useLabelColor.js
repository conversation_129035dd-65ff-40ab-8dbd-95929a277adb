const labelColors = {
  danger: "critical",
  warning: "attention",
  primary: "success",
};

export function useLabelColor(issues) {
  if (issues >= 6) return labelColors.danger;
  else if (issues >= 3) return labelColors.warning;

  return labelColors.primary;
}

/**
 * 
 * @param {number} issues 
 * @param {number} total 
 * @returns 
 */
export function useIssueBadgeColor(issues, total) {
  if(issues === 0) return labelColors.primary;
  if (issues >= (total * .50)) return labelColors.danger;
  return labelColors.warning;
}

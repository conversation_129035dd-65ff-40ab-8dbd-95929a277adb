import { Modal, Text } from "@shopify/polaris";
import { debounce, isEqual } from "lodash";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

export const useSaveChanges = (originalData, currentData) => {
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showDiscardChangeModal, setShowDiscardChangeModal] = useState(false);

  const { t } = useTranslation();

  const compareData = useCallback(
    debounce((originalData, currentData) => {
      setHasUnsavedChanges(!isEqual(originalData, currentData));
    }, 450),
    []
  );

  const DiscardChangesModal = useCallback(
    () => (
      <Modal
        open={showDiscardChangeModal}
        title={t("Discard all unsaved changes")}
        primaryAction={{
          content: t("Discard changes"),
          destructive: true,
        }}
        secondaryActions={[
          {
            content: t("Continue editing"),
          },
        ]}
      >
        <Modal.Section>
          <Text>If you discard changes, you’ll delete any edits you made since you last saved.</Text>
        </Modal.Section>
      </Modal>
    ),
    [showDiscardChangeModal]
  );

  useEffect(() => {
    compareData(originalData, currentData);
  }, [originalData, currentData]);

  return {
    hasUnsavedChanges,
    DiscardChangesModal,
  };
};

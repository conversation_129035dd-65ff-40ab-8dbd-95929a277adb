import { debounce, isEmpty } from "lodash";
import { useCallback, useEffect, useState } from "react";
import { useMutation } from "react-query";
import { useDocsApi } from "../apiHooks/useDocsApi";

export function useDocReanalysis(id, formData, docData) {
  const [score, setScore] = useState(0);
  const [optimizationData, setOptimizationData] = useState({});
  const [focusKeywordSuggestions, setFocusKeywordSuggestions] = useState([]);

  const docApi = useDocsApi();

  const { mutate: reanalyseDoc } = useMutation({
    mutationFn: () => docApi.calculateOptimizationData(id, formData),
    onSuccess: ({ score, optimizationData, focusKeywordSuggestions }) => {
      setScore(score);
      setOptimizationData(optimizationData);
      // setFocusKeywordSuggestions(focusKeywordSuggestions);
    },
  });

  const _debReanalyseDoc = useCallback(debounce(reanalyseDoc, 1200), []);

  useEffect(() => {
    if (!isEmpty(formData)) _debReanalyseDoc();
  }, [formData]);

  useEffect(
    function resetStateOnDataChange() {
      if (!isEmpty(docData)) {
        setScore(docData.doc.score);
        setOptimizationData(docData.optimizationData);
        setFocusKeywordSuggestions(docData.focusKeywordSuggestions);
      }
    },
    [docData]
  );

  return {
    score,
    optimizationData,
    focusKeywordSuggestions,
  };
}

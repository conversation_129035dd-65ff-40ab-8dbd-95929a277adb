import { getQueryFromUrlSearchParam } from "@/utility/helpers";
import queryKeys from "@/utility/queryKeys";
import { useSearchParams } from "react-router-dom";
import { useDocsApi } from "../apiHooks/useDocsApi";
import { useAppQuery } from "../useAppQuery";
import { useMutation } from "react-query";

/**
 * @param {object} reactQueryOptions
 * @returns
 */
export const useGetDocs = (reactQueryOptions = {}) => {
  const [searchParams] = useSearchParams();
  const query = getQueryFromUrlSearchParam(searchParams);
  const docApi = useDocsApi();

  return useAppQuery({
    queryKey: [queryKeys.DOCS_LIST, query],
    queryFn: async () => await docApi.getPaginatedDocsList(query),
    reactQueryOptions: {
      refetchInterval: false,
      ...reactQueryOptions,
    },
  });
};

/**
 * Custom hook to update a doc
 * @param {object} mutationOptions
 * @returns
 */
export const useUpdateDoc = (mutationOptions) => {
  const docApi = useDocsApi();
  return useMutation({
    mutationFn: ({ id, data, setErrors }) => docApi.updateSingleDoc(id, data, setErrors),
    ...mutationOptions,
  });
};

/**
 * Custom hook to get a single doc with optimization data
 * @param {{id:string|number, docQueryKey : string[]}} param
 * @param {object} reactQueryOptions
 * @returns
 */
export const useGetDocWithOptimizationData = ({ id, docQueryKey }, reactQueryOptions = {}) => {
  const docApi = useDocsApi();

  return useAppQuery({
    queryKey: docQueryKey,
    queryFn: () => docApi.getDocWithOptimizationData(id),
    reactQueryOptions: {
      staleTime: 0,
      refetchInterval: false,
      ...reactQueryOptions,
    },
  });
};

/**
 * @param {object} mutationOptions
 * @returns
 */
export const useSyncDoc = (id, mutationOptions = {}) => {
  const docApi = useDocsApi();
  return useMutation({
    mutationFn: () => docApi.syncDoc(id),
    ...mutationOptions,
  });
};

/**
 * @param {object} mutationOptions
 * @returns
 */
export const useSyncDocs = (mutationOptions) => {
  const docApi = useDocsApi();
  return useMutation({
    mutationFn: () => docApi.syncDocs(),
    ...mutationOptions,
  });
};

export const useDocCount = () => {
  const docApi = useDocsApi();
  return useAppQuery({
    queryKey: ["DOCS_COUNT"],
    queryFn: async () => await docApi.docCount(),
  });
};

export const useUpdateNoIndexStatus = (mutationOptions = {}) => {
  const docApi = useDocsApi();
  return useMutation({
    mutationFn: ({ id, noindex }) => docApi.updateDocNoIndex(id, noindex),
    ...mutationOptions,
  });
};

export const useUpdateNoFollowStatus = (mutationOptions = {}) => {
  const docApi = useDocsApi();
  return useMutation({
    mutationFn: ({ id, nofollow }) => docApi.updateDocNoFollow(id, nofollow),
    ...mutationOptions,
  });
};

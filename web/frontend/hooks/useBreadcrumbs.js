import { isEmpty } from "lodash";
import { useMemo } from "react";
import { basicBreadcrumbs, breadcrumbType } from "../config";

export function useBreadcrumbs(type, data) {
  const breadcrumbs = useMemo(() => {
    if (isEmpty(data)) return basicBreadcrumbs[type];

    const breadcrumbs = [...basicBreadcrumbs[type]];

    if (type === breadcrumbType.PRODUCT_VIEW || type === breadcrumbType.PRODUCT_EDIT)
      breadcrumbs.push({
        title: `Product ${type === breadcrumbType.PRODUCT_EDIT ? "Fix" : "Preview"}: ${data.title}`,
        url: `/products/${data.id}/edit`,
        isActive: true,
      });
    else if (type === breadcrumbType.PAGE_VIEW || type === breadcrumbType.PAGE_EDIT)
      breadcrumbs.push({
        title: `Page ${type === breadcrumbType.PAGE_EDIT ? "Fix" : "Preview"}: ${data.title}`,
        url: `/pages/${data.id}/edit`,
        isActive: true,
      });
    else if (type === breadcrumbType.ARTICLE_VIEW || type === breadcrumbType.ARTICLE_EDIT)
      breadcrumbs.push({
        title: `${type === breadcrumbType.ARTICLE_EDIT ? "Fix: " : ""} ${data.title}`,
        url: `/blogs/${data.blog_id}/articles/${data.id}/edit`,
        isActive: true,
      });

    return breadcrumbs;
  }, [type, data]);

  return breadcrumbs;
}

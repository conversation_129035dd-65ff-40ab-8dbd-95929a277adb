import { useImageOptimizeSettings } from "@/lib/hooks/image-optimizer/useImageOptimizeSettings";
import { isEmpty } from "lodash";
import { useCallback, useEffect, useState } from "react";

export function useImageOptimizationOptions(previousSettings) {
  const { settings, defaultSettings } = useImageOptimizeSettings();

  const [compressionType, setCompressionType] = useState(defaultSettings.compressionType);
  const handleCompressionTypeChange = useCallback((item) => setCompressionType(item.value), []);

  const [compressionFormat, setCompressionFormat] = useState(defaultSettings.format);
  const handleCompressionFormatChange = useCallback((item) => setCompressionFormat(item.value), []);

  const [resizeType, setResizeType] = useState(defaultSettings.resize);
  const handleResizeTypeChange = useCallback((item) => setResizeType(item), []);

  useEffect(
    function setInitialOptimizationSetting() {
      if (!isEmpty(previousSettings)) {
        const { compression_type, target_format, target_width } = previousSettings;

        setCompressionType(compression_type || compressionType);
        // setCompressionFormat(target_format || compressionFormat);
        setResizeType(target_width || resizeType);
      } else {
        setCompressionType(settings?.compressionType || compressionType);
        setCompressionFormat(settings?.format || compressionFormat);
        setResizeType(settings?.resize || resizeType);
      }
    },
    [settings, previousSettings]
  );

  return {
    compressionType,
    compressionFormat,
    resizeType,
    handleCompressionTypeChange,
    handleCompressionFormatChange,
    handleResizeTypeChange,
  };
}

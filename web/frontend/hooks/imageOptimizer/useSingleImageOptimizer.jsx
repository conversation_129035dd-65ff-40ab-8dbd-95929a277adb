import { MODAL_IDS } from "@/config";
import { isEmpty } from "lodash";
import { useCallback } from "react";
import { useMutation } from "react-query";
import ResourceType from "storeseo-enums/resourceType";
import { useImageApi } from "../apiHooks";
import useUserAddon from "../useUserAddon";
import { useImageOptimizationOptions } from "./useImageOptimzationOptions";

/**
 * @typedef {keyof Pick<typeof ResourceType, "PRODUCT" | "COLLECTION" | "PAGE" | "ARTICLE">} ImageResourceType
 */

/**
 * Function to optimize a single image.
 *
 * @param {{ onOptimize: (object) => void, onRestore: (object) => void, resourceType: ImageResourceType }} param0
 * @returns
 */
export function useSingleImageOptimizer({ onOptimize, onRestore, resourceType }) {
  const imageApi = useImageApi();

  const { compressionType, compressionFormat, resizeType } = useImageOptimizationOptions();

  const { updateImageOptimizerUsage } = useUserAddon();

  const optimizeImage = useCallback(
    (image) => {
      if (compressionType === "none" && compressionFormat === "none" && resizeType === "none") {
        return shopify.modal.show(MODAL_IDS.IMAGE_OPTIMIZER_SETTINGS);
      }

      const data = {};
      if (compressionType !== "none") data["compression_type"] = compressionType;
      if (compressionFormat !== "none") data["target_format"] = compressionFormat;
      if (resizeType !== "none") data["target_width"] = resizeType;

      if (!isEmpty(image)) {
        data["image_details"] = [
          {
            image_url: image.src,
            image_id: String(image.id),
            resource_id: image.resources[0]?.resource_id,
            media_id: image.media_id,
            title: image.title,
          },
        ];

        optimizeImageMutation(data);
      }
    },
    [compressionType, compressionFormat, resizeType, onOptimize]
  );

  const restoreImage = useCallback(
    (image) => {
      const data = {
        id: image.id,
        media_id: image.media_id,
        resource_id: image.resources[0]?.resource_id,
        originalSource: image.optimization_meta.original_image_url,
        file_size: image.optimization_meta.original_size,
      };

      restoreImageMutation([data]);
    },
    [onRestore]
  );

  const { isLoading: isRestoringImage, mutate: restoreImageMutation } = useMutation({
    mutationFn: (data) => imageApi.restoreImage(data, resourceType),
    onSuccess: (data) => {
      onRestore(data.images);
      return;
    },
  });

  const { isLoading: isOptimizingImage, mutate: optimizeImageMutation } = useMutation({
    mutationFn: (data) => imageApi.optimizeImage(data, resourceType),
    onSuccess: (data) => {
      updateImageOptimizerUsage(data.images.length);
      onOptimize(data.images);
      return;
    },
  });

  return {
    optimizeImage,
    restoreImage,
    isRestoringImage,
    isOptimizingImage,
  };
}

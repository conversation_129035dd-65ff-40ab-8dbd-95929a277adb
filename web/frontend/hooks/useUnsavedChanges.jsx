import Modal from "@/modules/components/Modal";
import { Box, Button, Text } from "@shopify/polaris";
import { debounce, isEqual } from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useBlocker } from "react-router-dom";

export const useUnsavedChanges = ({ originalData, currentData, onDiscardAction = () => {} }) => {
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showDiscardChangeModal, setShowDiscardChangeModal] = useState(false);
  const [isURLChanged, setIsURLChanged] = useState(false);

  const navigationBlocker = useBlocker(({ currentLocation, nextLocation }) => {
    return hasUnsavedChanges && currentLocation.pathname !== nextLocation.pathname;
  });

  const { t } = useTranslation();

  const compareData = useCallback(
    debounce((originalData, currentData) => {
      setHasUnsavedChanges(!isEqual(originalData, currentData));
      setIsURLChanged(!isEqual(originalData.handle, currentData.handle));
    }, 450),
    []
  );

  /**
   * @type {import("@shopify/polaris").MenuActionDescriptor}
   */
  const discardChangesMenuAction = useMemo(
    () => ({
      content: t("Discard"),
      onAction: () => setShowDiscardChangeModal(true),
      destructive: true,
      disabled: !hasUnsavedChanges,
    }),
    [hasUnsavedChanges]
  );

  const DiscardChangesButton = useCallback(
    () => (
      <Button
        tone="critical"
        onClick={() => setShowDiscardChangeModal(true)}
        disabled={!hasUnsavedChanges}
      >
        {t("Discard")}
      </Button>
    ),
    [hasUnsavedChanges]
  );

  const DiscardChangesModal = useCallback(
    () => (
      <Modal
        type="app-bridge"
        open={showDiscardChangeModal}
        setOpen={setShowDiscardChangeModal}
        onClose={() => setShowDiscardChangeModal(false)}
      >
        <Modal.Section>
          <Box padding="400">
            <Text>{t("If you discard changes, you'll delete any edits you made since you last saved.")}</Text>
          </Box>
          <Modal.TitleBar title={t("Discard all unsaved changes")}>
            <button
              variant="primary"
              tone="critical"
              onClick={() => {
                setShowDiscardChangeModal(false);
                onDiscardAction();
              }}
            >
              {t("Discard changes")}
            </button>
            <button onClick={() => setShowDiscardChangeModal(false)}>{t("Continue editing")}</button>
          </Modal.TitleBar>
        </Modal.Section>
      </Modal>
    ),
    [showDiscardChangeModal]
  );

  const NavigationBlockerModal = useCallback(
    () => (
      <Modal
        type="app-bridge"
        open={navigationBlocker.state === "blocked"}
        setOpen={(open) => {
          if (!open) navigationBlocker.reset();
        }}
        onClose={() => navigationBlocker.reset()}
      >
        <Modal.Section>
          <Box padding="400">
            <Text>{t("Leaving this page will delete all unsaved changes.")}</Text>
          </Box>
          <Modal.TitleBar title={t("Leave page with unsaved changes?")}>
            <button
              variant="primary"
              tone="critical"
              onClick={() => navigationBlocker.proceed()}
            >
              {t("Leave page")}
            </button>
            <button onClick={() => navigationBlocker.reset()}>{t("Stay")}</button>
          </Modal.TitleBar>
        </Modal.Section>
      </Modal>
    ),
    [navigationBlocker.state]
  );

  /**
   * @type {EventListener}
   */
  const beforeUnloadHandler = useCallback((event) => {
    event.preventDefault();

    // Included for legacy support, e.g. Chrome/Edge < 119
    event.returnValue = true;
  }, []);

  useEffect(() => {
    if (hasUnsavedChanges) window.addEventListener("beforeunload", beforeUnloadHandler);
    else window.removeEventListener("beforeunload", beforeUnloadHandler);
  }, [hasUnsavedChanges]);

  useEffect(() => {
    compareData(originalData, currentData);
  }, [originalData, currentData]);

  return {
    isURLChanged,
    hasUnsavedChanges,
    setHasUnsavedChanges,
    discardChangesMenuAction,
    DiscardChangesModal,
    DiscardChangesButton,
    NavigationBlockerModal,
    setShowDiscardChangeModal,
  };
};

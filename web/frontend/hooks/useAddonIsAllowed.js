import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import planStatus from "storeseo-enums/planStatus";

export function useAddonIsAllowed(planData) {
  const [addonIsAllowed, setAddonIsAllowed] = useState(false);
  const user = useSelector((s) => s.user);

  useEffect(() => {
    if (planData.status === planStatus.HIDDEN) {
      setAddonIsAllowed(!(planData.isFree && user.isPremium));
    } else {
      setAddonIsAllowed(false);
    }
  }, [planData, user]);

  return addonIsAllowed;
  // return false;
}

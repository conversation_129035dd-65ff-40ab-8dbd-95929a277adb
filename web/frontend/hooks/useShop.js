import { useSelector } from "react-redux";
import { useMultiLanguageSetting } from "./useMultiLanguageSetting";

export default function useShop() {
  const user = useSelector((state) => state.user);
  const { url } = user;

  const { usingMultiLanguageMode, selectedLanguage } = useMultiLanguageSetting();

  const productURLPrefix = usingMultiLanguageMode ? `${url}/${selectedLanguage}/products/` : `${url}/products/`;
  const collectionUrlPrefix = `${url}/collections`;

  const pageURLPrefix = `${url}/pages/`;
  const articleURLPrefix = `${url}/articles/`;
  const collectionURLPrefix = `${url}/collections/`;
  const docURLPrefix = `${url}/docs`;

  return {
    productURLPrefix,
    collectionUrlPrefix,
    pageURLPrefix,
    articleURLPrefix,
    collectionURLPrefix,
    docURLPrefix,
  };
}

import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import { menus } from "../config";

export const useMenuItems = () => {
  const user = useSelector((state) => state.user);
  const location = useLocation();
  const [menuItems, setMenuItems] = useState(() => menus);

  useEffect(() => {
    if (!user?.isSubscribed) {
      setMenuItems(menus.filter((m) => m.label === "Home"));
      return;
    }

    setMenuItems(menus);
  }, [user, location.pathname]);

  return menuItems;
};

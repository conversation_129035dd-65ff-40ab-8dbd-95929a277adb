import { useState, useCallback } from "react";

/**
 * Blog Auto-Write Modal Hook
 * 
 * Handles modal state management including open/close state,
 * current step tracking, and form data persistence.
 * Focused solely on modal UI state.
 */
export function useBlogAutoWriteModal() {
  const [isOpen, setIsOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState(null);

  // Modal controls
  const openModal = useCallback(() => {
    setIsOpen(true);
    setCurrentStep(1);
    setFormData(null);
  }, []);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setCurrentStep(1);
    setFormData(null);
  }, []);

  // Step navigation
  const goToStep = useCallback((step) => {
    setCurrentStep(step);
  }, []);

  const nextStep = useCallback(() => {
    setCurrentStep((prev) => prev + 1);
  }, []);

  const previousStep = useCallback(() => {
    setCurrentStep((prev) => Math.max(1, prev - 1));
  }, []);

  // Form data management
  const updateFormData = useCallback((data) => {
    setFormData(data);
  }, []);

  return {
    // Modal state
    isOpen,
    currentStep,
    formData,
    
    // Modal controls
    openModal,
    closeModal,
    
    // Step navigation
    goToStep,
    nextStep,
    previousStep,
    
    // Form data
    updateFormData,
  };
}

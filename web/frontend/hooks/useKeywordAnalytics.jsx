import { debounce } from "lodash";
import { useCallback, useEffect, useState } from "react";
import { useAppQuery } from "./useAppQuery";
import queryKeys from "../utility/queryKeys";
import { useUtilityApi } from "./apiHooks";

export const useKeywordAnalytics = (keyword) => {
  const [targetKeyword, setTargetKeyword] = useState(keyword);

  const _debSetTargetKeyword = useCallback(debounce(setTargetKeyword, 1200), []);

  useEffect(() => {
    _debSetTargetKeyword(keyword);
  }, [keyword]);

  const utilityApi = useUtilityApi();
  const { data: keywordAnalytics } = useAppQuery({
    queryKey: [queryKeys.KEYWORD_METRICS, { keyword: targetKeyword }],
    queryFn: () => utilityApi.getKeywordMetrics(targetKeyword),
    reactQueryOptions: {
      enabled: targetKeyword?.length > 0,
      staleTime: Infinity,
      cacheTime: Infinity,
      refetchInterval: false,
    },
  });

  return { keywordAnalytics };
};

// // @ts-check
import { useMutation } from "react-query";
import { useSearchParams } from "react-router-dom";
import { getQueryFromUrlSearchParam } from "../../utility/helpers";
import queryKeys from "../../utility/queryKeys";
import { useCollectionApi } from "../apiHooks/useCollectionApi";
import { useAppQuery } from "../useAppQuery";

/**
 * Custom hook to get paginated list of collections
 * @param {object} reactQueryOptions
 * @returns
 */
export const useGetCollections = (reactQueryOptions = {}) => {
  const [searchParams] = useSearchParams();
  const query = getQueryFromUrlSearchParam(searchParams);
  const collectionApi = useCollectionApi();

  return useAppQuery({
    queryKey: [queryKeys.COLLECTIONS_LIST, query],
    queryFn: async () => await collectionApi.getPaginatedCollectionsList(query),
    reactQueryOptions: {
      refetchInterval: false,
      ...reactQueryOptions,
    },
  });
};

/**
 * Custom hook to sync collections from shopify
 * @param {object} mutationOptions
 * @returns
 */
export const useSyncCollections = (mutationOptions) => {
  const collectionApi = useCollectionApi();
  return useMutation({
    mutationFn: () => collectionApi.syncCollectionsFromShopify(),
    ...mutationOptions,
  });
};

/**
 * Custom hook to get a single collection with optimization data
 * @param {{id:string|number, collectionQueryKey : string[]}} param0
 * @param {object} reactQueryOptions
 * @returns
 */
export const useGetCollectionsWithOptimizationData = ({ id, collectionQueryKey }, reactQueryOptions = {}) => {
  const collectionApi = useCollectionApi();

  return useAppQuery({
    queryKey: collectionQueryKey,
    queryFn: () => collectionApi.getCollectionWithOptimizationData(id),
    reactQueryOptions: {
      staleTime: 0,
      refetchInterval: false,
      ...reactQueryOptions,
    },
  });
};

/**
 * Custom hook to update a collection
 * @param {object} mutationOptions
 * @returns
 */
export const useUpdateCollection = (mutationOptions) => {
  const collectionApi = useCollectionApi();
  return useMutation({
    mutationFn: ({ id, data, setErrors }) => collectionApi.updateSingleCollection(id, data, setErrors),
    ...mutationOptions,
  });
};

/**
 * Custom hook to get total count of shopify collections
 * @returns
 */
export const useGetShopifyCollectionsCount = () => {
  const collectionApi = useCollectionApi();
  return useAppQuery({
    queryKey: queryKeys.TOTAL_SHOPIFY_COLLECTIONS_COUNT,
    queryFn: collectionApi.getTotalShopifyCollectionsCount,
  });
};

/**
 * Custom hook to update collection index status
 * @param {*} mutationOptions
 * @returns
 */
export const useUpdateCollectionIndexStatus = (mutationOptions) => {
  const collectionApi = useCollectionApi();
  return useMutation({
    mutationFn: (id) => collectionApi.updateCollectionIndexStatus(id),
    ...mutationOptions,
  });
};

/**
 * Custom hook to update collection index status
 * @param {*} mutationOptions
 * @returns
 */
export const useUpdateCollectionFollowStatus = (mutationOptions) => {
  const collectionApi = useCollectionApi();
  return useMutation({
    mutationFn: (id) => collectionApi.updateCollectionFollowStatus(id),
    ...mutationOptions,
  });
};

export const useGetCollectionsSitemaps = ({ query }, reactQueryOptions = {}) => {
  const collectionApi = useCollectionApi();
  return useAppQuery({
    queryKey: [queryKeys.COLLECTIONS_SITEMAP_LIST, query],
    queryFn: async () => await collectionApi.getPaginatedSitemapList(query),
    reactQueryOptions: {
      staleTime: 0,
      ...reactQueryOptions,
    },
  });
};

/**
 * Custom hook to update collection sitemap status
 * @param {*} mutationOptions
 * @returns
 */
export const useUpdateCollectionSitemapStatus = (mutationOptions) => {
  const collectionApi = useCollectionApi();
  return useMutation({
    mutationFn: ({ id, status }) => collectionApi.updateCollectionSitemapStatus(id, status),
    ...mutationOptions,
  });
};

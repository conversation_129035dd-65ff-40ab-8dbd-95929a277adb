import { debounce, isEmpty } from "lodash";
import { useCallback, useEffect, useState } from "react";
import { useMutation } from "react-query";
import { useCollectionApi } from "../apiHooks/useCollectionApi";

export function useCollectionReanalysis(id, formData, collectionData) {
  const [score, setScore] = useState(0);
  const [optimizationData, setOptimizationData] = useState({});
  const [focusKeywordSuggestions, setFocusKeywordSuggestions] = useState([]);

  const collectionApi = useCollectionApi();
  const { mutate: reanalyseCollection } = useMutation({
    mutationFn: () => collectionApi.calculateOptimizationData(id, formData),
    onSuccess: ({ score, optimizationData, focusKeywordSuggestions }) => {
      setScore(score);
      setOptimizationData(optimizationData);
      // setFocusKeywordSuggestions(focusKeywordSuggestions);
    },
  });

  const _debReanalyseCollection = useCallback(debounce(reanalyseCollection, 1200), []);

  useEffect(() => {
    if (!isEmpty(formData)) _debReanalyseCollection();
  }, [formData]);

  useEffect(
    function resetStateOnDataChange() {
      if (!isEmpty(collectionData)) {
        setScore(collectionData.collection.score);
        setOptimizationData(collectionData.optimizationData);
        setFocusKeywordSuggestions(collectionData.focusKeywordSuggestions);
      }
    },
    [collectionData]
  );

  return {
    score,
    optimizationData,
    focusKeywordSuggestions,
  };
}

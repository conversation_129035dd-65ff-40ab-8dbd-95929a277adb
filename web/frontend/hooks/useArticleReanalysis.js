import { debounce, isEmpty } from "lodash";
import { useCallback, useEffect, useState } from "react";
import { useMutation } from "react-query";
import { useBlogApi } from "./apiHooks/useBlogApi";

export function useArticleReanalysis(id, formData, apiData) {
  const [score, setScore] = useState(0);
  const [optimizationData, setOptimizationData] = useState({});
  const [focusKeywordSuggestions, setFocusKeywordSuggestions] = useState([]);

  const blogApi = useBlogApi();
  const { mutate: reanalyseProduct } = useMutation({
    mutationFn: () => blogApi.calculateOptimizationData(id, formData),
    onSuccess: ({ score, optimizationData, focusKeywordSuggestions }) => {
      setScore(score);
      setOptimizationData(optimizationData);
      // setFocusKeywordSuggestions(focusKeywordSuggestions);
    },
  });

  const _debReanalyseArticle = useCallback(debounce(reanalyseProduct, 1200), []);

  useEffect(() => {
    if (!isEmpty(formData)) _debReanalyseArticle();
  }, [formData]);

  useEffect(
    function resetStateOnDataChange() {
      if (!isEmpty(apiData)) {
        setScore(apiData.article.score);
        setOptimizationData(apiData.optimizationData);
        setFocusKeywordSuggestions(apiData.focusKeywordSuggestions);
      }
    },
    [apiData]
  );

  return {
    score,
    optimizationData,
    focusKeywordSuggestions,
  };
}

import { useEffect, useState } from "react";

export const useGeneralSeoErrors = (formData) => {
  const [errors, setErrors] = useState({});

  useEffect(
    function removeTitleError() {
      const { metaTitle } = formData;
      const errs = { ...errors };

      if (metaTitle?.length) delete errs.metaTitle;

      setErrors(errs);
    },
    [formData.metaTitle]
  );

  useEffect(
    function removeDescriptionError() {
      const { metaDescription } = formData;
      const errs = { ...errors };

      if (metaDescription?.length) delete errs.metaDescription;

      setErrors(errs);
    },
    [formData.metaDescription]
  );

  useEffect(
    function removeFocusKeywordError() {
      const { focusKeyword } = formData;
      const errs = { ...errors };

      if (focusKeyword?.length) delete errs.focusKeyword;

      setErrors(errs);
    },
    [formData.focusKeyword]
  );

  return {
    errors,
    setErrors,
  };
};

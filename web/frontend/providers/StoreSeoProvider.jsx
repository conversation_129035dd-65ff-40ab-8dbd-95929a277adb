import { useDispatch, useSelector } from "react-redux";
import { Link, useLocation, useNavigate, useSearchParams } from "react-router-dom";
import { useAppQuery, useMenuItems } from "../hooks/index.js";

import CheckoutModal from "@/components/checkout/CheckoutModal.jsx";
import PurchaseModal from "@/components/checkout/PurchaseModal.jsx";
import GlobalNotifications from "@/components/notifications/GlobalNotifications.jsx";
import { useBackupRestoreStatus } from "@/lib/hooks/settings/backup-restore/index.jsx";
import { NavMenu } from "@shopify/app-bridge-react";
import { isEmpty } from "lodash";
import { createContext, Suspense, useCallback, useContext, useEffect, useRef, useState } from "react";
import { browserEvents, emitter } from "../config/index.js";
import { useUserApi } from "../hooks/apiHooks/useUserApi.js";
import { useThirdPartyScripts } from "../hooks/useThirdPartyScripts.js";
import { setBlogSyncStatus } from "../store/features/BlogSync.js";
import { setCampaign } from "../store/features/Campaign.js";
import { setCollectionSyncStatus } from "../store/features/CollectionSync.js";
import { setAllBanner } from "../store/features/HiddenBanner.js";
import { setUnreadNotificationsCount } from "../store/features/Notifications.js";
import { setOptimizationTaskOngoing } from "../store/features/OptimizationTask.js";
import { setPageSyncStatus } from "../store/features/PageSync.js";
import { setProductSyncStatus } from "../store/features/ProductSync.js";
import { updateUser } from "../store/features/User.js";
import { scrollToTop } from "../utility/helpers.jsx";
import queryKeys from "../utility/queryKeys.js";
import PusherProvider from "./PusherProvider.jsx";

const StoreSeoContext = createContext({ loadLowPriorityContent: false, doManualRefresh: false });

/**
 *
 * @returns {{ doManualRefresh: boolean, loadLowPriorityContent: boolean }}
 */
export const useStoreSeo = () => useContext(StoreSeoContext);

export default function StoreSeoProvider({ children }) {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const userApi = useUserApi();
  const user = useSelector((state) => state.user);
  const checkout = useSelector((state) => state.checkout);
  const purchase = useSelector((state) => state.purchase);
  const [searchParams, setSearchParams] = useSearchParams();
  const refreshParam = searchParams.get("refresh");
  const doManualRefresh = refreshParam === "1";

  const [loadLowPriorityContent, setLoadLowPriorityContent] = useState(false);
  const [thirdPartyScriptsLoaded, setThirdPartyScriptsLoaded] = useState(false);
  const [appContentDisplayed, setAppContentDisplayed] = useState(false);

  const displayAppContent = useCallback(() => {
    if (appContentDisplayed) return;

    // document.getElementById("static-loader").remove();
    // document.getElementById("static-loader").style.opacity = "0";
    // document.getElementById("app").style.display = "initial";

    const staticLoaderElement = document.getElementById("static-loader");
    // const staticLoaderHeight = staticLoaderElement.offsetHeight;
    const appElement = document.getElementById("storeseo-wrapper");
    appElement.style.transition = "all .35s ease-in-out";

    appElement.style.translate = "none";
    // appElement.style.zIndex = "9";
    // appElement.style.transform = `translateY(2500px)`;

    setTimeout(() => {
      appElement.style.zIndex = "1";
      appElement.style.opacity = "1";

      if (staticLoaderElement) staticLoaderElement.style.zIndex = "-9";

      appElement.click();
    }, 350);

    setTimeout(() => staticLoaderElement?.remove(), 1000);

    setTimeout(() => setLoadLowPriorityContent(true), 1600);

    setAppContentDisplayed(true);
  }, []);

  useEffect(function listenForPageDataLoadEvent() {
    emitter.on(browserEvents.PAGE_DATA_LOADED, displayAppContent);

    return () => emitter.off(browserEvents.PAGE_DATA_LOADED, displayAppContent);
  }, []);

  useEffect(function triggerAppContentDisplayAfterTimeout() {
    const delay = location.pathname.match(/(?:(products)|(?:pages)|(?:collections)|(?:articles))\/.*/gim) ? 5500 : 950;
    setTimeout(displayAppContent, delay);
  }, []);

  useEffect(
    function preloadSettingsLayout() {
      if (!loadLowPriorityContent) return;

      import("../layouts/SettingsLayout");
    },
    [loadLowPriorityContent]
  );

  const lastHash = useRef("");

  useEffect(() => {
    if (location.hash) {
      lastHash.current = location.hash.slice(1); // safe hash for further use after navigation
    }

    if (lastHash.current && document.getElementById(lastHash.current)) {
      setTimeout(() => {
        document.getElementById(lastHash.current)?.scrollIntoView({ behavior: "smooth", block: "start" });
        lastHash.current = "";
      }, 100);
    }
  }, [location]);

  useEffect(
    function scrollToTopOnRouteChange() {
      scrollToTop();
    },
    [location.pathname]
  );

  useEffect(() => {
    if (!isEmpty(user)) {
      // const subsRegex = /\/(checkout|subscription)\/\w+/;
      // if (!location.pathname.match(subsRegex)) {
      //   if (location.pathname !== "/subscription" && !user.isSubscribed) {
      //     navigate("/subscription");
      //     return;
      //   }
      // }

      if (!user.isSubscribed && !["/onboarding", "/onboarding/pricing"].includes(location.pathname)) {
        navigate(`/onboarding`);
      }
      // const onboardRegex = /^\/onboarding(\/(pricing))?$/gi;
      // if (location.pathname.match(onboardRegex)?.length !== undefined) {
      //   navigate("/onboarding");
      // }
    }
  }, [user, location.pathname]);

  useEffect(() => {
    const params = Object.fromEntries(searchParams.entries());
    delete params.refresh;
    setSearchParams(params, { replace: true });
  }, [doManualRefresh]);

  const { data } = useAppQuery({
    queryKey: [queryKeys.AUTH_USER],
    queryFn: () => userApi.getAuthUserData(),
    reactQueryOptions: {
      keepPreviousData: true,
    },
  });

  const { data: checkStatus, refetch: fetchBetterDocAppInstallationStatus } = useAppQuery({
    queryKey: [queryKeys.BETTERDOCS_INSTALL_STATUS],
    queryFn: () => userApi.getBetterDocsAppInstallStatus(),
    reactQueryOptions: {
      keepPreviousData: true,
      enabled: false,
    },
  });

  const { data: storeBackupRestoreStatus } = useBackupRestoreStatus({
    queryKey: [queryKeys.BACKUP_RESTORE_STATUS],
  });

  useEffect(() => {
    if (data) {
      dispatch(updateUser(data.user));
      dispatch(setCampaign(data.campaign));
      dispatch(setProductSyncStatus(data.user.productSyncOngoing));
      dispatch(setCollectionSyncStatus(data.user.collectionSyncOngoing));
      dispatch(setBlogSyncStatus(data.user.blogSyncOngoing));
      dispatch(setPageSyncStatus(data.user.pageSyncOngoing));
      dispatch(setUnreadNotificationsCount(data.unreadNotifications));
      dispatch(setOptimizationTaskOngoing(data.optimizationTaskOngoing));
      // dispatch(setOnboardStep(data.user.onboardStep));
      dispatch(setAllBanner(data.hiddenBanner));
      dispatch(
        updateUser({
          betterDocsInstallationStatus: checkStatus?.status || false,
          backupRestoreStatus: storeBackupRestoreStatus,
        })
      );
      // dispatch(setDocSyncStatus(data.user.docSyncOngoing));
    }
  }, [data, checkStatus, storeBackupRestoreStatus]);

  const { crisp, googleAnalytics, clarityTracking } = useThirdPartyScripts();
  useEffect(() => {
    if (!loadLowPriorityContent || thirdPartyScriptsLoaded) return;

    // window.Userback = window.Userback || {};
    // Userback.access_token = "6133|60336|7fdtokaCkPgvcaECH57NG8UCNx9HT9xZTdOrZI5Oy1FJTZkOSK";
    // (function (d) {
    //   let s = d.createElement("script");
    //   s.async = true;
    //   s.src = "https://static.userback.io/widget/v1.js";
    //   (d.head || d.body).appendChild(s);
    // })(document);

    crisp(user);
    googleAnalytics();
    clarityTracking(user);
    setThirdPartyScriptsLoaded(true);

    const betterDocInstallationStatus = setTimeout(fetchBetterDocAppInstallationStatus, 2500);

    () => {
      clearTimeout(betterDocInstallationStatus);
    };
  }, [user, loadLowPriorityContent]);

  const navigationLinks = useMenuItems();

  const shoAppNavMenu = !!location.pathname.match(/^(?!\/login|\/exitIframe).*$/);

  // Support extension registration
  // This is used to open the chat window when the support extension is loaded
  useEffect(() => {
    const handler = () => {
      $crisp.push(["do", "chat:open"]);
    };

    shopify.support.registerHandler(handler);
  }, []);

  return (
    <>
      {shoAppNavMenu && (
        <NavMenu>
          {navigationLinks.map((link, index) => {
            return (
              <Link
                key={index}
                to={link.destination}
                rel={link.label === "Home" ? "home" : ""}
              >
                {link.label}
              </Link>
            );
          })}
        </NavMenu>
      )}
      <Suspense fallback={<></>}>
        <StoreSeoContext.Provider value={{ loadLowPriorityContent, doManualRefresh }}>
          <PusherProvider>
            {children}
            {loadLowPriorityContent && (
              <>
                {purchase?.show && <PurchaseModal />}
                {checkout?.slug && <CheckoutModal />}
                <GlobalNotifications />
              </>
            )}
          </PusherProvider>
        </StoreSeoContext.Provider>
      </Suspense>
    </>
  );
}

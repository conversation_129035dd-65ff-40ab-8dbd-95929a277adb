//@ts-check
import { createContext, useCallback, useContext, useState } from "react";
import { useQueryClient } from "react-query";
import imageOptimization from "storeseo-enums/imageOptimization";

/**
 * @template T
 * @typedef {import("react").Dispatch<T>} Dispatch
 */

/**
 * @template T
 * @typedef {import("react").SetStateAction<T>} SetStateAction
 */

/**
 * @typedef {{
 * isLoading: boolean,
 * setIsLoading: Dispatch<SetStateAction<boolean>>,
 * countSelectableImages: (images: Array<object>) => number,
 * DUMMY_IMAGES: Array<object>,
 * handleImagesUpdate: (images: Array<object>, queryKey: Array<string> | string) => void,
 * imageToCompare: object | null,
 * setImageToCompare: Dispatch<SetStateAction<object | null>>,
 * }} ImageOptimizerLayoutContext
 *
 * @type {import("react").Context<ImageOptimizerLayoutContext> | null}
 */
const ImageOptimizerLayoutContext = createContext(null);

// Constants
const defaultRowItem = { id: null, title: null, focus_keyword: null, issues: null, score: null, created_at: null };
const DUMMY_IMAGES = new Array(10).fill(defaultRowItem).map((p) => ({
  ...p,
}));

export default function ImageOptimizerLayoutProvider({ ...props }) {
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(false);
  const [imageToCompare, setImageToCompare] = useState(null);

  // Update the images in the query cache
  const handleImagesUpdate = useCallback((updatedImages, queryKey) => {
    const updatedImageHash = updatedImages.reduce(
      (hash, img) => ({
        ...hash,
        [img.id]: img,
      }),
      {}
    );
    // @ts-ignore
    const { images, pagination } = queryClient.getQueryData(queryKey);
    const updatedImageList = images.map((img) => {
      if (!updatedImageHash[img.id]) {
        return img;
      } else {
        return {
          ...updatedImageHash[img.id],
        };
      }
    });

    queryClient.setQueryData(queryKey, { images: updatedImageList, pagination });
  }, []);

  // Count the number of selectable images
  const countSelectableImages = useCallback((images) => {
    const filteredImages = images?.filter((img) => img.optimization_status !== imageOptimization.PENDING) || [];

    return filteredImages.length;
  }, []);

  // Context value
  const value = {
    isLoading,
    setIsLoading,
    countSelectableImages,
    DUMMY_IMAGES,
    handleImagesUpdate,
    imageToCompare,
    setImageToCompare,
  };

  return (
    <ImageOptimizerLayoutContext.Provider
      value={value}
      {...props}
    />
  );
}

// Custom hook to use the ImageOptimizerLayoutContext
export const useImageOptimizerLayoutContext = () => {
  const context = useContext(ImageOptimizerLayoutContext);
  if (!context) {
    throw new Error("useImageOptimizerLayoutContext must be used within an ImageOptimizerLayoutProvider");
  }
  return context;
};

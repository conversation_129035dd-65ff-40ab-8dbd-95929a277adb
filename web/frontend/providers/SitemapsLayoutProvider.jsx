import { createContext, useContext, useState } from "react";

const SitemapLayoutContext = createContext(null);

export default function SitemapsLayoutProvider({ ...props }) {
  const [isLoading, setIsLoading] = useState(false);

  const value = { isLoading, setIsLoading };
  return (
    <SitemapLayoutContext.Provider
      value={value}
      {...props}
    />
  );
}

export const useSitemapLayoutContext = () => {
  const context = useContext(SitemapLayoutContext);
  if (!context) {
    throw new Error("useSitemapLayoutContext must be used within a SitemapsLayoutProvider");
  }
  return context;
};

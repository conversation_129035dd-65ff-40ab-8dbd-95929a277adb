enum SocketEvents {
  PRODUCT_SYNC_UPDATE = "PRODUCT_SYNC_UPDATE",
  PRODUCT_SYNC_COMPLETE = "PRODUCT_SYNC_COMPLETE",
  PRODUCT_OPTIMIZATION_COMPLETE = "PRODUCT_OPTIMIZATION_COMPLETE",

  PAGE_SYNC_UPDATE = "PAGE_SYNC_UPDATE",
  PAGE_SYNC_COMPLETE = "PAGE_SYNC_COMPLETE",

  BLOG_SYNC_UPDATE = "BLOG_SYNC_UPDATE",
  BLOG_SYNC_COMPLETE = "BLOG_SYNC_COMPLETE",

  JOIN_ROOM = "JOIN_ROOM",
  LEAVE_ROOM = "LEAVE_ROOM",

  NEW_NOTIFICATION = "NEW_NOTIFICATION",
  NOTIFICATION_READ = "NOTIFICATION_READ",
  NOTIFICATION_ALL_READ = "NOTIFICATION_ALL_READ",

  COLLECTION_SYNC_UPDATE = "COLLECTION_SYNC_UPDATE",
  COLLECTION_SYNC_COMPLETE = "COLLECTION_SYNC_COMPLETE",
  COLLECTION_PRODUCTS_SYNC_COMPLETE = "COLLECTION_PRODUCTS_SYNC_COMPLETE",

  DOC_SYNC_UPDATE = "DOC_SYNC_UPDATE",
  DOC_SYNC_COMPLETE = "DOC_SYNC_COMPLETE",

  MULTI_LANGUAGE_PRODUCTS_SYNCED = "MULTI_LANGUAGE_PRODUCTS_SYNCED",

  BACKUP_INITIALIZED = "BACKUP_INITIALIZED",
  BACKUP_COMPLETED = "BACKUP_COMPLETED",
  BACKUP_RESTORE_INITIALIZED = "BACKUP_RESTORE_INITIALIZED",
  BACKUP_RESTORE_COMPLETED = "BACKUP_RESTORE_COMPLETED",

  // Bulk operation related events
  IMAGE_ALT_OPTIMIZATION_BULK_OP_COMPLETE = "IMAGE_ALT_OPTIMIZATION_BULK_OP_COMPLETE",
  IMAGE_OPTIMIZATION_BULK_OP_COMPLETE = "IMAGE_OPTIMIZATION_BULK_OP_COMPLETE",
  PRODUCT_AI_OPTIMIZATION_BULK_OP_COMPLETE = "PRODUCT_AI_OPTIMIZATION_BULK_OP_COMPLETE",
  COLLECTION_AI_OPTIMIZATION_BULK_OP_COMPLETE = "COLLECTION_AI_OPTIMIZATION_BULK_OP_COMPLETE",
  ARTICLE_AI_OPTIMIZATION_BULK_OP_COMPLETE = "ARTICLE_AI_OPTIMIZATION_BULK_OP_COMPLETE",

  // LLMs.txt Generator related events
  LLMS_TXT_GENERATION_COMPLETE = "LLMS_TXT_GENERATION_COMPLETE",
  LLMS_TXT_GENERATION_FAILED = "LLMS_TXT_GENERATION_FAILED",
}

export = SocketEvents;

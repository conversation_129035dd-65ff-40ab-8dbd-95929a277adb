"use strict";
var BlogType;
(function (BlogType) {
    BlogType["PRODUCT_BASED"] = "Product-based";
    BlogType["GUIDE"] = "Guide";
    BlogType["LISTICLE"] = "Listicle";
    BlogType["INFORMATIONAL"] = "Informational";
    BlogType["NEWS"] = "News";
    BlogType["SEASONAL"] = "Seasonal";
})(BlogType || (BlogType = {}));
(function (BlogType) {
    BlogType.labels = {
        [BlogType.PRODUCT_BASED]: "Product-based",
        [BlogType.GUIDE]: "Guide",
        [BlogType.LISTICLE]: "Listicle",
        [BlogType.INFORMATIONAL]: "Informational",
        [BlogType.NEWS]: "News",
        [BlogType.SEASONAL]: "Seasonal",
    };
    BlogType.descriptions = {
        [BlogType.PRODUCT_BASED]: "Blog posts that focus on specific products or product categories",
        [BlogType.GUIDE]: "Step-by-step guides and how-to content",
        [BlogType.LISTICLE]: "List-based articles (e.g., '10 Best...', 'Top 5...')",
        [BlogType.INFORMATIONAL]: "Educational content that provides valuable information",
        [BlogType.NEWS]: "News updates and industry announcements",
        [BlogType.SEASONAL]: "Content related to seasons, holidays, or special events",
    };
})(BlogType || (BlogType = {}));
module.exports = BlogType;

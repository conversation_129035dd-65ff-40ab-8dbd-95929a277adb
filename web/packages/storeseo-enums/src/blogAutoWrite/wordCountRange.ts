enum WordCountRange {
  UP_TO_500 = "Up to 500",
  RANGE_500_800 = "500-800",
  RANGE_800_1200 = "800-1200",
  OVER_1200 = "1200+",
}

namespace WordCountRange {
  export const labels: Record<WordCountRange, string> = {
    [WordCountRange.UP_TO_500]: "Up to 500 words",
    [WordCountRange.RANGE_500_800]: "500-800 words",
    [WordCountRange.RANGE_800_1200]: "800-1200 words",
    [WordCountRange.OVER_1200]: "1200+ words",
  };

  export const descriptions: Record<WordCountRange, string> = {
    [WordCountRange.UP_TO_500]: "Short-form content, ideal for quick reads and social media",
    [WordCountRange.RANGE_500_800]: "Medium-length content, good for most blog topics",
    [WordCountRange.RANGE_800_1200]: "Long-form content, comprehensive coverage of topics",
    [WordCountRange.OVER_1200]: "In-depth content, detailed guides and extensive coverage",
  };

  export const estimatedReadingTime: Record<WordCountRange, string> = {
    [WordCountRange.UP_TO_500]: "1-2 minutes",
    [WordCountRange.RANGE_500_800]: "2-3 minutes",
    [WordCountRange.RANGE_800_1200]: "3-5 minutes",
    [WordCountRange.OVER_1200]: "5+ minutes",
  };
}

export = WordCountRange;

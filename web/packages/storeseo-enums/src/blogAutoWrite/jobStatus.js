"use strict";
var BlogAutoWriteJobStatus;
(function (BlogAutoWriteJobStatus) {
    BlogAutoWriteJobStatus["PENDING"] = "pending";
    BlogAutoWriteJobStatus["GENERATING_CONTENT"] = "generating_content";
    BlogAutoWriteJobStatus["CREATING_DRAFT"] = "creating_draft";
    BlogAutoWriteJobStatus["LINKING_ARTICLE"] = "linking_article";
    BlogAutoWriteJobStatus["ANALYZING_SEO"] = "analyzing_seo";
    BlogAutoWriteJobStatus["GENERATING_IMAGE"] = "generating_image";
    BlogAutoWriteJobStatus["UPLOADING_IMAGE"] = "uploading_image";
    BlogAutoWriteJobStatus["UPDATING_ARTICLE"] = "updating_article";
    BlogAutoWriteJobStatus["FINALIZING_SEO"] = "finalizing_seo";
    BlogAutoWriteJobStatus["PUBLISHING"] = "publishing";
    BlogAutoWriteJobStatus["COMPLETED"] = "completed";
    BlogAutoWriteJobStatus["FAILED"] = "failed";
    BlogAutoWriteJobStatus["CANCELLED"] = "cancelled";
})(BlogAutoWriteJobStatus || (BlogAutoWriteJobStatus = {}));
(function (BlogAutoWriteJobStatus) {
    BlogAutoWriteJobStatus.labels = {
        [BlogAutoWriteJobStatus.PENDING]: "Pending",
        [BlogAutoWriteJobStatus.GENERATING_CONTENT]: "Preparing content",
        [BlogAutoWriteJobStatus.CREATING_DRAFT]: "Saving a draft to your Shopify blog",
        [BlogAutoWriteJobStatus.LINKING_ARTICLE]: "Structuring your blog",
        [BlogAutoWriteJobStatus.ANALYZING_SEO]: "Optimizing for SEO",
        [BlogAutoWriteJobStatus.GENERATING_IMAGE]: "Generating Featured Image",
        [BlogAutoWriteJobStatus.UPLOADING_IMAGE]: "Uploading Image to Shopify",
        [BlogAutoWriteJobStatus.UPDATING_ARTICLE]: "Updating Article with Image",
        [BlogAutoWriteJobStatus.FINALIZING_SEO]: "Finalizing details",
        [BlogAutoWriteJobStatus.PUBLISHING]: "Saving",
        [BlogAutoWriteJobStatus.COMPLETED]: "Completed",
        [BlogAutoWriteJobStatus.FAILED]: "Failed",
        [BlogAutoWriteJobStatus.CANCELLED]: "Cancelled",
    };
    BlogAutoWriteJobStatus.progressMapping = {
        [BlogAutoWriteJobStatus.PENDING]: 0,
        [BlogAutoWriteJobStatus.GENERATING_CONTENT]: 30,
        [BlogAutoWriteJobStatus.CREATING_DRAFT]: 40,
        [BlogAutoWriteJobStatus.LINKING_ARTICLE]: 45,
        [BlogAutoWriteJobStatus.ANALYZING_SEO]: 55,
        [BlogAutoWriteJobStatus.GENERATING_IMAGE]: 75,
        [BlogAutoWriteJobStatus.UPLOADING_IMAGE]: 80,
        [BlogAutoWriteJobStatus.UPDATING_ARTICLE]: 85,
        [BlogAutoWriteJobStatus.FINALIZING_SEO]: 95,
        [BlogAutoWriteJobStatus.PUBLISHING]: 100,
        [BlogAutoWriteJobStatus.COMPLETED]: 100,
        [BlogAutoWriteJobStatus.FAILED]: 0,
        [BlogAutoWriteJobStatus.CANCELLED]: 0,
    };
})(BlogAutoWriteJobStatus || (BlogAutoWriteJobStatus = {}));
module.exports = BlogAutoWriteJobStatus;

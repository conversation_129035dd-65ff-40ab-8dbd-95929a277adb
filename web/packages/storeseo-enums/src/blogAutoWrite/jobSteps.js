"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const jobStatus_1 = __importDefault(require("./jobStatus"));
var BlogAutoWriteJobSteps;
(function (BlogAutoWriteJobSteps) {
    BlogAutoWriteJobSteps["CONTENT_GENERATION"] = "CONTENT_GENERATION";
    BlogAutoWriteJobSteps["SHOPIFY_DRAFT_CREATION"] = "SHOPIFY_DRAFT_CREATION";
    BlogAutoWriteJobSteps["ARTICLE_LINKING"] = "ARTICLE_LINKING";
    BlogAutoWriteJobSteps["FIRST_SEO_ANALYSIS"] = "FIRST_SEO_ANALYSIS";
    BlogAutoWriteJobSteps["IMAGE_GENERATION"] = "IMAGE_GENERATION";
    BlogAutoWriteJobSteps["SHOPIFY_IMAGE_UPLOAD"] = "SHOPIFY_IMAGE_UPLOAD";
    BlogAutoWriteJobSteps["SHOPIFY_ARTICLE_UPDATE"] = "SHOPIFY_ARTICLE_UPDATE";
    BlogAutoWriteJobSteps["FINAL_SEO_ANALYSIS"] = "FINAL_SEO_ANALYSIS";
    BlogAutoWriteJobSteps["SHOPIFY_ARTICLE_PUBLISH"] = "SHOPIFY_ARTICLE_PUBLISH";
})(BlogAutoWriteJobSteps || (BlogAutoWriteJobSteps = {}));
(function (BlogAutoWriteJobSteps) {
    BlogAutoWriteJobSteps.labels = {
        [BlogAutoWriteJobSteps.CONTENT_GENERATION]: "Preparing content",
        [BlogAutoWriteJobSteps.SHOPIFY_DRAFT_CREATION]: "Saving a draft to your Shopify blog",
        [BlogAutoWriteJobSteps.ARTICLE_LINKING]: "Structuring your blog",
        [BlogAutoWriteJobSteps.FIRST_SEO_ANALYSIS]: "Optimizing for SEO",
        [BlogAutoWriteJobSteps.IMAGE_GENERATION]: "Generating Featured Image",
        [BlogAutoWriteJobSteps.SHOPIFY_IMAGE_UPLOAD]: "Uploading Image to Shopify",
        [BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_UPDATE]: "Updating Article with Image",
        [BlogAutoWriteJobSteps.FINAL_SEO_ANALYSIS]: "Finalizing details",
        [BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_PUBLISH]: "Successfully generated",
    };
    BlogAutoWriteJobSteps.descriptions = {
        [BlogAutoWriteJobSteps.CONTENT_GENERATION]: "AI generates main blog content using OpenAI",
        [BlogAutoWriteJobSteps.SHOPIFY_DRAFT_CREATION]: "Save generated content to Shopify as draft blog post",
        [BlogAutoWriteJobSteps.ARTICLE_LINKING]: "Link dummy article with Shopify draft blog post",
        [BlogAutoWriteJobSteps.FIRST_SEO_ANALYSIS]: "First round of SEO analysis on content",
        [BlogAutoWriteJobSteps.IMAGE_GENERATION]: "Generate featured image using DALL-E",
        [BlogAutoWriteJobSteps.SHOPIFY_IMAGE_UPLOAD]: "Save featured image in Shopify",
        [BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_UPDATE]: "Update article in Shopify with featured image",
        [BlogAutoWriteJobSteps.FINAL_SEO_ANALYSIS]: "Final SEO analysis including image optimization",
        [BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_PUBLISH]: "Publish article to Shopify (if autopublish enabled)",
    };
    BlogAutoWriteJobSteps.weights = {
        [BlogAutoWriteJobSteps.CONTENT_GENERATION]: 30,
        [BlogAutoWriteJobSteps.SHOPIFY_DRAFT_CREATION]: 10,
        [BlogAutoWriteJobSteps.ARTICLE_LINKING]: 5,
        [BlogAutoWriteJobSteps.FIRST_SEO_ANALYSIS]: 10,
        [BlogAutoWriteJobSteps.IMAGE_GENERATION]: 20,
        [BlogAutoWriteJobSteps.SHOPIFY_IMAGE_UPLOAD]: 5,
        [BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_UPDATE]: 5,
        [BlogAutoWriteJobSteps.FINAL_SEO_ANALYSIS]: 10,
        [BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_PUBLISH]: 5,
    };
    BlogAutoWriteJobSteps.orderedSteps = [
        BlogAutoWriteJobSteps.CONTENT_GENERATION,
        BlogAutoWriteJobSteps.SHOPIFY_DRAFT_CREATION,
        BlogAutoWriteJobSteps.ARTICLE_LINKING,
        BlogAutoWriteJobSteps.FIRST_SEO_ANALYSIS,
        BlogAutoWriteJobSteps.IMAGE_GENERATION,
        BlogAutoWriteJobSteps.SHOPIFY_IMAGE_UPLOAD,
        BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_UPDATE,
        BlogAutoWriteJobSteps.FINAL_SEO_ANALYSIS,
        BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_PUBLISH,
    ];
    function calculateProgress(steps) {
        let totalProgress = 0;
        steps.forEach((stepData) => {
            if (stepData.completed && BlogAutoWriteJobSteps.weights[stepData.step]) {
                totalProgress += BlogAutoWriteJobSteps.weights[stepData.step];
            }
        });
        return Math.min(totalProgress, 100);
    }
    BlogAutoWriteJobSteps.calculateProgress = calculateProgress;
    function calculateProgressWithWeights(steps, dynamicWeights) {
        let totalProgress = 0;
        steps.forEach((stepData) => {
            if (stepData.completed && dynamicWeights[stepData.step]) {
                totalProgress += dynamicWeights[stepData.step];
            }
        });
        return Math.min(totalProgress, 100);
    }
    BlogAutoWriteJobSteps.calculateProgressWithWeights = calculateProgressWithWeights;
    function validateWeights() {
        const total = Object.values(BlogAutoWriteJobSteps.weights).reduce((sum, weight) => sum + weight, 0);
        const isValid = total === 100;
        return {
            isValid,
            total,
            error: isValid ? undefined : `Step weights sum to ${total}% instead of 100%`,
        };
    }
    BlogAutoWriteJobSteps.validateWeights = validateWeights;
    function validateStepCompletion(steps) {
        const errors = [];
        const stepMap = new Map(steps.map((s) => [s.step, s.completed]));
        for (const requiredStep of BlogAutoWriteJobSteps.orderedSteps) {
            if (!stepMap.has(requiredStep)) {
                errors.push(`Missing required step: ${requiredStep}`);
            }
        }
        let foundIncomplete = false;
        for (const step of BlogAutoWriteJobSteps.orderedSteps) {
            const isCompleted = stepMap.get(step) || false;
            if (foundIncomplete && isCompleted) {
                errors.push(`Invalid step order: ${step} is completed but previous steps are incomplete`);
            }
            if (!isCompleted) {
                foundIncomplete = true;
            }
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    BlogAutoWriteJobSteps.validateStepCompletion = validateStepCompletion;
    function getNextStep(steps) {
        const stepMap = new Map(steps.map((s) => [s.step, s.completed]));
        for (const step of BlogAutoWriteJobSteps.orderedSteps) {
            if (!stepMap.get(step)) {
                return step;
            }
        }
        return null;
    }
    BlogAutoWriteJobSteps.getNextStep = getNextStep;
    function canStartStep(targetStep, steps) {
        const stepMap = new Map(steps.map((s) => [s.step, s.completed]));
        const targetIndex = BlogAutoWriteJobSteps.orderedSteps.indexOf(targetStep);
        if (targetIndex === -1) {
            return false;
        }
        for (let i = 0; i < targetIndex; i++) {
            if (!stepMap.get(BlogAutoWriteJobSteps.orderedSteps[i])) {
                return false;
            }
        }
        return true;
    }
    BlogAutoWriteJobSteps.canStartStep = canStartStep;
    BlogAutoWriteJobSteps.stepToStatusMapping = {
        [BlogAutoWriteJobSteps.CONTENT_GENERATION]: jobStatus_1.default.GENERATING_CONTENT,
        [BlogAutoWriteJobSteps.SHOPIFY_DRAFT_CREATION]: jobStatus_1.default.CREATING_DRAFT,
        [BlogAutoWriteJobSteps.ARTICLE_LINKING]: jobStatus_1.default.LINKING_ARTICLE,
        [BlogAutoWriteJobSteps.FIRST_SEO_ANALYSIS]: jobStatus_1.default.ANALYZING_SEO,
        [BlogAutoWriteJobSteps.IMAGE_GENERATION]: jobStatus_1.default.GENERATING_IMAGE,
        [BlogAutoWriteJobSteps.SHOPIFY_IMAGE_UPLOAD]: jobStatus_1.default.UPLOADING_IMAGE,
        [BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_UPDATE]: jobStatus_1.default.UPDATING_ARTICLE,
        [BlogAutoWriteJobSteps.FINAL_SEO_ANALYSIS]: jobStatus_1.default.FINALIZING_SEO,
        [BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_PUBLISH]: jobStatus_1.default.PUBLISHING,
    };
    function getStatusForStep(step) {
        return BlogAutoWriteJobSteps.stepToStatusMapping[step];
    }
    BlogAutoWriteJobSteps.getStatusForStep = getStatusForStep;
    function getStepForStatus(status) {
        for (const [step, stepStatus] of Object.entries(BlogAutoWriteJobSteps.stepToStatusMapping)) {
            if (stepStatus === status) {
                return step;
            }
        }
        return null;
    }
    BlogAutoWriteJobSteps.getStepForStatus = getStepForStatus;
    function isProcessingStatus(status) {
        return Object.values(BlogAutoWriteJobSteps.stepToStatusMapping).includes(status);
    }
    BlogAutoWriteJobSteps.isProcessingStatus = isProcessingStatus;
    function getExpectedStatus(steps) {
        const nextStep = getNextStep(steps);
        if (nextStep === null) {
            return jobStatus_1.default.COMPLETED;
        }
        return getStatusForStep(nextStep);
    }
    BlogAutoWriteJobSteps.getExpectedStatus = getExpectedStatus;
    BlogAutoWriteJobSteps.statusToStepMapping = {
        [jobStatus_1.default.PENDING]: null,
        [jobStatus_1.default.GENERATING_CONTENT]: BlogAutoWriteJobSteps.CONTENT_GENERATION,
        [jobStatus_1.default.CREATING_DRAFT]: BlogAutoWriteJobSteps.SHOPIFY_DRAFT_CREATION,
        [jobStatus_1.default.LINKING_ARTICLE]: BlogAutoWriteJobSteps.ARTICLE_LINKING,
        [jobStatus_1.default.ANALYZING_SEO]: BlogAutoWriteJobSteps.FIRST_SEO_ANALYSIS,
        [jobStatus_1.default.GENERATING_IMAGE]: BlogAutoWriteJobSteps.IMAGE_GENERATION,
        [jobStatus_1.default.UPLOADING_IMAGE]: BlogAutoWriteJobSteps.SHOPIFY_IMAGE_UPLOAD,
        [jobStatus_1.default.UPDATING_ARTICLE]: BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_UPDATE,
        [jobStatus_1.default.FINALIZING_SEO]: BlogAutoWriteJobSteps.FINAL_SEO_ANALYSIS,
        [jobStatus_1.default.PUBLISHING]: BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_PUBLISH,
        [jobStatus_1.default.COMPLETED]: null,
        [jobStatus_1.default.FAILED]: null,
        [jobStatus_1.default.CANCELLED]: null,
    };
    function getCurrentStep(status) {
        return BlogAutoWriteJobSteps.statusToStepMapping[status];
    }
    BlogAutoWriteJobSteps.getCurrentStep = getCurrentStep;
    function getDefaultSteps() {
        return BlogAutoWriteJobSteps.orderedSteps.map((step) => ({
            step,
            completed: false,
            error: null,
            startedAt: null,
            completedAt: null,
        }));
    }
    BlogAutoWriteJobSteps.getDefaultSteps = getDefaultSteps;
})(BlogAutoWriteJobSteps || (BlogAutoWriteJobSteps = {}));
module.exports = BlogAutoWriteJobSteps;

"use strict";
var WordCountRange;
(function (WordCountRange) {
    WordCountRange["UP_TO_500"] = "Up to 500";
    WordCountRange["RANGE_500_800"] = "500-800";
    WordCountRange["RANGE_800_1200"] = "800-1200";
    WordCountRange["OVER_1200"] = "1200+";
})(WordCountRange || (WordCountRange = {}));
(function (WordCountRange) {
    WordCountRange.labels = {
        [WordCountRange.UP_TO_500]: "Up to 500 words",
        [WordCountRange.RANGE_500_800]: "500-800 words",
        [WordCountRange.RANGE_800_1200]: "800-1200 words",
        [WordCountRange.OVER_1200]: "1200+ words",
    };
    WordCountRange.descriptions = {
        [WordCountRange.UP_TO_500]: "Short-form content, ideal for quick reads and social media",
        [WordCountRange.RANGE_500_800]: "Medium-length content, good for most blog topics",
        [WordCountRange.RANGE_800_1200]: "Long-form content, comprehensive coverage of topics",
        [WordCountRange.OVER_1200]: "In-depth content, detailed guides and extensive coverage",
    };
    WordCountRange.estimatedReadingTime = {
        [WordCountRange.UP_TO_500]: "1-2 minutes",
        [WordCountRange.RANGE_500_800]: "2-3 minutes",
        [WordCountRange.RANGE_800_1200]: "3-5 minutes",
        [WordCountRange.OVER_1200]: "5+ minutes",
    };
})(WordCountRange || (WordCountRange = {}));
module.exports = WordCountRange;

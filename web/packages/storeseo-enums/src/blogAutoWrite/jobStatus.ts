enum BlogAutoWriteJobStatus {
  PENDING = "pending",
  GENERATING_CONTENT = "generating_content", // Step 1: CONTENT_GENERATION
  CREATING_DRAFT = "creating_draft", // Step 2: SHOPIFY_DRAFT_CREATION
  LINKING_ARTICLE = "linking_article", // Step 3: ARTICLE_LINKING
  ANALYZING_SEO = "analyzing_seo", // Step 4: FIRST_SEO_ANALYSIS
  GENERATING_IMAGE = "generating_image", // Step 5: IMAGE_GENERATION
  UPLOADING_IMAGE = "uploading_image", // Step 6: SHOPIFY_IMAGE_UPLOAD
  UPDATING_ARTICLE = "updating_article", // Step 7: SHOPIFY_ARTICLE_UPDATE
  FINALIZING_SEO = "finalizing_seo", // Step 8: FINAL_SEO_ANALYSIS
  PUBLISHING = "publishing", // Step 9: SHOPIFY_ARTICLE_PUBLISH
  COMPLETED = "completed",
  FAILED = "failed",
  CANCELLED = "cancelled", // Job cancelled by user - distinct from FAILED for audit trails
}

namespace BlogAutoWriteJobStatus {
  export const labels: Record<BlogAutoWriteJobStatus, string> = {
    [BlogAutoWriteJobStatus.PENDING]: "Pending",
    [BlogAutoWriteJobStatus.GENERATING_CONTENT]: "Preparing content",
    [BlogAutoWriteJobStatus.CREATING_DRAFT]: "Saving a draft to your Shopify blog",
    [BlogAutoWriteJobStatus.LINKING_ARTICLE]: "Structuring your blog",
    [BlogAutoWriteJobStatus.ANALYZING_SEO]: "Optimizing for SEO",
    [BlogAutoWriteJobStatus.GENERATING_IMAGE]: "Generating Featured Image",
    [BlogAutoWriteJobStatus.UPLOADING_IMAGE]: "Uploading Image to Shopify",
    [BlogAutoWriteJobStatus.UPDATING_ARTICLE]: "Updating Article with Image",
    [BlogAutoWriteJobStatus.FINALIZING_SEO]: "Finalizing details",
    [BlogAutoWriteJobStatus.PUBLISHING]: "Saving",
    [BlogAutoWriteJobStatus.COMPLETED]: "Completed",
    [BlogAutoWriteJobStatus.FAILED]: "Failed",
    [BlogAutoWriteJobStatus.CANCELLED]: "Cancelled",
  };

  export const progressMapping: Record<BlogAutoWriteJobStatus, number> = {
    [BlogAutoWriteJobStatus.PENDING]: 0,
    [BlogAutoWriteJobStatus.GENERATING_CONTENT]: 30, // Step 1: 30% weight
    [BlogAutoWriteJobStatus.CREATING_DRAFT]: 40, // Step 2: +10% = 40%
    [BlogAutoWriteJobStatus.LINKING_ARTICLE]: 45, // Step 3: +5% = 45%
    [BlogAutoWriteJobStatus.ANALYZING_SEO]: 55, // Step 4: +10% = 55%
    [BlogAutoWriteJobStatus.GENERATING_IMAGE]: 75, // Step 5: +20% = 75%
    [BlogAutoWriteJobStatus.UPLOADING_IMAGE]: 80, // Step 6: +5% = 80%
    [BlogAutoWriteJobStatus.UPDATING_ARTICLE]: 85, // Step 7: +5% = 85%
    [BlogAutoWriteJobStatus.FINALIZING_SEO]: 95, // Step 8: +10% = 95%
    [BlogAutoWriteJobStatus.PUBLISHING]: 100, // Step 9: +5% = 100%
    [BlogAutoWriteJobStatus.COMPLETED]: 100,
    [BlogAutoWriteJobStatus.FAILED]: 0,
    [BlogAutoWriteJobStatus.CANCELLED]: 0, // Reset progress for cancelled jobs
  };
}

export = BlogAutoWriteJobStatus;

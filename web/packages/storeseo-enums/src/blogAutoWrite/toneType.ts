enum ToneType {
  FORMAL = "Formal",
  INFORMAL = "Informal",
  CONVERSATIONAL = "Conversational",
  PERSUASIVE = "Persuasive",
  FRIENDLY = "Friendly",
  ENCOURAGING = "Encouraging",
}

namespace ToneType {
  export const labels: Record<ToneType, string> = {
    [ToneType.FORMAL]: "Formal",
    [ToneType.INFORMAL]: "Informal",
    [ToneType.CONVERSATIONAL]: "Conversational",
    [ToneType.PERSUASIVE]: "Persuasive",
    [ToneType.FRIENDLY]: "Friendly",
    [ToneType.ENCOURAGING]: "Encouraging",
  };

  export const descriptions: Record<ToneType, string> = {
    [ToneType.FORMAL]: "Professional, business-like tone suitable for corporate content",
    [ToneType.INFORMAL]: "Relaxed, casual tone that feels approachable and easy-going",
    [ToneType.CONVERSATIONAL]: "Natural, dialogue-like tone as if speaking directly to the reader",
    [ToneType.PERSUASIVE]: "Compelling tone designed to convince and motivate action",
    [ToneType.FRIENDLY]: "Warm, welcoming tone that builds rapport with readers",
    [ToneType.ENCOURAGING]: "Supportive, motivational tone that inspires and uplifts",
  };

  export const examples: Record<ToneType, string> = {
    [ToneType.FORMAL]: "Our comprehensive analysis demonstrates...",
    [ToneType.INFORMAL]: "Let's dive into this awesome topic...",
    [ToneType.CONVERSATIONAL]: "You know what I love about this?...",
    [ToneType.PERSUASIVE]: "Imagine transforming your business with...",
    [ToneType.FRIENDLY]: "Hey there! I'm excited to share...",
    [ToneType.ENCOURAGING]: "You've got this! Here's how to...",
  };
}

export = ToneType;

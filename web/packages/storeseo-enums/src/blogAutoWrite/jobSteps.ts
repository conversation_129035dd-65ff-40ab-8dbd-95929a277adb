// Import BlogAutoWriteJobStatus for step-to-status mapping
import BlogAutoWriteJobStatus from "./jobStatus";

enum BlogAutoWriteJobSteps {
  CONTENT_GENERATION = "CONTENT_GENERATION",
  SHOPIFY_DRAFT_CREATION = "SHOPIFY_DRAFT_CREATION",
  ARTICLE_LINKING = "ARTICLE_LINKING",
  FIRST_SEO_ANALYSIS = "FIRST_SEO_ANALYSIS",
  IMAGE_GENERATION = "IMAGE_GENERATION",
  SHOPIFY_IMAGE_UPLOAD = "SHOPIFY_IMAGE_UPLOAD",
  SHOPIFY_ARTICLE_UPDATE = "SHOPIFY_ARTICLE_UPDATE",
  FINAL_SEO_ANALYSIS = "FINAL_SEO_ANALYSIS",
  SHOPIFY_ARTICLE_PUBLISH = "SHOPIFY_ARTICLE_PUBLISH",
}

namespace BlogAutoWriteJobSteps {
  export const labels: Record<BlogAutoWriteJobSteps, string> = {
    [BlogAutoWriteJobSteps.CONTENT_GENERATION]: "Preparing content",
    [BlogAutoWriteJobSteps.SHOPIFY_DRAFT_CREATION]: "Saving a draft to your Shopify blog",
    [BlogAutoWriteJobSteps.ARTICLE_LINKING]: "Structuring your blog",
    [BlogAutoWriteJobSteps.FIRST_SEO_ANALYSIS]: "Optimizing for SEO",
    [BlogAutoWriteJobSteps.IMAGE_GENERATION]: "Generating Featured Image",
    [BlogAutoWriteJobSteps.SHOPIFY_IMAGE_UPLOAD]: "Uploading Image to Shopify",
    [BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_UPDATE]: "Updating Article with Image",
    [BlogAutoWriteJobSteps.FINAL_SEO_ANALYSIS]: "Finalizing details",
    [BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_PUBLISH]: "Successfully generated",
  };

  export const descriptions: Record<BlogAutoWriteJobSteps, string> = {
    [BlogAutoWriteJobSteps.CONTENT_GENERATION]: "AI generates main blog content using OpenAI",
    [BlogAutoWriteJobSteps.SHOPIFY_DRAFT_CREATION]: "Save generated content to Shopify as draft blog post",
    [BlogAutoWriteJobSteps.ARTICLE_LINKING]: "Link dummy article with Shopify draft blog post",
    [BlogAutoWriteJobSteps.FIRST_SEO_ANALYSIS]: "First round of SEO analysis on content",
    [BlogAutoWriteJobSteps.IMAGE_GENERATION]: "Generate featured image using DALL-E",
    [BlogAutoWriteJobSteps.SHOPIFY_IMAGE_UPLOAD]: "Save featured image in Shopify",
    [BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_UPDATE]: "Update article in Shopify with featured image",
    [BlogAutoWriteJobSteps.FINAL_SEO_ANALYSIS]: "Final SEO analysis including image optimization",
    [BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_PUBLISH]: "Publish article to Shopify (if autopublish enabled)",
  };

  // Step weights for progress calculation (must sum to 100)
  export const weights: Record<BlogAutoWriteJobSteps, number> = {
    [BlogAutoWriteJobSteps.CONTENT_GENERATION]: 30, // 30% - Most time-consuming AI task
    [BlogAutoWriteJobSteps.SHOPIFY_DRAFT_CREATION]: 10, // 10% - Save to Shopify as draft
    [BlogAutoWriteJobSteps.ARTICLE_LINKING]: 5, // 5% - Link dummy article with Shopify draft
    [BlogAutoWriteJobSteps.FIRST_SEO_ANALYSIS]: 10, // 10% - First SEO analysis
    [BlogAutoWriteJobSteps.IMAGE_GENERATION]: 20, // 20% - AI image generation (time-consuming)
    [BlogAutoWriteJobSteps.SHOPIFY_IMAGE_UPLOAD]: 5, // 5% - Upload image to Shopify
    [BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_UPDATE]: 5, // 5% - Update article with image
    [BlogAutoWriteJobSteps.FINAL_SEO_ANALYSIS]: 10, // 10% - Final SEO analysis
    [BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_PUBLISH]: 5, // 5% - Publish to Shopify (if enabled)
  };

  // Get ordered list of steps for processing
  export const orderedSteps: BlogAutoWriteJobSteps[] = [
    BlogAutoWriteJobSteps.CONTENT_GENERATION,
    BlogAutoWriteJobSteps.SHOPIFY_DRAFT_CREATION,
    BlogAutoWriteJobSteps.ARTICLE_LINKING,
    BlogAutoWriteJobSteps.FIRST_SEO_ANALYSIS,
    BlogAutoWriteJobSteps.IMAGE_GENERATION,
    BlogAutoWriteJobSteps.SHOPIFY_IMAGE_UPLOAD,
    BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_UPDATE,
    BlogAutoWriteJobSteps.FINAL_SEO_ANALYSIS,
    BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_PUBLISH,
  ];

  // Calculate progress based on completed steps
  export function calculateProgress(steps: Array<{ step: string; completed: boolean }>): number {
    let totalProgress = 0;
    steps.forEach((stepData) => {
      if (stepData.completed && weights[stepData.step as BlogAutoWriteJobSteps]) {
        totalProgress += weights[stepData.step as BlogAutoWriteJobSteps];
      }
    });
    return Math.min(totalProgress, 100);
  }

  // Calculate progress based on completed steps with dynamic weights (feature flag aware)
  // NOTE: Only used when FEATURE_BLOG_AUTO_WRITE_IMAGE_GENERATION is disabled
  // When feature is stable and enabled permanently, this method can be removed
  export function calculateProgressWithWeights(
    steps: Array<{ step: string; completed: boolean }>,
    dynamicWeights: Record<string, number>
  ): number {
    let totalProgress = 0;
    steps.forEach((stepData) => {
      if (stepData.completed && dynamicWeights[stepData.step]) {
        totalProgress += dynamicWeights[stepData.step];
      }
    });
    return Math.min(totalProgress, 100);
  }

  // Validate that step weights sum to exactly 100%
  export function validateWeights(): { isValid: boolean; total: number; error?: string } {
    const total = Object.values(weights).reduce((sum, weight) => sum + weight, 0);
    const isValid = total === 100;

    return {
      isValid,
      total,
      error: isValid ? undefined : `Step weights sum to ${total}% instead of 100%`,
    };
  }

  // Validate step completion order and detect invalid transitions
  export function validateStepCompletion(steps: Array<{ step: string; completed: boolean }>): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];
    const stepMap = new Map(steps.map((s) => [s.step, s.completed]));

    // Check if all required steps are present
    for (const requiredStep of orderedSteps) {
      if (!stepMap.has(requiredStep)) {
        errors.push(`Missing required step: ${requiredStep}`);
      }
    }

    // Check for invalid step order (completed steps should be sequential from start)
    let foundIncomplete = false;
    for (const step of orderedSteps) {
      const isCompleted = stepMap.get(step) || false;

      if (foundIncomplete && isCompleted) {
        errors.push(`Invalid step order: ${step} is completed but previous steps are incomplete`);
      }

      if (!isCompleted) {
        foundIncomplete = true;
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // Get the next step that should be processed
  export function getNextStep(steps: Array<{ step: string; completed: boolean }>): BlogAutoWriteJobSteps | null {
    const stepMap = new Map(steps.map((s) => [s.step, s.completed]));

    for (const step of orderedSteps) {
      if (!stepMap.get(step)) {
        return step;
      }
    }

    return null; // All steps completed
  }

  // Check if a specific step can be started (all previous steps completed)
  export function canStartStep(
    targetStep: BlogAutoWriteJobSteps,
    steps: Array<{ step: string; completed: boolean }>
  ): boolean {
    const stepMap = new Map(steps.map((s) => [s.step, s.completed]));
    const targetIndex = orderedSteps.indexOf(targetStep);

    if (targetIndex === -1) {
      return false; // Invalid step
    }

    // Check if all previous steps are completed
    for (let i = 0; i < targetIndex; i++) {
      if (!stepMap.get(orderedSteps[i])) {
        return false;
      }
    }

    return true;
  }

  // Reverse mapping from steps to corresponding job statuses
  export const stepToStatusMapping: Record<BlogAutoWriteJobSteps, BlogAutoWriteJobStatus> = {
    [BlogAutoWriteJobSteps.CONTENT_GENERATION]: BlogAutoWriteJobStatus.GENERATING_CONTENT,
    [BlogAutoWriteJobSteps.SHOPIFY_DRAFT_CREATION]: BlogAutoWriteJobStatus.CREATING_DRAFT,
    [BlogAutoWriteJobSteps.ARTICLE_LINKING]: BlogAutoWriteJobStatus.LINKING_ARTICLE,
    [BlogAutoWriteJobSteps.FIRST_SEO_ANALYSIS]: BlogAutoWriteJobStatus.ANALYZING_SEO,
    [BlogAutoWriteJobSteps.IMAGE_GENERATION]: BlogAutoWriteJobStatus.GENERATING_IMAGE,
    [BlogAutoWriteJobSteps.SHOPIFY_IMAGE_UPLOAD]: BlogAutoWriteJobStatus.UPLOADING_IMAGE,
    [BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_UPDATE]: BlogAutoWriteJobStatus.UPDATING_ARTICLE,
    [BlogAutoWriteJobSteps.FINAL_SEO_ANALYSIS]: BlogAutoWriteJobStatus.FINALIZING_SEO,
    [BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_PUBLISH]: BlogAutoWriteJobStatus.PUBLISHING,
  };

  // Get the job status that corresponds to a specific step
  export function getStatusForStep(step: BlogAutoWriteJobSteps): BlogAutoWriteJobStatus {
    return stepToStatusMapping[step];
  }

  // Get the step that corresponds to a specific job status
  export function getStepForStatus(status: BlogAutoWriteJobStatus): BlogAutoWriteJobSteps | null {
    // Use the reverse lookup from the status mapping
    for (const [step, stepStatus] of Object.entries(stepToStatusMapping)) {
      if (stepStatus === status) {
        return step as BlogAutoWriteJobSteps;
      }
    }
    return null; // No corresponding step (e.g., PENDING, COMPLETED, FAILED, CANCELLED)
  }

  // Check if a job status corresponds to an active processing step
  export function isProcessingStatus(status: BlogAutoWriteJobStatus): boolean {
    return Object.values(stepToStatusMapping).includes(status);
  }

  // Get the expected job status for the current processing step
  export function getExpectedStatus(steps: Array<{ step: string; completed: boolean }>): BlogAutoWriteJobStatus {
    const nextStep = getNextStep(steps);

    if (nextStep === null) {
      // All steps completed
      return BlogAutoWriteJobStatus.COMPLETED;
    }

    return getStatusForStep(nextStep);
  }

  // Reverse mapping from JobStatus to corresponding JobStep (moved from jobStatus.ts to avoid circular dependency)
  export const statusToStepMapping: Record<BlogAutoWriteJobStatus, BlogAutoWriteJobSteps | null> = {
    [BlogAutoWriteJobStatus.PENDING]: null,
    [BlogAutoWriteJobStatus.GENERATING_CONTENT]: BlogAutoWriteJobSteps.CONTENT_GENERATION,
    [BlogAutoWriteJobStatus.CREATING_DRAFT]: BlogAutoWriteJobSteps.SHOPIFY_DRAFT_CREATION,
    [BlogAutoWriteJobStatus.LINKING_ARTICLE]: BlogAutoWriteJobSteps.ARTICLE_LINKING,
    [BlogAutoWriteJobStatus.ANALYZING_SEO]: BlogAutoWriteJobSteps.FIRST_SEO_ANALYSIS,
    [BlogAutoWriteJobStatus.GENERATING_IMAGE]: BlogAutoWriteJobSteps.IMAGE_GENERATION,
    [BlogAutoWriteJobStatus.UPLOADING_IMAGE]: BlogAutoWriteJobSteps.SHOPIFY_IMAGE_UPLOAD,
    [BlogAutoWriteJobStatus.UPDATING_ARTICLE]: BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_UPDATE,
    [BlogAutoWriteJobStatus.FINALIZING_SEO]: BlogAutoWriteJobSteps.FINAL_SEO_ANALYSIS,
    [BlogAutoWriteJobStatus.PUBLISHING]: BlogAutoWriteJobSteps.SHOPIFY_ARTICLE_PUBLISH,
    [BlogAutoWriteJobStatus.COMPLETED]: null,
    [BlogAutoWriteJobStatus.FAILED]: null,
    [BlogAutoWriteJobStatus.CANCELLED]: null, // No associated step for cancelled jobs
  };

  // Get the current step based on job status (moved from jobStatus.ts to avoid circular dependency)
  export function getCurrentStep(status: BlogAutoWriteJobStatus): BlogAutoWriteJobSteps | null {
    return statusToStepMapping[status];
  }

  // Get default step structure for new jobs
  export function getDefaultSteps(): Array<{
    step: BlogAutoWriteJobSteps;
    completed: boolean;
    error: string | null;
    startedAt: string | null;
    completedAt: string | null;
  }> {
    return orderedSteps.map((step) => ({
      step,
      completed: false,
      error: null,
      startedAt: null,
      completedAt: null,
    }));
  }
}

export = BlogAutoWriteJobSteps;

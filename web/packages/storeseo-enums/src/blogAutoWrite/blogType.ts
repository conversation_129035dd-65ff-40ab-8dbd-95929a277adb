enum BlogType {
  PRODUCT_BASED = "Product-based",
  GUIDE = "Guide",
  LISTICLE = "Listicle",
  INFORMATIONAL = "Informational",
  NEWS = "News",
  SEASONAL = "Seasonal",
}

namespace BlogType {
  export const labels: Record<BlogType, string> = {
    [BlogType.PRODUCT_BASED]: "Product-based",
    [BlogType.GUIDE]: "Guide",
    [BlogType.LISTICLE]: "Listicle",
    [BlogType.INFORMATIONAL]: "Informational",
    [BlogType.NEWS]: "News",
    [BlogType.SEASONAL]: "Seasonal",
  };

  export const descriptions: Record<BlogType, string> = {
    [BlogType.PRODUCT_BASED]: "Blog posts that focus on specific products or product categories",
    [BlogType.GUIDE]: "Step-by-step guides and how-to content",
    [BlogType.LISTICLE]: "List-based articles (e.g., '10 Best...', 'Top 5...')",
    [BlogType.INFORMATIONAL]: "Educational content that provides valuable information",
    [BlogType.NEWS]: "News updates and industry announcements",
    [BlogType.SEASONAL]: "Content related to seasons, holidays, or special events",
  };
}

export = BlogType;

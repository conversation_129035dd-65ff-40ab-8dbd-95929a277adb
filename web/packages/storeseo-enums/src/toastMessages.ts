enum ToastMessages {
  // Success messages
  COUPON_CLEARED = "Coupon discount cleared",
  COUPON_APPLIED = "Coupon applied",
  SUBSCRIPTION_SUCCESSFUL = "Subscription successful",
  SUBSCRIPTION_CANCELLED = "Subscription cancelled",
  PURCHASE_SUCCESSFUL = "Purchase successful",
  PREVIEW_IMAGE_UPLOADED = "Preview image uploaded",
  SINGLE_ARTICLE_SYNCED = "Article synced",
  ARTICLES_SYNC_STARTED = "Blogs sync started",
  ARTICLES_SYNC_COMPLETED = "Blogs sync completed",
  ARTICLE_UPDATED = "Blog updated",
  SINGLE_PAGE_SYNCED = "Page synced",
  PAGES_SYNC_STARTED = "Page sync started",
  PAGES_SYNC_COMPLETED = "Pages sync completed",
  SINGLE_PRODUCT_SYNCED = "Product synced",
  PAGE_UPDATED = "Page updated",
  NO_FOLLOW_STATUS_UPDATED = "Nofollow updated",
  NO_INDEX_STATUS_UPDATED = "Noindex updated",
  IMAGE_UPDATED = "Image updated",
  DATA_UPDATED = "Data updated",
  STATUS_UPDATED = "Status updated",
  LOGO_UPDATED = "Logo updated",
  UPLOAD_SUCCESSFUL = "Upload successful",
  GOOGLE_CONNECTED = "Google connected",
  GOOGLE_DISCONNECTED = "Google Disconnected",
  GOOGLE_CONFIG_UPDATED = "Google config updated",
  SERVICE_JSON_UPDATED = "Service JSON updated",
  SIGN_IN_COMPLETED = "Sign-in completed",
  VERIFICATION_DONE = "Verification done",
  SEARCH_CONSOLE_LINKED = "Search console linked",
  SITEMAP_PERMITTED = "Sitemap permitted",
  INDEXING_ALLOWED = "Indexing allowed",
  ANALYTICS_ENABLED = "Analytics enabled",
  SHOP_DATA_UPDATED = "Shop updated",
  AUTO_FIX_STARTED = "Auto fix started",
  ONBOARD_COMPLETED = "Onboard completed",
  SEO_SETTINGS_UPDATED = "SEO settings updated",
  DATA_MIGRATION_STARTED = "Data migration started",
  PRODUCTS_SYNC_STARTED = "Products sync started",
  PRODUCTS_SYNC_COMPLETED = "Products sync completed",
  PRODUCTS_OPTIMIZATION_COMPLETED = "Product optimization completed",
  PRODUCT_UPDATED = "Product updated",
  SITEMAP_UPDATED = "Sitemap updated",
  SITEMAP_SUBMITTED = "Sitemap submitted",
  HTML_SITEMAP_UPDATED = "HTML Sitemap updated",
  IMAGE_OPTIMIZER_UPDATED = "Settings updated",
  AUTO_OPTIMIZER_ENABLED = "Auto optimizer enabled",
  AUTO_OPTIMIZER_DISABLED = "Auto optimizer disabled",
  COLLECTIONS_SYNC_STARTED = "Collections sync started",
  COLLECTIONS_SYNC_COMPLETED = "Collections sync completed",
  COLLECTIONS_UPDATE = "Collections updated",
  NOTIFICATION_SETTINGS_UPDATED = "Notification settings updated",
  SINGLE_DOC_SYNCED = "Doc synced",
  DOCS_SYNC_STARTED = "Docs sync started",
  DOCS_SYNC_COMPLETED = "Docs sync completed",
  DOC_UPDATED = "Doc updated",
  MULTI_LANGUAGE_ENABLED = "Multilingual SEO enabled",
  MULTI_LANGUAGE_DISABLED = "Multilingual SEO disabled",
  LANGUAGE_SYNC_STARTED = "Language sync started",
  TRANSLATION_SYNCED = "Translation synced",
  BACKUP_COMPLETED = "Backup completed",
  BACKUP_RESTORE_COMPLETED = "Backup restore completed",
  LLMS_SETTINGS_UPDATED = "LLMs.txt settings updated",
  LLMS_SETTINGS_RESET = "LLMs.txt settings reset",
  LLMS_GENERATION_QUEUED = "LLMs.txt generation queued successfully",
  LLMS_GENERATION_COMPLETED = "LLMs.txt generation completed",
  // Bulk Operation success messages
  IMAGE_ALT_OPTIMIZATION_BULK_OP_COMPLETE = "Alt text generated",
  IMAGE_OPTIMIZATION_BULK_OP_COMPLETE = "Image optimization completed",
  PRODUCT_AI_OPTIMIZATION_BULK_OP_COMPLETE = "Product AI optimization completed",
  COLLECTION_AI_OPTIMIZATION_BULK_OP_COMPLETE = "Collection AI optimization completed",
  ARTICLE_AI_OPTIMIZATION_BULK_OP_COMPLETE = "Article AI optimization completed",
  // Error messages
  SET_TEMPLATE_FOR_OPTIMIZATION_FIRST = "Set template first",
  SITEMAP_SUBMIT_FAILED = "Sitemap submission failed",
  SITE_NOT_VERIFIED = "Site not verified",
  SERVICE_ACCOUNT_CONFIG_NOT_SET = "Config not set",
  INVALID_JSON_FILE = "Please input a valid JSON file",
  SET_A_CONFIG_FIRST = "Set the config first",
  UNAUTHORIZED = "Unauthorized",
  SOMETHING_WENT_WRONG = "Something went wrong!",
  PERMISSION_DENIED = "Permission denied",
  PLAN_UNAVAILABLE = "Plan unavailable",
  PLAN_IS_EMPTY = "Plan is empty",
}

export = ToastMessages;

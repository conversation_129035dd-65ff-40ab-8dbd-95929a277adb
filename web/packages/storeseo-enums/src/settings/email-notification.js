"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OPTIONS = exports.EMAIL_NOTIFICATION_REPORT = exports.EMAIL_NOTIFICATION = void 0;
const eventTopics_1 = __importDefault(require("storeseo-enums/eventTopics"));
const EMAIL_NOTIFICATION = "EMAIL_NOTIFICATION";
exports.EMAIL_NOTIFICATION = EMAIL_NOTIFICATION;
const EMAIL_NOTIFICATION_REPORT = "EMAIL_NOTIFICATION_REPORT";
exports.EMAIL_NOTIFICATION_REPORT = EMAIL_NOTIFICATION_REPORT;
const OPTIONS = {
    [eventTopics_1.default.WEEKLY_STORE_REPORT]: {
        title: "Overall Product Optimization Report",
        description: "Get weekly updates on overall product optimization",
        emailSubject: "Overall Product Optimization Report from StoreSEO",
        configurable: true,
        instantNotification: false,
        day: "monday",
    },
    [eventTopics_1.default.GOOGLE_ANALYTICS_INTEGRATION]: {
        title: "Google Analytics Integration",
        description: "Notify if Google Analytics is not connected",
        emailSubject: "Connect Google Analytics to your website",
        configurable: false,
        instantNotification: false,
        day: "",
    },
    [eventTopics_1.default.GOOGLE_SEARCH_CONSOLE_INTEGRATION]: {
        title: "Google Search Console Integration",
        description: "Notify if Google Search Console is not connected",
        emailSubject: "Connect Your Website with Google (Reminder)",
        configurable: false,
        instantNotification: false,
        day: "",
    },
    [eventTopics_1.default.HTML_SITEMAP_INTEGRATION]: {
        title: "HTML Sitemap Page",
        description: "Send a notification if the HTML Sitemap page is not generated",
        emailSubject: "Generate Your HTML Sitemap Page",
        configurable: false,
        instantNotification: false,
        day: "",
    },
    [eventTopics_1.default.AUTO_IMAGE_OPTIMIZER_REMINDER]: {
        title: "Auto Image Optimizer",
        description: "Notify if Auto Image Optimizer is not enabled",
        emailSubject: "Activate Auto Image Optimization",
        configurable: false,
        instantNotification: false,
        day: "",
    },
    [eventTopics_1.default.PRODUCT_SYNC_COMPLETE]: {
        title: "Product Sync Complete",
        description: "Send a notification when product sync is complete",
        emailSubject: "Product sync on StoreSEO is complete!",
        configurable: false,
        instantNotification: true,
        day: "",
    },
    [eventTopics_1.default.IMAGE_OPTIMIZER_USAGE_NOTIFICATION]: {
        title: "Image Optimizer Usage Notification",
        description: "Send a notification when the image optimizer usage exceeds the threshold",
        emailSubject: "You have used of Your Image Optimizer Plan",
        configurable: false,
        instantNotification: true,
        day: "",
    },
    [eventTopics_1.default.AI_CONTENT_OPTIMIZER_USAGE_NOTIFICATION]: {
        title: "AI Content Optimizer Usage Notification",
        description: "Send a notification when the AI Content Optimizer usage exceeds the threshold",
        emailSubject: "You have used of Your AI Content Optimizer Plan",
        configurable: false,
        instantNotification: true,
        day: "",
    },
    [eventTopics_1.default.AUTO_AI_CONTENT_OPTIMIZATION_REPORT]: {
        title: "Auto AI Content Optimization Report",
        description: "Get weekly updates on auto AI content optimization",
        emailSubject: "Weekly Auto AI Content Optimization Report",
        configurable: true,
        instantNotification: false,
        day: "tuesday",
    },
    [eventTopics_1.default.BULK_IMAGE_OPTIMIZATION_COMPLETE]: {
        title: "Bulk Image Optimization Complete",
        description: "Send a notification when the bulk image optimization is complete",
        emailSubject: "Bulk {{resourceType}} Image Optimization Complete",
        configurable: false,
        instantNotification: true,
        day: "",
    },
    [eventTopics_1.default.BULK_AI_CONTENT_OPTIMIZATION_COMPLETE]: {
        title: "Bulk AI Content Optimization Complete",
        description: "Send a notification when the bulk AI content optimization is complete",
        emailSubject: "Bulk {{resourceType}} AI Content Optimization Complete",
        configurable: false,
        instantNotification: true,
        day: "",
    },
};
exports.OPTIONS = OPTIONS;

"use strict";
const { DASHBOARD_READ, STORE_READ, STORE_WRITE, STORE_DELETE, PRODUCT_READ, PAYMENT_HISTORY_READ, SUBSCRIPTION_PLAN_READ, SUBSCRIPTION_PLAN_WRITE, <PERSON><PERSON>ON_READ, SUBS<PERSON>IPTION_PLAN_DELETE, SUBS<PERSON>IPTION_ADDON_READ, SUBSCRIPTION_ADDON_WRITE, SUBSCRIPTION_ADDON_DELETE, COUPON_WRITE, COUP<PERSON>_DELETE, ADMIN_USER_READ, ADMIN_USER_WRITE, ADMIN_USER_DELETE, ACTIVITY_LOG_READ, ACTIVITY_LOG_DELETE, REPORT_DAILY_STATUS, PARTNER_READ, PARTNER_WRITE, PARTNER_DELETE, COLLECTION_READ, ARTICLE_READ, PAGE_READ, } = require("./ApiActionList");
module.exports = {
    "DASHBOARD:READ": {
        code: "DASHBOARD:READ",
        description: "Can view list of stores, Installation Graph, Customer Growth graph in the admin dashboard",
        actions: [DASHBOARD_READ, STORE_READ, SUBSCRIPTION_ADDON_READ, SUBSCRIPTION_PLAN_READ],
    },
    "STORE:READ": {
        code: "STORE:READ",
        description: "Can view list of stores, store details, product details in the admin dashboard",
        actions: [STORE_READ, SUBSCRIPTION_ADDON_READ, SUBSCRIPTION_PLAN_READ],
    },
    "STORE:WRITE": {
        code: "STORE:WRITE",
        description: "Can write verify branding, remove branding in the admin dashboard",
        actions: [STORE_WRITE, STORE_READ, SUBSCRIPTION_ADDON_READ, SUBSCRIPTION_PLAN_READ],
    },
    "STORE:DELETE": {
        code: "STORE:DELETE",
        description: "Can delete store releated data & cancel subscription",
        actions: [STORE_DELETE, STORE_WRITE, STORE_READ, SUBSCRIPTION_ADDON_READ, SUBSCRIPTION_PLAN_READ],
    },
    "PRODUCT:READ": {
        code: "PRODUCT:READ",
        description: "Can view list of products, product details, meta details, images, analytics in the admin dashboard",
        actions: [PRODUCT_READ, STORE_READ],
    },
    "COLLECTION:READ": {
        code: "COLLECTION:READ",
        description: "Can view list of collections, collection details, meta details, images, analytics in the admin dashboard",
        actions: [COLLECTION_READ],
    },
    "ARTICLE:READ": {
        code: "ARTICLE:READ",
        description: "Can view list of articles, article details, meta details, images, analytics in the admin dashboard",
        actions: [ARTICLE_READ],
    },
    "PAGE:READ": {
        code: "PAGE:READ",
        description: "Can view list of pages, page details, meta details, analytics in the admin dashboard",
        actions: [PAGE_READ],
    },
    "PAYMENT_HISTORY:READ": {
        code: "PAYMENT_HISTORY:READ",
        description: "Can view list of payment history, payment history details in the admin dashboard",
        actions: [PAYMENT_HISTORY_READ],
    },
    "SUBSCRIPTION_PLAN:READ": {
        code: "SUBSCRIPTION_PLAN:READ",
        description: "Can view list of subscription plan, plan details in the admin dashboard",
        actions: [SUBSCRIPTION_PLAN_READ],
    },
    "SUBSCRIPTION_PLAN:WRITE": {
        code: "SUBSCRIPTION_PLAN:WRITE",
        description: "Can add, edit, copy subscription plan in the admin dashboard",
        actions: [SUBSCRIPTION_PLAN_WRITE, SUBSCRIPTION_PLAN_READ, COUPON_READ],
    },
    "SUBSCRIPTION_PLAN:DELETE": {
        code: "SUBSCRIPTION_PLAN:DELETE",
        description: "Can delete subscription plan in the admin dashboard",
        actions: [SUBSCRIPTION_PLAN_DELETE, SUBSCRIPTION_PLAN_WRITE, SUBSCRIPTION_PLAN_READ, COUPON_READ],
    },
    "SUBSCRIPTION_ADDON:READ": {
        code: "SUBSCRIPTION_ADDON:READ",
        description: "Can view list of subscription addons, addon details in the admin dashboard",
        actions: [SUBSCRIPTION_ADDON_READ],
    },
    "SUBSCRIPTION_ADDON:WRITE": {
        code: "SUBSCRIPTION_ADDON:WRITE",
        description: "Can add, edit, copy subscription addon in the admin dashboard",
        actions: [SUBSCRIPTION_ADDON_WRITE, SUBSCRIPTION_ADDON_READ],
    },
    "SUBSCRIPTION_ADDON:DELETE": {
        code: "SUBSCRIPTION_ADDON:DELETE",
        description: "Can delete subscription addon in the admin dashboard",
        actions: [SUBSCRIPTION_ADDON_DELETE, SUBSCRIPTION_ADDON_WRITE, SUBSCRIPTION_ADDON_READ],
    },
    "COUPON:READ": {
        code: "COUPON:READ",
        description: "Can view list of coupons, coupon details in the admin dashboard",
        actions: [COUPON_READ],
    },
    "COUPON:WRITE": {
        code: "COUPON:WRITE",
        description: "Can add coupon in the admin dashboard",
        actions: [COUPON_WRITE, SUBSCRIPTION_PLAN_READ, COUPON_READ],
    },
    "COUPON:DELETE": {
        code: "COUPON:DELETE",
        description: "Can delete coupon in the admin dashboard",
        actions: [COUPON_READ, COUPON_DELETE, COUPON_WRITE, SUBSCRIPTION_PLAN_READ],
    },
    "ADMIN_USER:READ": {
        code: "ADMIN_USER:READ",
        description: "Can view list of admin, admin details in the admin dashboard",
        actions: [ADMIN_USER_READ],
    },
    "ADMIN_USER:WRITE": {
        code: "ADMIN_USER:WRITE",
        description: "Can add, update, change password, update permissions in the admin dashboard",
        actions: [ADMIN_USER_WRITE, ADMIN_USER_READ],
    },
    "ADMIN_USER:DELETE": {
        code: "ADMIN_USER:DELETE",
        description: "Can delete admin in the admin dashboard",
        actions: [ADMIN_USER_DELETE, ADMIN_USER_WRITE, ADMIN_USER_READ],
    },
    "ACTIVITY_LOG:READ": {
        code: "ACTIVITY_LOG:READ",
        description: "Can view list of activity log, activity log details, data changes in the admin dashboard",
        actions: [ACTIVITY_LOG_READ],
    },
    "ACTIVITY_LOG:DELETE": {
        code: "ACTIVITY_LOG:DELETE",
        description: "Can delete activity log from the admin dashboard",
        actions: [ACTIVITY_LOG_DELETE],
    },
    "REPORT_DAILY_STATUS:READ": {
        code: "REPORT_DAILY_STATUS:READ",
        description: "Can view the daily status report",
        actions: [REPORT_DAILY_STATUS],
    },
    "PARTNER:READ": {
        code: "PARTNER:READ",
        description: "Can view list of partner, partner details in the admin dashboard",
        actions: [PARTNER_READ],
    },
    "PARTNER:WRITE": {
        code: "PARTNER:WRITE",
        description: "Can add, update partner details in the admin dashboard",
        actions: [PARTNER_READ, PARTNER_WRITE],
    },
    "PARTNER:DELETE": {
        code: "PARTNER:DELETE",
        description: "Can delete partner in the admin dashboard",
        actions: [PARTNER_DELETE, PARTNER_WRITE, PARTNER_READ],
    },
};

"use strict";
var ToastMessages;
(function (ToastMessages) {
    ToastMessages["COUPON_CLEARED"] = "Coupon discount cleared";
    ToastMessages["COUPON_APPLIED"] = "Coupon applied";
    ToastMessages["SUBSCRIPTION_SUCCESSFUL"] = "Subscription successful";
    ToastMessages["SUBSCRIPTION_CANCELLED"] = "Subscription cancelled";
    ToastMessages["PURCHASE_SUCCESSFUL"] = "Purchase successful";
    ToastMessages["PREVIEW_IMAGE_UPLOADED"] = "Preview image uploaded";
    ToastMessages["SINGLE_ARTICLE_SYNCED"] = "Article synced";
    ToastMessages["ARTICLES_SYNC_STARTED"] = "Blogs sync started";
    ToastMessages["ARTICLES_SYNC_COMPLETED"] = "Blogs sync completed";
    ToastMessages["ARTICLE_UPDATED"] = "Blog updated";
    ToastMessages["SINGLE_PAGE_SYNCED"] = "Page synced";
    ToastMessages["PAGES_SYNC_STARTED"] = "Page sync started";
    ToastMessages["PAGES_SYNC_COMPLETED"] = "Pages sync completed";
    ToastMessages["SINGLE_PRODUCT_SYNCED"] = "Product synced";
    ToastMessages["PAGE_UPDATED"] = "Page updated";
    ToastMessages["NO_FOLLOW_STATUS_UPDATED"] = "Nofollow updated";
    ToastMessages["NO_INDEX_STATUS_UPDATED"] = "Noindex updated";
    ToastMessages["IMAGE_UPDATED"] = "Image updated";
    ToastMessages["DATA_UPDATED"] = "Data updated";
    ToastMessages["STATUS_UPDATED"] = "Status updated";
    ToastMessages["LOGO_UPDATED"] = "Logo updated";
    ToastMessages["UPLOAD_SUCCESSFUL"] = "Upload successful";
    ToastMessages["GOOGLE_CONNECTED"] = "Google connected";
    ToastMessages["GOOGLE_DISCONNECTED"] = "Google Disconnected";
    ToastMessages["GOOGLE_CONFIG_UPDATED"] = "Google config updated";
    ToastMessages["SERVICE_JSON_UPDATED"] = "Service JSON updated";
    ToastMessages["SIGN_IN_COMPLETED"] = "Sign-in completed";
    ToastMessages["VERIFICATION_DONE"] = "Verification done";
    ToastMessages["SEARCH_CONSOLE_LINKED"] = "Search console linked";
    ToastMessages["SITEMAP_PERMITTED"] = "Sitemap permitted";
    ToastMessages["INDEXING_ALLOWED"] = "Indexing allowed";
    ToastMessages["ANALYTICS_ENABLED"] = "Analytics enabled";
    ToastMessages["SHOP_DATA_UPDATED"] = "Shop updated";
    ToastMessages["AUTO_FIX_STARTED"] = "Auto fix started";
    ToastMessages["ONBOARD_COMPLETED"] = "Onboard completed";
    ToastMessages["SEO_SETTINGS_UPDATED"] = "SEO settings updated";
    ToastMessages["DATA_MIGRATION_STARTED"] = "Data migration started";
    ToastMessages["PRODUCTS_SYNC_STARTED"] = "Products sync started";
    ToastMessages["PRODUCTS_SYNC_COMPLETED"] = "Products sync completed";
    ToastMessages["PRODUCTS_OPTIMIZATION_COMPLETED"] = "Product optimization completed";
    ToastMessages["PRODUCT_UPDATED"] = "Product updated";
    ToastMessages["SITEMAP_UPDATED"] = "Sitemap updated";
    ToastMessages["SITEMAP_SUBMITTED"] = "Sitemap submitted";
    ToastMessages["HTML_SITEMAP_UPDATED"] = "HTML Sitemap updated";
    ToastMessages["IMAGE_OPTIMIZER_UPDATED"] = "Settings updated";
    ToastMessages["AUTO_OPTIMIZER_ENABLED"] = "Auto optimizer enabled";
    ToastMessages["AUTO_OPTIMIZER_DISABLED"] = "Auto optimizer disabled";
    ToastMessages["COLLECTIONS_SYNC_STARTED"] = "Collections sync started";
    ToastMessages["COLLECTIONS_SYNC_COMPLETED"] = "Collections sync completed";
    ToastMessages["COLLECTIONS_UPDATE"] = "Collections updated";
    ToastMessages["NOTIFICATION_SETTINGS_UPDATED"] = "Notification settings updated";
    ToastMessages["SINGLE_DOC_SYNCED"] = "Doc synced";
    ToastMessages["DOCS_SYNC_STARTED"] = "Docs sync started";
    ToastMessages["DOCS_SYNC_COMPLETED"] = "Docs sync completed";
    ToastMessages["DOC_UPDATED"] = "Doc updated";
    ToastMessages["MULTI_LANGUAGE_ENABLED"] = "Multilingual SEO enabled";
    ToastMessages["MULTI_LANGUAGE_DISABLED"] = "Multilingual SEO disabled";
    ToastMessages["LANGUAGE_SYNC_STARTED"] = "Language sync started";
    ToastMessages["TRANSLATION_SYNCED"] = "Translation synced";
    ToastMessages["BACKUP_COMPLETED"] = "Backup completed";
    ToastMessages["BACKUP_RESTORE_COMPLETED"] = "Backup restore completed";
    ToastMessages["LLMS_SETTINGS_UPDATED"] = "LLMs.txt settings updated";
    ToastMessages["LLMS_SETTINGS_RESET"] = "LLMs.txt settings reset";
    ToastMessages["LLMS_GENERATION_QUEUED"] = "LLMs.txt generation queued successfully";
    ToastMessages["LLMS_GENERATION_COMPLETED"] = "LLMs.txt generation completed";
    ToastMessages["IMAGE_ALT_OPTIMIZATION_BULK_OP_COMPLETE"] = "Alt text generated";
    ToastMessages["IMAGE_OPTIMIZATION_BULK_OP_COMPLETE"] = "Image optimization completed";
    ToastMessages["PRODUCT_AI_OPTIMIZATION_BULK_OP_COMPLETE"] = "Product AI optimization completed";
    ToastMessages["COLLECTION_AI_OPTIMIZATION_BULK_OP_COMPLETE"] = "Collection AI optimization completed";
    ToastMessages["ARTICLE_AI_OPTIMIZATION_BULK_OP_COMPLETE"] = "Article AI optimization completed";
    ToastMessages["SET_TEMPLATE_FOR_OPTIMIZATION_FIRST"] = "Set template first";
    ToastMessages["SITEMAP_SUBMIT_FAILED"] = "Sitemap submission failed";
    ToastMessages["SITE_NOT_VERIFIED"] = "Site not verified";
    ToastMessages["SERVICE_ACCOUNT_CONFIG_NOT_SET"] = "Config not set";
    ToastMessages["INVALID_JSON_FILE"] = "Please input a valid JSON file";
    ToastMessages["SET_A_CONFIG_FIRST"] = "Set the config first";
    ToastMessages["UNAUTHORIZED"] = "Unauthorized";
    ToastMessages["SOMETHING_WENT_WRONG"] = "Something went wrong!";
    ToastMessages["PERMISSION_DENIED"] = "Permission denied";
    ToastMessages["PLAN_UNAVAILABLE"] = "Plan unavailable";
    ToastMessages["PLAN_IS_EMPTY"] = "Plan is empty";
})(ToastMessages || (ToastMessages = {}));
module.exports = ToastMessages;

enum SeoApps {
  SMART_SEO = "SMART_SEO",
  SEO_MANAGER = "SEO_MANAGER",
  SEO_KING = "SEO_KING",
}

namespace SeoApps {
  interface SeoAppMetadata {
    title: string;
    handle: string;
  }

  export const metadata: Record<SeoApps, SeoAppMetadata> = {
    [SeoApps.SMART_SEO]: { title: "Smart SEO", handle: "smart-seo" },
    [SeoApps.SEO_MANAGER]: { title: "SEO Manager", handle: "seo-manager" },
    [SeoApps.SEO_KING]: { title: "SEO King", handle: "seo-king" },
  };
}

export = SeoApps;

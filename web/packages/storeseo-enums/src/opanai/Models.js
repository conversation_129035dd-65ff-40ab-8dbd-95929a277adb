"use strict";
var OpenAiModels;
(function (OpenAiModels) {
    OpenAiModels["GPT_3_5_TURBO"] = "gpt-3.5-turbo";
    OpenAiModels["GPT_3_5_TURBO_16K"] = "gpt-3.5-turbo-16k";
    OpenAiModels["GPT_4"] = "gpt-4";
    OpenAiModels["GPT_4o"] = "gpt-4o";
    OpenAiModels["GPT_4o_MINI"] = "gpt-4o-mini";
    OpenAiModels["GPT_4_1"] = "gpt-4.1";
    OpenAiModels["GPT_4_1_MINI"] = "gpt-4.1-mini";
    OpenAiModels["GPT_4_1_NANO"] = "gpt-4.1-nano";
    OpenAiModels["GPT_O1"] = "o1";
    OpenAiModels["GPT_O1_MINI"] = "o1-mini";
    OpenAiModels["GPT_O3_MINI"] = "o3-mini";
})(OpenAiModels || (OpenAiModels = {}));
module.exports = OpenAiModels;

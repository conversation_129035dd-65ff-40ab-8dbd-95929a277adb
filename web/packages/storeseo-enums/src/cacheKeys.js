"use strict";
var CacheKeys;
(function (CacheKeys) {
    CacheKeys["HIDE_PRO_PACKAGE_AD"] = "HIDE_PRO_PACKAGE_AD";
    CacheKeys["PRODUCT_SYNC_CURSOR"] = "PRODUCT_SYNC_CURSOR";
    CacheKeys["ACTIVE_SHOPIFY_SHOPS"] = "ACTIVE_SHOPIFY_SHOPS";
    CacheKeys["MULTI_LANGUAGE"] = "MULTI_LANGUAGE";
    CacheKeys["HAS_MULTIPLE_LANGUAGES"] = "HAS_MULTIPLE_LANGUAGES";
    CacheKeys["ENABLED_MULTI_LANGUAGE"] = "ENABLED_MULTI_LANGUAGE";
    CacheKeys["DEFAULT_LANGUAGE"] = "DEFAULT_LANGUAGE";
    CacheKeys["ARTICLE_AI_OPTIMIZER"] = "ARTICLE_AI_OPTIMIZER";
    C<PERSON><PERSON>eys["PRODUCT_SYNC_ONGOING"] = "PRODUCT_SYNC_ONGOING";
    Cache<PERSON>eys["PRODUCT_SYNC_FILE_CURSOR"] = "PRODUCT_SYNC_FILE_CURSOR";
    CacheKeys["PRODUCT_SYNC_FILE"] = "PRODUCT_SYNC_FILE";
    CacheKeys["MIGRATE_DATA_FROM_APP"] = "MIGRATE_DATA_FROM_APP";
    CacheKeys["PAGE_SYNC_ONGOING"] = "PAGE_SYNC_ONGOING";
    CacheKeys["BLOG_SYNC_ONGOING"] = "BLOG_SYNC_ONGOING";
    CacheKeys["LLMS_TXT_GENERATION_ONGOING"] = "LLMS_TXT_GENERATION_ONGOING";
    CacheKeys["BLOGS_PENDING_IN_QUEUE"] = "BLOGS_PENDING_IN_QUEUE";
    CacheKeys["API_RATE_LIMIT_EXCEEDED"] = "API_RATE_LIMIT_EXCEEDED";
    CacheKeys["SHOPIFY_GRAPHQL_API"] = "SHOPIFY_GRAPHQL_API";
    CacheKeys["SHOPIFY_HTTP_API"] = "SHOPIFY_HTTP_API";
    CacheKeys["AUTO_OPTIMIZATION_TASK"] = "AUTO_OPTIMIZATION_TASK";
    CacheKeys["PRODUCTS_TO_OPTIMIZE"] = "PRODUCTS_TO_OPTIMIZE";
    CacheKeys["PRODUCTS_OPTIMIZED"] = "PRODUCTS_OPTIMIZED";
    CacheKeys["TOTAL_BATCHES"] = "TOTAL_BATCHES";
    CacheKeys["LAST_BATCH_NUMBER"] = "LAST_BATCH_NUMBER";
    CacheKeys["RUNNING"] = "RUNNING";
    CacheKeys["ONBOARD_STEP"] = "ONBOARD_STEP";
    CacheKeys["PLAN_ID"] = "PLAN_ID";
    CacheKeys["BANNER_BETTERDOCS"] = "BANNER_BETTERDOCS";
    CacheKeys["BANNER_GET_STARTED"] = "BANNER_GET_STARTED";
    CacheKeys["BANNER_SEO_GUIDE"] = "BANNER_SEO_GUIDE";
    CacheKeys["BANNER_HOMEPAGE_HINT"] = "BANNER_HOMEPAGE_HINT";
    CacheKeys["BANNER_DEAL"] = "BANNER_DEAL";
    CacheKeys["BANNER_GOOGLE_SITEMAP"] = "BANNER_GOOGLE_SITEMAP";
    CacheKeys["BANNER_WHATS_NEW"] = "BANNER_WHATS_NEW";
    CacheKeys["BANNER_SCHEDULE_A_CALL"] = "BANNER_SCHEDULE_A_CALL";
    CacheKeys["BANNER_VERIFY_STORE"] = "BANNER_VERIFY_STORE";
    CacheKeys["BANNER_AI_CREDIT_GIFT"] = "BANNER_AI_CREDIT_GIFT";
    CacheKeys["BANNER_PARTNERSHIPS"] = "BANNER_PARTNERSHIPS";
    CacheKeys["BANNER_UPGRADE_IMAGE_OPTIMIZER"] = "BANNER_UPGRADE_IMAGE_OPTIMIZER";
    CacheKeys["BANNER_ENABLE_AUTO_IMAGE_OPTIMIZER"] = "BANNER_ENABLE_AUTO_IMAGE_OPTIMIZER";
    CacheKeys["BANNER_ENABLE_AUTO_AI_OPTIMIZER"] = "BANNER_ENABLE_AUTO_AI_OPTIMIZER";
    CacheKeys["BANNER_ENABLE_AI_OPTIMIZER"] = "BANNER_ENABLE_AI_OPTIMIZER";
    CacheKeys["BANNER_CREDIT_BUNDLE_GUIDE"] = "BANNER_CREDIT_BUNDLE_GUIDE";
    CacheKeys["BANNER_AI_OPTIMIZER_PROMO"] = "BANNER_AI_OPTIMIZER_PROMO";
    CacheKeys["BANNER_BLOG_AI_AUTOWRITE"] = "BANNER_BLOG_AI_AUTOWRITE";
    CacheKeys["BANNER_ENABLE_MULTI_LANGUAGE"] = "BANNER_ENABLE_MULTI_LANGUAGE";
    CacheKeys["BANNER_BLAM_THEME"] = "BANNER_BLAM_THEME";
    CacheKeys["BANNER_APP_BY_STOREWARE"] = "BANNER_APP_BY_STOREWARE";
    CacheKeys["BANNER_PAGEFLY"] = "BANNER_PAGEFLY";
    CacheKeys["WEBHOOKS_COUNT"] = "WEBHOOKS_COUNT";
    CacheKeys["TEMP_SUBSCRIPTION_DATA"] = "TEMP_SUBSCRIPTION_DATA";
    CacheKeys["TEMP_ONETIME_ADDONS"] = "TEMP_ONETIME_ADDONS";
    CacheKeys["IMAGE_OPTIMIZER"] = "IMAGE_OPTIMIZER";
    CacheKeys["COLLECTION_IMAGE_OPTIMIZER"] = "COLLECTION_IMAGE_OPTIMIZER";
    CacheKeys["ARTICLE_IMAGE_OPTIMIZER"] = "ARTICLE_IMAGE_OPTIMIZER";
    CacheKeys["AI_OPTIMIZER"] = "AI_OPTIMIZER";
    CacheKeys["ALT_TEXT_OPTIMIZER"] = "ALT_TEXT_OPTIMIZER";
    CacheKeys["INVALID_IMAGE_COUNT"] = "INVALID_IMAGE_COUNT";
    CacheKeys["AI_CONTENT_GENERATOR"] = "AI_CONTENT_GENERATOR";
    CacheKeys["AI_CONTENT_GENERATOR_LANG"] = "AI_CONTENT_GENERATOR_LANG";
    CacheKeys["USAGE_LIMIT"] = "USAGE_LIMIT";
    CacheKeys["USAGE_COUNT"] = "USAGE_COUNT";
    CacheKeys["TOTAL_USAGE_COUNT"] = "TOTAL_USAGE_COUNT";
    CacheKeys["MEDIA_STATUS"] = "MEDIA_STATUS";
    CacheKeys["MEDIA_STAGED_TARGET"] = "MEDIA_STAGED_TARGET";
    CacheKeys["MEDIA_BATCH_NUMBER"] = "MEDIA_BATCH_NUMBER";
    CacheKeys["PENDING_IMAGE_OPTIMIZATION_QUEUE"] = "PENDING_IMAGE_OPTIMIZATION_QUEUE";
    CacheKeys["PENDING_COLLECTION_IMAGE_OPTIMIZATION_QUEUE"] = "PENDING_COLLECTION_IMAGE_OPTIMIZATION_QUEUE";
    CacheKeys["PENDING_ARTICLE_IMAGE_OPTIMIZATION_QUEUE"] = "PENDING_ARTICLE_IMAGE_OPTIMIZATION_QUEUE";
    CacheKeys["PENDING_ALT_TEXT_OPTIMIZATION_QUEUE"] = "PENDING_ALT_TEXT_OPTIMIZATION_QUEUE";
    CacheKeys["PENDING_AI_OPTIMIZATION_QUEUE"] = "PENDING_AI_OPTIMIZATION_QUEUE";
    CacheKeys["QUEUE_USAGE"] = "QUEUE_USAGE";
    CacheKeys["STORES_QUEUED"] = "STORES_QUEUED";
    CacheKeys["SET_SUFFIX"] = "set";
    CacheKeys["IMAGES_QUEUED"] = "IMAGES_QUEUED";
    CacheKeys["IMAGES_PROCESSED"] = "IMAGES_PROCESSED";
    CacheKeys["IMAGES_FAILED"] = "IMAGES_FAILED";
    CacheKeys["PRODUCTS_QUEUED"] = "PRODUCTS_QUEUED";
    CacheKeys["PRODUCTS_PROCESSED"] = "PRODUCTS_PROCESSED";
    CacheKeys["ARTICLES_QUEUED"] = "ARTICLES_QUEUED";
    CacheKeys["ARTICLES_PROCESSED"] = "ARTICLES_PROCESSED";
    CacheKeys["ARTICLES_FAILED"] = "ARTICLES_FAILED";
    CacheKeys["PENDING_ARTICLE_AI_OPTIMIZATION_QUEUE"] = "PENDING_ARTICLE_AI_OPTIMIZATION_QUEUE";
    CacheKeys["PENDING_SYNC"] = "PENDING_SYNC";
    CacheKeys["MAX"] = "MAX";
    CacheKeys["MIN"] = "MIN";
    CacheKeys["PROCESSING_FAILED"] = "PROCESSING_FAILED";
    CacheKeys["DATES"] = "DATES";
    CacheKeys["STATISTICS"] = "STATISTICS";
    CacheKeys["HOST"] = "HOST";
    CacheKeys["COLLECTION_SYNC"] = "COLLECTION_SYNC";
    CacheKeys["COLLECTION_SYNC_CURSOR"] = "SYNC_CURSOR";
    CacheKeys["COLLECTION_SYNC_ONGOING"] = "SYNC_ONGOING";
    CacheKeys["COLLECTION_PRODUCTS_SYNC"] = "COLLECTION_PRODUCTS_SYNC";
    CacheKeys["COLLECTION_PRODUCTS_SYNC_CURSOR"] = "SYNC_CURSOR";
    CacheKeys["COLLECTION_PRODUCTS_SYNC_ONGOING"] = "SYNC_ONGOING";
    CacheKeys["COLLECTIONS_QUEUED"] = "COLLECTIONS_QUEUED";
    CacheKeys["COLLECTIONS_PROCESSED"] = "COLLECTIONS_PROCESSED";
    CacheKeys["PENDING_COLLECTION_AI_OPTIMIZATION_QUEUE"] = "PENDING_COLLECTION_AI_OPTIMIZATION_QUEUE";
    CacheKeys["COLLECTION_AI_OPTIMIZER"] = "COLLECTION_AI_OPTIMIZER";
    CacheKeys["BETTERDOCS_IS_INSTALLED"] = "BETTERDOCS_IS_INSTALLED";
    CacheKeys["APP_EMBED_STATUS"] = "APP_EMBED_STATUS";
    CacheKeys["BULK_OPERATION_TRACKER"] = "BULK_OPERATION_TRACKER";
    CacheKeys["EMAIL_NOTIFICATION"] = "EMAIL_NOTIFICATION";
    CacheKeys["LAST_SENT_EMAIL"] = "LAST_SENT_EMAIL";
    CacheKeys["SHOPS_PENDING_WEBHOOK_REGISTRATION"] = "SHOPS_PENDING_WEBHOOK_REGISTRATION";
})(CacheKeys || (CacheKeys = {}));
module.exports = CacheKeys;

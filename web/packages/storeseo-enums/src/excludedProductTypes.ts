/**
 * Product types that should be excluded from sync operations
 */
const EXCLUDED_PRODUCT_TYPES = {
  EASYFLOW_GENERATED: "23p_po_generated",
} as const;

/**
 * Utility function to check if a product type should be excluded from sync
 * @param productType - The product type to check
 * @returns true if the product should be excluded, false otherwise
 */
const isExcludedProductType = (productType: string | null | undefined): boolean => {
  if (!productType) return false;
  return Object.values(EXCLUDED_PRODUCT_TYPES).includes(productType as any);
};

/**
 * Utility function to filter out excluded products from an array
 * @param products - Array of products with productType property
 * @returns Filtered array with excluded products removed
 */
const filterExcludedProducts = <T extends { productType?: string | null }>(products: T[]): T[] => {
  return products.filter(product => !isExcludedProductType(product.productType));
};

export { EXCLUDED_PRODUCT_TYPES, isExcludedProductType, filterExcludedProducts };

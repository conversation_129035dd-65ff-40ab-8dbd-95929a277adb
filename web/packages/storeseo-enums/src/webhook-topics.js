"use strict";
var WebhookTopics;
(function (WebhookTopics) {
    WebhookTopics["PRODUCTS_CREATE"] = "PRODUCTS_CREATE";
    WebhookTopics["PRODUCTS_UPDATE"] = "PRODUCTS_UPDATE";
    WebhookTopics["PRODUCTS_DELETE"] = "PRODUCTS_DELETE";
    WebhookTopics["APP_UNINSTALLED"] = "APP_UNINSTALLED";
    WebhookTopics["APP_PURCHASES_ONE_TIME_UPDATE"] = "APP_PURCHASES_ONE_TIME_UPDATE";
    WebhookTopics["APP_SUBSCRIPTIONS_UPDATE"] = "APP_SUBSCRIPTIONS_UPDATE";
    WebhookTopics["THEMES_PUBLISH"] = "THEMES_PUBLISH";
    WebhookTopics["SHOP_UPDATE"] = "SHOP_UPDATE";
    WebhookTopics["LOCATIONS_CREATE"] = "LOCATIONS_CREATE";
    WebhookTopics["LOCATIONS_UPDATE"] = "LOCATIONS_UPDATE";
    WebhookTopics["LOCATIONS_DELETE"] = "LOCATIONS_DELETE";
    WebhookTopics["BULK_OPERATIONS_FINISH"] = "BULK_OPERATIONS_FINISH";
    WebhookTopics["COLLECTIONS_CREATE"] = "COLLECTIONS_CREATE";
    WebhookTopics["COLLECTIONS_UPDATE"] = "COLLECTIONS_UPDATE";
    WebhookTopics["COLLECTIONS_DELETE"] = "COLLECTIONS_DELETE";
    WebhookTopics["DOCS_CREATE"] = "DOCS_CREATE";
    WebhookTopics["DOCS_UPDATE"] = "DOCS_UPDATE";
    WebhookTopics["DOCS_DELETE"] = "DOCS_DELETE";
    WebhookTopics["CUSTOMERS_DATA_REQUEST"] = "CUSTOMERS_DATA_REQUEST";
    WebhookTopics["CUSTOMERS_REDACT"] = "CUSTOMERS_REDACT";
    WebhookTopics["SHOP_REDACT"] = "SHOP_REDACT";
    WebhookTopics["LOCALES_CREATE"] = "LOCALES_CREATE";
    WebhookTopics["LOCALES_UPDATE"] = "LOCALES_UPDATE";
})(WebhookTopics || (WebhookTopics = {}));
module.exports = WebhookTopics;

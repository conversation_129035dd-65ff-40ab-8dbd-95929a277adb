enum BlogGenerationStatus {
  MANUAL = "manual",
  PENDING = "pending",
  GENERATING = "generating",
  CONTENT_READY = "content_ready",
  DRAFT_CREATED = "draft_created",
  PUBLISHING = "publishing",
  PUBLISHED = "published",
  DRAFT_PUBLISHED = "draft_published",
  FAILED = "failed",
}

namespace BlogGenerationStatus {
  export const labels: Record<BlogGenerationStatus, string> = {
    [BlogGenerationStatus.MANUAL]: "Manual",
    [BlogGenerationStatus.PENDING]: "Pending",
    [BlogGenerationStatus.GENERATING]: "Generating",
    [BlogGenerationStatus.CONTENT_READY]: "Content Ready",
    [BlogGenerationStatus.DRAFT_CREATED]: "Draft Created",
    [BlogGenerationStatus.PUBLISHING]: "Publishing",
    [BlogGenerationStatus.PUBLISHED]: "Published",
    [BlogGenerationStatus.DRAFT_PUBLISHED]: "Draft Published",
    [BlogGenerationStatus.FAILED]: "Failed",
  };
}

export = BlogGenerationStatus;

export interface Metafield {
  id?: string;
  namespace: string;
  key: string;
  type: string;
  value: string;
  ownerId: string;
}

export enum Metafields {
  STORE_SEO = "store_seo",
  GLOBAL = "global",
  SEO = "seo",
  INSTRUCTIONS = "instructions",
}

const NAMESPACE = {
  STORE_SEO: "store_seo",
  GLOBAL: "global",
  SEO: "seo",
  INSTRUCTIONS: "instructions",
} as const;

const METAFIELD_TYPES = {
  SINGLE_LINE_TEXT: "single_line_text_field",
  JSON: "json",
  NUMBER_INTEGER: "number_integer",
  STRING: "string",
  INTEGER: "integer",
} as const;

const METAFIELD_KEYS = {
  JSON_LD_ENABLED: "jsonld_enabled",
  REDIRECT_OUT_OF_STOCK_ENABLED: "redirect_out_of_stock_enabled",
  REDIRECT_OUT_OF_STOCK_URL: "redirect_out_of_stock_url",
  LOCAL_SEO: "local_seo",
  GOOGLE_SITE_VERIFICATION_META: "google_site_verification_meta",
  OPTIMIZATION_META: "optimization_meta",
  FACEBOOK_PREVIEW_IMAGE_URL: "facebook_preview_image_url",
  TWITTER_PREVIEW_IMAGE_URL: "twitter_preview_image_url",
  HIDDEN: "hidden",
  TITLE_TAG: "title_tag",
  DESCRIPTION_TAG: "description_tag",
  NEW: "new",
  WASH: "wash",
  NO_INDEX: "no_index",
  NO_FOLLOW: "no_follow",
  CANNONICAL_URL: "cannonical_url",
  SITEMAP_DISABLED: "sitemap_disabled",
  LOCAL_SEO_BREADCRUMB_SCHEMA: "local_seo_breadcrumb_schema",
  LOCAL_SEO_COLLECTION_SCHEMA: "local_seo_collection_schema",
  LOCAL_SEO_BLOG_SCHEMA: "local_seo_blog_schema",
  LOCAL_SEO_PRODUCT_SCHEMA: "local_seo_product_schema",
  LOCAL_SEO_PRODUCT_MERCHANT_SCHEMA: "local_seo_product_merchant_schema",
  LOCAL_SEO_ARTICLE_SCHEMA: "local_seo_article_schema",
  LOCAL_SEO_ORGANIZATION_SCHEMA: "local_seo_organization_schema",
  LOCAL_SEO_COMMON_SCHEMA: "local_seo_common_schema",
  LOCAL_SEO_LOCAL_BUSINESS_SCHEMA: "local_seo_local_business_schema",
} as const;

const METAFIELD_DEFINITIONS: Record<string, Metafield> = {
  [METAFIELD_KEYS.TITLE_TAG]: {
    namespace: NAMESPACE.GLOBAL,
    key: METAFIELD_KEYS.TITLE_TAG,
    type: METAFIELD_TYPES.SINGLE_LINE_TEXT,
    value: "",
    ownerId: "",
  },
  [METAFIELD_KEYS.DESCRIPTION_TAG]: {
    namespace: NAMESPACE.GLOBAL,
    key: METAFIELD_KEYS.DESCRIPTION_TAG,
    type: METAFIELD_TYPES.SINGLE_LINE_TEXT,
    value: "",
    ownerId: "",
  },

  [METAFIELD_KEYS.HIDDEN]: {
    namespace: NAMESPACE.SEO,
    key: METAFIELD_KEYS.HIDDEN,
    type: METAFIELD_TYPES.NUMBER_INTEGER,
    value: "",
    ownerId: "",
  },

  [METAFIELD_KEYS.NO_INDEX]: {
    namespace: NAMESPACE.STORE_SEO,
    key: METAFIELD_KEYS.NO_INDEX,
    type: METAFIELD_TYPES.NUMBER_INTEGER,
    value: "",
    ownerId: "",
  },
  [METAFIELD_KEYS.NO_FOLLOW]: {
    namespace: NAMESPACE.STORE_SEO,
    key: METAFIELD_KEYS.NO_FOLLOW,
    type: METAFIELD_TYPES.NUMBER_INTEGER,
    value: "",
    ownerId: "",
  },

  [METAFIELD_KEYS.OPTIMIZATION_META]: {
    namespace: NAMESPACE.STORE_SEO,
    key: METAFIELD_KEYS.OPTIMIZATION_META,
    type: METAFIELD_TYPES.JSON,
    value: "",
    ownerId: "",
  },

  [METAFIELD_KEYS.JSON_LD_ENABLED]: {
    namespace: NAMESPACE.STORE_SEO,
    key: METAFIELD_KEYS.JSON_LD_ENABLED,
    type: METAFIELD_TYPES.SINGLE_LINE_TEXT,
    value: "",
    ownerId: "",
  },
  [METAFIELD_KEYS.REDIRECT_OUT_OF_STOCK_ENABLED]: {
    namespace: NAMESPACE.STORE_SEO,
    key: METAFIELD_KEYS.REDIRECT_OUT_OF_STOCK_ENABLED,
    type: METAFIELD_TYPES.SINGLE_LINE_TEXT,
    value: "",
    ownerId: "",
  },
  [METAFIELD_KEYS.REDIRECT_OUT_OF_STOCK_URL]: {
    namespace: NAMESPACE.STORE_SEO,
    key: METAFIELD_KEYS.REDIRECT_OUT_OF_STOCK_URL,
    type: METAFIELD_TYPES.SINGLE_LINE_TEXT,
    value: "",
    ownerId: "",
  },
  [METAFIELD_KEYS.LOCAL_SEO]: {
    namespace: NAMESPACE.STORE_SEO,
    key: METAFIELD_KEYS.LOCAL_SEO,
    type: METAFIELD_TYPES.JSON,
    value: "",
    ownerId: "",
  },
  [METAFIELD_KEYS.GOOGLE_SITE_VERIFICATION_META]: {
    namespace: NAMESPACE.STORE_SEO,
    key: METAFIELD_KEYS.GOOGLE_SITE_VERIFICATION_META,
    type: METAFIELD_TYPES.SINGLE_LINE_TEXT,
    value: "",
    ownerId: "",
  },

  [METAFIELD_KEYS.LOCAL_SEO_BREADCRUMB_SCHEMA]: {
    namespace: NAMESPACE.STORE_SEO,
    key: METAFIELD_KEYS.LOCAL_SEO_BREADCRUMB_SCHEMA,
    type: METAFIELD_TYPES.JSON,
    value: "",
    ownerId: "",
  },
  [METAFIELD_KEYS.LOCAL_SEO_COLLECTION_SCHEMA]: {
    namespace: NAMESPACE.STORE_SEO,
    key: METAFIELD_KEYS.LOCAL_SEO_COLLECTION_SCHEMA,
    type: METAFIELD_TYPES.JSON,
    value: "",
    ownerId: "",
  },
  [METAFIELD_KEYS.LOCAL_SEO_BLOG_SCHEMA]: {
    namespace: NAMESPACE.STORE_SEO,
    key: METAFIELD_KEYS.LOCAL_SEO_BLOG_SCHEMA,
    type: METAFIELD_TYPES.JSON,
    value: "",
    ownerId: "",
  },
  [METAFIELD_KEYS.LOCAL_SEO_PRODUCT_SCHEMA]: {
    namespace: NAMESPACE.STORE_SEO,
    key: METAFIELD_KEYS.LOCAL_SEO_PRODUCT_SCHEMA,
    type: METAFIELD_TYPES.JSON,
    value: "",
    ownerId: "",
  },
  [METAFIELD_KEYS.LOCAL_SEO_PRODUCT_MERCHANT_SCHEMA]: {
    namespace: NAMESPACE.STORE_SEO,
    key: METAFIELD_KEYS.LOCAL_SEO_PRODUCT_MERCHANT_SCHEMA,
    type: METAFIELD_TYPES.JSON,
    value: "",
    ownerId: "",
  },
  [METAFIELD_KEYS.LOCAL_SEO_ARTICLE_SCHEMA]: {
    namespace: NAMESPACE.STORE_SEO,
    key: METAFIELD_KEYS.LOCAL_SEO_ARTICLE_SCHEMA,
    type: METAFIELD_TYPES.JSON,
    value: "",
    ownerId: "",
  },
  [METAFIELD_KEYS.LOCAL_SEO_ORGANIZATION_SCHEMA]: {
    namespace: NAMESPACE.STORE_SEO,
    key: METAFIELD_KEYS.LOCAL_SEO_ORGANIZATION_SCHEMA,
    type: METAFIELD_TYPES.JSON,
    value: "",
    ownerId: "",
  },
  [METAFIELD_KEYS.LOCAL_SEO_COMMON_SCHEMA]: {
    namespace: NAMESPACE.STORE_SEO,
    key: METAFIELD_KEYS.LOCAL_SEO_COMMON_SCHEMA,
    type: METAFIELD_TYPES.JSON,
    value: "",
    ownerId: "",
  },
  [METAFIELD_KEYS.LOCAL_SEO_LOCAL_BUSINESS_SCHEMA]: {
    namespace: NAMESPACE.STORE_SEO,
    key: METAFIELD_KEYS.LOCAL_SEO_LOCAL_BUSINESS_SCHEMA,
    type: METAFIELD_TYPES.JSON,
    value: "",
    ownerId: "",
  },
};

const metafieldKeysFilterArray = [
  `${NAMESPACE.GLOBAL}.${METAFIELD_KEYS.TITLE_TAG}`,
  `${NAMESPACE.GLOBAL}.${METAFIELD_KEYS.DESCRIPTION_TAG}`,

  `${NAMESPACE.SEO}.${METAFIELD_KEYS.HIDDEN}`,

  `${NAMESPACE.STORE_SEO}.${METAFIELD_KEYS.NO_FOLLOW}`,
  `${NAMESPACE.STORE_SEO}.${METAFIELD_KEYS.NO_INDEX}`,
  `${NAMESPACE.STORE_SEO}.${METAFIELD_KEYS.FACEBOOK_PREVIEW_IMAGE_URL}`,
  `${NAMESPACE.STORE_SEO}.${METAFIELD_KEYS.TWITTER_PREVIEW_IMAGE_URL}`,
  `${NAMESPACE.STORE_SEO}.${METAFIELD_KEYS.CANNONICAL_URL}`,
];

export { METAFIELD_DEFINITIONS, METAFIELD_KEYS, METAFIELD_TYPES, metafieldKeysFilterArray, NAMESPACE };

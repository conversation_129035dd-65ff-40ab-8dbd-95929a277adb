"use strict";
var SocketEvents;
(function (SocketEvents) {
    SocketEvents["PRODUCT_SYNC_UPDATE"] = "PRODUCT_SYNC_UPDATE";
    SocketEvents["PRODUCT_SYNC_COMPLETE"] = "PRODUCT_SYNC_COMPLETE";
    SocketEvents["PRODUCT_OPTIMIZATION_COMPLETE"] = "PRODUCT_OPTIMIZATION_COMPLETE";
    SocketEvents["PAGE_SYNC_UPDATE"] = "PAGE_SYNC_UPDATE";
    SocketEvents["PAGE_SYNC_COMPLETE"] = "PAGE_SYNC_COMPLETE";
    SocketEvents["BLOG_SYNC_UPDATE"] = "BLOG_SYNC_UPDATE";
    SocketEvents["BLOG_SYNC_COMPLETE"] = "BLOG_SYNC_COMPLETE";
    SocketEvents["JOIN_ROOM"] = "JOIN_ROOM";
    SocketEvents["LEAVE_ROOM"] = "LEAVE_ROOM";
    SocketEvents["NEW_NOTIFICATION"] = "NEW_NOTIFICATION";
    SocketEvents["NOTIFICATION_READ"] = "NOTIFICATION_READ";
    SocketEvents["NOTIFICATION_ALL_READ"] = "NOTIFICATION_ALL_READ";
    SocketEvents["COLLECTION_SYNC_UPDATE"] = "COLLECTION_SYNC_UPDATE";
    SocketEvents["COLLECTION_SYNC_COMPLETE"] = "COLLECTION_SYNC_COMPLETE";
    SocketEvents["COLLECTION_PRODUCTS_SYNC_COMPLETE"] = "COLLECTION_PRODUCTS_SYNC_COMPLETE";
    SocketEvents["DOC_SYNC_UPDATE"] = "DOC_SYNC_UPDATE";
    SocketEvents["DOC_SYNC_COMPLETE"] = "DOC_SYNC_COMPLETE";
    SocketEvents["MULTI_LANGUAGE_PRODUCTS_SYNCED"] = "MULTI_LANGUAGE_PRODUCTS_SYNCED";
    SocketEvents["BACKUP_INITIALIZED"] = "BACKUP_INITIALIZED";
    SocketEvents["BACKUP_COMPLETED"] = "BACKUP_COMPLETED";
    SocketEvents["BACKUP_RESTORE_INITIALIZED"] = "BACKUP_RESTORE_INITIALIZED";
    SocketEvents["BACKUP_RESTORE_COMPLETED"] = "BACKUP_RESTORE_COMPLETED";
    SocketEvents["IMAGE_ALT_OPTIMIZATION_BULK_OP_COMPLETE"] = "IMAGE_ALT_OPTIMIZATION_BULK_OP_COMPLETE";
    SocketEvents["IMAGE_OPTIMIZATION_BULK_OP_COMPLETE"] = "IMAGE_OPTIMIZATION_BULK_OP_COMPLETE";
    SocketEvents["PRODUCT_AI_OPTIMIZATION_BULK_OP_COMPLETE"] = "PRODUCT_AI_OPTIMIZATION_BULK_OP_COMPLETE";
    SocketEvents["COLLECTION_AI_OPTIMIZATION_BULK_OP_COMPLETE"] = "COLLECTION_AI_OPTIMIZATION_BULK_OP_COMPLETE";
    SocketEvents["ARTICLE_AI_OPTIMIZATION_BULK_OP_COMPLETE"] = "ARTICLE_AI_OPTIMIZATION_BULK_OP_COMPLETE";
    SocketEvents["LLMS_TXT_GENERATION_COMPLETE"] = "LLMS_TXT_GENERATION_COMPLETE";
    SocketEvents["LLMS_TXT_GENERATION_FAILED"] = "LLMS_TXT_GENERATION_FAILED";
})(SocketEvents || (SocketEvents = {}));
module.exports = SocketEvents;

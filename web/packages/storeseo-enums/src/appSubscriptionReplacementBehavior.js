"use strict";
var AppSubscriptionReplacementBehavior;
(function (AppSubscriptionReplacementBehavior) {
    AppSubscriptionReplacementBehavior["APPLY_IMMEDIATELY"] = "APPLY_IMMEDIATELY";
    AppSubscriptionReplacementBehavior["APPLY_ON_NEXT_BILLING_CYCLE"] = "APPLY_ON_NEXT_BILLING_CYCLE";
    AppSubscriptionReplacementBehavior["STANDARD"] = "STANDARD";
})(AppSubscriptionReplacementBehavior || (AppSubscriptionReplacementBehavior = {}));
module.exports = AppSubscriptionReplacementBehavior;

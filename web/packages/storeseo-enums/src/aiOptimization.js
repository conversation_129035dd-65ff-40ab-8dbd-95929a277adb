"use strict";
var AiOptimizationStatus;
(function (AiOptimizationStatus) {
    AiOptimizationStatus["NOT_OPTIMIZED"] = "NOT_OPTIMIZED";
    AiOptimizationStatus["PENDING"] = "PENDING";
    AiOptimizationStatus["DISPATCHED"] = "DISPATCHED";
    AiOptimizationStatus["PROCESSING"] = "PROCESSING";
    AiOptimizationStatus["SAVING"] = "SAVING";
    AiOptimizationStatus["OPTIMIZED"] = "OPTIMIZED";
})(AiOptimizationStatus || (AiOptimizationStatus = {}));
(function (AiOptimizationStatus) {
    AiOptimizationStatus.labels = {
        [AiOptimizationStatus.NOT_OPTIMIZED]: "Not Optimized",
        [AiOptimizationStatus.PENDING]: "Pending",
        [AiOptimizationStatus.DISPATCHED]: "Pending",
        [AiOptimizationStatus.PROCESSING]: "Pending",
        [AiOptimizationStatus.SAVING]: "Pending",
        [AiOptimizationStatus.OPTIMIZED]: "Optimized",
    };
})(AiOptimizationStatus || (AiOptimizationStatus = {}));
module.exports = AiOptimizationStatus;

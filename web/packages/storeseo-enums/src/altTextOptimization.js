"use strict";
var AltTextOptimizationStatus;
(function (AltTextOptimizationStatus) {
    AltTextOptimizationStatus["NOT_OPTIMIZED"] = "NOT_OPTIMIZED";
    AltTextOptimizationStatus["PENDING"] = "PENDING";
    AltTextOptimizationStatus["DISPATCHED"] = "DISPATCHED";
    AltTextOptimizationStatus["PROCESSING"] = "PROCESSING";
    AltTextOptimizationStatus["SAVING"] = "SAVING";
    AltTextOptimizationStatus["OPTIMIZED"] = "OPTIMIZED";
    AltTextOptimizationStatus["RESTORED"] = "RESTORED";
})(AltTextOptimizationStatus || (AltTextOptimizationStatus = {}));
(function (AltTextOptimizationStatus) {
    AltTextOptimizationStatus.labels = {
        [AltTextOptimizationStatus.NOT_OPTIMIZED]: "Not Generated",
        [AltTextOptimizationStatus.PENDING]: "Pending",
        [AltTextOptimizationStatus.DISPATCHED]: "Pending",
        [AltTextOptimizationStatus.PROCESSING]: "Pending",
        [AltTextOptimizationStatus.SAVING]: "Pending",
        [AltTextOptimizationStatus.OPTIMIZED]: "AI Generated",
        [AltTextOptimizationStatus.RESTORED]: "Not Generated",
    };
})(AltTextOptimizationStatus || (AltTextOptimizationStatus = {}));
module.exports = AltTextOptimizationStatus;

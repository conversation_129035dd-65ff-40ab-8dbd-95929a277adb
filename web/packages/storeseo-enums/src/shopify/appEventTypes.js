"use strict";
var AppEventTypes;
(function (AppEventTypes) {
    AppEventTypes["CREDIT_APPLIED"] = "CREDIT_APPLIED";
    AppEventTypes["CREDIT_FAILED"] = "CREDIT_FAILED";
    AppEventTypes["CREDIT_PENDING"] = "CREDIT_PENDING";
    AppEventTypes["ONE_TIME_CHARGE_ACCEPTED"] = "ONE_TIME_CHARGE_ACCEPTED";
    AppEventTypes["ONE_TIME_CHARGE_ACTIVATED"] = "ONE_TIME_CHARGE_ACTIVATED";
    AppEventTypes["ONE_TIME_CHARGE_DECLINED"] = "ONE_TIME_CHARGE_DECLINED";
    AppEventTypes["ONE_TIME_CHARGE_EXPIRED"] = "ONE_TIME_CHARGE_EXPIRED";
    AppEventTypes["RELATIONSHIP_DEACTIVATED"] = "RELATIONSHIP_DEACTIVATED";
    AppEventTypes["RELATIONSHIP_INSTALLED"] = "RELATIONSHIP_INSTALLED";
    AppEventTypes["RELATIONSHIP_REACTIVATED"] = "RELATIONSHIP_REACTIVATED";
    AppEventTypes["RELATIONSHIP_UNINSTALLED"] = "RELATIONSHIP_UNINSTALLED";
    AppEventTypes["SUBSCRIPTION_APPROACHING_CAPPED_AMOUNT"] = "SUBSCRIPTION_APPROACHING_CAPPED_AMOUNT";
    AppEventTypes["SUBSCRIPTION_CAPPED_AMOUNT_UPDATED"] = "SUBSCRIPTION_CAPPED_AMOUNT_UPDATED";
    AppEventTypes["SUBSCRIPTION_CHARGE_ACCEPTED"] = "SUBSCRIPTION_CHARGE_ACCEPTED";
    AppEventTypes["SUBSCRIPTION_CHARGE_ACTIVATED"] = "SUBSCRIPTION_CHARGE_ACTIVATED";
    AppEventTypes["SUBSCRIPTION_CHARGE_CANCELED"] = "SUBSCRIPTION_CHARGE_CANCELED";
    AppEventTypes["SUBSCRIPTION_CHARGE_DECLINED"] = "SUBSCRIPTION_CHARGE_DECLINED";
    AppEventTypes["SUBSCRIPTION_CHARGE_EXPIRED"] = "SUBSCRIPTION_CHARGE_EXPIRED";
    AppEventTypes["SUBSCRIPTION_CHARGE_FROZEN"] = "SUBSCRIPTION_CHARGE_FROZEN";
    AppEventTypes["SUBSCRIPTION_CHARGE_UNFROZEN"] = "SUBSCRIPTION_CHARGE_UNFROZEN";
    AppEventTypes["USAGE_CHARGE_APPLIED"] = "USAGE_CHARGE_APPLIED";
})(AppEventTypes || (AppEventTypes = {}));
module.exports = AppEventTypes;

enum AppEventTypes {
  CREDIT_APPLIED = "CREDIT_APPLIED",
  CREDIT_FAILED = "CREDIT_FAILED",
  CREDIT_PENDING = "CREDIT_PENDING",
  ONE_TIME_CHARGE_ACCEPTED = "ONE_TIME_CHARGE_ACCEPTED",
  ONE_TIME_CHARGE_ACTIVATED = "ONE_TIME_CHARGE_ACTIVATED",
  ONE_TIME_CHARGE_DECLINED = "ONE_TIME_CHARGE_DECLINED",
  ONE_TIME_CHARGE_EXPIRED = "ONE_TIME_CHARGE_EXPIRED",
  RELATIONSHIP_DEACTIVATED = "RELATIONSHIP_DEACTIVATED",
  R<PERSON><PERSON><PERSON>SHIP_INSTALLED = "RELATIONSHIP_INSTALLED",
  RELATIONSHIP_REACTIVATED = "RELATIONSHIP_REACTIVATED",
  RELATIONSHIP_UNINSTALLED = "RELATIONSHIP_UNINSTALLED",
  SUBSCRIPTION_APPROACHING_CAPPED_AMOUNT = "SUBSCRIPTION_APPROACHING_CAPPED_AMOUNT",
  SUBSCRIPTION_CAPPED_AMOUNT_UPDATED = "SUBSCRIPTION_CAPPED_AMOUNT_UPDATED",
  SUBSCRIPTION_CHARGE_ACCEPTED = "SUBSCRIPTION_CHARGE_ACCEPTED",
  SUBSCRIPTION_CHARGE_ACTIVATED = "SUBSCRIPTION_CHARGE_ACTIVATED",
  SUBSCRIPTION_CHARGE_CANCELED = "SUBSCRIPTION_CHARGE_CANCELED",
  SUBSCRIPTION_CHARGE_DECLINED = "SUBSCRIPTION_CHARGE_DECLINED",
  SUBSCRIPTION_CHARGE_EXPIRED = "SUBSCRIPTION_CHARGE_EXPIRED",
  SUBSCRIPTION_CHARGE_FROZEN = "SUBSCRIPTION_CHARGE_FROZEN",
  SUBSCRIPTION_CHARGE_UNFROZEN = "SUBSCRIPTION_CHARGE_UNFROZEN",
  USAGE_CHARGE_APPLIED = "USAGE_CHARGE_APPLIED",
}
export = AppEventTypes;

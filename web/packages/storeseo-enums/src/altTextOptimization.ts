enum AltTextOptimizationStatus {
  NOT_OPTIMIZED = "NOT_OPTIMIZED",
  PENDING = "PENDING",
  DISPATCHED = "DISPATCHED",
  PROCESSING = "PROCESSING",
  SAVING = "SAVING",
  OPTIMIZED = "OPTIMIZED",
  RESTORED = "RESTORED",
}
namespace AltTextOptimizationStatus {
  export const labels: Record<AltTextOptimizationStatus, string> = {
    [AltTextOptimizationStatus.NOT_OPTIMIZED]: "Not Generated",
    [AltTextOptimizationStatus.PENDING]: "Pending",
    [AltTextOptimizationStatus.DISPATCHED]: "Pending",
    [AltTextOptimizationStatus.PROCESSING]: "Pending",
    [AltTextOptimizationStatus.SAVING]: "Pending",
    [AltTextOptimizationStatus.OPTIMIZED]: "AI Generated",
    [AltTextOptimizationStatus.RESTORED]: "Not Generated",
  };
}

export = AltTextOptimizationStatus;

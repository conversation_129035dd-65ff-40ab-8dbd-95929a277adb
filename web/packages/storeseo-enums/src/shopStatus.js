"use strict";
var ShopStatus;
(function (ShopStatus) {
    ShopStatus["ACTIVE"] = "ACTIVE";
    ShopStatus["INACTIVE"] = "INACTIVE";
    ShopStatus["UNINSTALLED"] = "UNINSTALLED";
    ShopStatus["CLOSED"] = "CLOSED";
    ShopStatus["DELETED"] = "DELETED";
})(ShopStatus || (ShopStatus = {}));
const getStatus = (code) => {
    switch (code) {
        case 401:
            return ShopStatus.UNINSTALLED;
        case 402:
            return ShopStatus.CLOSED;
        default:
            return ShopStatus.INACTIVE;
    }
};
module.exports = ShopStatus;

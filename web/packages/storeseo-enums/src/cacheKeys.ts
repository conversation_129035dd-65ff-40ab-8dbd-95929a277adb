enum CacheKeys {
  HIDE_PRO_PACKAGE_AD = "HIDE_PRO_PACKAGE_AD",
  PRODUCT_SYNC_CURSOR = "PRODUCT_SYNC_CURSOR",
  ACTIVE_SHOPIFY_SHOPS = "ACTIVE_SHOPIFY_SHOPS",

  MULTI_LANGUAGE = "MULTI_LANGUAGE",
  HAS_MULTIPLE_LANGUAGES = "HAS_MULTIPLE_LANGUAGES",
  ENABLED_MULTI_LANGUAGE = "ENABLED_MULTI_LANGUAGE",
  DEFAULT_LANGUAGE = "DEFAULT_LANGUAGE",

  ARTICLE_AI_OPTIMIZER = "ARTICLE_AI_OPTIMIZER",

  PRODUCT_SYNC_ONGOING = "PRODUCT_SYNC_ONGOING",
  PRODUCT_SYNC_FILE_CURSOR = "PRODUCT_SYNC_FILE_CURSOR",
  PRODUCT_SYNC_FILE = "PRODUCT_SYNC_FILE",

  MIGRATE_DATA_FROM_APP = "MIGRATE_DATA_FROM_APP",

  PAGE_SYNC_ONGOING = "PAGE_SYNC_ONGOING",
  BLOG_SYNC_ONGOING = "BLOG_SYNC_ONGOING",
  LLMS_TXT_GENERATION_ONGOING = "LLMS_TXT_GENERATION_ONGOING",
  BLOGS_PENDING_IN_QUEUE = "BLOGS_PENDING_IN_QUEUE",
  API_RATE_LIMIT_EXCEEDED = "API_RATE_LIMIT_EXCEEDED",
  SHOPIFY_GRAPHQL_API = "SHOPIFY_GRAPHQL_API",
  SHOPIFY_HTTP_API = "SHOPIFY_HTTP_API",
  AUTO_OPTIMIZATION_TASK = "AUTO_OPTIMIZATION_TASK",
  PRODUCTS_TO_OPTIMIZE = "PRODUCTS_TO_OPTIMIZE",
  PRODUCTS_OPTIMIZED = "PRODUCTS_OPTIMIZED",
  TOTAL_BATCHES = "TOTAL_BATCHES",
  LAST_BATCH_NUMBER = "LAST_BATCH_NUMBER",
  RUNNING = "RUNNING",
  ONBOARD_STEP = "ONBOARD_STEP",
  PLAN_ID = "PLAN_ID",

  BANNER_BETTERDOCS = "BANNER_BETTERDOCS",
  BANNER_GET_STARTED = "BANNER_GET_STARTED",
  BANNER_SEO_GUIDE = "BANNER_SEO_GUIDE",
  BANNER_HOMEPAGE_HINT = "BANNER_HOMEPAGE_HINT",
  BANNER_DEAL = "BANNER_DEAL",
  BANNER_GOOGLE_SITEMAP = "BANNER_GOOGLE_SITEMAP",
  BANNER_WHATS_NEW = "BANNER_WHATS_NEW",
  BANNER_SCHEDULE_A_CALL = "BANNER_SCHEDULE_A_CALL",
  BANNER_VERIFY_STORE = "BANNER_VERIFY_STORE",
  BANNER_AI_CREDIT_GIFT = "BANNER_AI_CREDIT_GIFT",
  BANNER_PARTNERSHIPS = "BANNER_PARTNERSHIPS",
  BANNER_UPGRADE_IMAGE_OPTIMIZER = "BANNER_UPGRADE_IMAGE_OPTIMIZER",
  BANNER_ENABLE_AUTO_IMAGE_OPTIMIZER = "BANNER_ENABLE_AUTO_IMAGE_OPTIMIZER",
  BANNER_ENABLE_AUTO_AI_OPTIMIZER = "BANNER_ENABLE_AUTO_AI_OPTIMIZER",

  BANNER_ENABLE_AI_OPTIMIZER = "BANNER_ENABLE_AI_OPTIMIZER",
  BANNER_CREDIT_BUNDLE_GUIDE = "BANNER_CREDIT_BUNDLE_GUIDE",
  BANNER_AI_OPTIMIZER_PROMO = "BANNER_AI_OPTIMIZER_PROMO",
  BANNER_BLOG_AI_AUTOWRITE = "BANNER_BLOG_AI_AUTOWRITE",

  BANNER_ENABLE_MULTI_LANGUAGE = "BANNER_ENABLE_MULTI_LANGUAGE",

  // Exclusive partner
  BANNER_BLAM_THEME = "BANNER_BLAM_THEME",
  BANNER_APP_BY_STOREWARE = "BANNER_APP_BY_STOREWARE",
  BANNER_PAGEFLY = "BANNER_PAGEFLY",

  WEBHOOKS_COUNT = "WEBHOOKS_COUNT",

  TEMP_SUBSCRIPTION_DATA = "TEMP_SUBSCRIPTION_DATA",
  TEMP_ONETIME_ADDONS = "TEMP_ONETIME_ADDONS",

  IMAGE_OPTIMIZER = "IMAGE_OPTIMIZER",
  COLLECTION_IMAGE_OPTIMIZER = "COLLECTION_IMAGE_OPTIMIZER",
  ARTICLE_IMAGE_OPTIMIZER = "ARTICLE_IMAGE_OPTIMIZER",
  AI_OPTIMIZER = "AI_OPTIMIZER",
  ALT_TEXT_OPTIMIZER = "ALT_TEXT_OPTIMIZER",

  INVALID_IMAGE_COUNT = "INVALID_IMAGE_COUNT",

  AI_CONTENT_GENERATOR = "AI_CONTENT_GENERATOR",
  AI_CONTENT_GENERATOR_LANG = "AI_CONTENT_GENERATOR_LANG",
  USAGE_LIMIT = "USAGE_LIMIT",
  USAGE_COUNT = "USAGE_COUNT",
  TOTAL_USAGE_COUNT = "TOTAL_USAGE_COUNT",

  MEDIA_STATUS = "MEDIA_STATUS",
  MEDIA_STAGED_TARGET = "MEDIA_STAGED_TARGET",
  MEDIA_BATCH_NUMBER = "MEDIA_BATCH_NUMBER",

  PENDING_IMAGE_OPTIMIZATION_QUEUE = "PENDING_IMAGE_OPTIMIZATION_QUEUE",
  PENDING_COLLECTION_IMAGE_OPTIMIZATION_QUEUE = "PENDING_COLLECTION_IMAGE_OPTIMIZATION_QUEUE",
  PENDING_ARTICLE_IMAGE_OPTIMIZATION_QUEUE = "PENDING_ARTICLE_IMAGE_OPTIMIZATION_QUEUE",
  PENDING_ALT_TEXT_OPTIMIZATION_QUEUE = "PENDING_ALT_TEXT_OPTIMIZATION_QUEUE",
  PENDING_AI_OPTIMIZATION_QUEUE = "PENDING_AI_OPTIMIZATION_QUEUE",

  QUEUE_USAGE = "QUEUE_USAGE",
  STORES_QUEUED = "STORES_QUEUED",
  SET_SUFFIX = "set",

  IMAGES_QUEUED = "IMAGES_QUEUED",
  IMAGES_PROCESSED = "IMAGES_PROCESSED",
  IMAGES_FAILED = "IMAGES_FAILED",

  PRODUCTS_QUEUED = "PRODUCTS_QUEUED",
  PRODUCTS_PROCESSED = "PRODUCTS_PROCESSED",

  ARTICLES_QUEUED = "ARTICLES_QUEUED",
  ARTICLES_PROCESSED = "ARTICLES_PROCESSED",
  ARTICLES_FAILED = "ARTICLES_FAILED",
  PENDING_ARTICLE_AI_OPTIMIZATION_QUEUE = "PENDING_ARTICLE_AI_OPTIMIZATION_QUEUE",

  PENDING_SYNC = "PENDING_SYNC",

  MAX = "MAX",
  MIN = "MIN",

  PROCESSING_FAILED = "PROCESSING_FAILED",

  DATES = "DATES",
  STATISTICS = "STATISTICS",

  HOST = "HOST",

  // Collection related keys
  COLLECTION_SYNC = "COLLECTION_SYNC",
  COLLECTION_SYNC_CURSOR = "SYNC_CURSOR",
  COLLECTION_SYNC_ONGOING = "SYNC_ONGOING",
  COLLECTION_PRODUCTS_SYNC = "COLLECTION_PRODUCTS_SYNC",
  COLLECTION_PRODUCTS_SYNC_CURSOR = "SYNC_CURSOR",
  COLLECTION_PRODUCTS_SYNC_ONGOING = "SYNC_ONGOING",
  COLLECTIONS_QUEUED = "COLLECTIONS_QUEUED",
  COLLECTIONS_PROCESSED = "COLLECTIONS_PROCESSED",
  PENDING_COLLECTION_AI_OPTIMIZATION_QUEUE = "PENDING_COLLECTION_AI_OPTIMIZATION_QUEUE",
  COLLECTION_AI_OPTIMIZER = "COLLECTION_AI_OPTIMIZER",
  // BetterDocs related keys
  BETTERDOCS_IS_INSTALLED = "BETTERDOCS_IS_INSTALLED",

  APP_EMBED_STATUS = "APP_EMBED_STATUS",

  // Bulk operation tracker
  BULK_OPERATION_TRACKER = "BULK_OPERATION_TRACKER",

  // Email Notification
  EMAIL_NOTIFICATION = "EMAIL_NOTIFICATION",
  LAST_SENT_EMAIL = "LAST_SENT_EMAIL",

  // Webhook Registration
  SHOPS_PENDING_WEBHOOK_REGISTRATION = "SHOPS_PENDING_WEBHOOK_REGISTRATION",
}

export = CacheKeys;

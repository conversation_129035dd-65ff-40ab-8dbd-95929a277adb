"use strict";
var DimensionOrderTypes;
(function (DimensionOrderTypes) {
    DimensionOrderTypes["ORDER_TYPE_UNSPECIFIED"] = "ORDER_TYPE_UNSPECIFIED";
    DimensionOrderTypes["ALPHANUMERIC"] = "ALPHANUMERIC";
    DimensionOrderTypes["CASE_INSENSITIVE_ALPHANUMERIC"] = "CASE_INSENSITIVE_ALPHANUMERIC";
    DimensionOrderTypes["NUMERIC"] = "NUMERIC";
})(DimensionOrderTypes || (DimensionOrderTypes = {}));
module.exports = DimensionOrderTypes;

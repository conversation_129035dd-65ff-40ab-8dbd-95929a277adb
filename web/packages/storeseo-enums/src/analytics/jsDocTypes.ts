export interface DateRange {
  startDate: string;
  endDate: string;
}

export interface SingleDimensionMetrics {
  metricName: string;
  dimensionName: string;
  dateRange: DateRange;
  total: number;
  values: { [key: string]: string };
}

export interface SingleDimensionMetricsWithPrevData extends SingleDimensionMetrics {
  prevData?: SingleDimensionMetrics;
}

export interface PageReport {
  title: string;
  path: string;
  metrics: { [key: string]: string };
}

export interface PageReportWithPrevData extends PageReport {
  prevData?: PageReport;
}

export interface KeywordsReport {
  dateRange: DateRange;
  totalUsers: number;
}

export interface KeywordsReportWithPrevData extends KeywordsReport {
  prevData?: KeywordsReport;
}

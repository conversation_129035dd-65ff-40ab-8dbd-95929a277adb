"use strict";
var BlogGenerationStatus;
(function (BlogGenerationStatus) {
    BlogGenerationStatus["MANUAL"] = "manual";
    BlogGenerationStatus["PENDING"] = "pending";
    BlogGenerationStatus["GENERATING"] = "generating";
    BlogGenerationStatus["CONTENT_READY"] = "content_ready";
    BlogGenerationStatus["DRAFT_CREATED"] = "draft_created";
    BlogGenerationStatus["PUBLISHING"] = "publishing";
    BlogGenerationStatus["PUBLISHED"] = "published";
    BlogGenerationStatus["DRAFT_PUBLISHED"] = "draft_published";
    BlogGenerationStatus["FAILED"] = "failed";
})(BlogGenerationStatus || (BlogGenerationStatus = {}));
(function (BlogGenerationStatus) {
    BlogGenerationStatus.labels = {
        [BlogGenerationStatus.MANUAL]: "Manual",
        [BlogGenerationStatus.PENDING]: "Pending",
        [BlogGenerationStatus.GENERATING]: "Generating",
        [BlogGenerationStatus.CONTENT_READY]: "Content Ready",
        [BlogGenerationStatus.DRAFT_CREATED]: "Draft Created",
        [BlogGenerationStatus.PUBLISHING]: "Publishing",
        [BlogGenerationStatus.PUBLISHED]: "Published",
        [BlogGenerationStatus.DRAFT_PUBLISHED]: "Draft Published",
        [BlogGenerationStatus.FAILED]: "Failed",
    };
})(BlogGenerationStatus || (BlogGenerationStatus = {}));
module.exports = BlogGenerationStatus;

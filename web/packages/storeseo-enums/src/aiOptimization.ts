enum AiOptimizationStatus {
  NOT_OPTIMIZED = "NOT_OPTIMIZED",
  PENDING = "PENDING",
  DISPATCHED = "DISPATCHED",
  PROCESSING = "PROCESSING",
  SAVING = "SAVING",
  OPTIMIZED = "OPTIMIZED",
}

namespace AiOptimizationStatus {
  export const labels: Record<AiOptimizationStatus, string> = {
    [AiOptimizationStatus.NOT_OPTIMIZED]: "Not Optimized",
    [AiOptimizationStatus.PENDING]: "Pending",
    [AiOptimizationStatus.DISPATCHED]: "Pending",
    [AiOptimizationStatus.PROCESSING]: "Pending",
    [AiOptimizationStatus.SAVING]: "Pending",
    [AiOptimizationStatus.OPTIMIZED]: "Optimized",
  };
}

export = AiOptimizationStatus;

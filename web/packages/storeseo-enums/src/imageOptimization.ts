enum ImageOptimizationStatus {
  NOT_OPTIMIZED = "NOT_OPTIMIZED",
  PENDING = "PENDING",
  DISPATCHED = "DISPATCHED",
  PROCESSING = "PROCESSING",
  SAVING = "SAVING",
  OPTIMIZED = "OPTIMIZED",
  ALREADY_OPTIMIZED = "ALREADY_OPTIMIZED",
  RESTORED = "RESTORED",
}

interface OptimizationMeta {
  original_image_url: string | null;
}

interface OptimizationStats {
  optimization_count: number;
  restore_count: number;
  last_queued_date: Date | null;
  last_queue_process_completed_date: Date | null;
}

namespace ImageOptimizationStatus {
  export const defaultValue: ImageOptimizationStatus = ImageOptimizationStatus.NOT_OPTIMIZED;

  export const labels: Record<ImageOptimizationStatus, string> = {
    [ImageOptimizationStatus.NOT_OPTIMIZED]: "Not Optimized",
    [ImageOptimizationStatus.PENDING]: "Pending",
    [ImageOptimizationStatus.DISPATCHED]: "Pending",
    [ImageOptimizationStatus.PROCESSING]: "Pending",
    [ImageOptimizationStatus.SAVING]: "Pending",
    [ImageOptimizationStatus.OPTIMIZED]: "Optimized",
    [ImageOptimizationStatus.ALREADY_OPTIMIZED]: "Already Optimized",
    [ImageOptimizationStatus.RESTORED]: "Not Optimized",
  };

  export const optimizationMeta: OptimizationMeta = {
    original_image_url: null,
  };

  export const optimizationStats: OptimizationStats = {
    optimization_count: 0,
    restore_count: 0,
    last_queued_date: null,
    last_queue_process_completed_date: null,
  };
}

export = ImageOptimizationStatus;

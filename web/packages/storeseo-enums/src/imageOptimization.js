"use strict";
var ImageOptimizationStatus;
(function (ImageOptimizationStatus) {
    ImageOptimizationStatus["NOT_OPTIMIZED"] = "NOT_OPTIMIZED";
    ImageOptimizationStatus["PENDING"] = "PENDING";
    ImageOptimizationStatus["DISPATCHED"] = "DISPATCHED";
    ImageOptimizationStatus["PROCESSING"] = "PROCESSING";
    ImageOptimizationStatus["SAVING"] = "SAVING";
    ImageOptimizationStatus["OPTIMIZED"] = "OPTIMIZED";
    ImageOptimizationStatus["ALREADY_OPTIMIZED"] = "ALREADY_OPTIMIZED";
    ImageOptimizationStatus["RESTORED"] = "RESTORED";
})(ImageOptimizationStatus || (ImageOptimizationStatus = {}));
(function (ImageOptimizationStatus) {
    ImageOptimizationStatus.defaultValue = ImageOptimizationStatus.NOT_OPTIMIZED;
    ImageOptimizationStatus.labels = {
        [ImageOptimizationStatus.NOT_OPTIMIZED]: "Not Optimized",
        [ImageOptimizationStatus.PENDING]: "Pending",
        [ImageOptimizationStatus.DISPATCHED]: "Pending",
        [ImageOptimizationStatus.PROCESSING]: "Pending",
        [ImageOptimizationStatus.SAVING]: "Pending",
        [ImageOptimizationStatus.OPTIMIZED]: "Optimized",
        [ImageOptimizationStatus.ALREADY_OPTIMIZED]: "Already Optimized",
        [ImageOptimizationStatus.RESTORED]: "Not Optimized",
    };
    ImageOptimizationStatus.optimizationMeta = {
        original_image_url: null,
    };
    ImageOptimizationStatus.optimizationStats = {
        optimization_count: 0,
        restore_count: 0,
        last_queued_date: null,
        last_queue_process_completed_date: null,
    };
})(ImageOptimizationStatus || (ImageOptimizationStatus = {}));
module.exports = ImageOptimizationStatus;

"use strict";
var ResponseCodes;
(function (ResponseCodes) {
    ResponseCodes[ResponseCodes["SUCCESS"] = 200] = "SUCCESS";
    ResponseCodes[ResponseCodes["FAILED"] = 400] = "FAILED";
    ResponseCodes[ResponseCodes["AUTH_FAILED"] = 403] = "AUTH_FAILED";
    ResponseCodes[ResponseCodes["NOT_FOUND"] = 404] = "NOT_FOUND";
    ResponseCodes[ResponseCodes["VALIDATION_ERROR"] = 422] = "VALIDATION_ERROR";
    ResponseCodes[ResponseCodes["SERVER_ERROR"] = 500] = "SERVER_ERROR";
})(ResponseCodes || (ResponseCodes = {}));
module.exports = ResponseCodes;

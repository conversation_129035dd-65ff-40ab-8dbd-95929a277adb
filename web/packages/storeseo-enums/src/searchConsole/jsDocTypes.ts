export interface SearchConsoleAnalyticsQueryData {
  date?: string;
  country?: string;
  device?: string;
  page?: string;
  query?: string;
  searchAppearance?: string;
  clicks: number;
  impressions: number;
  ctr: number;
  position: number;
}

export type SearchConsoleAnalyticsQueryApiResponse = Array<
  SearchConsoleAnalyticsQueryData & {
    prevData?: SearchConsoleAnalyticsQueryData;
    positionByDates: {
      [dateString: string]: number;
    };
  }
>;

"use strict";
var SubscriptionPlanRules;
(function (SubscriptionPlanRules) {
    SubscriptionPlanRules["seo_analysis"] = "seo_analysis";
    SubscriptionPlanRules["fix_instruction"] = "fix_instruction";
    SubscriptionPlanRules["alt_text"] = "alt_text";
    SubscriptionPlanRules["control_sitemap"] = "control_sitemap";
    SubscriptionPlanRules["unlimited_tags"] = "unlimited_tags";
    SubscriptionPlanRules["preview_snippets"] = "preview_snippets";
    SubscriptionPlanRules["html_sitemap"] = "html_sitemap";
    SubscriptionPlanRules["keyword_analytics"] = "keyword_analytics";
    SubscriptionPlanRules["seo_reports"] = "seo_reports";
    SubscriptionPlanRules["bulk_seo"] = "bulk_seo";
    SubscriptionPlanRules["sitemap_submission"] = "sitemap_submission";
    SubscriptionPlanRules["instant_indexing"] = "instant_indexing";
    SubscriptionPlanRules["google_analytics"] = "google_analytics";
    SubscriptionPlanRules["json_ld"] = "json_ld";
    SubscriptionPlanRules["rich_snippet"] = "rich_snippet";
    SubscriptionPlanRules["google_console"] = "google_console";
    SubscriptionPlanRules["local_seo"] = "local_seo";
    SubscriptionPlanRules["redirect_out_of_stock"] = "redirect_out_of_stock";
    SubscriptionPlanRules["multi_language_seo"] = "multi_language_seo";
    SubscriptionPlanRules["llms_txt_generation"] = "llms_txt_generation";
})(SubscriptionPlanRules || (SubscriptionPlanRules = {}));
module.exports = SubscriptionPlanRules;

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.filterExcludedProducts = exports.isExcludedProductType = exports.EXCLUDED_PRODUCT_TYPES = void 0;
const EXCLUDED_PRODUCT_TYPES = {
    EASYFLOW_GENERATED: "23p_po_generated",
};
exports.EXCLUDED_PRODUCT_TYPES = EXCLUDED_PRODUCT_TYPES;
const isExcludedProductType = (productType) => {
    if (!productType)
        return false;
    return Object.values(EXCLUDED_PRODUCT_TYPES).includes(productType);
};
exports.isExcludedProductType = isExcludedProductType;
const filterExcludedProducts = (products) => {
    return products.filter(product => !isExcludedProductType(product.productType));
};
exports.filterExcludedProducts = filterExcludedProducts;

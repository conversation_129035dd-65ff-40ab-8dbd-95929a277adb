//@ts-check
const yup = require("yup");

const imageOptimizerSchema = yup
  .object()
  .shape({
    compressionType: yup
      .string()
      .oneOf(["none", "lossless", "balanced", "lossy"])
      .required("Compression type is required"),
    format: yup
      .string()
      .oneOf(["none", "jpeg", "webp"])
      .required("Format is required"),
    resize: yup
      .string()
      .oneOf(["none", "1600", "2048", "4000"])
      .required("Resize option is required"),
  })
  .test("at-least-one-optimization", "At least one optimization setting must be selected", function (values) {
    const { compressionType, format, resize } = values;

    // If all optimization settings are "none", it's invalid
    if (compressionType === "none" && format === "none" && resize === "none") {
      return this.createError({
        path: "optimizationSettings",
        message: "Please select at least one of 'image compression', 'image format', or 'resize' options. Currently all selected settings are 'none'.",
      });
    }
    return true;
  });

const imageOptimizerAPISchema = yup.object({
  status: yup.boolean().required(),
  settings: imageOptimizerSchema.optional(),
});

module.exports = {
  imageOptimizerSchema,
  imageOptimizerAPISchema,
};

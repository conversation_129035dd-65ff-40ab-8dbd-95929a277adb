//@ts-check
const yup = require("yup");

/**
 * Schema for LLMS.txt generator settings
 * Defines which resources to include and generation schedule
 */
const llmsSettingsSchema = yup
  .object()
  .shape({
    resources_enabled: yup
      .object()
      .shape({
        products: yup.boolean().required(),
        collections: yup.boolean().required(),
        pages: yup.boolean().required(),
        blogs: yup.boolean().required(),
        articles: yup.boolean().required(),
        variants: yup.boolean().required(),
      })
      .required("Resource selection is required"),
    schedule: yup
      .string()
      .oneOf(["daily", "weekly", "monthly"], "Schedule must be daily, weekly, or monthly")
      .required("Schedule is required"),
  })
  .test("at-least-one-resource", "At least one resource must be enabled", function (values) {
    const { resources_enabled } = values;
    const hasEnabledResource = Object.values(resources_enabled).some(enabled => enabled === true);
    
    if (!hasEnabledResource) {
      return this.createError({
        path: "resources_enabled",
        message: "At least one resource type must be enabled for LLMS.txt generation",
      });
    }
    return true;
  })
  .required();

/**
 * API schema for LLMS settings endpoints
 * Includes status flag and optional settings object
 */
const llmsSettingsAPISchema = yup.object({
  status: yup.boolean().required(),
  settings: llmsSettingsSchema.optional(),
});

/**
 * Default LLMS settings configuration
 * Used when no settings exist for a shop
 */
const defaultLlmsSettings = {
  resources_enabled: {
    products: true,
    collections: true,
    pages: false,
    blogs: false,
    articles: false,
    variants: false,
  },
  schedule: "weekly",
};

module.exports = {
  llmsSettingsSchema,
  llmsSettingsAPISchema,
  defaultLlmsSettings,
};

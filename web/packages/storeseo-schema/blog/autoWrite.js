//@ts-check
const yup = require("yup");
const { inHTMLData } = require("xss-filters");
const BlogType = require("storeseo-enums/blogAutoWrite/blogType");
const WordCountRange = require("storeseo-enums/blogAutoWrite/wordCountRange");
const ToneType = require("storeseo-enums/blogAutoWrite/toneType");

// Extract enum values for validation (filter out non-string values)
const blogTypeValues = Object.values(BlogType).filter((value) => typeof value === "string");
const wordCountRangeValues = Object.values(WordCountRange).filter((value) => typeof value === "string");
const toneTypeValues = Object.values(ToneType).filter((value) => typeof value === "string");

/**
 * Sanitize HTML content from input
 * @param {string} value - The value to sanitize
 * @returns {string} Sanitized value
 */
const sanitizeHtml = (value) => {
  if (typeof value !== "string") return value;
  return inHTMLData(value).trim();
};

/**
 * SEO-friendly keyword validation
 * Allows Unicode letters (all languages), numbers, spaces, hyphens, and common punctuation
 * Supports international keywords in any language
 */
const seoKeywordRegex = /^[\p{L}\p{N}\s\-.,!?'"()&]+$/u;

/**
 * Blog Auto-Write Input Validation Schema
 * Shared between frontend and backend for consistent validation
 */
const blogAutoWriteInputSchema = yup.object().shape({
  topic: yup
    .string()
    .required("Topic is required")
    .min(2, "Topic must be at least 2 characters")
    .max(200, "Topic must not exceed 200 characters")
    .transform(sanitizeHtml),

  keyword: yup
    .string()
    .optional()
    .nullable()
    .min(2, "Keyword must be at least 2 characters")
    .max(50, "Keyword must not exceed 50 characters")
    .matches(seoKeywordRegex, "Keyword contains invalid characters for SEO")
    .transform((value) => value || null),

  targetBlog: yup
    .string()
    .required("Please select a blog to publish your article to")
    .matches(/^\d+$/, "Invalid blog selection. Please choose a valid blog from the dropdown")
    .test("is-positive-integer", "Invalid blog selection. Please choose a valid blog from the dropdown", (value) => {
      if (!value) return false;
      const num = parseInt(value, 10);
      return num > 0 && num.toString() === value;
    }),

  blogType: yup
    .string()
    .required("Blog type is required")
    .oneOf(blogTypeValues, `Blog type must be one of: ${blogTypeValues.join(", ")}`),

  wordCount: yup
    .string()
    .required("Word count range is required")
    .oneOf(wordCountRangeValues, `Word count must be one of: ${wordCountRangeValues.join(", ")}`),

  tone: yup
    .string()
    .required("Tone is required")
    .oneOf(toneTypeValues, `Tone must be one of: ${toneTypeValues.join(", ")}`),

  customInstructions: yup
    .string()
    .optional()
    .nullable()
    .max(500, "Custom instructions must not exceed 500 characters")
    .transform((value) => value || null),

  generateFeaturedImage: yup.boolean().optional().default(false),

  featuredImageDescription: yup
    .string()
    .optional()
    .nullable()
    .max(500, "Featured image description must not exceed 500 characters")
    .transform((value) => value || null),

  autoPublish: yup.boolean().optional().default(false), // Default to draft for current release
});

/**
 * Blog Auto-Write Credit Estimation Schema
 * For credit estimation endpoint (excludes autoPublish as it doesn't affect cost)
 */
const blogAutoWriteCreditEstimationSchema = blogAutoWriteInputSchema.omit(['autoPublish']);

module.exports = {
  blogAutoWriteInputSchema,
  blogAutoWriteCreditEstimationSchema,
};

"use strict";
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", { value: true });
exports.sentryLevelMaps = exports.sentryOptions = exports.colors = exports.logFormat = exports.levels = exports.logFilesPath = exports.defaultLogLevel = void 0;
const dotenv_1 = require("dotenv");
const winston_1 = require("winston");
(0, dotenv_1.config)();
exports.defaultLogLevel = "info";
exports.logFilesPath = "storage/logs";
exports.levels = {
    error: 0,
    warn: 1,
    debug: 2,
    info: 3,
    http: 4,
    silly: 6,
};
exports.logFormat = [
    winston_1.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss" }),
    winston_1.format.errors({ stack: true }),
];
exports.colors = {
    error: "red",
    warn: "yellow",
    info: "blue",
    http: "gray",
    sync: "black",
    debug: "gray",
    silly: "black",
};
exports.sentryOptions = {
    dsn: (_a = process.env) === null || _a === void 0 ? void 0 : _a.SENTRY_DSN,
    level: "info",
    environment: (_b = process.env) === null || _b === void 0 ? void 0 : _b.SENTRY_ENVIRONMENT,
    release: (_c = process.env) === null || _c === void 0 ? void 0 : _c.FRONTEND_APP_VERSION,
    maxBreadcrumbs: 5,
};
exports.sentryLevelMaps = {
    error: "error",
    warn: "warning",
    info: "info",
    debug: "debug",
    http: "info",
    silly: "debug",
};

"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const Sentry = __importStar(require("@sentry/node"));
const winston_transport_1 = __importDefault(require("winston-transport"));
const config_1 = require("../config");
class SentryTransport extends winston_transport_1.default {
    constructor(opts) {
        super(opts);
        Sentry.init({ ...opts });
        this.level = opts.level || "error";
    }
    log(info, callback) {
        setImmediate(() => {
            this.emit("logged", info);
        });
        const { level, message, domain, email, tags, transaction, ...meta } = info;
        Sentry.withScope((scope) => {
            scope.setLevel(config_1.sentryLevelMaps[level] || "error");
            if (domain || email)
                scope.setUser({ id: domain, email });
            if (transaction)
                scope.setTransactionName(transaction);
            if (meta)
                scope.setExtras(meta);
            if (tags)
                scope.setTags(tags);
            if (info instanceof Error) {
                Sentry.captureException(info);
            }
            else {
                Sentry.captureMessage(message);
            }
        });
        callback();
    }
}
module.exports = SentryTransport;

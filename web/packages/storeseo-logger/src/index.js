"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const path_1 = require("path");
const winston_1 = require("winston");
const config_1 = require("./config");
const SentryTransport_1 = __importDefault(require("./transports/SentryTransport"));
const logFilter = (0, winston_1.format)((info, opts) => {
    return opts && info.level === opts.include ? info : false;
});
const printMsg = ({ logTimestamp = false, timestamp, level, message, stack, ...rest }) => {
    return `${logTimestamp ? timestamp + " - " : ""}${level === null || level === void 0 ? void 0 : level.toUpperCase()}: ${(message === null || message === void 0 ? void 0 : message.trim()) || ""}${stack ? "\n" + stack : ""}`;
};
const customTransports = [
    ...Object.keys(config_1.levels).map((level) => new winston_1.transports.File({
        filename: (0, path_1.join)(config_1.logFilesPath, `${level}.log`),
        level,
        format: winston_1.format.combine(logFilter({ include: level }), ...config_1.logFormat, winston_1.format.printf((log) => printMsg({ logTimestamp: true, ...log }))),
    })),
    new winston_1.transports.Console({
        format: winston_1.format.combine(...config_1.logFormat, winston_1.format.printf(printMsg), winston_1.format.colorize({ all: true, colors: config_1.colors })),
    }),
];
const options = {
    level: config_1.defaultLogLevel,
    levels: config_1.levels,
    format: winston_1.format.combine(...config_1.logFormat),
    transports: customTransports,
    exceptionHandlers: [new winston_1.transports.File({ filename: (0, path_1.join)(config_1.logFilesPath, "exceptions.log") })],
    rejectionHandlers: [new winston_1.transports.File({ filename: (0, path_1.join)(config_1.logFilesPath, "rejections.log") })],
};
const logger = (0, winston_1.createLogger)(options);
if (config_1.sentryOptions.dsn) {
    logger.add(new SentryTransport_1.default(config_1.sentryOptions));
}
module.exports = logger;

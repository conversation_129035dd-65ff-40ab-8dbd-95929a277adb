const { AI_OPTIMIZER } = require("storeseo-enums/subscriptionAddonGroup");
const { SubscriptionAddon } = require("../../web/sequelize");
const { CREDIT } = require("storeseo-enums/subscriptionAddonInterval");
const { PRO } = require("storeseo-enums/subscriptionAddonType");
const SubscriptionAddonStatus = require("storeseo-enums/subscriptionAddonStatus");

const giftAddonData = {
  name: "Gift",
  group: AI_OPTIMIZER,
  type: PRO,
  interval: CREDIT,
  limit: 500,
  status: SubscriptionAddonStatus.HIDDEN,
};

(async () => {
  try {
    const existingGiftAddon = await SubscriptionAddon.findOne({
      where: {
        name: "Gift",
        group: AI_OPTIMIZER,
      },
    });

    if (existingGiftAddon) {
      console.log("----- Gift Addon already exist. Skip adding -----");
      return;
    }

    const newAddon = await SubscriptionAddon.create(giftAddonData);

    console.log("---New Gift Addon Created---\n", newAddon.toJSON());
  } catch (err) {
    console.log("err: ", err);
  } finally {
    process.exit(0);
  }
})();

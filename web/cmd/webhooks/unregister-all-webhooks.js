// @ts-check
require("dotenv").config();
require("../../api/config");
const { Op } = require("sequelize");
const ShopStatus = require("storeseo-enums/shopStatus");
const WebhookService = require("../../api/services/WebhookService");
const ShopService = require("../../api/services/ShopService");

(async () => {
  try {
    for await (let shops of ShopService.iterateOverAllShops(
      {
        plan_id: { [Op.gt]: 0 },
        status: ShopStatus.ACTIVE,
      },
      ["id", "domain", "access_token"]
    )) {
      for (let shop of shops) {
        try {
          console.log(`Removing webhooks for shop (${shop.domain})`);
          await WebhookService.unregisterAllWebhooks({ shop: shop.domain, accessToken: shop.access_token }, shop.id);
          console.log(`>> Webhook deleted for shop (${shop.id} - ${shop.domain}) done!`);
        } catch (error) {
          console.error(`Error removing webhooks for the shop (${shop.domain}):`, error.message);
        }
      }
    }
  } catch (err) {
    console.error(err);
  }
  process.exit();
})();

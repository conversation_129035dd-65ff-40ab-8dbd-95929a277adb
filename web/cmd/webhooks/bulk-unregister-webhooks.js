/**
 * --------
 * Example: WEBHOOK=pubsub|http node cmd/bulk-unregister-webhooks.js ../domains.txt
 * --------
 */

const readline = require("readline");
const path = require("path");
const fs = require("fs");
const WebhookService = require("../../api/services/WebhookService");
const ShopService = require("../../api/services/ShopService");

(async () => {
  try {
    console.log("\n---");
    console.log("Unregistering webhooks from input file...");
    console.log("WEBHOOK trigger: ", process.env.WEBHOOK);
    console.log("---\n");

    const inputFile = process.argv.slice(2)[0];

    const rl = readline.createInterface({
      input: fs.createReadStream(path.resolve(__dirname, inputFile), { encoding: "utf-8" }),
      crlfDelay: Infinity,
    });

    for await (let shopDomain of rl) {
      console.log("\nUnregistering webhok for ", shopDomain);
      try {
        const shop = await ShopService.getShop(shopDomain);
        const session = { shop: shopDomain, accessToken: shop.access_token };

        await WebhookService.unregisterAllWebhooks(session, shop.id, process.env.WEBHOOK);
        console.log("Done!");
      } catch (err) {
        console.log(err);
      }
    }

    console.log("\n---\nDone!\n---\n");
  } catch (err) {
    console.log("Error: ", err);
  } finally {
    process.exit(0);
  }
})();

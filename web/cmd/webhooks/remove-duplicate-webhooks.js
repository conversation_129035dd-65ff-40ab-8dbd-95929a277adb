// @ts-check
require("dotenv").config();
const ShopService = require("../../api/services/ShopService");
const WebhookService = require("../../api/services/WebhookService");
const ShopifyWebhookService = require("../../api/services/ShopifyWebhookService");
const { Op } = require("../../sequelize");

/**
 * Removes duplicate webhook registrations for a shop, keeping only the latest one for each topic.
 *
 * This function performs the following operations:
 * 1. Fetches all webhook subscriptions from Shopify for the shop
 * 2. Groups them by topic
 * 3. For each topic with multiple webhooks, keeps only the latest one
 * 4. Deletes the older duplicates from Shopify
 * 5. Updates the database to reflect the changes
 *
 * @async
 * @param {Object} shop - The shop object for which duplicate webhooks will be removed
 * @param {string} shop.id - The unique identifier of the shop
 * @param {string} shop.domain - The domain of the shop
 * @throws {Error} If any step in the process fails
 * @returns {Promise<{removed: number, total: number}>} A promise that resolves with the count of removed webhooks and total webhooks
 */
const removeDuplicateWebhooks = async (shop) => {
  try {
    // Fetch all webhook subscriptions from Shopify
    const webhooks = await ShopifyWebhookService.webhookSubscriptions(shop.domain);
    console.log(`Found ${webhooks.length} webhooks for shop ${shop.domain}`);

    // Group webhooks by topic
    const webhooksByTopic = {};
    webhooks.forEach((webhook) => {
      if (!webhooksByTopic[webhook.topic]) {
        webhooksByTopic[webhook.topic] = [];
      }
      webhooksByTopic[webhook.topic].push(webhook);
    });

    let removedCount = 0;

    // Process each topic
    for (const [topic, topicWebhooks] of Object.entries(webhooksByTopic)) {
      if (topicWebhooks.length > 1) {
        console.log(`Found ${topicWebhooks.length} webhooks for topic ${topic}`);

        // Sort webhooks by creation date (newest first)
        topicWebhooks.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

        // Keep the newest webhook, delete the rest
        const keepWebhook = topicWebhooks[0];
        console.log(`Keeping webhook ID: ${keepWebhook.id}, created at: ${keepWebhook.createdAt}`);

        // Delete older webhooks
        for (let i = 1; i < topicWebhooks.length; i++) {
          const webhookToDelete = topicWebhooks[i];
          console.log(`Deleting duplicate webhook ID: ${webhookToDelete.id}, created at: ${webhookToDelete.createdAt}`);

          try {
            await ShopifyWebhookService.webhookSubscriptionDelete(shop.domain, webhookToDelete.id);
            // Also delete from database if it exists
            await WebhookService.deleteWebhookByConditions({
              shop_id: shop.id,
              wh_subs_id: webhookToDelete.id,
            });
            removedCount++;
          } catch (error) {
            console.error(`Error deleting webhook ${webhookToDelete.id}:`, error.message);
          }
        }
      }
    }

    return { removed: removedCount, total: webhooks.length };
  } catch (error) {
    console.error(`Error processing shop ${shop.domain}:`, error.message);
    return { removed: 0, total: 0 };
  }
};

(async () => {
  const domain = process.argv[2];
  try {
    console.log("-----------------------------------------------------------------");
    console.log("⏳ Removing duplicate webhooks..");
    console.log("-----------------------------------------------------------------");

    let totalProcessed = 0;
    let totalRemoved = 0;

    if (domain) {
      const shop = await ShopService.getShop(domain, ["id", "domain", "status"]);
      if (!shop) {
        console.error("Shop not found.");
        return;
      }

      console.log(`Processing shop: ${shop.domain}`);
      const result = await removeDuplicateWebhooks(shop);
      totalProcessed = 1;
      totalRemoved = result.removed;

      console.log(`Removed ${result.removed} duplicate webhooks out of ${result.total} total webhooks.`);
    } else {
      let count = 0;

      for await (let shops of ShopService.iterateOverAllShops({ id: { [Op.gte]: 0 } }, ["id", "domain", "status"])) {
        for (let shop of shops) {
          count++;
          console.log("-----------------------------------------------------------------");
          console.log(`${count}: Processing shop: ${shop.domain}, Status: ${shop.status}`);

          const result = await removeDuplicateWebhooks(shop);
          totalProcessed++;
          totalRemoved += result.removed;

          console.log(`Removed ${result.removed} duplicate webhooks out of ${result.total} total webhooks.`);
          console.log("-----------------------------------------------------------------");
        }
      }
    }

    console.log("\n\n");
    console.log(`✅ Done processing ${totalProcessed} shops. Removed ${totalRemoved} duplicate webhooks.`);
  } catch (err) {
    console.error("Error processing webhooks:", err);
  } finally {
    domain && process.exit(0);
  }
})();

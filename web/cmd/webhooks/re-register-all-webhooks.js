// @ts-check
require("dotenv").config();
const ShopService = require("../../api/services/ShopService");
const WebhookService = require("../../api/services/WebhookService");
const ShopifyWebhookService = require("../../api/services/ShopifyWebhookService");
const { WEBHOOKS, PRIORITY } = require("../../api/config/webhook");
const { Op } = require("../../sequelize");

/**
 * Re-registers all webhooks for a given shop by removing existing webhooks and registering new ones.
 * 
 * This function performs the following operations:
 * 1. Deletes all webhooks from the database associated with the shop
 * 2. Retrieves and deletes all existing webhook subscriptions from Shopify
 * 3. Registers new webhooks with ADDITIONAL priority level
 * 4. Saves the webhook data to the database
 * 
 * @async
 * @param {Object} shop - The shop object for which webhooks will be re-registered
 * @param {string} shop.id - The unique identifier of the shop
 * @param {string} shop.domain - The domain of the shop
 * @throws {Error} If any step in the re-registration process fails
 * @returns {Promise<void>} A promise that resolves when all webhooks have been re-registered
 */
const reRegisterWebhooks = async (shop) => {
  try {
    await WebhookService.deleteWebhookByConditions({ shop_id: shop.id });
    const webhooks = await ShopifyWebhookService.webhookSubscriptions(shop.domain);

    for (let [index, webhook] of webhooks.entries()) {
      await ShopifyWebhookService.webhookSubscriptionDelete(shop.domain, webhook.id);
      console.log(
        `${index + 1}/${webhooks.length} ID: ${webhook.id}, Topic: ${webhook.topic}, Url: ${webhook.endpoint.callbackUrl} => Deleted.`
      );
    }

    console.log(`Deleted ${webhooks.length} webhooks for shop. \n`);

    const webhooksToRegister = WEBHOOKS.filter((wh) => wh.priority === PRIORITY.ADDITIONAL);

    for (let [index, wh] of webhooksToRegister.entries()) {
      const whSubs = await ShopifyWebhookService.webhookSubscriptionCreate(shop.domain, wh);
      await WebhookService.saveWebhookData(shop.id, wh, whSubs);
      console.log(
        `${index + 1}/${webhooksToRegister.length} ID: ${whSubs.id}, Topic: ${whSubs.topic}, Url: ${whSubs.endpoint.callbackUrl} => Registered.`
      );
    }

    console.log(`Registered ${webhooksToRegister.length} webhooks for shop. \n`);
  } catch (error) {
    await WebhookService.deleteWebhookByConditions({ shop_id: shop.id });
    console.error("Deleted all webhooks for inactive shop.", error.message);
  }
};

(async () => {
  const domain = process.argv[2];
  try {
    console.log("-----------------------------------------------------------------");
    console.log("⏳ Removing invalid webhooks..");
    console.log("------------------------------------------------------------------");

    let count = 0;
    let totalCount = 0;

    if (domain) {
      const shop = await ShopService.getShop(domain, ["id", "domain", "status"]);
      if (!shop) {
        console.error("Shop not found.");
        return;
      }
      await reRegisterWebhooks(shop);
    } else {
      for await (let shops of ShopService.iterateOverAllShops({ id: { [Op.gte]: 0 } }, ["id", "domain", "status"])) {
        totalCount += shops.length;
        for (let shop of shops) {
          count++;
          let sl = `${count}/${totalCount}:`;

          await reRegisterWebhooks(shop);

          console.log("-----------------------------------------------------------------");
          console.log(sl, "Processing shop:", shop.domain, "Status:", shop.status);
          console.log("-----------------------------------------------------------------");
        }
      }
    }

    console.log("\n\n");
    console.log("✅ Done removing invalid webhooks.");
  } catch (err) {
    console.error("Error fetching webhooks:", err);
  } finally {
    domain && process.exit(0);
  }
})();

/**
 * Check Trial Expiration Command
 * This script checks trial expiration for a specific shop using SubscriptionService
 *
 * Usage: node check-trial-expiration.js <shop-domain> [--force]
 *
 * Examples:
 *   node check-trial-expiration.js example.myshopify.com
 *   node check-trial-expiration.js example.myshopify.com --force
 */

require("dotenv").config();
const SubscriptionService = require("../../api/services/SubscriptionService");
const logger = require("storeseo-logger");

const checkTrialExpiration = async () => {
  try {
    console.log("🔧 Check Trial Expiration Command");
    console.log("=".repeat(60));

    // Get shop domain from command line arguments
    const shopDomain = process.argv[2];
    const forceFlag = process.argv.includes("--force");

    if (!shopDomain) {
      console.log("❌ Please provide a shop domain as argument");
      console.log("\nUsage:");
      console.log("  node check-trial-expiration.js <shop-domain> [--force]");
      console.log("\nExamples:");
      console.log("  node check-trial-expiration.js example.myshopify.com");
      console.log("  node check-trial-expiration.js example.myshopify.com --force");
      console.log("\nFlags:");
      console.log("  --force  Dispatch queue even if trial hasn't expired yet");
      process.exit(1);
    }

    console.log(`🏪 Checking shop: ${shopDomain}`);
    if (forceFlag) {
      console.log("⚠️  Force mode enabled - will dispatch regardless of trial status");
    }
    console.log("-".repeat(60));

    // Use SubscriptionService to check trial expiration
    console.log("📋 Checking trial expiration using SubscriptionService...");
    const result = await SubscriptionService.checkTrialExpiration(shopDomain, forceFlag);

    // Display results
    console.log("\n📊 Results:");
    console.log("-".repeat(60));

    if (!result.processed) {
      console.log("❌ Processing failed:");
      console.log(`   Reason: ${result.reason}`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
      process.exit(1);
    }

    console.log("✅ Processing completed:");
    console.log(`   Shop: ${result.shop?.domain || shopDomain}`);
    console.log(`   Shop ID: ${result.shop?.id || "N/A"}`);
    console.log(`   Shop Status: ${result.shop?.status || "N/A"}`);
    console.log(`   Plan ID: ${result.shop?.plan_id || "N/A"}`);
    console.log(`   Plan Status: ${result.shop?.plan_status || "N/A"}`);

    if (result.trialData) {
      console.log("\n🔍 Trial Information:");
      console.log(`   Status: ${result.trialData.status}`);
      console.log(`   Started At: ${result.trialData.startedAt}`);
      console.log(`   Expires At: ${result.trialData.expiresAt}`);
      console.log(`   Trial Days: ${result.trialData.trialDays}`);
      console.log(`   Is Trial User: ${result.trialData.isTrialUser}`);
      if (typeof result.remainingDays !== "undefined") {
        console.log(`   Remaining Days: ${result.remainingDays}`);
      }
    }

    console.log("\n⚙️  Action Taken:");
    console.log(`   Queue Dispatched: ${result.dispatched ? "✅ YES" : "❌ NO"}`);
    console.log(`   Reason: ${result.reason}`);

    if (result.dispatched) {
      console.log("\n🚀 Queue Dispatch Details:");
      console.log("   ✅ Trial expiration queue dispatched successfully!");
      console.log("   📝 Check queue logs for processing results");
      console.log("   🔄 Monitor trial status updates in database");

      console.log("\n🔄 Next Steps:");
      console.log("1. The trial expiration queue will process this shop");
      console.log("2. Check queue logs for processing results");
      console.log("3. Verify trial status updates in database");
      console.log("4. Monitor for any errors in queue processing");
    } else {
      console.log("\n💡 Tips:");
      console.log("• Use --force flag to dispatch regardless of trial status");
      console.log("• Check if shop has active trial data");
      console.log("• Verify shop status is 'ACTIVE'");
    }

    // Summary
    console.log("\n📋 Summary:");
    console.log("-".repeat(60));
    console.log(`Shop: ${result.shop?.domain || shopDomain}`);
    console.log(`Trial Status: ${result.trialData?.status || "No trial data"}`);
    console.log(`Remaining Days: ${result.remainingDays ?? "N/A"}`);
    console.log(`Action: ${result.dispatched ? "Queue Dispatched" : "No Action Taken"}`);
    console.log(`Reason: ${result.reason}`);
  } catch (error) {
    console.error("❌ Check trial expiration command failed:", error);
    logger.error(error, {
      transaction: "check-trial-expiration-command",
      domain: process.argv[2],
    });
    process.exit(1);
  } finally {
    setTimeout(() => {
      console.log("\n🏁 Command completed. Exiting...");
      process.exit(0);
    }, 1000);
  }
};

// Run the script
if (require.main === module) {
  checkTrialExpiration();
}

module.exports = checkTrialExpiration;

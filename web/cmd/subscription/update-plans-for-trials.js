/**
 * Update subscription plans to support trial functionality
 * This script updates existing plans in the database to match the new configuration
 * with trialDays and FREE plan status changes
 */

const SubscriptionPlanService = require("../../api/services/SubscriptionPlanService");
const { PRO, FREE } = require("storeseo-enums/planType");
const { HIDDEN, ACTIVE } = require("storeseo-enums/planStatus");
const { MONTHLY, ANNUALLY } = require("storeseo-enums/planInterval");
const { trialDays, trialRules } = require("../../api/config/subscription-plan");
const { sortPlanRules } = require("../../api/utils/helper");

const updatePlansForTrials = async () => {
  try {
    console.log("Starting plan updates for trial support...");

    // Get all plans from database
    const allPlans = await SubscriptionPlanService.getSubscriptionPlansByCondition(undefined, "status");
    console.log(`Found ${allPlans.length} plans to update`);

    let updatedCount = 0;

    for (const plan of allPlans) {
      const updates = {
        rules: sortPlanRules(plan.rules),
      };

      // Add trialDays: 7 to all PRO plans that don't have it
      if (plan.type === PRO && [MONTHLY, ANNUALLY].includes(plan.interval) && plan.status === ACTIVE) {
        updates.meta = { ...plan.meta, trialDays, trialRules };
        console.log(`Adding trialDays to PRO plan: ${plan.name} (${plan.interval})`);
      }

      // Set FREE plan status to HIDDEN
      if (plan.type === FREE && plan.status !== HIDDEN) {
        updates.status = HIDDEN;
        console.log(`Setting FREE plan status to HIDDEN: ${plan.name}`);
      }

      // Update the plan if needed
      await SubscriptionPlanService.updateSubscriptionPlan(plan.id, updates);
      updatedCount++;
      console.log(`✓ Updated plan: ${plan.name} (ID: ${plan.id})`);
    }

    console.log(`\n✅ Plan update completed! Updated ${updatedCount} out of ${allPlans.length} plans.`);

    // Verify updates
    console.log("\n📋 Verification - Current plan status:");
    const updatedPlans = await SubscriptionPlanService.getSubscriptionPlansByCondition(undefined, "status");

    console.table(
      updatedPlans.map((plan) => {
        return {
          id: plan.id,
          name: plan.name,
          type: plan.type,
          interval: plan.interval,
          status: plan.status,
          trialDays: plan.meta?.trialDays ? `${plan.meta?.trialDays || "N/A"} days` : "No trial",
        };
      })
    );
  } catch (error) {
    console.error("❌ Error updating plans for trials:", error);
    throw error;
  }
};

const rollbackPlansForTrials = async () => {
  try {
    console.log("Rolling back plan updates...");

    const allPlans = await SubscriptionPlanService.getSubscriptionPlansByCondition(undefined, "status");
    let rolledBackCount = 0;

    for (const plan of allPlans) {
      const updates = {};
      let needsRollback = false;

      // Remove trialDays from PRO plans
      if (plan.type === PRO && plan.meta?.trialDays) {
        updates.meta = { ...plan.meta, trialDays: undefined, trialRules: undefined };
        needsRollback = true;
        console.log(`Removing trialDays from PRO plan: ${plan.name} (${plan.interval})`);
      }

      // Set FREE plan status back to ACTIVE
      if (plan.type === FREE && plan.status === HIDDEN) {
        updates.status = 1; // ACTIVE
        needsRollback = true;
        console.log(`Setting FREE plan status back to ACTIVE: ${plan.name}`);
      }

      if (needsRollback) {
        await SubscriptionPlanService.updateSubscriptionPlan(plan.id, updates);
        rolledBackCount++;
        console.log(`✓ Rolled back plan: ${plan.name} (ID: ${plan.id})`);
      }
    }

    console.log(`\n✅ Rollback completed! Rolled back ${rolledBackCount} plans.`);
  } catch (error) {
    console.error("❌ Error rolling back plans:", error);
    throw error;
  }
};

// Main execution
(async () => {
  const [, , action] = process.argv;

  if (action === "rollback") {
    console.log("🔄 Rolling back plan updates for trials...");
    await rollbackPlansForTrials();
  } else {
    console.log("🚀 Updating plans for trial support...");
    await updatePlansForTrials();
  }

  process.exit(0);
})();

/**
 * Manual Trial Expiration Check Script
 * This script manually checks and triggers trial expiration queue for a specific shop domain
 * 
 * Usage: node manual-trial-expiration-check.js <shop-domain> [--force]
 * 
 * Examples:
 *   node manual-trial-expiration-check.js example.myshopify.com
 *   node manual-trial-expiration-check.js example.myshopify.com --force
 */

const ShopService = require("../../api/services/ShopService");
const { dispatchQueue } = require("../../api/queue/queueDispatcher");
const { QUEUE_NAMES } = require("../../api/queue/config");
const { getRemainingTrialDays, isTrialActive } = require("../../api/utils/trialHelpers");
const TrialStatus = require("storeseo-enums/trialStatus");
const logger = require("storeseo-logger");

const manualTrialExpirationCheck = async () => {
  try {
    console.log("🔧 Manual Trial Expiration Check");
    console.log("=" .repeat(60));

    // Get shop domain from command line arguments
    const shopDomain = process.argv[2];
    const forceFlag = process.argv.includes("--force");

    if (!shopDomain) {
      console.log("❌ Please provide a shop domain as argument");
      console.log("\nUsage:");
      console.log("  node manual-trial-expiration-check.js <shop-domain> [--force]");
      console.log("\nExamples:");
      console.log("  node manual-trial-expiration-check.js example.myshopify.com");
      console.log("  node manual-trial-expiration-check.js example.myshopify.com --force");
      console.log("\nFlags:");
      console.log("  --force  Dispatch queue even if trial hasn't expired yet");
      process.exit(1);
    }

    console.log(`🏪 Checking shop: ${shopDomain}`);
    if (forceFlag) {
      console.log("⚠️  Force mode enabled - will dispatch regardless of trial status");
    }
    console.log("-".repeat(60));

    // Step 1: Fetch shop data
    console.log("📋 Step 1: Fetching shop data...");
    const shop = await ShopService.getShop(shopDomain);

    if (!shop) {
      console.log(`❌ Shop not found: ${shopDomain}`);
      process.exit(1);
    }

    console.log("✅ Shop found:");
    console.log(`   ID: ${shop.id}`);
    console.log(`   Domain: ${shop.domain}`);
    console.log(`   Status: ${shop.status}`);
    console.log(`   Plan ID: ${shop.plan_id}`);
    console.log(`   Plan Status: ${shop.plan_status || 'Not set'}`);
    console.log(`   Access Token: ${shop.access_token ? 'Present' : 'Missing'}`);

    // Step 2: Check trial data
    console.log("\n🔍 Step 2: Analyzing trial data...");

    if (!shop.trial_data) {
      console.log("❌ No trial data found for this shop");
      console.log("💡 This shop doesn't have trial data - cannot process trial expiration");
      process.exit(0);
    }

    const trialData = typeof shop.trial_data === "string" ? JSON.parse(shop.trial_data) : shop.trial_data;

    console.log("✅ Trial data found:");
    console.log(`   Status: ${trialData.status}`);
    console.log(`   Started At: ${trialData.startedAt}`);
    console.log(`   Expires At: ${trialData.expiresAt}`);
    console.log(`   Trial Days: ${trialData.trialDays}`);
    console.log(`   Is Trial User: ${trialData.isTrialUser}`);

    const isActive = isTrialActive(trialData);
    const remainingDays = getRemainingTrialDays(trialData);

    console.log(`   Is Active: ${isActive}`);
    console.log(`   Remaining Days: ${remainingDays}`);

    // Step 3: Determine if queue should be dispatched
    console.log("\n⚙️  Step 3: Determining dispatch eligibility...");

    let shouldDispatch = false;
    let reason = "";

    if (forceFlag) {
      shouldDispatch = true;
      reason = "Force mode enabled";
    } else if (!isActive) {
      shouldDispatch = false;
      reason = "Trial is not active";
    } else if (trialData.status !== TrialStatus.ACTIVE) {
      shouldDispatch = false;
      reason = `Trial status is '${trialData.status}', not 'ACTIVE'`;
    } else if (remainingDays > 0) {
      shouldDispatch = false;
      reason = `Trial has ${remainingDays} days remaining`;
    } else if (remainingDays === 0) {
      shouldDispatch = true;
      reason = "Trial has expired (0 days remaining)";
    } else {
      shouldDispatch = false;
      reason = "Trial expiration date is in the past";
    }

    console.log(`Decision: ${shouldDispatch ? '✅ DISPATCH' : '❌ SKIP'}`);
    console.log(`Reason: ${reason}`);

    // Step 4: Dispatch queue if eligible
    if (shouldDispatch) {
      console.log("\n🚀 Step 4: Dispatching trial expiration queue...");

      try {
        dispatchQueue({
          queueName: QUEUE_NAMES.TRIAL_EXPIRATION_QUEUE,
          message: { shopDomain: shop.domain },
        });

        console.log("✅ Trial expiration queue dispatched successfully!");
        console.log(`   Queue: ${QUEUE_NAMES.TRIAL_EXPIRATION_QUEUE}`);
        console.log(`   Shop: ${shop.domain}`);

        // Log the manual dispatch
        logger.info("Manual trial expiration queue dispatch", {
          shopDomain: shop.domain,
          shopId: shop.id,
          trialStatus: trialData.status,
          remainingDays: remainingDays,
          forceMode: forceFlag,
          triggeredBy: "manual-script",
        });

      } catch (error) {
        console.log("❌ Failed to dispatch queue:", error.message);
        logger.error(error, {
          shopDomain: shop.domain,
          context: "manual-trial-expiration-dispatch",
        });
        process.exit(1);
      }
    } else {
      console.log("\n⏭️  Step 4: Skipping queue dispatch");
      console.log("💡 Use --force flag to dispatch regardless of trial status");
    }

    // Step 5: Summary
    console.log("\n📊 Summary:");
    console.log("-".repeat(60));
    console.log(`Shop: ${shop.domain}`);
    console.log(`Trial Status: ${trialData.status}`);
    console.log(`Remaining Days: ${remainingDays}`);
    console.log(`Action: ${shouldDispatch ? 'Queue Dispatched' : 'No Action Taken'}`);
    console.log(`Reason: ${reason}`);

    if (shouldDispatch) {
      console.log("\n🔄 Next Steps:");
      console.log("1. The trial expiration queue will process this shop");
      console.log("2. Check queue logs for processing results");
      console.log("3. Verify trial status updates in database");
      console.log("4. Monitor for any errors in queue processing");
    }

  } catch (error) {
    console.error("❌ Manual trial expiration check failed:", error);
    logger.error(error, {
      context: "manual-trial-expiration-check",
      shopDomain: process.argv[2],
    });
    process.exit(1);
  } finally {
    setTimeout(() => {
      console.log("\n🏁 Manual check completed. Exiting...");
      process.exit(0);
    }, 1000);
  }
};

// Run the script
if (require.main === module) {
  manualTrialExpirationCheck();
}

module.exports = manualTrialExpirationCheck;

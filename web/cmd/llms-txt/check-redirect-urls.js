/**
 * Command to check and fix LLMs text redirect URLs for all active shops or a single shop
 *
 * This command:
 * 1. Checks if LLMs text is generated for each shop
 * 2. If generated, checks if redirect URL exists with correct paths
 * 3. Creates redirect URL if it doesn't exist
 * 4. Updates redirect URL if it exists but has incorrect paths
 *
 * Usage:
 *   All shops: cd web && node cmd/llms-txt/check-redirect-urls.js
 *   Single shop: cd web && node cmd/llms-txt/check-redirect-urls.js <shop-domain>
 *
 * Examples:
 *   cd web && node cmd/llms-txt/check-redirect-urls.js
 *   cd web && node cmd/llms-txt/check-redirect-urls.js example.myshopify.com
 */

const { Op } = require("sequelize");
const { ACTIVE } = require("storeseo-enums/shopStatus");
const ShopService = require("../../api/services/ShopService");
const ShopifyService = require("../../api/services/ShopifyService");
const LlmsTxtGeneratorService = require("../../api/services/LlmsTxtGeneratorService");

class LlmsTxtRedirectChecker {
  constructor() {
    this.stats = {
      totalShops: 0,
      processedShops: 0,
      shopsWithGeneration: 0,
      redirectsChecked: 0,
      redirectsCreated: 0,
      redirectsUpdated: 0,
      redirectsAlreadyCorrect: 0,
      errors: 0,
    };
  }

  /**
   * Check and fix redirect URL for a single shop
   * @param {Object} shop - Shop object with id, domain, etc.
   * @returns {Promise<Object>} Result object with action taken
   */
  async checkShopRedirectUrl(shop) {
    try {
      // Check if LLMs text is generated for this shop
      const latestFile = await LlmsTxtGeneratorService.getLatestFileVersion(shop.id);

      if (!latestFile) {
        return {
          success: true,
          action: "skipped",
          reason: "No LLMs text generated",
        };
      }

      this.stats.shopsWithGeneration++;

      // Generate correct redirect paths
      const { sourcePath, targetPath } = await LlmsTxtGeneratorService.generateRedirectPathsFromProxyPaths();

      if (!sourcePath || !targetPath) {
        return {
          success: false,
          action: "error",
          reason: "Failed to generate redirect paths",
        };
      }

      // Check if redirect already exists
      const existingRedirect = await ShopifyService.getRedirectURL(shop.domain, sourcePath);
      this.stats.redirectsChecked++;

      if (!existingRedirect) {
        // Create new redirect URL
        const newRedirect = await ShopifyService.createRedirectURL(shop.domain, {
          oldPath: sourcePath,
          newPath: targetPath,
        });

        if (newRedirect) {
          this.stats.redirectsCreated++;
          return {
            success: true,
            action: "created",
            redirect: newRedirect,
            paths: { sourcePath, targetPath },
          };
        } else {
          return {
            success: false,
            action: "error",
            reason: "Failed to create redirect URL",
          };
        }
      }

      // Check if existing redirect has correct target path
      if (existingRedirect.target === targetPath) {
        this.stats.redirectsAlreadyCorrect++;
        return {
          success: true,
          action: "already_correct",
          redirect: existingRedirect,
          paths: { sourcePath, targetPath },
        };
      }

      // Update redirect URL (delete and recreate with correct path)
      const deleted = await ShopifyService.deleteRedirectURL(shop.domain, existingRedirect.id);

      if (!deleted) {
        return {
          success: false,
          action: "error",
          reason: "Failed to delete existing redirect",
        };
      }

      const newRedirect = await ShopifyService.createRedirectURL(shop.domain, {
        oldPath: sourcePath,
        newPath: targetPath,
      });

      if (newRedirect) {
        this.stats.redirectsUpdated++;
        return {
          success: true,
          action: "updated",
          redirect: newRedirect,
          oldTarget: existingRedirect.target,
          newTarget: targetPath,
          paths: { sourcePath, targetPath },
        };
      } else {
        return {
          success: false,
          action: "error",
          reason: "Failed to recreate redirect URL after deletion",
        };
      }
    } catch (error) {
      this.stats.errors++;
      return {
        success: false,
        action: "error",
        reason: error.message,
        error: error,
      };
    }
  }

  /**
   * Process a single shop
   * @param {string} domain - Shop domain to process
   */
  async processSingleShop(domain) {
    console.log(`🔍 Checking LLMs text redirect URL for: ${domain}`);
    console.log("=".repeat(60));

    try {
      // Get shop details
      const shop = await ShopService.getShop(domain);
      if (!shop) {
        console.error(`❌ Shop not found: ${domain}`);
        return;
      }

      console.log(`✅ Shop found: ${shop.name} (ID: ${shop.id})`);
      this.stats.totalShops = 1;

      // Check and fix redirect URL
      const result = await this.checkShopRedirectUrl(shop);
      this.stats.processedShops = 1;

      // Display result
      const logPrefix = `${shop.domain}:`;
      switch (result.action) {
        case "skipped":
          console.log(`${logPrefix} ⏭️  Skipped - ${result.reason}`);
          break;
        case "created":
          console.log(`${logPrefix} ✅ Created redirect: ${result.paths.sourcePath} → ${result.paths.targetPath}`);
          console.log(`   Full URL: ${shop.url}${result.paths.sourcePath}`);
          break;
        case "updated":
          console.log(`${logPrefix} 🔄 Updated redirect: ${result.paths.sourcePath}`);
          console.log(`   Old target: ${result.oldTarget}`);
          console.log(`   New target: ${result.newTarget}`);
          console.log(`   Full URL: ${shop.url}${result.paths.sourcePath}`);
          break;
        case "already_correct":
          console.log(`${logPrefix} ✓ Already correct: ${result.paths.sourcePath} → ${result.paths.targetPath}`);
          console.log(`   Full URL: ${shop.url}${result.paths.sourcePath}`);
          break;
        case "error":
          console.error(`${logPrefix} ❌ Error - ${result.reason}`);
          if (result.error) {
            console.error(`   Details: ${result.error.message}`);
          }
          break;
      }

      console.log("\n" + "=".repeat(60));
      this.printSummary(0);
    } catch (error) {
      console.error("❌ Error processing shop:", error.message);
      this.stats.errors++;
      this.printSummary(0);
    }
  }

  /**
   * Process all active shops in chunks
   * @param {number} batchSize - Number of shops to process in each batch
   */
  async processAllShops(batchSize = 50) {
    console.log("🚀 Starting LLMs text redirect URL check for all active shops...");
    console.log(`📊 Processing shops in batches of ${batchSize}`);

    const startTime = Date.now();

    try {
      // Use iterateOverAllShops for memory-efficient processing
      for await (const shopBatch of ShopService.iterateOverAllShops(
        {
          status: ACTIVE,
          access_token: { [Op.ne]: null },
        },
        ["id", "domain", "url"] // only fetch needed fields
      )) {
        if (shopBatch.length === 0) break;

        this.stats.totalShops += shopBatch.length;
        console.log(`\n📦 Processing batch of ${shopBatch.length} shops...`);

        // Process shops in parallel within the batch
        const batchPromises = shopBatch.map(async (shop) => {
          try {
            const result = await this.checkShopRedirectUrl(shop);
            this.stats.processedShops++;

            const shopNumber = this.stats.processedShops;
            const logPrefix = `[${shopNumber}] ${shop.domain}:`;

            switch (result.action) {
              case "skipped":
                console.log(`${logPrefix} ⏭️  Skipped - ${result.reason}`);
                break;
              case "created":
                console.log(
                  `${logPrefix} ✅ Created redirect: ${result.paths.sourcePath} → ${result.paths.targetPath}`
                );
                break;
              case "updated":
                console.log(`${logPrefix} 🔄 Updated redirect: ${result.paths.sourcePath}`);
                console.log(`    Old target: ${result.oldTarget}`);
                console.log(`    New target: ${result.newTarget}`);
                break;
              case "already_correct":
                console.log(`${logPrefix} ✓ Already correct: ${result.paths.sourcePath} → ${result.paths.targetPath}`);
                break;
              case "error":
                console.error(`${logPrefix} ❌ Error - ${result.reason}`);
                if (result.error) {
                  console.error(`    Details: ${result.error.message}`);
                }
                break;
            }

            return result;
          } catch (error) {
            this.stats.processedShops++;
            this.stats.errors++;
            console.error(`[${this.stats.processedShops}] ${shop.domain}: ❌ Unexpected error - ${error.message}`);
            return { success: false, action: "error", reason: error.message };
          }
        });

        // Wait for all shops in the batch to complete
        await Promise.allSettled(batchPromises);

        // Add a small delay between batches to avoid overwhelming the API
        if (shopBatch.length === batchSize) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }

      const endTime = Date.now();
      const duration = Math.round((endTime - startTime) / 1000);

      this.printSummary(duration);
    } catch (error) {
      console.error("❌ Fatal error during processing:", error);
      throw error;
    }
  }

  /**
   * Print summary statistics
   * @param {number} duration - Processing duration in seconds
   */
  printSummary(duration) {
    console.log("\n" + "=".repeat(60));
    console.log("📊 LLMS TEXT REDIRECT URL CHECK SUMMARY");
    console.log("=".repeat(60));
    console.log(`⏱️  Total processing time: ${duration} seconds`);
    console.log(`🏪 Total shops processed: ${this.stats.processedShops}/${this.stats.totalShops}`);
    console.log(`📄 Shops with LLMs text generated: ${this.stats.shopsWithGeneration}`);
    console.log(`🔍 Redirect URLs checked: ${this.stats.redirectsChecked}`);
    console.log(`✅ Redirect URLs created: ${this.stats.redirectsCreated}`);
    console.log(`🔄 Redirect URLs updated: ${this.stats.redirectsUpdated}`);
    console.log(`✓ Redirect URLs already correct: ${this.stats.redirectsAlreadyCorrect}`);
    console.log(`❌ Errors encountered: ${this.stats.errors}`);

    if (this.stats.errors > 0) {
      console.log(`\n⚠️  ${this.stats.errors} errors occurred during processing. Check the logs above for details.`);
    }

    console.log("=".repeat(60));
  }
}

// Main execution
(async () => {
  try {
    const domain = process.argv[2];
    const checker = new LlmsTxtRedirectChecker();

    if (domain) {
      // Process single shop
      console.log(`🎯 Single shop mode: ${domain}`);
      await checker.processSingleShop(domain);
      console.log("✅ Single shop redirect URL check completed!");
    } else {
      // Process all shops
      console.log("🌐 All shops mode");
      await checker.processAllShops();
      console.log("✅ All shops redirect URL check completed successfully!");
    }
  } catch (error) {
    console.error("❌ Command failed:", error);
    process.exit(1);
  } finally {
    process.exit(0);
  }
})();

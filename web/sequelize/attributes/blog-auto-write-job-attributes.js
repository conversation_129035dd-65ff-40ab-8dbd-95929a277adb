const { DataTypes } = require("sequelize");
const { timestampFields } = require("../config/common-options");
const BlogAutoWriteJobStatus = require("storeseo-enums/blogAutoWrite/jobStatus");
const BlogAutoWriteJobSteps = require("storeseo-enums/blogAutoWrite/jobSteps");

/**
 * @type {import("sequelize").ModelAttributes}
 */
module.exports = {
  shop_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  article_id: {
    type: DataTypes.INTEGER,
    allowNull: true, // NULL if article deleted (preserves audit trail)
    references: {
      model: "articles",
      key: "id",
    },
    onDelete: "SET NULL", // Preserve audit trail
  },
  status: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: BlogAutoWriteJobStatus.PENDING,
  },
  progress: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0,
      max: 100,
    },
  },
  input_data: {
    type: DataTypes.JSONB,
    allowNull: false,
    defaultValue: {},
  },
  estimated_credit_usage: {
    type: DataTypes.JSONB,
    allowNull: false,
    defaultValue: {},
    comment: "Immutable estimation from CreditEstimationService (set at job creation)",
  },
  credit_usage: {
    type: DataTypes.JSONB,
    allowNull: false,
    defaultValue: {},
    comment: "Mutable runtime tracking updated during processing",
  },
  error_message: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  processing_started_at: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  processing_completed_at: {
    type: DataTypes.DATE,
    allowNull: true,
  },

  // Step tracking fields for job recovery and detailed progress monitoring
  steps: {
    type: DataTypes.JSONB,
    allowNull: false,
    defaultValue: BlogAutoWriteJobSteps.getDefaultSteps(),
    comment: "Detailed step tracking for job recovery and progress monitoring",
  },
  retry_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0,
    },
    comment: "Number of times this job has been retried after failure",
  },
  max_retries: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 3,
    validate: {
      min: 0,
      max: 10,
    },
    comment: "Maximum number of retries allowed for this job",
  },

  // Virtual fields for computed values (automatically included in JSON)
  currentProgress: {
    type: DataTypes.VIRTUAL,
    get() {
      return BlogAutoWriteJobSteps.calculateProgress(this.steps || []);
    },
  },
  nextStep: {
    type: DataTypes.VIRTUAL,
    get() {
      const steps = this.steps || [];
      const nextStep = steps.find((step) => !step.completed);
      return nextStep ? nextStep.step : null;
    },
  },
  canRetry: {
    type: DataTypes.VIRTUAL,
    get() {
      return this.retry_count < this.max_retries;
    },
  },
  lastFailedStep: {
    type: DataTypes.VIRTUAL,
    get() {
      const steps = this.steps || [];
      const failedStep = steps.find((step) => step.error && !step.completed);
      return failedStep ? failedStep.step : null;
    },
  },
  ...timestampFields,
};

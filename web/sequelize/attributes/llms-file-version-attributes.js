const { DataTypes } = require("sequelize");
const { timestampFields } = require("../config/common-options");

module.exports = {
  shop_id: {
    type: DataTypes.BIGINT,
    allowNull: false,
    references: {
      model: "shops",
      key: "id",
    },
    onUpdate: "CASCADE",
    onDelete: "CASCADE",
  },

  file_url: {
    type: DataTypes.STRING(500),
    allowNull: false,
    comment: "GCP bucket public URL for the file",
  },
  file_path: {
    type: DataTypes.STRING(500),
    allowNull: false,
    comment: "GCP bucket file path",
  },
  file_size: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: "File size in bytes",
  },
  generated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    comment: "Timestamp when the file was generated",
  },
  generated_by: {
    type: DataTypes.ENUM("manual", "scheduled"),
    allowNull: false,
    defaultValue: "manual",
    comment: "How the file was generated - manual or scheduled",
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: "Optional notes about the generation",
  },
  ...timestampFields,
};

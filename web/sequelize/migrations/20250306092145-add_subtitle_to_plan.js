"use strict";

const { SUBSCRIPTION_PLAN } = require("../config/table-names");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn(SUBSCRIPTION_PLAN, "subtitle", {
      type: Sequelize.STRING,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn(SUBSCRIPTION_PLAN, "subtitle");
  },
};

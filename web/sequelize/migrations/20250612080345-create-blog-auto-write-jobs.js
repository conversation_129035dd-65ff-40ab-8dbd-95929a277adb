"use strict";

const attributes = require("../attributes/blog-auto-write-job-attributes");
const { BLOG_AUTO_WRITE_JOBS } = require("../config/table-names");
const BlogAutoWriteJobSteps = require("storeseo-enums/blogAutoWrite/jobSteps");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Filter out virtual fields from attributes for migration
      const { currentProgress, nextStep, canRetry, lastFailedStep, ...dbAttributes } = attributes;

      await queryInterface.createTable(
        BLOG_AUTO_WRITE_JOBS,
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          ...dbAttributes,
          // Override the steps default to use the correct 9-step workflow
          steps: {
            type: Sequelize.JSONB,
            allowNull: false,
            defaultValue: BlogAutoWriteJobSteps.getDefaultSteps(),
          },
        },
        { transaction }
      );

      // Create indexes for performance
      await queryInterface.addIndex(BLOG_AUTO_WRITE_JOBS, ["shop_id"], {
        name: "idx_blog_auto_write_jobs_shop_id",
        transaction,
      });

      await queryInterface.addIndex(BLOG_AUTO_WRITE_JOBS, ["status"], {
        name: "idx_blog_auto_write_jobs_status",
        transaction,
      });

      await queryInterface.addIndex(BLOG_AUTO_WRITE_JOBS, ["article_id"], {
        name: "idx_blog_auto_write_jobs_article_id",
        transaction,
      });

      await queryInterface.addIndex(BLOG_AUTO_WRITE_JOBS, ["created_at"], {
        name: "idx_blog_auto_write_jobs_created_at",
        transaction,
      });

      // Composite index for shop-specific queries
      await queryInterface.addIndex(BLOG_AUTO_WRITE_JOBS, ["shop_id", "status"], {
        name: "idx_blog_auto_write_jobs_shop_status",
        transaction,
      });

      await queryInterface.addIndex(BLOG_AUTO_WRITE_JOBS, ["shop_id", "created_at"], {
        name: "idx_blog_auto_write_jobs_shop_created",
        transaction,
      });

      // Step tracking indexes
      await queryInterface.addIndex(BLOG_AUTO_WRITE_JOBS, ["retry_count"], {
        name: "idx_blog_auto_write_jobs_retry_count",
        transaction,
      });

      await queryInterface.addIndex(BLOG_AUTO_WRITE_JOBS, ["status", "retry_count"], {
        name: "idx_blog_auto_write_jobs_status_retry",
        transaction,
      });

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      console.error("Migration failed:", err);
      throw err;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      await queryInterface.dropTable(BLOG_AUTO_WRITE_JOBS, { transaction });
      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      console.error("Migration rollback failed:", err);
      throw err;
    }
  },
};

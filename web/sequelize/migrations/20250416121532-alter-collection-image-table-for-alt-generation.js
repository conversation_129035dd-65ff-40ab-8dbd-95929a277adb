"use strict";

const { COLLECTION_IMAGE } = require("../config/table-names");
const AltTextOptimizationStatus = require("storeseo-enums/altTextOptimization");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn(
        COLLECTION_IMAGE,
        "alt_text_optimization_status",
        {
          type: Sequelize.STRING(50),
          defaultValue: AltTextOptimizationStatus.NOT_OPTIMIZED,
          allowNull: false,
        },
        { transaction }
      );

      await queryInterface.addColumn(
        COLLECTION_IMAGE,
        "alt_text_optimized_at",
        {
          type: Sequelize.DATE,
          allowNull: true,
        },
        { transaction }
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn(COLLECTION_IMAGE, "alt_text_optimization_status", { transaction });
      await queryInterface.removeColumn(COLLECTION_IMAGE, "alt_text_optimized_at", { transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};

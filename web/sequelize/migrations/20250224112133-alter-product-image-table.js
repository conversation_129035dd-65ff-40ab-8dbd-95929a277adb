"use strict";
const imageOptimization = require("storeseo-enums/imageOptimization");
const { PRODUCT_IMAGE } = require("../config/table-names");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn(PRODUCT_IMAGE, "optimization_status", {
      type: Sequelize.STRING(50),
      defaultValue: imageOptimization.NOT_OPTIMIZED,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn(PRODUCT_IMAGE, "optimization_status", {
      type: Sequelize.ENUM(...Object.keys(imageOptimization)),
      defaultValue: imageOptimization.defaultValue,
    });
  },
};

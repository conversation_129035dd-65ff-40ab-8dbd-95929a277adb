"use strict";
const { ARTICLE_IMAGES } = require("../config/table-names");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Remove the old unique constraint if it exists
    try {
      await queryInterface.removeConstraint(ARTICLE_IMAGES, "shopid_articleid_unique");
    } catch (error) {
      console.log("Old constraint 'shopid_articleid_unique' not found, continuing...");
    }

    // Add the new unique constraint with three fields including existing media_id
    await queryInterface.addConstraint(ARTICLE_IMAGES, {
      fields: ["shop_id", "article_id", "media_id"],
      type: "unique",
      name: "article_images_shopid_articleid_mediaid_unique",
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove the new unique constraint
    try {
      await queryInterface.removeConstraint(ARTICLE_IMAGES, "article_images_shopid_articleid_mediaid_unique");
    } catch (error) {
      console.log("New constraint 'article_images_shopid_articleid_mediaid_unique' not found, continuing...");
    }

    // Add back the old unique constraint
    try {
      await queryInterface.addConstraint(ARTICLE_IMAGES, {
        fields: ["shop_id", "article_id"],
        type: "unique",
        name: "shopid_articleid_unique",
      });
    } catch (error) {
      console.log("Could not add old constraint 'shopid_articleid_unique', it might already exist...");
    }
  },
};

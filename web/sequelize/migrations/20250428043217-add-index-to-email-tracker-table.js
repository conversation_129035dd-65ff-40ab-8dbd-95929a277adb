'use strict';

const { EMAIL_TRACKER } = require("../config/table-names")

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.addIndex(EMAIL_TRACKER, ["status"], {
      name: "email_tracker_status_index",
      unique: false
    })
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */

    await queryInterface.removeIndex(EMAIL_TRACKER, "email_tracker_status_index")
  }
};

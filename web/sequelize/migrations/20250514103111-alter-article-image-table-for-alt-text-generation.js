"use strict";

const AltTextOptimizationStatus = require("storeseo-enums/altTextOptimization");
const { ARTICLE_IMAGES } = require("../config/table-names");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn(
        ARTICLE_IMAGES,
        "alt_text_optimization_status",
        {
          type: Sequelize.STRING(50),
          defaultValue: AltTextOptimizationStatus.NOT_OPTIMIZED,
          allowNull: false,
        },
        { transaction }
      );

      await queryInterface.addColumn(
        ARTICLE_IMAGES,
        "alt_text_optimized_at",
        {
          type: Sequelize.DATE,
          allowNull: true,
        },
        { transaction }
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn(ARTICLE_IMAGES, "alt_text_optimization_status", { transaction });
      await queryInterface.removeColumn(ARTICLE_IMAGES, "alt_text_optimized_at", { transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};

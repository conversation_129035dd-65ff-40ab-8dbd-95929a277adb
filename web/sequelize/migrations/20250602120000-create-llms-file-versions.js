"use strict";
const { LLMS_FILE_VERSIONS } = require("../config/table-names");
const attributes = require("../attributes/llms-file-version-attributes");

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable(LLMS_FILE_VERSIONS, {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      ...attributes,
    });

    // Add indexes for better query performance
    await queryInterface.addIndex(LLMS_FILE_VERSIONS, ["shop_id"], {
      name: "idx_llms_file_versions_shop_id",
    });

    await queryInterface.addIndex(LLMS_FILE_VERSIONS, ["shop_id", "generated_at"], {
      name: "idx_llms_file_versions_shop_generated_at",
    });

    await queryInterface.addIndex(LLMS_FILE_VERSIONS, ["generated_by"], {
      name: "idx_llms_file_versions_generated_by",
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable(LLMS_FILE_VERSIONS);
  },
};

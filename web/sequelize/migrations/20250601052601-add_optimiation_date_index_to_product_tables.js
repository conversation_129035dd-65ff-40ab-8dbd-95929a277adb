"use strict";

const tableNames = require("../config/table-names");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addIndex(tableNames.PRODUCT, {
      name: "idx_ai_optimization_status_optimized_at",
      fields: ["ai_optimization_status", "ai_optimized_at"],
      using: "BTREE",
    });

    await queryInterface.addIndex(tableNames.PRODUCT_IMAGE, {
      name: "idx_alt_text_optimization_status_optimized_at",
      fields: ["alt_text_optimization_status", "alt_text_optimized_at"],
      using: "BTREE",
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeIndex(tableNames.PRODUCT, "idx_ai_optimization_status_optimized_at");
    await queryInterface.removeIndex(tableNames.PRODUCT_IMAGE, "idx_alt_text_optimization_status_optimized_at");
  },
};

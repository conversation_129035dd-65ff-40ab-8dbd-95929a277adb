"use strict";

const { SUBSCRIPTION_PLAN } = require("../config/table-names");
const { FREE, PRO } = require("storeseo-enums/planType");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Update FREE plans - set llms_txt_generation to false
      await queryInterface.sequelize.query(
        `UPDATE ${SUBSCRIPTION_PLAN}
         SET rules = rules || '{"llms_txt_generation": false}'::jsonb
         WHERE type = :freeType`,
        {
          replacements: { freeType: FREE },
          transaction,
        }
      );

      // Update PRO plans - set llms_txt_generation to true
      await queryInterface.sequelize.query(
        `UPDATE ${SUBSCRIPTION_PLAN}
         SET rules = rules || '{"llms_txt_generation": true}'::jsonb
         WHERE type = :proType`,
        {
          replacements: { proType: PRO },
          transaction,
        }
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Remove llms_txt_generation from all subscription plans
      await queryInterface.sequelize.query(
        `UPDATE ${SUBSCRIPTION_PLAN}
         SET rules = rules - 'llms_txt_generation'`,
        { transaction }
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};

"use strict";
const altTextOptimizationStatus = require("storeseo-enums/altTextOptimization");
const { PRODUCT_IMAGE } = require("../config/table-names");
const imageOptimization = require("storeseo-enums/imageOptimization");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.addColumn(PRODUCT_IMAGE, "original_alt_text", {
      type: Sequelize.TEXT,
    });
    await queryInterface.addColumn(PRODUCT_IMAGE, "alt_text_optimization_status", {
      type: Sequelize.STRING(50),
      defaultValue: altTextOptimizationStatus.NOT_OPTIMIZED,
    });
    await queryInterface.addColumn(PRODUCT_IMAGE, "alt_text_optimization_meta", {
      type: Sequelize.JSON,
      defaultValue: {},
    });
    await queryInterface.addColumn(PRODUCT_IMAGE, "alt_text_optimization_stats", {
      type: Sequelize.JSON,
      defaultValue: imageOptimization.optimizationStats,
    });
    await queryInterface.addColumn(PRODUCT_IMAGE, "alt_text_optimized_at", {
      type: Sequelize.DATE,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.removeColumn(PRODUCT_IMAGE, "original_alt_text");
    await queryInterface.removeColumn(PRODUCT_IMAGE, "alt_text_optimization_status");
    await queryInterface.removeColumn(PRODUCT_IMAGE, "alt_text_optimization_meta");
    await queryInterface.removeColumn(PRODUCT_IMAGE, "alt_text_optimization_stats");
    await queryInterface.removeColumn(PRODUCT_IMAGE, "alt_text_optimized_at");
  },
};

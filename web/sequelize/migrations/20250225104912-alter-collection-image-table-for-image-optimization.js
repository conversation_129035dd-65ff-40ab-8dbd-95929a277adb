"use strict";

const imageOptimization = require("storeseo-enums/imageOptimization");
const { COLLECTION_IMAGE } = require("../config/table-names");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.transaction(async (transaction) => {
      // Change optimization_status to STRING
      await queryInterface.changeColumn(
        COLLECTION_IMAGE,
        "optimization_status",
        {
          type: Sequelize.STRING(50),
          defaultValue: imageOptimization.NOT_OPTIMIZED,
        },
        { transaction }
      );

      // Remove optimization_meta column
      await queryInterface.removeColumn(COLLECTION_IMAGE, "optimization_meta", { transaction });
      // Remove optimization_setting column
      await queryInterface.removeColumn(COLLECTION_IMAGE, "optimization_setting", { transaction });

      // Add file_size column
      await queryInterface.addColumn(
        COLLECTION_IMAGE,
        "file_size",
        {
          type: Sequelize.INTEGER,
        },
        { transaction }
      );
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.transaction(async (transaction) => {
      // Remove file_size column
      await queryInterface.removeColumn(COLLECTION_IMAGE, "file_size", { transaction });

      // Add optimization_meta column
      await queryInterface.addColumn(
        COLLECTION_IMAGE,
        "optimization_meta",
        {
          type: Sequelize.JSON,
          defaultValue: imageOptimization.optimizationMeta,
        },
        { transaction }
      );
      // Add optimization_setting column
      await queryInterface.addColumn(
        COLLECTION_IMAGE,
        "optimization_setting",
        {
          type: Sequelize.JSON,
        },
        { transaction }
      );

      // Revert optimization_status back to ENUM
      await queryInterface.changeColumn(
        COLLECTION_IMAGE,
        "optimization_status",
        {
          type: Sequelize.ENUM,
          values: Object.keys(imageOptimization),
        },
        { transaction }
      );
    });
  },
};

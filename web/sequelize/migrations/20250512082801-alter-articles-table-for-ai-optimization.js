"use strict";

const { ARTICLE } = require("../config/table-names");
const AiOptimizationStatus = require("storeseo-enums/aiOptimization");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.transaction(async (transaction) => {
      // Add ai_optimization_status column
      await queryInterface.addColumn(
        ARTICLE,
        "ai_optimization_status",
        {
          type: Sequelize.STRING(50),
          defaultValue: AiOptimizationStatus.NOT_OPTIMIZED,
          allowNull: false,
        },
        { transaction }
      );

      // Add ai_optimized_at column
      await queryInterface.addColumn(
        ARTICLE,
        "ai_optimized_at",
        {
          type: Sequelize.DATE,
          allowNull: true,
        },
        { transaction }
      );
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.transaction(async (transaction) => {
      // Remove columns
      await queryInterface.removeColumn(ARTICLE, "ai_optimized_at", { transaction });
      await queryInterface.removeColumn(ARTICLE, "ai_optimization_status", { transaction });
    });
  },
};

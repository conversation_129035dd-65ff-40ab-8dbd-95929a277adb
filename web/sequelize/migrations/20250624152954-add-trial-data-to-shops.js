"use strict";

const { SHOP } = require("../config/table-names");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Add trial_data column
      await queryInterface.addColumn(
        SHOP,
        "trial_data",
        {
          type: Sequelize.JSONB,
          allowNull: true,
          defaultValue: null,
          comment: "Trial-related data including status, dates, and limits",
        },
        { transaction }
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Remove column
      await queryInterface.removeColumn(SHOP, "trial_data", { transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};

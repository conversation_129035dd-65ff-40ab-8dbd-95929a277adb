"use strict";

const { PRODUCT } = require("../config/table-names");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.transaction(async (transaction) => {
      try {
        // Add is_synced column to products table
        await queryInterface.addColumn(
          PRODUCT,
          "is_synced",
          {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
          },
          { transaction }
        );

        // Create index for performance on sync status queries
        await queryInterface.addIndex(PRODUCT, ["shop_id", "is_synced"], {
          name: "idx_products_shop_synced",
          transaction,
        });
      } catch (error) {
        console.log("error: ", err);
        throw error;
      }
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.transaction(async (transaction) => {
      try {
        // Remove indexes
        await queryInterface.removeIndex(PRODUCT, "idx_products_shop_synced", { transaction });

        // Remove column
        await queryInterface.removeColumn(PRODUCT, "is_synced", { transaction });
      } catch (error) {
        console.log("error: ", err);
        throw error;
      }
    });
  },
};

"use strict";

const { ARTICLE } = require("../config/table-names");
const BlogGenerationStatus = require("storeseo-enums/blogGenerationStatus");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Add new columns to articles table for AI generation support
      await queryInterface.addColumn(
        ARTICLE,
        "generation_status",
        {
          type: Sequelize.STRING(50),
          defaultValue: BlogGenerationStatus.MANUAL,
          allowNull: false,
        },
        { transaction }
      );

      await queryInterface.addColumn(
        ARTICLE,
        "is_ai_generated",
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
          allowNull: false,
        },
        { transaction }
      );

      // Create indexes for performance
      await queryInterface.addIndex(
        ARTICLE,
        ["generation_status"],
        {
          name: "idx_articles_generation_status",
          transaction,
        }
      );

      await queryInterface.addIndex(
        ARTICLE,
        ["is_ai_generated"],
        {
          name: "idx_articles_ai_generated",
          transaction,
        }
      );

      // Create composite index for shop_id + generation_status queries
      await queryInterface.addIndex(
        ARTICLE,
        ["shop_id", "generation_status"],
        {
          name: "idx_articles_shop_generation_status",
          transaction,
        }
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      console.error("Migration failed:", err);
      throw err;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Remove indexes
      await queryInterface.removeIndex(ARTICLE, "idx_articles_shop_generation_status", { transaction });
      await queryInterface.removeIndex(ARTICLE, "idx_articles_ai_generated", { transaction });
      await queryInterface.removeIndex(ARTICLE, "idx_articles_generation_status", { transaction });

      // Remove columns
      await queryInterface.removeColumn(ARTICLE, "is_ai_generated", { transaction });
      await queryInterface.removeColumn(ARTICLE, "generation_status", { transaction });

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      console.error("Migration rollback failed:", err);
      throw err;
    }
  },
};

"use strict";
const { Model } = require("sequelize");
const { LLMS_FILE_VERSIONS } = require("../config/table-names");
const attributes = require("../attributes/llms-file-version-attributes");
const { timestamps } = require("../config/common-options");

module.exports = (sequelize, DataTypes) => {
  class LlmsFileVersion extends Model {
    static associate(models) {
      // Define association with Shop model
      LlmsFileVersion.belongsTo(models.Shop, { 
        foreignKey: "shop_id",
        as: "shop"
      });
    }
  }

  LlmsFileVersion.init(attributes, {
    sequelize,
    modelName: "LlmsFileVersion",
    tableName: LLMS_FILE_VERSIONS,
    ...timestamps,
  });

  return LlmsFileVersion;
};

"use strict";
const { Model } = require("sequelize");
const attributes = require("../attributes/blog-auto-write-job-attributes");
const { timestamps } = require("../config/common-options");
const { BLOG_AUTO_WRITE_JOBS } = require("../config/table-names");

module.exports = (sequelize, DataTypes) => {
  class BlogAutoWriteJob extends Model {
    static associate(models) {
      // define association here
      BlogAutoWriteJob.belongsTo(models.Article, {
        as: "article",
        foreignKey: "article_id",
        onDelete: "SET NULL", // Preserve audit trail
      });
    }
  }

  BlogAutoWriteJob.init(attributes, {
    sequelize,
    modelName: "BlogAutoWriteJob",
    tableName: BLOG_AUTO_WRITE_JOBS,
    ...timestamps,
  });

  return BlogAutoWriteJob;
};

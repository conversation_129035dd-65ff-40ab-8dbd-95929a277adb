"use strict";

const staticTags = require("storeseo-enums/mailchimp/staticTags");
const FluentCrmService = require("../../api/services/FluentCrmService");
const { capitalize } = require("lodash");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      console.log("\n");
      console.log("Creating trial tags in fluentcrm");
      console.log("=".repeat(60));

      const tags = Object.values(staticTags).filter((t) => t.includes("TRIAL_"));

      const {
        data: {
          tags: { data: existingTags },
        },
      } = await FluentCrmService.Client.get(`/tags?search=trial-`);

      for (let tag of tags) {
        const slug = tag.replace("_", "-").toLowerCase();
        const existingTag = existingTags.find((t) => t.slug === slug);

        if (existingTag) {
          console.log(`- ℹ️ Tag ${tag} already exists in fluentcrm, updating to db.`);
          await FluentCrmService.updateTagInDb(existingTag);
          continue;
        }

        const title = capitalize(tag.replace("_", " ").toLowerCase());
        const description = `Tag for ${title}`;
        const createdTag = await FluentCrmService.createTag(title, slug, description);
        createdTag.tag_id = createdTag.id;
        delete createdTag.id;
        await FluentCrmService.saveTagInDb(createdTag);
        console.log(`- ✅ Creating tag ${tag} with slug ${slug} & title ${title} & description ${description}`);
      }
      console.log("=".repeat(60));
    } catch (error) {
      console.error(error);
    }
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
  },
};

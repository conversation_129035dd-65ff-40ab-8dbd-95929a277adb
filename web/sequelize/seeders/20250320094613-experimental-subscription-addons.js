"use strict";

const { <PERSON>MAGE_OPTIMIZER, AI_OPTIMIZER } = require("storeseo-enums/subscriptionAddonGroup");
const { SUBSCRIPTION_ADDON } = require("../config/table-names");
const { FREE, PRO, COMBINED } = require("storeseo-enums/subscriptionAddonType");
const { MONTHLY, CREDIT, LIFETIME } = require("storeseo-enums/subscriptionAddonInterval");
const { ACTIVE, HIDDEN } = require("storeseo-enums/subscriptionAddonStatus");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.bulkUpdate(SUBSCRIPTION_ADDON, { status: HIDDEN }, { group: IMAGE_OPTIMIZER });

    const addons = [
      {
        name: "Free",
        group: IMAGE_OPTIMIZER,
        type: COMBINED,
        interval: LIFETIME,
        limit: 50,
      },
      {
        name: "Essential",
        group: IMAGE_OPTIMIZER,
        type: COMBINED,
        interval: MONTHLY,
        limit: 2000,
      },
      {
        name: "Growth",
        group: IMAGE_OPTIMIZER,
        type: COMBINED,
        interval: MONTHLY,
        limit: 5000,
      },
      {
        name: "Advanced",
        group: IMAGE_OPTIMIZER,
        type: COMBINED,
        interval: MONTHLY,
        limit: 50000,
      },
      {
        name: "Elite",
        group: IMAGE_OPTIMIZER,
        type: COMBINED,
        interval: MONTHLY,
        limit: 100000,
      },
      {
        name: "Free",
        group: AI_OPTIMIZER,
        type: COMBINED,
        interval: LIFETIME,
        limit: 10,
      },
      {
        name: "Essential",
        group: AI_OPTIMIZER,
        type: COMBINED,
        interval: MONTHLY,
        limit: 5000,
      },
      {
        name: "Growth",
        group: AI_OPTIMIZER,
        type: COMBINED,
        interval: MONTHLY,
        limit: 25000,
      },

      {
        name: "Advanced",
        group: AI_OPTIMIZER,
        type: COMBINED,
        interval: MONTHLY,
        limit: 50000,
      },
      {
        name: "Elite",
        group: AI_OPTIMIZER,
        type: COMBINED,
        interval: MONTHLY,
        limit: 100000,
      },
    ];

    await queryInterface.bulkInsert(
      SUBSCRIPTION_ADDON,
      addons.map((a) => ({
        ...a,
        price: 0,
        discount: 0,
        subtotal: 0,
        status: ACTIVE,
        meta: JSON.stringify(a.meta),
        created_at: Sequelize.fn("NOW"),
        updated_at: Sequelize.fn("NOW"),
      })),
      {}
    );
  },

  async down(queryInterface, Sequelize) {
    // await queryInterface.bulkDelete(SUBSCRIPTION_ADDON, null, { truncate: true });
  },
};

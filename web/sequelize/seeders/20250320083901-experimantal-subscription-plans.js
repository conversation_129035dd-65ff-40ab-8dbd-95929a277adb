"use strict";
const { kebabCase } = require("lodash");
const plans = require("../../api/config/experimantalPlans");
const { SUBSCRIPTION_PLAN } = require("../config/table-names");
const { ANNUALLY } = require("storeseo-enums/planInterval");
const PlanStatus = require("storeseo-enums/planStatus");
const PlanType = require("storeseo-enums/planType");

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.bulkUpdate(
      SUBSCRIPTION_PLAN,
      { status: PlanStatus.HIDDEN },
      { type: { [Sequelize.Op.not]: PlanType.FREE } }
    );

    const serializedPlans = plans.map((plan) => ({
      ...plan,
      slug: kebabCase(plan.name.toLowerCase()) + (plan.interval === ANNUALLY ? "-yearly" : ""),
      subtotal: Number(plan.price) - Number(plan.discount),
      meta: plan.meta ? JSON.stringify(plan.meta) : null,
      rules: JSON.stringify(plan.rules),
      created_at: Sequelize.fn("NOW"),
      updated_at: Sequelize.fn("NOW"),
    }));

    await queryInterface.bulkInsert(SUBSCRIPTION_PLAN, serializedPlans, {});
  },

  async down(queryInterface, Sequelize) {},
};

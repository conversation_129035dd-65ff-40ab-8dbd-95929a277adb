"use strict";
const { kebabCase } = require("lodash");
const plans = require("../../api/config/subscriptionPlans");
const { ANNUALLY } = require("storeseo-enums/planInterval");
const { SubscriptionPlan } = require("../../sequelize");

/** @type {import("sequelize-cli").Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await Promise.all(
      plans.map((plan) => {
        const slug = kebabCase(plan.name.toLowerCase()) + (plan.interval === ANNUALLY ? "-yearly" : "");
        const updateData = {
          subtitle: plan.subtitle,
          rules: plan.rules,
          updated_at: Sequelize.fn("NOW"),
        };
        return SubscriptionPlan.update(updateData, { where: { slug } });
      })
    );
  },

  async down(queryInterface, Sequelize) {},
};
